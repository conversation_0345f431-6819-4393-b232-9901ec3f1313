pipeline {
  agent any
	options{
		timestamps() // 日志时间
	}
	tools{
      nodejs 'nodeJs14.18.3'
  }
	environment {
		SOURCE_DIR = '/home/<USER>/jenkins/jenkins_home/workspace' // jenkins工作目录
		def sshServer = ''
		def git_version = ''
		def master_host = '*************'
  }
  parameters {
        gitParameter description: '请选择构建分支', branch: 'dev', branchFilter: 'origin/(dev.*)', defaultValue: '', name: 'git_fetch', quickFilterEnabled: false, selectedValue: 'NONE', sortMode: 'NONE', tagFilter: '*', type: 'GitParameterDefinition', useRepository: 'https://***************/pd-rs/code/rs-acp-web.git'
  }
  stages {
	stage('连接服务器') {
      steps {
				timeout(time:5, unit:"MINUTES"){
					script{
						sshServer = getServer("*************")
						// 初始化发布分支
						if(params.git_fetch == ''){
							git_version = "dev_0512"
						}else {
							git_version = params.git_fetch
						}
					}
				}
      }
    }
    stage('拉取代码') {
      steps {
        script{
          checkout([$class: 'GitSCM', branches: [[name: git_version]], extensions: [], userRemoteConfigs: [[credentialsId: 'gitLab', url: 'https://***************/pd-rs/code/rs-acp-web.git']]])
        }
      }
    }
    stage('打包构建') {
      steps {
				script{
				    sh "rm -rf ./dist && npm install && npm run build"
				}
      }
    }
	stage('部署镜像') {
      steps {
				script{
				  sshCommand remote: sshServer, command: "sh -x ${SOURCE_DIR}/${JOB_NAME}/deploy/deploy.sh ${SOURCE_DIR}/${JOB_NAME}"
          sshCommand remote: sshServer, command: "docker restart acp-web"
          sshCommand remote: sshServer, command: "sh /home/<USER>/deploy-rs-nginx.sh acp-web"
          sshCommand remote: sshServer, command: "docker restart acp-web-yls acp-web-jds acp-web-jls acp-web-6ks acp-web-2ks acp-web-3ks"
				}
      }
    }
  }

	post{
		success{
			script{
				currentBuild.description = "\n 构建成功"
			}
		}
		failure{
			script{
				currentBuild.description = "\n 构建失败"
			}
		}
		aborted{
			script{
				currentBuild.description = "\n 构建取消"
			}
		}
	}
}

// 声明一个获取远程服务的方法
def getServer(ip){
	def remote = [:]
	remote.name = "${ip}"
	remote.host = ip
	remote.port = 22
	remote.allowAnyHosts = true
	withCredentials([usernamePassword(credentialsId: '*************', passwordVariable: 'password', usernameVariable: 'user_name')]) {
		remote.user = "${user_name}"
		remote.password = "${password}"
	}
	return remote
}
