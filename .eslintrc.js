module.exports = {
  root: true,
  extends: ["plugin:vue/vue2-recommended"],
  rules: {
    // allow async-await
    "generator-star-spacing": "off",
    // allow debugger during development
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
    "vue/no-parsing-error": [
      2,
      {
        "x-invalid-end-tag": false,
      },
    ],
    "vue/no-unused-vars": "warn",
    "vue/no-unused-components": "warn",
    "vue/no-deprecated-slot-attribute": "off",
    "vue/no-deprecated-slot-scope-attribute": "off",
    "vue/multi-word-component-names": "off",
    "vue/no-deprecated-delete-set": "off",
    "vue/no-deprecated-v-on-native-modifier": "off",
    "vue/no-deprecated-v-bind-sync": "off",
    "vue/max-attributes-per-line": "off",
    "vue/attribute-hyphenation": "off",
    "vue/first-attribute-per-line": "off",
    "vue/first-attribute-linebreak": "off",
    "vue/first-attribute-hyphenation": "off",
    "vue/html-indent": "off",
    "vue/attributes-order": "off",
    "vue/html-closing-bracket-newline": "off",
    "vue/html-closing-bracket-spacing": "off",
    "vue/multiline-html-element-content-newline": "off",
    "vue/singleline-html-element-content-newline": "off",
    "vue/html-self-closing": "off",
    "vue/require-explicit-emits": "off",
    "vue/component-definition-name-casing": "off",
    "vue/order-in-components": "off",
    "vue/v-on-event-hyphenation": "off",
    "vue/no-unused-vars": "off",
    "vue/html-quotes": "off",
    "vue/no-multi-spaces": "off",
    "vue/mustache-interpolation-spacing": "off",
    "vue/require-v-for-key": "warn",
    "comma-dangle": "off",
    "no-undef": "off",
    camelcase: "off",
    // 关闭分号检查（允许自由选择是否使用分号）
    semi: "off",
    // 关闭引号类型检查（允许单双引号混用）
    quotes: "off",
    // 关闭缩进检查
    indent: "off",
    // "comma-dangle": [
    //   "error",
    //   {
    //     arrays: "always-multiline",
    //     objects: "only-multiline",
    //     imports: "never",
    //     exports: "never",
    //     functions: "never",
    //   },
    // ],
    "space-before-function-paren": "off",
    "spaced-comment": "off",
  },
  globals: {
    serverConfig: false,
  },
  parserOptions: {
    parser: "@babel/eslint-parser", // 替换为新的解析器
    requireConfigFile: false, // 不需要单独的 Babel 配置文件
    babelOptions: {
      presets: ["@babel/preset-env"], // 使用预设
    },
  },
};
