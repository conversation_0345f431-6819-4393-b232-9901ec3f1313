/* @import url('https://fonts.googleapis.com/css?family=Nunito+Sans:200,200i,400,400i,600,600i,700,700i'); */

body.document-editor {
    width: 15.8cm;
    min-height: 24cm;
    padding: 1cm 2cm 2cm;
    margin: 0.5cm auto;
    border: 1px #D3D3D3 solid;
    border-radius: 5px;
    background: white;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    margin-top: 0px;
    margin-bottom: 0px;
    font-size: 16px;
    line-height: 24px;
    font-family: "Nunito Sans", Verdana, Helvetica, sans-serif;
    box-sizing: border-box;
}
body.document-editor td, body.document-editor th {
    font-size: 0.9em;
}

body.document-editor h1 {
    margin-bottom:1cm;
}

body.document-editor table {
    margin-top:0.5cm;
    margin-bottom:0.5cm;
}

body.document-editor table td {
    border-right: 1px solid #d0d0d0;
    border-bottom: 1px solid #d0d0d0;
}

body.document-editor table td:first-of-type {
    border-left: 1px solid #d0d0d0;
}

body.document-editor table tr:first-of-type td {
    border-top: 1px solid #d0d0d0;
}

body.document-editor table thead th {
    border-top: 1px solid #d0d0d0;
    border-right: 1px solid #d0d0d0;
}

body.document-editor table thead th:first-of-type {
    border-left: 1px solid #d0d0d0;
}
