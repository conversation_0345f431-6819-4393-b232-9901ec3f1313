/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'tr', {
	alt: 'Alt<PERSON>ati<PERSON> Yazı',
	btnUpload: '<PERSON><PERSON><PERSON><PERSON>',
	captioned: 'Başlıklı resim',
	captionPlaceholder: '<PERSON><PERSON>l<PERSON>k',
	infoTab: '<PERSON>sim Bilgisi',
	lockRatio: '<PERSON><PERSON><PERSON>',
	menu: 'Resi<PERSON> Özellikleri',
	pathName: 'Resim',
	pathNameCaption: 'başlık',
	resetSize: '<PERSON>utu Başa Döndür',
	resizer: 'Boyutlandırmak için, tıklayın ve sürükleyin',
	title: '<PERSON>si<PERSON> Özellikler<PERSON>',
	uploadTab: '<PERSON><PERSON><PERSON><PERSON><PERSON> Yükle',
	urlMissing: '<PERSON><PERSON><PERSON> U<PERSON> kaynağı bulunamadı.',
	altMissing: 'Alternatif yazı eksik.'
} );
