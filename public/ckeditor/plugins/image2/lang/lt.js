/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'lt', {
	alt: 'Alternatyvus Te<PERSON>',
	btnUpload: 'Siųsti į serverį',
	captioned: 'Captioned image', // MISSING
	captionPlaceholder: 'Caption', // MISSING
	infoTab: 'Vaizdo informacija',
	lockRatio: 'Iš<PERSON><PERSON>ti proporciją',
	menu: 'Vai<PERSON>do savybės',
	pathName: 'image', // MISSING
	pathNameCaption: 'caption', // MISSING
	resetSize: 'Atstatyti dydį',
	resizer: 'Click and drag to resize', // MISSING
	title: 'Vaizdo savybės',
	uploadTab: 'Si<PERSON>sti',
	urlMissing: 'Paveiksliuko nuorodos nėra.',
	altMissing: 'Alternative text is missing.' // MISSING
} );
