/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'hu', {
	alt: 'Alternatív szöveg',
	btnUpload: '<PERSON><PERSON><PERSON><PERSON> a szerverre',
	captioned: '<PERSON><PERSON><PERSON>zo<PERSON> kép',
	captionPlaceholder: 'Képfelirat',
	infoTab: 'Alaptulajdonságok',
	lockRatio: '<PERSON>r<PERSON><PERSON> megtart<PERSON>a',
	menu: 'Kép tulajdons<PERSON>gai',
	pathName: 'kép',
	pathNameCaption: 'felirat',
	resetSize: '<PERSON><PERSON>eti méret',
	resizer: '<PERSON><PERSON>ts<PERSON> és húzza az átméretezéshez',
	title: 'Kép tulajdonságai',
	uploadTab: 'Feltöltés',
	urlMissing: 'Hi<PERSON><PERSON>zik a kép URL-je',
	altMissing: 'Az alternatív szöveg hiányzik.'
} );
