/*
Copyright (c) 2003-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'es', {
	alt: 'Texto Alternativo',
	btnUpload: 'Enviar al Servidor',
	captioned: 'Imagen subtitulada',
	captionPlaceholder: 'Leyenda',
	infoTab: 'Información de Imagen',
	lockRatio: 'Proporcional',
	menu: 'Propiedades de Imagen',
	pathName: 'image',
	pathNameCaption: 'subtítulo',
	resetSize: 'Tamaño Original',
	resizer: 'Dar clic y arrastrar para cambiar tamaño',
	title: 'Propiedades de Imagen',
	uploadTab: 'Cargar',
	urlMissing: 'Debe indicar la URL de la imagen.',
	altMissing: 'Alternative text is missing.' // MISSING
} );
