<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=11">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="favicon.ico">
    <title>实战平台</title>
    <script type="text/javascript" src="base.js"></script>
    <!-- video 直接播放webRTC视频的必要插件 【npm没有特定的包】 -->
    <script type="text/javascript" defer src="adapter-7.4.0.min.js"></script>
    <script type="text/javascript" defer src="srs.sdk.js"></script>
    <script type="text/javascript" defer src="ckeditor/ckeditor.js"></script>
    <script>
         // 等待base.js执行后更新title
        document.addEventListener('DOMContentLoaded', function() {
          if (window.System) {
            document.title = window.System.global.ImsServerConfig.APP_NAME;
          }
          // console.log(window.System.global.ImsServerConfig.APP_NAME,'window.serverConfig.APP_NAME')
        });
  </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but ihc doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
