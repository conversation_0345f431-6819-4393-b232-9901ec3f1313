{"name": "esp-web", "version": "1.0.0", "author": "", "private": false, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build --no-module", "build:report": "vue-cli-service build --no-module --report", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@fortawesome/fontawesome-free": "^7.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "autofit.js": "^3.2.8", "axios": "^0.18.0", "babel-plugin-component": "^1.1.1", "clipboard": "^2.0.0", "codemirror": "^5.38.0", "core-js": "^3.36.1", "countup.js": "^2.9.0", "cropperjs": "^1.2.2", "crypto-js": "^4.2.0", "dayjs": "^1.7.7", "echarts": "^5.5.0", "echarts-wordcloud": "^1.1.3", "element-resize-detector": "^1.2.4", "element-ui": "^2.15.14", "es6-promise": "^4.2.8", "file-saver": "^2.0.5", "gm-crypto": "^0.1.8", "gs-general-audit": "^1.0.0", "gs-special-name": "^1.0.0", "gs-start-approval": "^1.0.24", "gxx-general-audit": "^1.0.2", "html2canvas": "^1.4.1", "ims-electronic-file-5021": "^1.0.27", "iview-area": "^1.6.0", "jquery": "^3.7.1", "js-calendar": "^1.2.3", "js-cookie": "^2.2.0", "jssip": "^3.10.1", "pixi.js": "^8.10.2", "postcss": "^8.1.0", "print-js": "^1.6.0", "sd-area-selector": "^1.0.4", "sd-batch-start-approval": "^1.0.10", "sd-custom-dialog": "^1.0.14", "sd-data-grid": "^1.2.41", "sd-dic-grid": "^1.0.23", "sd-fm-query": "^1.0.3", "sd-form-dialog": "^1.0.5", "sd-general-history": "^1.0.6", "sd-img-upload": "^1.0.4", "sd-minio-upfile": "^1.0.12", "sd-minio-upimg": "^1.1.2", "sd-oper-authorize": "^1.0.3", "sd-org-selector": "^1.0.7", "sd-portal-components": "^1.0.22", "sd-prison-select": "^1.0.10", "sd-role-assign": "^1.0.3", "sd-room-select": "^1.0.1", "sd-start-approval": "^1.0.18", "sd-statistical-analysis": "^1.0.1", "sd-user-assign": "^1.0.11", "sd-user-selector": "^1.0.27", "simplemde": "^1.11.2", "sortablejs": "^1.7.0", "swiper": "^4.5.1", "tui-image-editor": "^3.15.3", "v-calendar": "^2.3.3", "v-viewer": "^1.7.4", "video.js": "^7.21.7", "videojs-pip": "^1.0.3", "view-design": "^4.7.0", "viewerjs": "^1.11.7", "vue": "^2.7.16", "vue-cron": "^1.0.9", "vue-giant-tree": "^1.0.0", "vue-infinite-scroll": "^2.0.2", "vue-picture-bd-marker": "^1.4.3", "vue-router": "^3.6.5", "vue-seamless-scroll": "^1.1.23", "vue-slick-carousel": "^1.0.6", "vue-to-pdf": "^1.0.0", "vuedraggable": "^2.16.0", "vuex": "^3.6.2", "wangeditor": "^3.1.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/eslint-parser": "^7.28.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.23.2", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-unit-mocha": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^3.0.0-beta.10", "@vue/test-utils": "^1.3.0", "eslint-plugin-vue": "^10.3.0", "less": "^3.5.0", "less-loader": "^8.1.0", "lint-staged": "^6.0.0", "postcss-loader": "^4.0.3", "postcss-pxtorem": "^5.1.1", "vue-awesome-swiper": "^3.1.3", "vue-template-compiler": "^2.7.16", "webpack": "^5.89.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}