import Vue from 'vue'

const Coms = [{
  name: "sjzl",   //总览
  component: () =>
    import ('@/components/mhPageComponents/sjzl.vue')
},
  {
    name: "fbqk",   //基本信息
    component: () =>
      import ('@/components/mhPageComponents/fbqk.vue')
  },
  {

    name: "yxmj",   //工作统计
    component: () =>
      import ('@/components/mhPageComponents/yxmj.vue')
  },
  {
    name: "tjfs",   //工作统计
    component: () =>
      import ('@/components/mhPageComponents/tjfs.vue')
  }
  ,
  {
    name: "jbfb",   //工作统计
    component: () =>
      import ('@/components/mhPageComponents/jbfb.vue')
  }
  ,
  {
    name: "application",   //全部应用
    component: () =>
      import ('@/components/mhPageComponents/application/index.vue')
  },
  {
    name: "zyqk",   //在押情况
    component: () =>
      import ('@/components/mhPageComponents/zyqk/index.vue')
  },
  {
    name: "sycsqk",   //在押情况
    component: () =>
      import ('@/components/mhPageComponents/csqk/index.vue')
  },
  {
    name: "ssjd",   //诉讼阶段
    component: () =>
      import ('@/components/mhPageComponents/ssjd/index.vue')
  },
  {
    name: 'acpDbxx',  //待办消息
    component: () =>
      import ('@/components/mhPageComponents/dbxx/index.vue')
    // import ('@/components/mhPageComponents/messageNotification/index.vue')
  },
  {
    name: 'zdgz',  //重点关注
    component: () =>
      // import ('@/components/mhPageComponents/dbxx/index.vue')
      import ('@/components/mhPageComponents/zdgz/index.vue')
  },
  {
    name: 'zbqk',  //值班情况
    component: () =>
      // import ('@/components/mhPageComponents/dbxx/index.vue')
      import ('@/components/mhPageComponents/zbqk/index.vue')
  },
  {
    name: 'messageNotification',  //待办消息
    component: () =>
      import ('@/components/mhPageComponents/messageNotification/index.vue')
  },
  {
    name: 'todayUpdates',  //今日动态
    component: () =>
      import ('@/components/mhPageComponents/todayUpdates.vue')
  },
  {
    name: 'meetingRoomStatus',  //会见室状态
    component: () =>
      import ('@/components/mhPageComponents/meetingRoomStatus.vue')
  },
  {
    name: 'messageWindow',  //消息通知
    component: () =>
      import ('@/components/mhPageComponents/messageWindow.vue')
  },
  {
    name: 'quickEntrance',  //快捷入口
    component: () =>
      import ('@/components/mhPageComponents/quickEntrance.vue')
  },
  {
    name: 'hjdt',  //会见动态
    component: () =>
      import ('@/components/mhPageComponents/hjdt/index.vue')
  },
  {
    name: 'dynamics',  //出入所动态
    component: () =>
      import ('@/components/mhPageComponents/dynamics/index.vue')
  },
  {
    name: 'medicalupdates',  //就医动态
    component: () =>
      import ('@/components/mhPageComponents/medicalupdates/index.vue')
  },
  {
    name: 'talkEducation',  //谈话教育
    component: () =>
      import ('@/components/mhPageComponents/talkEducation/index.vue')
  },
  {
    name: 'monitoring',  //监室动态
    component: () =>
      import ('@/components/mhPageComponents/monitoring.vue')
  },
  {
    name: 'keyPersonnel',  //重点人员
    component: () =>
      import ('@/components/mhPageComponents/keyPersonnel/index.vue')
  },
  {
    name: 'controlSituation',  //风险管控情况
    component: () =>
      import ('@/components/mhPageComponents/seekMedicalAdvice/seekMedicalAdvice.vue')
  },
  {
    name: 'functionality',  //我的应用全部应用
    component: () =>
      import ('@/components/functionality/acp-application.vue')
  },
  {
    name: "bbyy",   //
    component: () =>
      import ('@/components/dataBoard/bbyy.vue')
  },
  {
    name: "pcfb",   //
    component: () =>
      import ('@/components/dataBoard/bcfb.vue')
  },
  {

    name: "fxyj",   //
    component: () =>
      import ('@/components/dataBoard/fxyj.vue')
  },
  {
    name: "jyqs",   //
    component: () =>
      import ('@/components/dataBoard/jyqs.vue')
  },
  {
    name: "pbxx",   //
    component: () =>
      import ('@/components/dataBoard/pbxx.vue')
  },
  {
    name: "ylbq",   //
    component: () =>
      import ('@/components/dataBoard/ylbq.vue')
  },
  {
    name: "ylgk",   //
    component: () =>
      import ('@/components/dataBoard/ylgk.vue')
  },
  {
    name: "ypkcyj",   //
    component: () =>
      import ('@/components/dataBoard/ypkcyj.vue')
  },
  {
    name: "yzzx",   //
    component: () =>
      import ('@/components/dataBoard/yzzx.vue')
  },
  {
    name: "application",   //全部应用
    component: () =>
      import ('@/components/mhPageComponents/application/index.vue')
  },
    {
    name: "card",
    component: () => import("@/components/dataBoard/card.vue"),
  },
  {
    name: "statis",
    component: () => import("@/components/dataBoard/statis.vue"),
  },
  {
    name: "jztj",
    component: () => import("@/components/dataBoard/jztj.vue"),
  },
  {
    name: "szdadbsx",
    component: () => import("@/components/dataBoard/szdadbsx.vue"),
  }


]

const vueComs = () => {
  Coms.forEach(item => {
    return Vue.component(item.name, item.component)
  })
}
export default vueComs


