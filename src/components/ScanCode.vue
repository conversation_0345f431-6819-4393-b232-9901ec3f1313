<template>
	<div class="dzsmq_toast_box" v-show="showToast">
		<ReplaceIcon text="请将二维码对准扫码器识别" type="scan"></ReplaceIcon>
		<div class="ajdj_sdlr_btn" @click="sdlr">手动录入</div>
	</div>
</template>
<script>
export default {
	props: {
		smabListen: Boolean,

		// 是否展示扫码弹窗
		showToast: {
			type: Boolean,
			default: true
		},
		// 是否需要获取案件信息
		isGetAjInfo: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			smsbVal: '',
			key: {
				48: '0',
				49: '1',
				50: '2',
				51: '3',
				52: '4',
				53: '5',
				54: '6',
				55: '7',
				56: '8',
				57: '9',
				65: 'A',
				66: 'B',
				67: 'C',
				68: 'D',
				69: 'E',
				70: 'F',
				71: 'G',
				72: 'H',
				73: 'I',
				74: 'J',
				75: 'K',
				76: 'L',
				77: 'M',
				78: 'N',
				79: 'O',
				80: 'P',
				81: 'Q',
				82: 'R',
				83: 'S',
				84: 'T',
				85: 'U',
				86: 'V',
				87: 'W',
				88: 'X',
				89: 'Y',
				90: 'Z',
				188: ',',
				219: '{',
				220: '\\',
				221: '}',
				222: '"',
				186: ':'
			}
		}
	},
	mounted() {
		if (this.smabListen) {
			this.smsb()
		}
	},
	methods: {
		sdlr() {
			//关闭modal
			this.$parent.$parent.ScanCodeModal = false
			$(document).unbind('keydown')
		},
		//扫码识别
		smsb() {
			this.smsbVal = ''
			$(document).unbind('keydown')
			var flag = this.GetOSInfo()
			$(document).keydown((e) => {
				var _code = e.keyCode + ''
				var key = this.key
				if (_code != 16 && _code != 13 && key[_code] != undefined && !(e.shiftKey && e.keyCode == 54)) {
					this.smsbVal += key[_code]
				}
				if (e.shiftKey && e.keyCode == 54) {
					this.smsbVal += '^'
				}
				if (flag && e.shiftKey && e.keyCode == 59 && ':' != this.smsbVal.substr(this.smsbVal.length - 1, 1)) {
					this.smsbVal += ':'
				}
				if (_code == 13) {
					if (this.isGetAjInfo) {
						this.judgeFormat(this.smsbVal)
					} else {
						this.$emit('qr-data', this.smsbVal)
					}
					this.smsbVal = ''
				}
			})
		},
		//查询当前的操作系统
		GetOSInfo() {
			var _pf = navigator.platform
			if (String(_pf).indexOf('Linux') > -1) {
				return true
			} else {
				return false
			}
		},

		judgeFormat(srnr) {
			srnr = srnr.replace(/\\000026/g, '')
			if (srnr != null && srnr != '') {
				//根据文书ID获取案件信息
				this.getAjxxByWsid(srnr)
			} else {
				this.$alert({
					type: 'warning',
					title: '温馨提示',
					content: '案卷二维码识别失败，请重试！'
				})
				this.smsb()
			}
		},
		//根据二维码内容获取案件信息
		getAjxxByWsid(qrContent) {
			this.$Post(this.API.ZfptApi.GET_DATA_BY_QR_CONTENT, { qrContent: qrContent, zbgType: serverConfig.zbgType }).then((res) => {
				if (res.success) {
					if (res.data) {
						this.$emit('qr_result', res)
					} else {
						this.$Message.error('未查询到案件信息')
					}
				} else {
					this.$Message.error(res.msg)
					this.smsb()
				}
			})
		},

		unBindKeydown() {
			$(document).unbind('keydown')
		}
	},

	beforeDestroy() {
		this.unBindKeydown()
	}
}
</script>
<style lang="less" scoped>
.dzsmq_toast_box {
	width: 100%;
	padding-top: 0.2rem;
	box-sizing: border-box;
	flex-direction: column;
	.dzsmq_toast_box_icon_new {
		width: 1.3rem;
		height: 1.32rem;
	}
	.dzsmq_toast_box_icon_new_bg {
		width: 1.3rem;
		height: 1.32rem;
		background-image: url('../assets/images/znaj/ajdj_sm_toast_icon.png');
	}
	.ajdj_sdlr_btn {
		width: 1.2rem;
		height: 0.4rem;
		background-color: #0080f7;
		border-radius: 2px;
		color: #ffffff;
		font-size: 0.18rem;
		margin: auto;
		text-align: center;
		line-height: 0.4rem;
		margin-top: 0.2rem;
		cursor: pointer;
	}
}
</style>
