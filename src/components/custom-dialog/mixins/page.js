/* eslint-disable no-tabs */
/* eslint-disable no-mixed-spaces-and-tabs */
import data from './data'
export default {
  mixins: [data],
  methods: {
    // 构建分页
    bulid_page (pageSizeOptions, pageSize) {
      try {
        let option = eval('(' + pageSizeOptions + ')')
        if (pageSize) {
          this.pageSize = parseInt(pageSize)
        }
        if (option.length > 0) {
          this.pageSizeOpts = option
        }
      } catch (err) {
      }
    },
    // 改变页数
    change_page (page) {
      this.query_data(page)
    },
    // 改变每页大小
    change_size (pageSize) {
      this.pageSize = pageSize
      this.query_data()
    }
  }
}
