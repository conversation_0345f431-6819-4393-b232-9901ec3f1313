import tem from 'template_js'
export default {
  props: {
    dialogMark: String,
    params: {
      type: Object,
      default: function () {
        return {}
      }

    }
  },
  data () {
    return {
      modal: false,
      component: null,
      mark: this.dialogMark,
      style: '',
      // 条件字段
      conditionField: [],
      fixedQueryFields: [],
      // 展示字段
      displayField: [],
      // 排序字段
      sortField: [],
      // 返回字段
      returnField: [],
      pageSearch: true,
      searchShow: false,
      tableData: [],
      tableColumns: [],
      batch_select: [],
      pageNo: 1,
      pageSize: 10,
      pageSizeOpts: [10, 20, 50, 100],
      totalNum: 0,
      // 排序字段
      orderKey: [],
      // 排序类别（asc，desc）
      orderType: [],
      loading: false,
      showCheckbox: false,
      dialogTitle: '',
      // 是否单选
      isSingle: '1',
      // 单选时当前选中列索引
      curIdx: '',
      modalWidth: 520,
      modalHeight: 400
    }
  },
  methods: {
    template (tpl, data) {
      tem.config({ sTag: '{{', eTag: '}}', escape: true })
      return tem(tpl, data)
    },
    // 查询表格数据
    query_data (pageNo) {
      this.loading = true
      if (pageNo) {
        this.pageNo = pageNo
      }
      this.batch_select = []
      this.curIdx = -1
      let params = { mark: this.mark, pageNo: this.pageNo, pageSize: this.pageSize }

      // 查询条件
      let searchCondis = this.build_query_condis()
      if (searchCondis.length > 0) {
        params.condis = JSON.stringify(searchCondis)
      }

      // 排序字段
      params.sortName = this.orderKey.join(',')
      params.sortType = this.orderType.join(',')
      Object.assign(params, this.$route.query, this.params)
      this.$store.dispatch('postRequest', { url: this.$path.com_fm_dialog_query_url, params: params }).then(resp => {
        if (resp.success) {
          this.tableData = resp.rows
          this.totalNum = resp.total
        }
        this.loading = false
      })
    },
    // 构建查询条件
    build_query_condis () {
      let queryParams = []
      let queryFields = this.conditionField.concat()
      if (this.fixedQueryFields.length > 0) {
        queryFields = queryFields.concat(this.fixedQueryFields)
      }
      queryFields.forEach(function (item) {
        if (item.condition === 'between') {
          if (item.start) {
            let param = { name: item.field, op: '>=', value: item.start, controlType: item.controlType }
            queryParams.push(param)
          }
          if (item.end) {
            var endDefault = item.end
            if (endDefault.length === 16 && item.controlType === '04') {
              endDefault += ':59'
            } else if (endDefault.length === 14 && item.controlType === '04') {
              endDefault += ':59:59'
            }
            let param = { name: item.field, op: '<=', value: endDefault, controlType: item.controlType }
            queryParams.push(param)
          }
        } else if (item.searchValue) {
          let param = { name: item.field, op: item.condition, value: item.searchValue, controlType: item.controlType }
          queryParams.push(param)
        }
      })
      return queryParams
    },
    errorMsg (msg) {
      this.$Modal.error({
        title: '温馨提示',
        content: msg
      })
    },
    warnMsg (msg) {
      this.$Modal.warning({
        title: '温馨提示',
        content: msg
      })
    },
    // 批量选择
    on_batch_select (data) {
      this.batch_select = data
    },
    array_index (arr, key) {
      for (let i = 0, len = arr.length; i < len; i++) {
        if (arr[i] === key) {
          return i
        }
	    }
	    return -1
    }
  }
}
