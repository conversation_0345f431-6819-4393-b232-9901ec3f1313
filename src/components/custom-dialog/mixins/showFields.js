/* eslint-disable no-eval */
/* eslint-disable no-tabs */
/* eslint-disable no-mixed-spaces-and-tabs */
import data from './data'
export default {
  mixins: [data],
  methods: {
    // 构建表格展示字段
    build_show_field (sortField) {
      let _that = this
      let orderObj = {}
      if (sortField && sortField.length > 0) {
        sortField.forEach(item => {
          _that.orderKey.push(item.field.toLowerCase())
          let type = item.comment ? 'asc' : 'desc'
          _that.orderType.push(type)
          orderObj[item.field.toLowerCase()] = type
        })
      }
      let tableColumns = []
      // 展示复选框/单选框
      if (this.isSingle === '0') {
        _that.showCheckbox = true
        tableColumns.push(_that.checkbox_column())
      } else {
        tableColumns.push(_that.radio_column())
      }
      tableColumns.push(_that.index_column())
      // 设置表格展示字段
      _that.displayField.forEach(function (item, index) {
        let colItem = {}
        colItem.title = item.comment
        colItem.show = true
        colItem.align = item.align
        colItem.width = item.width
        colItem.tooltip = true

        // 是否字典
        colItem.key = item.field.toLowerCase()
        if (item.dicName && item.dicName !== '') {
          colItem.key = item.field.toLowerCase() + 'Name'
        }
        if (orderObj[item.field.toLowerCase()]) {
          colItem.sortable = 'custom'
          colItem.sortType = orderObj[item.field.toLowerCase()]
        }

        tableColumns.push(colItem)
      })
      _that.tableColumns = tableColumns
    },
    on_sort_change ({ column, key, order }) {
      if (key.indexOf('Name') > -1) {
        key = key.substring(0, key.indexOf('Name'))
      }
      let idx = this.array_index(this.orderKey, key)
      if (idx > -1) {
        this.orderType.splice(idx, 1, order)
        // 从新加载列表数据
        this.query_data()
      }
    },
    index_column () {
      let indexWidth = 65
      return {
        title: '序号',
        type: 'index',
        width: indexWidth,
        align: 'center'
      }
    },
    // 多选
    checkbox_column () {
      let checkboxWidth = 50
      return {
        type: 'selection',
        width: checkboxWidth,
        align: 'center'
      }
    },
    // 单选
    radio_column () {
      let _this = this
      let checkboxWidth = 50
      return {
        title: ' ',
        align: 'center',
        width: checkboxWidth,
        render: (h, params) => {
          let checked = false
          if (params.index === _this.curIdx) {
            checked = true
          }
          return h('div', [
            h('Radio', {
              props: { value: checked }
            })
          ])
        }
      }
    }
  }
}
