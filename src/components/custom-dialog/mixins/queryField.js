/* eslint-disable no-tabs */
/* eslint-disable no-mixed-spaces-and-tabs */
import data from './data'
export default {
  mixins: [data],
  methods: {
    // 构建查询条件
    build_query_fields (fields) {
      var conditionField = []
      var fixedQueryFields = []
      for (var n = 0, len = fields.length; n < len; n++) {
        var item = fields[n]
        if (item.defaultValue) {
          let tem = this.template(item.defaultValue.trim(), { user: this.$store.getters.sessionUser, req: this.$route.query })
          item.searchValue = tem
        }
        let key = item.field.toLowerCase()
        let searchValue = this.params[key]
        if (searchValue) {
          item.searchValue = searchValue
        }
        if (item.defaultType === '1') {
          conditionField.push(item)
        } else if (item.defaultType === '2') { // 固定值
          fixedQueryFields.push(item)
        } else {
          if (searchValue) {
            item.searchValue = searchValue
            fixedQueryFields.push(item)
          }
        }
      }
      if (conditionField.length > 0) {
        this.searchShow = true
        this.conditionField = conditionField
      }
      this.fixedQueryFields = fixedQueryFields
    },
    on_row_click (row, index) {
      if (this.isSingle === '0') {
        this.$refs.table.toggleSelect(index)
      } else {
        this.curIdx = index
        this.batch_select = []
        this.batch_select.push(row)
      }
    },
    // 查询条件——重置
    reset_query_condis () {
      var _that = this
      this.queryFields.forEach(function (fields) {
        fields.forEach(function (item) {
          _that.$set(item, 'searchValue', item.defaultValue)
          _that.$set(item, 'start', item.startDefault)
          _that.$set(item, 'end', item.endDefault)
        })
      })
      this.query_data(1)
    }
  }
}
