import showFields from './mixins/showFields'
import queryField from './mixins/queryField'
import page from './mixins/page'
export default {
  mixins: [showFields, queryField, page],
  mounted () {
    this.init_dialog_config()
  },
  methods: {
    init_dialog_config () {
      let params = { mark: this.mark }
      this.$store.dispatch('postRequest', { url: this.$path.com_fm_dialog_mark_url, params: params }).then(resp => {
        if (resp.success) {
          let dialog = resp.data
          // 对话框标题
          this.dialogTitle = dialog.name
          // 对话框宽高
          this.modalWidth = Number(dialog.width)
          this.modalHeight = Number(dialog.height)
          // 是否单选
          this.isSingle = dialog.isSingle
          // 展示字段
          this.displayField = JSON.parse(dialog.displayField)
          // 排序字段
          let sortField = JSON.parse(dialog.sortField)
          if (this.displayField.length < 1) {
            this.errorMsg('未配置展示字段')
            return
          }
          this.build_show_field(sortField)

          // 条件字段
          let queryField = JSON.parse(dialog.conditionField)
          if (queryField.length > 0) {
            this.build_query_fields(queryField)
          }

          // 处理排序字段
          this.build_sort_field(this.sortField)
          // 是否分页
          if (dialog.pageSize === undefined || dialog.pageSize === '') {
            this.pageSearch = false
          }
          this.bulid_page({}, dialog.pageSize)

          //
          this.modal = true

          // 查询数据
          this.query_data(1)

          // 返回字段
          this.returnField = JSON.parse(dialog.returnField)
        } else {
          this.modal = false
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg
          })
        }
      })
    },
    // 处理排序字段
    build_sort_field (sortField) {
      let _this = this
      sortField.forEach(function (item) {
        _this.orderKey.push(item.field)
        // 升序、降序
        if (item.comment) {
          _this.orderType.push('asc')
        } else {
          _this.orderType.push('desc')
        }
      })
    }
  }
}
