.custom_box {
  padding: 5px;
  overflow-y: auto;
}
.custom_box .ivu-modal-content .ivu-modal-body {
      padding: 0px
  }
.custom_box .formbox {
  background: rgba(247, 249, 252, 0.9);
  padding: 8px 0px 0px 0px;
  margin-bottom: 10px;
  border: 1px solid #CEE0F0;
}

.custom_box .ivu-table th, .custom_box .ivu-table td {height: 36px;}

.custom_box .searchBox {
  display: flex;
  flex-wrap: wrap;
}

.custom_box .ivu-form-item {
  margin-bottom: 0px;
  height: 42px;
}

.custom_box .ivu-form .ivu-form-item-label {
  width: 60px;
  padding-right:2px;
}

.custom_box .btnBox {
  text-align: center;
}

.custom_box .button {
  margin: 10px;
  border-radius: 2px;
}

.custom_box .btn {
  color: #17B0FC;
  border: 1px solid #17B0FC;
  background: #fff;
  width: 80px;
}

.custom_box .btn1 {
  background: #3179F5;
  color: #fff;
  width: 80px;
}

.custom_box .btn2 {
  background: #17B0FC;
  color: #fff;
}

.custom_box .navleft>span {
  display: inline-block;
  padding: 0 12px;
  color: #fff;
  background: #C0C4CC;
  height: 30px;
  line-height: 30px;
  margin: 10px 10px 10px 0;
  border-radius: 2px;
  cursor: pointer;
}
.custom_box .titleNav{display: -webkit-box;display: -ms-flexbox;display: flex;}
.titleNav>div {
  width: 50%;
}

.custom_box .titleNav>div.navright {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.custom_box .navright span {
  display: inline-block;
  height: 30px;
  line-height: 30px;
  margin: 10px 5px;
  border-radius: 2px;
  padding: 0 3px;
  cursor: pointer;
  position: relative;
}

.custom_box .navright span:hover {
  background: #EFF6FF;
}

.custom_box .navright .active {
  border-bottom: 2px solid #3179F5;
  color: #2372FA;
}

.custom_box .navright .active-a {
  margin-right: 40px;
  position: relative;
}

.custom_box .i-data {
  position: absolute;
  display: inline-block;
  background: #E5EFFF;
  font-style: normal;
  color: #666;
  padding: 0 10px;
  height: 20px;
  line-height: 20px;
  top: 6px;
  border-radius: 50px;
  right: -35px;
}

.custom_box .common_optation .input,
.custom_box .common_optation .select {
  max-width: 300px;
}

.custom_box .page-Data,
.custom_box .page-btn {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  vertical-align: top;
  margin: 10px 0;

}

.custom_box .page-Data i {
  font-style: normal;
  color: #3179F5;
  margin: 0 5px;
  font-weight: bold;
}

.custom_box .Total{
  display: inline-block;
  height: 24px;
  line-height: 24px;
  vertical-align: top;
  margin: 10px 0px;
  padding: 0 15px;
  border-radius: 2px;
  cursor: pointer;
}

.custom_box .ivu-table-filter i:hover {
  color: #0394F9;
}
.custom_box .page_box{
  border:1px solid #ddd;border-top:none;padding: 0 20px;height: 42px;
}
.custom_box .ivu-poptip-content{border: 1px solid #2d8cf0;}
.custom_box .ivu-table th{background: #DEE9FC;}
.custom_box .ivu-table-border th, .ivu-table-border td{border-right: 1px solid #f5f4f4;}
.custom_box .steylehang .btnBox .button{margin: 0 10px;}
.ivu-table-wrapper > .ivu-spin-fix {background-color: rgba(255, 255, 255, 0.6)}
