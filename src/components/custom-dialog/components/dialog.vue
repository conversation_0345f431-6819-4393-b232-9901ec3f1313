<template>
    <div>
        <Input readonly search enter-button="选择"  placeholder="" @on-search="openDialog" /> 
        <s-dialog v-if="isShowConfirm" @on_select="on_select" @on_cancel="on_cancel" :params="ppp" :dialogMark='curMark'></s-dialog>
    </div>
</template>
<script>
import {sDialog} from '@/components/custom-dialog'
export default {
    name: "role_catalog",
    components: {
        sDialog
    },
    data() {
        return {
            modalComp: null,
            isShowConfirm: false,
            ppp: {ajlx: '02'},
			curMark: ''
        };
    },
    methods: {
        on_select (data) {
            this.on_cancel()
        },
        on_cancel () {
            this.isShowConfirm = false
        },
		openDialog() {
            this.curMark = 'flwsMultiSelect'
            //this.modalComp = 'CustomDialog'
            this.isShowConfirm = true
		},
        handleUnionRole(row) {
            this.cat = row;
            this.title = "关联角色 >>>  " + row.name;
            this.roleComponent = null;
            this.$nextTick(() => {
                this.showUnionRole = true;
                this.roleComponent = "unionRole";
            });
        }
    },
    mounted() {
        // this.$refs.tables.request();
    }
};
</script>



