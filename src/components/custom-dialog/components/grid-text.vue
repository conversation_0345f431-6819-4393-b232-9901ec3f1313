<template>
    <div class="user base_style">
        <form :model="formItem" :label-width="80">
            <div class="common_optation">
                <div class="listBox">
                    <div class="listDiv">
                        <label for>查询条件:</label>
                        <Input
                            class="input"
                            clearable
                            @on-enter="handleSearch"
                            @on-clear="handleSearch"
                            v-model="searchObj.name"
                            placeholder="请输入名称搜索"
                        />
                    </div>
                    <div class="listDiv">
                        <label for>查询条件:</label>
                        <Input
                            class="input"
                            clearable
                            @on-enter="handleSearch"
                            @on-clear="handleSearch"
                            v-model="searchObj.name"
                            placeholder="请输入名称搜索"
                        />
                    </div>
                    <div class="listDiv">
                        <label for>查询条件:</label>
                        <Input
                            class="input"
                            clearable
                            @on-enter="handleSearch"
                            @on-clear="handleSearch"
                            v-model="searchObj.name"
                            placeholder="请输入名称搜索"
                        />
                    </div>
                    <div class="listDiv">
                        <label for>查询条件:</label>
                        <Input
                            class="input"
                            clearable
                            @on-enter="handleSearch"
                            @on-clear="handleSearch"
                            v-model="searchObj.name"
                            placeholder="请输入名称搜索"
                        />
                    </div>
                </div>
                <div class="listBox">
                    <div class="listDiv">
                        <label for>查询条件:</label>
                        <Input class="input" clearable placeholder="请输入名称搜索" />
                    </div>
                    <div class="listDiv">
                        <label for>查询条件:</label>
                        <Input class="input" clearable placeholder="请输入名称搜索" />
                    </div>
                    <div class="listDiv">
                        <label for>查询条件:</label>
                        <Input class="input" clearable placeholder="请输入名称搜索" />
                    </div>
                    <div class="listDiv">
                        <label for>查询条件:</label>
                        <Input class="input" clearable placeholder="请输入名称搜索" />
                    </div>
                </div>
                <div class="searchBox">
                    <Button class="button btn" type="primary" @click="handleSearch">重置</Button>
                    <Button class="button btn1" type="primary" @click="resetSearch">查询</Button>
                    <Button class="button btn2" type="primary" @click="resetSearch">高级查询</Button>
                </div>
            </div>
        </form>
        <div class="titleNav">
            <div class="navleft">
                <span class="La">文字按钮</span>
                <span class="La">默认效果</span>
                <span class="Lv">默认效果</span>
                <span class="Hong">
                    <Icon type="ios-trash-outline" size="24" />默认效果
                </span>
                <span>不可选效果</span>
            </div>
            <div class="navright">
                <span class="active" style="position:relative;margin-right:45px;">
                    全部类别
                    <i class="i-data">99</i>
                </span>
                <span>类别筛选</span>
                <span>类别筛选</span>
                <span>类别筛选</span>
                <span>类别筛选</span>
            </div>
        </div>
        <tables
            @on-selection-change="handleTableSelection"
            @defaultData="handleCurrentChange"
            :defaultChoose="true"
            class="tables"
            highlight-row
            @on-current-change="handleCurrentChange"
            ref="tables"
            :url="$path.get_role_catalog_list"
            :columns="columns"
            :params="params"
        />
        <template>
            <Page :total="100" show-sizer />
        </template>
    </div>
</template>
<script>
import tables from "@/view/center/uac/component/tables";
import request from "@/view/center/uac/mixins/request";
import commonTablesMethod from "@/view/center/uac/mixins/commonTablesMethod";
export default {
    name: "role_catalog",
    components: {
        tables
    },
    mixins: [request, commonTablesMethod],
    data() {
        return {
            showModal: false,
            tableSelection: [],
            searchObj: {
                name: null
            },
            showUnionRole: false,
            roleComponent: null,
            title: "",
            cat: {}
        };
    },
    computed: {
        columns() {
            return [
                { type: "selection", width: 52 },
                { title: "分类名称", key: "name" },
                { title: "添加时间", key: "addTime" },
                {
                    title: "操作",
                    width: 220,
                    render: (h, { row }) => {
                        return [
                            <Button
                                v-permission="user_set_role"
                                onClick={() => this.handleUnionRole(row)}
                                tip="关联角色"
                                type="primary"
                                icon="md-contacts"
                            />,
                            <span>&nbsp;</span>,
                            <Button
                                v-permission="role_catalog_edit"
                                onClick={() => this.handleEdit(row)}
                                type="primary"
                                icon="ios-create-outline"
                                tip="编辑"
                            />,
                            <span>&nbsp;</span>,
                            <Button
                                v-permission="role_catalog_delete"
                                onClick={() => this.handleDelete(row)}
                                type="error"
                                icon="ios-trash-outline"
                                tip="删除"
                            />
                        ];
                    }
                }
            ];
        },
        params() {
            return this.searchObj;
        },
        isTableSelection() {
            return this.tableSelection.length === 0;
        }
    },
    methods: {
        handleUnionRole(row) {
            this.cat = row;
            this.title = "关联角色 >>>  " + row.name;
            this.roleComponent = null;
            this.$nextTick(() => {
                this.showUnionRole = true;
                this.roleComponent = "unionRole";
            });
        },
        handleAdd() {
            this.showModal = true;
        },
        handleEdit(row) {
            this.showModal = true;
            this.$refs.modal.refill(row, row.id);
        },
        async handleDelete(row) {
            await this.deleteRequest(
                { id: row.id },
                this.$path.delete_role_catalog
            );
        },
        handleCurrentChange(row) {
            if (row) {
                this.$emit("roleCatalogChange", row.id);
            }
        },
        handleTableSelection(val) {
            this.tableSelection = val;
        },
        async handleDeleteBrach() {
            let ids = [];
            this.tableSelection.forEach(it => {
                ids.push(it.id);
            });
            await this.deleteRequest(
                { ids },
                this.$path.delete_role_catalog_batch
            );
            this.tableSelection = [];
        }
    },
    mounted() {
        this.$refs.tables.request();
    }
};
</script>

<style lang="less" scoped>
.catalog_container {
    height: 100%;
    display: flex;
    width: 100%;
    flex-direction: column;
}
.common_optation {
    display: initial;
    margin: 0;
    background: rgba(247, 249, 252, 0.9);
    margin: 10px 0;
    border: 1px solid #cee0f0;
    padding: 15px 0;
}
.common_left {
}
.listBox {
    width: 100%;
}
.listDiv {
    width: 25%;
    display: inline-block;
    text-align: center;
}
.listDiv > label {
    display: inline-block;
    width: 70px;
}
.searchBox {
    text-align: center;
}
.button {
    margin: 10px;
    border-radius: 2px;
}
.btn {
    color: #17b0fc;
    border: 1px solid #17b0fc;
    background: #fff;
}
.btn1 {
    background: #3179f5;
    color: #fff;
}
.btn2 {
    background: #17b0fc;
    color: #fff;
}
.navleft > span {
    display: inline-block;
    padding: 0 12px;
    color: #fff;
    background: #c0c4cc;
    height: 30px;
    line-height: 30px;
    margin: 10px 5px;
    border-radius: 2px;
    cursor: pointer;
}
.titleNav > div {
    display: inline-block;
    width: 50%;
}
.navleft .La {
    background: #3179f5;
}
.navleft .Lv {
    background: #11c28a;
}
.navleft .Hong {
    background: #f06060;
}
.navright {
    text-align: right;
}
.navright > span {
    display: inline-block;
    height: 30px;
    line-height: 30px;
    margin: 10px 5px;
    border-radius: 2px;
    padding: 0 3px;
    cursor: pointer;
}
.navright > span:hover {
    background: #eff6ff;
}
.navright .active {
    border-bottom: 2px solid #3179f5;
    color: #2372fa;
}
.i-data {
    position: absolute;
    display: inline-block;
    background: #ccc;
    font-style: normal;
    padding: 0 10px;
    height: 20px;
    line-height: 20px;
    top: 6px;
    border-radius: 50px;
    right: -35px;
}
.common_optation .input,
.common_optation .select {
    max-width: 300px;
}
</style>
<style>
.ivu-table-header thead tr th {
    background-color: #ebf7ff !important;
}
</style>
