<template>
	<div>
		<Modal v-model="modal" :mask-closable="false"  :closable="false" class-name="custom_box" :width="modalWidth">
			 <div class="flow-modal-title" slot="header">
				<span>{{dialogTitle}}</span>
				<span @click="cancel" style="position: absolute; right: 6px;font-size: 32px;cursor: pointer;">
					<i class="ivu-icon ivu-icon-ios-close"></i>
				</span>
			</div>
			<div class="custom_box">
				<Form class="formbox" v-if="searchShow" :label-width="80">
					<div class="searchBox steylehang">
						<template v-for="(condi, index) in conditionField">
							<FormItem :label="condi.comment + '：'" :key="index">
								<template v-if="condi.controlType === '02'"> <!--字典组件-->
									<s-dicgrid v-model='condi.searchValue' :style="condi.controlStyle ? condi.controlStyle : 'width:220px'" ref="dicGrid"  :dicName='condi.dicName' />
								</template>
								<template v-else-if="condi.controlType === '03' && condi.condition === 'between'"> <!--日期组件-->
									<el-date-picker :style="condi.controlStyle" :format="condi.dateFormat" value-format="yyyy-MM-dd" size="small" v-model="condi.start" type="date"  placeholder="开始日期"></el-date-picker>
									至
									<el-date-picker :style="condi.controlStyle" :format="condi.dateFormat" value-format="yyyy-MM-dd" size="small" v-model="condi.end" type="date"  placeholder="结束日期"></el-date-picker>
								</template>
								<template v-else-if="condi.controlType === '03'"> <!--日期组件-->
									<el-date-picker :format="condi.format" :style="condi.dateFormat" value-format="yyyy-MM-dd" size="small" v-model="condi.searchValue" type="date"  placeholder="选择日期"></el-date-picker>
								</template>
								<template v-else-if="condi.controlType === '04' && condi.condition === 'between'"> <!--日期时间组件-->
									<el-date-picker :style="condi.controlStyle" :format="condi.dateFormat" :value-format="condi.dateFormat" size="small" v-model="condi.start" type="datetime"  placeholder="开始日期"></el-date-picker>
									至
									<el-date-picker :style="condi.controlStyle" :format="condi.dateFormat" :value-format="condi.dateFormat" size="small" v-model="condi.end" type="datetime"  placeholder="结束日期"></el-date-picker>
								</template>
								<template v-else-if="condi.controlType === '04'"> <!--日期时间组件-->
									<el-date-picker :format="condi.format" :style="condi.dateFormat" :value-format="condi.dateFormat" size="small" v-model="condi.searchValue" type="datetime"  placeholder="选择日期"></el-date-picker>
								</template>
								<template v-else>
									<Input :style="condi.controlStyle"@on-enter="query_data(1)" v-model='condi.searchValue'  placeholder="" />
								</template>
							</FormItem>
						</template>
						<div class="btnBox">
							<Button class="button btn1" @click="query_data(1)" type="primary">查询</Button>
						</div>
					</div>
				</Form>
				<div style="width:100%;">
					<Table :loading="loading"
						tooltip-theme="light"
						:height="modalHeight"
						@on-selection-change="on_batch_select"
						@on-sort-change="on_sort_change"
						@on-row-click="on_row_click"
						:columns="tableColumns"
						:data="tableData"
						ref="table"
						stripe
						border>
					</Table>
				</div>
				<template style="margin: 20px 0;">
					<div class="page_box">
						<span v-if="showCheckbox" class="page-Data">共选中<i>{{batch_select.length}}</i>条数据</span>
						<div class="" style="float: right;">
							<Page :total="totalNum" v-if="pageSearch" @on-change="change_page" :current="pageNo" @on-page-size-change="change_size" :page-size="pageSize" :page-size-opts="pageSizeOpts" size="small" show-elevator show-sizer style="display: inline-block;margin: 10px 0;" />
							<span class="Total">共 {{totalNum}} 条 </span>
						</div>
					</div>
				</template>
			</div>
			<div slot="footer">
				<Button type="error" style="margin: 0 15px;" @click="cancel">取&nbsp;&nbsp;消</Button>
				<Button @click="confirm" type="primary">确&nbsp;&nbsp;认</Button>
			</div>
		</Modal>
	</div>
</template>

<script>
	import customDialog from '../customDialog'
  	export default {
		mixins: [customDialog],
		methods: {
			// 对话框确定事件
			confirm () {
				if (this.batch_select.length == 0) {
					this.warnMsg('请选择数据')
					return false
				}

				let data = []
				// 处理返回字段
				this.batch_select.forEach((item, index) => {
					let obj = {}
					this.returnField.forEach(function (field){
						obj[field.field.toLowerCase()] = item[field.field.toLowerCase()]
					})
					data.push(obj)
				})

				// 单选返回对象，多选返回数组
				this.$emit('on_select', data)
			},
			// 弹窗取消事件
			cancel () {
				this.modal = false
				this.$emit('on_cancel')
			}
		}
    }
</script>
<style scoped>
	@import "../styles/dialog-text.css";
</style>
