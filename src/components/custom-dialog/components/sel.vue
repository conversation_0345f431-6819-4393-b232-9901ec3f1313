<template>
    <div id="fm">
		<Tooltip placement="top" :max-width="300" content="cint">
            <f-dialog ref="dialog" v-model="formData.wsmc" style="width:300px" :formData="formData" @setFormData="setFormData" params="ajlx|ajlx,ajbh|ajbh" return="id:ajbh|zwmc:ajmc,wsmc" mark="flwsMultiSelect"></f-dialog>
		</Tooltip>
		<div>
		    <f-dialog style="width:400px" mark="flwsMultiSelect"></f-dialog>
		</div>
        <Input v-model="formData.ajbh"  placeholder="请输入名称搜索"/>
        <Input v-model="formData.wsmc"  placeholder="请输入名称搜索"/>
        <Input v-model="formData.ajmc"  placeholder="请输入名称搜索"/>
    </div>
</template>
<script>
export default {
    name: "role_catalog",
    data() {
        return {
            modalComp: null,
            formData: {
                ajlx: '03',
                ajmc: '测试案件',
                ajbh: '',
                wsmc: ''
            },
            isShowConfirm: false,
            ppp: {ajlx: '02'},
			curMark: ''
        };
    },
    methods: {
        on_select (data) {
            this.on_cancel()
        },
        setFormData (field, content) {
            this.formData[field] = content
        },
        on_cancel () {
            this.isShowConfirm = false
        },
		openDialog() {
            this.curMark = 'flwsMultiSelect'
            //this.modalComp = 'CustomDialog'
            this.isShowConfirm = true
		},
        handleUnionRole(row) {
            this.cat = row;
            this.title = "关联角色 >>>  " + row.name;
            this.roleComponent = null;
            this.$nextTick(() => {
                this.showUnionRole = true;
                this.roleComponent = "unionRole";
            });
        }
    },
    mounted() {
        // this.$refs.tables.request();
    }
};
</script>



