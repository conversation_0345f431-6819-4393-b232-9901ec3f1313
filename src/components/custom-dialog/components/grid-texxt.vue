<template>
  <div class="BOX">
    <Form class="formbox" :model="formItem" :label-width="80" style="">
      <div class="searchBox steyle2">
        <div class="" >
          <FormItem label="查询条件:">
            <s-dicgrid v-model='formItem.input' ref="dicGrid" :multiple="false "  dicName='ZD_DIC' />
          </FormItem>
        </div>
        <div class="" >
          <FormItem label="查询条件:">
            <Input v-model="formItem.input" placeholder="Enter something..."></Input>
          </FormItem>
        </div>
        <div class="" >
          <FormItem label="查询条件:">
            <Input v-model="formItem.input" placeholder="Enter something..."></Input>
          </FormItem>
        </div>
        <div class="" >
          <FormItem label="查询条件:">
            <Input v-model="formItem.input" placeholder="Enter something..."></Input>
          </FormItem>
        </div>

      </div>
      <div class="searchBox">
        <div >
          <FormItem label="查询条件:">
            <Row>
              <Col span="11">
              <DatePicker type="date" placeholder="Select date" v-model="formItem.date"></DatePicker>
              </Col>
              <Col span="2" style="text-align: center">-</Col>
              <Col span="11">
               <DatePicker type="date" placeholder="Select date" v-model="formItem.date"></DatePicker>
              </Col>
            </Row>
          </FormItem>
        </div>
        <div >
          <FormItem label="日期条件:">
            <DatePicker style="width: 100%" type="date" placeholder="Select date" v-model="formItem.date"></DatePicker>
          </FormItem>
        </div>
        <div >
          <FormItem label="Select">
            <Select v-model="formItem.select">
                <Option value="beijing">New York</Option>
                <Option value="shanghai">London</Option>
                <Option value="shenzhen">Sydney</Option>
            </Select>
          </FormItem>
        </div>
        <div >
          <FormItem label="日期条件:">
            <Col span="24">
                <Select v-model="model12" filterable multiple>
                    <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
            </Col>
          </FormItem>
        </div>
      </div>
      <div class="btnBox">
        <Button class="button btn" type="primary" @click="handleSearch">重置</Button>
        <Button class="button btn1" type="primary" @click="resetSearch">查询</Button>
        <Button class="button btn2" type="primary" @click="resetSearch">高级查询</Button>
      </div>
      <!-- <div class="searchBox steyle2">
        <div class="" >
          <FormItem label="查询条件:">
            <Input v-model="formItem.input" placeholder="Enter something..."></Input>
          </FormItem>
        </div>
        <div class="" >
          <FormItem label="查询条件:">
            <Input v-model="formItem.input" placeholder="Enter something..."></Input>
          </FormItem>
        </div>
         <div class="" >
          <FormItem label="查询条件:">
            <Input v-model="formItem.input" placeholder="Enter something..."></Input>
          </FormItem>
        </div>
         <div class="" >
          <FormItem label="查询条件:">
            <Input v-model="formItem.input" placeholder="Enter something..."></Input>
          </FormItem>
        </div>
         <div class="btnBox">
            <Button class="button btn" type="primary" @click="handleSearch">重置</Button>
            <Button class="button btn1" type="primary" @click="resetSearch">查询</Button>
            <Button class="button btn2" type="primary" @click="resetSearch">高级查询</Button>
          </div>

      </div> -->





    </Form>
    <div class="titleNav">
      <div class="navleft">
        <span class="La">文字按钮</span>
        <span class="La"><Icon class="Iconclass" type="ios-browsers-outline" size="24"/>默认效果</span>
        <span class="Lv"><Icon class="Iconclass" type="ios-browsers-outline" size="24"/>默认效果</span>
        <span class="Hong"><Icon class="Iconclass" type="ios-trash-outline" size="24"/>默认效果</span>
        <span>不可选效果</span>
      </div>
      <div class="navright">
        <div class="navlist">
          <span v-for="(list,index) in navLists" :class="{active:changeRed == index}"  @click="bgs(index)">
            {{list.text}}
          </span>
        </div>
        <div class="navlist After">
          <span v-for="(list,index) in navListss" :class="{active:changeRed == index}"  @click="bgss(index)">
            {{list.text}}
          </span>
        </div>
      </div>
    </div>
    <Table border :columns="columns12" :data="data6" stripe >
      <template slot-scope="{ row }" slot="name">
        <strong>{{ row.name }}</strong>
      </template>
      <template slot-scope="{ row, index }" slot="action">
        <Button type="primary"  @click="show(index)" style="margin-right: 5px" >View</Button>
        <Button type="error" size="small" @click="remove(index)">Delete</Button>
      </template>
    </Table>
    <template style="margin: 20px 0;">
      <div class="page_box">
        <span class="page-Data">共选中<i>1000</i>条数据</span>
        <div class="" style="float: right;">
          <Page :total="40" size="small" show-total   show-elevator show-sizer style="display: inline-block;margin: 10px 0;" />
          <span class="Total">共{{400}}条</span>
          <span class="page-btn">
            <span @click="dlsx(index)">多列筛选</span>
            <ul class="ulnavlist" v-if="willShow" @mouseleave="Deleteleave(index)">
              <li @click="bgClick(index)"  v-for="(list,index) in ulnavlist" :class="{active:list.bOn}">
                <i class="iconlist"></i>
                {{list.text}}
              </li>

            <!-- <li><Icon class="iconlist" type="ios-square-outline" />案件编号</li>
              <li><Icon class="iconlist" type="ios-square-outline" />物品编号</li>
              <li><Icon class="iconlist" type="ios-square-outline" />关联物资</li>
              <li><Icon class="iconlist" type="ios-square-outline" />时间</li> -->
            </ul>


          </span>
        </div>
      </div>
    </template>

  </div>
</template>



<script>
  import '@/router/components-routers';
  import '@/components/data-grid/styles/grid-text.css' //css引入方式
  import dic from '_c/dic/dicGrid'                     //组件引入方式
  export default {
    components: {
      's-dicgrid': dic,
    },
    data() {
      return {
        currentid:0,
        changeRed:0,
        willShow:false,
        columns12: [{
            title: '选择',
            key: 'id',
            width: 100,
            align: 'center',
            render: (h, params) => {
              let id = params.row.idjishi;
              let flag = false;
              if (this.currentid === id) {
                flag = true
              } else {
                flag = false
              }
              let self = this
              return h('div', [
                h('Radio', {
                  props: {
                    value: flag
                  },
                  on: {
                    'on-change': () => {
                      self.currentid = id
                      this.id_jishi = params.row.idjishi

                    }
                  }
                })
              ])
            }
          },
          {
            title: 'Age',
            key: 'age'
          },
          {
            title: 'Address',
            key: 'address',
            filters: [
              {
                  label: 'Greater than 25',
                  value: 1
              },
              {
                  label: 'Less than 25',
                  value: 2
              },
              {
                  label: 'Less  25',
                  value: 3
              }
            ],
            filterMultiple: false,
            filterMethod (value, row) {
                if (value === 1) {
                    return row.age > 25;
                } else if (value === 2) {
                    return row.age < 25;
                }
            }
          },
          {
            title: 'Action',
            slot: 'action',
            width: 150,
            align: 'center',
            sortable: true
          }
        ],
        data6: [{
            name: 'John Brown',
            age: 18,
            address: 'New York No. 1 Lake Park',
            ss: '1 Lake Park',
            bbs: 'New York ',
            psv: 'No. 1 '
          },
          {
            name: 'Jim Green',
            age: 24,
            address: 'London No. 1 Lake Park'
          },
          {
            name: 'Joe Black',
            age: 30,
            address: 'Sydney No. 1 Lake Park'
          },
          {
            name: 'Jon Snow',
            age: 26,
            address: 'Ottawa No. 2 Lake Park'
          },
          {
            name: 'Jon Now',
            age: 26,
            address: 'Ottawa No. 2 Lake Park'
          },
          {
            name: 'Jon box',
            age: 26,
            address: 'Ottawa No. 2 Lake Park'
          }
        ],
        formItem: {
          input: '',
          select: '',
          radio: 'male',
          checkbox: [],
          switch: true,
          date: '',
          time: '',
          slider: [20, 50],
          textarea: ''
        },
        navLists:[
            {
                "text":"全部分类"
            },
            {
                "text":"类别筛选"
            },
            {
                "text":"性别筛选"
            },
            {
                "text":"姓名筛选"
            }
        ],
        navListss:[
            {
                "text":"全部"
            },
            {
                "text":"类别"
            },
            {
                "text":"性别"
            },
            {
                "text":"姓名"
            }
        ],
        ulnavlist:[
            {
                "text":"案件名称",
                 bOn:false
            },
            {
                "text":"案件编号",
                bOn:false
            },
            {
                "text":"物品编号",
                bOn:false
            },
            {
                "text":"关联物资",
                bOn:false
            },
            {
                "text":"关联物资",
                bOn:false
            },
            {
               "text":"关联物资",
               bOn:false
            },
            {
                "text":"时间",
                bOn:false
            }
        ],
        cityList: [
          {
              value: 'New York',
              label: 'New York'
          },
          {
              value: 'London',
              label: 'London'
          },
          {
              value: 'Sydney',
              label: 'Sydney'
          },
          {
              value: 'Ottawa',
              label: 'Ottawa'
          },
          {
              value: 'Paris',
              label: 'Paris'
          },
          {
              value: 'Canberra',
              label: 'Canberra'
          }
        ],
        model11: '',
        model12: [],




      }
    },
    methods: {
      show(index) {
        this.$Modal.info({
          title: 'User Info',
          content: `Name：${this.data6[index].name}<br>Age：${this.data6[index].age}<br>Address：${this.data6[index].address}`
        })
      },
      remove(index) {
        this.data6.splice(index, 1);
      },
      dlsx(index) {
        if(this.willShow==true){
          this.willShow=false;
        }else{
          this.willShow=true
        }
      },
     bgs(index){
       this.changeRed = index;
     },
     bgss(index){
       this.changeRed = index;
     },

      bgClick(i){
        this.ulnavlist[i].bOn = !this.ulnavlist[i].bOn;
      },
      Deleteleave(index){
        this.willShow=false;
      }
    }
  }
</script>
