<template>
	<div class="cross-day-time-picker">
	  <!-- 日期切换 -->
	  <!-- <div class="day-switcher">
		<Button 
		  @click="switchDay('today')" 
		  :type="currentDay === 'today' ? 'primary' : 'default'"
		  style="width: 80px; margin-right: 10px;"
		>当天</Button>
		<Button 
		  @click="switchDay('tomorrow')" 
		  :type="currentDay === 'tomorrow' ? 'primary' : 'default'"
		>下一天</Button>
	  </div> -->
	  
	  <!-- 时间选择 -->
	  <div class="time-picker-container">
		<Select v-model="model1" style="width:100px">
            <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
		<TimePicker
		  v-model="startTime"
		  format="HH:mm"
		  placeholder="开始时间"
		  @on-change="handleStartTimeChange"
		  :disabled="disabled"
		></TimePicker>
		<span class="separator">至</span>
		<Select v-model="model2" style="width:100px">
            <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
		<TimePicker
		  v-model="endTime"
		  format="HH:mm"
		  placeholder="结束时间"
		  @on-change="validateTimeRange"
		  :disabled="!startTime || disabled"
		></TimePicker>
	  </div>
	  
	  <!-- 错误提示 -->
	  <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
	  
	  <!-- 持续时间 -->
	  <div class="duration-hint" v-if="startTime && endTime">
		持续时间: {{ duration }} 小时
	  </div>
	</div>
  </template>
  
  <script>
  export default {
	props: {
	  // 外部传入的限制条件（其他组件的结束时间）
	  externalConstraints: {
		type: Array,
		default: () => []
	  },
	  // 是否禁用组件
	  disabled: {
		type: Boolean,
		default: false
	  },
	  // 初始值
	  value: {
		type: Object,
		default: () => ({})
	  }
	},
	data() {
	  return {
		currentDay: 'today',
		startTime: '',
		endTime: '',
		errorMessage: '',
		duration: 0,
		cityList: [
			{
				value: 'toDay',
				label: '当天'
			},
			{
				value: 'tomorrow',
				label: '下一天'
			}
		],
		model1: 'toDay',
		model2: 'toDay'
	  };
	},
	watch: {
	  value: {
		immediate: true,
		handler(newVal) {
		  if (newVal) {
			this.startTime = newVal.startTime || '';
			this.endTime = newVal.endTime || '';
			this.currentDay = newVal.day || 'today';
			this.calculateDuration();
		  }
		}
	  }
	},
	methods: {
	  switchDay(day) {
		this.currentDay = day;
		this.validateTimeRange();
	  },
	  
	  handleStartTimeChange(time) {
		this.startTime = time;
		this.endTime = ''; // 重置结束时间
		this.errorMessage = '';
		this.$emit('start-time-change', this.getTimeData());
	  },
	  
	  validateTimeRange() {
		if (!this.startTime || !this.endTime) {
		  this.errorMessage = '';
		  this.duration = 0;
		  return;
		}
		
		// 验证开始时间是否早于结束时间（考虑跨天情况）
		const isValid = this.validateTimeOrder();
		
		// 验证持续时间是否在24小时内
		const isWithin24Hours = this.validateDuration();
		
		// 验证是否与其他组件的时间范围冲突
		const isConflictWithOthers = this.validateExternalConstraints();
		
		if (!isValid) {
		  this.errorMessage = this.currentDay === 'today' 
			? '结束时间必须晚于开始时间' 
			: '跨天时间段，结束时间应小于开始时间';
		} else if (!isWithin24Hours) {
		  this.errorMessage = '时间段不能超过24小时';
		} else if (isConflictWithOthers) {
		  this.errorMessage = '时间范围与其他选择冲突';
		} else {
		  this.errorMessage = '';
		}
		
		this.calculateDuration();
		// this.$emit('change', this.getTimeData());
		 // 触发change事件，传递完整数据
		 this.$emit('change', {
			startTime: this.startTime,
			endTime: this.endTime,
			day: this.currentDay,
			isValid: !this.errorMessage
		 });
	  },
	  
	  validateTimeOrder() {
		const start = this.parseTime(this.startTime);
		const end = this.parseTime(this.endTime);
		
		if (this.currentDay === 'today') {
		  return end > start;
		} else {
		  // 跨天情况下，结束时间应该小于开始时间（表示第二天）
		  return end < start;
		}
	  },
	  
	  validateDuration() {
		const start = this.parseTime(this.startTime);
		const end = this.parseTime(this.endTime);
		
		if (this.currentDay === 'today') {
		  return (end - start) / 60 <= 24;
		} else {
		  // 跨天情况下的持续时间计算
		  return (24 * 60 - start + end) / 60 <= 24;
		}
	  },
	  
	  validateExternalConstraints() {
		if (!this.externalConstraints.length) return false;
		
		const currentStart = this.getAbsoluteTime(this.startTime, this.currentDay);
		
		// 检查当前开始时间是否早于其他组件的结束时间
		return this.externalConstraints.some(constraint => {
		  const constraintTime = this.getAbsoluteTime(constraint.endTime, constraint.day);
		  return currentStart < constraintTime;
		});
	  },
	  
	  parseTime(timeStr) {
		const [hours, minutes] = timeStr.split(':').map(Number);
		return hours * 60 + minutes;
	  },
	  
	  getAbsoluteTime(timeStr, day) {
		const minutes = this.parseTime(timeStr);
		// 为当天和下一天分配不同的基准值
		const dayOffset = day === 'today' ? 0 : 24 * 60;
		return dayOffset + minutes;
	  },
	  
	  calculateDuration() {
		if (!this.startTime || !this.endTime) {
		  this.duration = 0;
		  return;
		}
		
		const start = this.parseTime(this.startTime);
		const end = this.parseTime(this.endTime);
		
		if (this.currentDay === 'today') {
		  this.duration = ((end - start) / 60).toFixed(2);
		} else {
		  // 跨天持续时间计算
		  this.duration = ((24 * 60 - start + end) / 60).toFixed(2);
		}
	  },
	  
	  getTimeData() {
		return {
		  startTime: this.startTime,
		  endTime: this.endTime,
		  day: this.currentDay,
		  isValid: !this.errorMessage && this.startTime && this.endTime,
		  duration: this.duration
		};
	  }
	}
  };
  </script>
  
  <style scoped>
  .cross-day-time-picker {
	padding: 10px;
	border: 1px solid #dcdee2;
	border-radius: 4px;
	margin-bottom: 10px;
  }
  
  .day-switcher {
	margin-bottom: 10px;
	text-align: center;
  }
  
  .time-picker-container {
	display: flex;
	align-items: center;
  }
  
  .separator {
	margin: 0 10px;
  }
  
  .error-message {
	color: #ed4014;
	margin-top: 5px;
  }
  
  .duration-hint {
	margin-top: 5px;
	color: #808695;
	font-size: 12px;
  }
  </style>