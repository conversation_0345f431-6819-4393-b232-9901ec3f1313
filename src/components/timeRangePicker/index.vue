<template>
	<div class="cross-day-time-picker">
	  <!-- 时间选择 -->
	  <div class="time-picker-container">
		<Select v-model="startDayType" style="width:100px" @on-change="validateTimeRange">
		  <Option v-for="item in dayTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
		</Select>
		<TimePicker
		  v-model="startTime"
		  format="HH:mm"
		  placeholder="开始时间"
		  @on-change="handleStartTimeChange"
		  :disabled="disabled"
		></TimePicker>
		<span class="separator">至</span>
		<Select v-model="endDayType" style="width:100px" @on-change="validateTimeRange">
		  <Option v-for="item in dayTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
		</Select>
		<TimePicker
		  v-model="endTime"
		  format="HH:mm"
		  placeholder="结束时间"
		  @on-change="validateTimeRange"
		  :disabled="!startTime || disabled"
		></TimePicker>
	  </div>
	  
	  <!-- 错误提示 -->
	  <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
	  
	  <!-- 持续时间 -->
	  <div class="duration-hint" v-if="startTime && endTime">
		持续时间: {{ duration }} 小时
	  </div>
	</div>
  </template>
  
  <script>
  export default {
	props: {
	  // 外部传入的限制条件（其他组件的结束时间）
	  externalConstraints: {
		type: Array,
		default: () => []
	  },
	  // 是否禁用组件
	  disabled: {
		type: Boolean,
		default: false
	  },
	  // 初始值
	  value: {
		type: Object,
		default: () => ({})
	  },
	  prevShiftEnd: {
		type: Object,
		default: null
	}
	},
	data() {
	  return {
		startTime: '',
		endTime: '',
		startDayType: 'today', // 开始时间日期类型
		endDayType: 'today',   // 结束时间日期类型
		errorMessage: '',
		duration: 0,
		dayTypeList: [
		  {
			value: 'today',
			label: '当天'
		  },
		  {
			value: 'tomorrow',
			label: '下一天'
		  }
		]
	  };
	},
	watch: {
	  value: {
		immediate: true,
		handler(newVal) {
		  if (newVal) {
			console.log(newVal,'newValllll');
			this.startTime = newVal.startTime || '';
			this.endTime = newVal.endTime || '';
			this.startDayType = newVal.startDayType || 'today';
			this.endDayType = newVal.endDayType || 'today';
			this.calculateDuration();
		  }
		}
	  },
	  prevShiftEnd: {
		deep: true,
		handler(newVal) {
			console.log(newVal,'newVal');
			if (this.startTime && this.endTime) {
				this.validateTimeRange();
			}
		}
	  },
	  externalConstraints: {
		handler(value) {
			if(value) {
				console.log(value,'endtimeeeee');
			}
		},
		// immediate: true
	  }
	},
	methods: {
	  handleStartTimeChange(time) {
		this.startTime = time;
		this.endTime = ''; // 重置结束时间
		this.errorMessage = '';
		this.$emit('start-time-change', this.getTimeData());
	  },
	  
	  validateTimeRange() {
		console.log(this.prevShiftEnd,'this.prevShiftEnd');
		if (!this.startTime || !this.endTime) {
		  this.errorMessage = '';
		  this.duration = 0;
		  return;
		}
		
		// 验证开始时间是否早于结束时间（考虑跨天情况）
		const isValid = this.validateTimeOrder();
		
		// 验证持续时间是否在24小时内
		const isWithin24Hours = this.validateDuration();
		
		// 验证是否与其他组件的时间范围冲突
		const isConflictWithOthers = this.validateExternalConstraints();
		console.log(isConflictWithOthers,'isConflictWithOthers');
		
		if (!isValid) {
		  this.errorMessage = this.getTimeOrderError();
		} else if (!isWithin24Hours) {
		  this.errorMessage = '时间段不能超过24小时';
		} 
		// else if (isConflictWithOthers) {
		//   this.errorMessage = '时间范围与其他选择冲突';
		// } 
		else {
		  this.errorMessage = '';
		}

		if (this.prevShiftEnd) {
			// 转换父组件的时间类型为子组件识别的格式
			const convertTimeType = (type) => type === '1' ? 'today' : 'tomorrow';
			
			const prevEndTime = this.getAbsoluteTime(
				this.prevShiftEnd.endTime, 
				convertTimeType(this.prevShiftEnd.endTimeType)
			);
			
			const currentStart = this.getAbsoluteTime(
				this.startTime, 
				this.startDayType
			);
			
			if (currentStart <= prevEndTime) {
				this.errorMessage = '开始时间必须晚于上一个班次的结束时间';
				this.emitChangeEvent();
				return;
			}
		}
		
		this.calculateDuration();
		this.emitChangeEvent();
	  },
	  
	  getTimeOrderError() {
		// 同一天
		if (this.startDayType === 'today' && this.endDayType === 'today') {
		  return '结束时间必须晚于开始时间';
		}
		// 跨天（开始当天，结束下一天）
		if (this.startDayType === 'today' && this.endDayType === 'tomorrow') {
		  return this.startTime <= this.endTime ? '' : '跨天时间段，结束时间应小于开始时间';
		}
		// 其他情况
		return '时间范围无效';
	  },
	  
	  validateTimeOrder() {
		const start = this.parseTime(this.startTime);
		const end = this.parseTime(this.endTime);
		
		// 同一天
		if (this.startDayType === 'today' && this.endDayType === 'today') {
		  return end > start;
		}
		// 跨天（开始当天，结束下一天）
		if (this.startDayType === 'today' && this.endDayType === 'tomorrow') {
		  return start <= end;
		}
		// 其他情况视为无效
		return false;
	  },
	  
	  validateDuration() {
		const start = this.parseTime(this.startTime);
		const end = this.parseTime(this.endTime);
		
		// 同一天
		if (this.startDayType === 'today' && this.endDayType === 'today') {
		  return (end - start) / 60 <= 24;
		}
		// 跨天（开始当天，结束下一天）
		if (this.startDayType === 'today' && this.endDayType === 'tomorrow') {
		  return (24 * 60 - start + end) / 60 <= 24;
		}
		// 其他情况
		return false;
	  },
	  
	  validateExternalConstraints() {
		if (!this.externalConstraints.length) return false;
		
		const currentStart = this.getAbsoluteTime(this.startTime, this.startDayType);
		
		// 检查当前开始时间是否早于其他组件的结束时间
		return this.externalConstraints.some(constraint => {
		  const constraintTime = this.getAbsoluteTime(constraint.endTime, constraint.endDayType);
		  return currentStart < constraintTime;
		});
	  },
	  
	  emitChangeEvent() {
		this.$emit('change', {
		  startTime: this.startTime,
		  endTime: this.endTime,
		  startDayType: this.startDayType,
		  endDayType: this.endDayType,
		  isValid: !this.errorMessage && this.startTime && this.endTime
		});
	  },
	  
	  parseTime(timeStr) {
		const [hours, minutes] = timeStr.split(':').map(Number);
		return hours * 60 + minutes;
	  },
	  
	  getAbsoluteTime(timeStr, dayType) {
		const minutes = this.parseTime(timeStr);
		// 为当天和下一天分配不同的基准值
		const dayOffset = dayType === 'today' ? 0 : 24 * 60;
		return dayOffset + minutes;
	  },
	  
	  calculateDuration() {
		if (!this.startTime || !this.endTime) {
		  this.duration = 0;
		  return;
		}
		
		const start = this.parseTime(this.startTime);
		const end = this.parseTime(this.endTime);
		
		// 同一天
		if (this.startDayType === 'today' && this.endDayType === 'today') {
		  this.duration = ((end - start) / 60).toFixed(2);
		} 
		// 跨天（开始当天，结束下一天）
		else if (this.startDayType === 'today' && this.endDayType === 'tomorrow') {
		  this.duration = ((24 * 60 - start + end) / 60).toFixed(2);
		} 
		// 其他情况
		else {
		  this.duration = 0;
		}
	  },
	  
	  getTimeData() {
		return {
		  startTime: this.startTime,
		  endTime: this.endTime,
		  startDayType: this.startDayType,
		  endDayType: this.endDayType,
		  isValid: !this.errorMessage && this.startTime && this.endTime,
		  duration: this.duration
		};
	  }
	}
  };
  </script>
  
  <style scoped>
  .cross-day-time-picker {
	padding: 10px;
	border: 1px solid #dcdee2;
	border-radius: 4px;
	margin-bottom: 10px;
  }
  
  .time-picker-container {
	display: flex;
	align-items: center;
  }
  
  .separator {
	margin: 0 10px;
  }
  
  .error-message {
	color: #ed4014;
	margin-top: 5px;
  }
  
  .duration-hint {
	margin-top: 5px;
	color: #808695;
	font-size: 12px;
  }
  </style>