<template>
  <div
    class="com-single-uploader"
    ref="drag-box"
    :style="{ height }"
    @click="uploadSelect"
    :class="{ dragable: dragFlag }"
    @dragover.prevent="dragOver"
    @dragleave.prevent="dragLeave"
    @drop.prevent="drop"
  >
    <div class="uploader-preview-box" v-if="value && type === 'pic'">
      <bsp-img :src="value" type="contain" />
    </div>
    <div class="uploader-preview-box" v-else-if="value && type !== 'pic'">
      <div class="file">
        <u class="icon-file"></u>
        <p :text="fileName" class="mt-10">{{ fileName }}</p>
      </div>
    </div>
    <div v-show="!value">
      <u class="icon"></u>
      <input
        id="inp"
        ref="fileInput"
        type="file"
        @change="submitFile"
        v-show="false"
        :accept="accept"
      />
      <p class="main"><a href="javascript:;">请点击</a>或拖拽文件至此区域</p>
      <p v-if="tips">{{ tips }}</p>
    </div>
  </div>
</template>

<script>
import bspImg from '../bsp-img/bsp-img.vue';
export default {
  name: "comSingleUploader",
  components: {bspImg},
  data() {
    return {
      dragFlag: false,
    };
  },
  computed: {
    fileName() {
      let arr = this.value.split("/");
      return arr[arr.length - 1];
    },
  },
  props: {
    accept: {
      type: String,
    },
    value: {
      type: String,
    },
    tips: {
      type: String,
    },
    height: {
      type: String,
      default: "auto",
    },
    // pic 图片
    type: {
      type: String,
      default: "",
    },
  },
  model: {
    prop: "value",
    event: "change",
  },
  methods: {
    submitFile() {
      let file = this.$refs.fileInput.files[0];
      this.uploadFile(file);
    },
    uploadFile(file) {
      let formData = new FormData();
      formData.append("file", file);
      this.$store
        .dispatch("authPostRequest", {
          url: this.$path.apiTerminal.com_upload,
          params: formData,
        })
        .then((res) => {
          this.$emit("change", res.data.url);
        })
        .catch((err) => this.errorModal({ content: err }));
    },
    uploadSelect() {
      this.$refs.fileInput.click();
    },
    dragOver() {
      this.dragFlag = true;
    },
    dragLeave() {
      this.dragFlag = false;
    },
    drop(e) {
      this.dragFlag = false;
      let file = e.dataTransfer.files[0];
      let type = file.type;
      let flag = !this.accept || this.accept.indexOf(type) > -1;
      if (flag) {
        this.uploadFile(file);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.com-single-uploader {
  width: 100%;
  min-height: 300px;
  background: #f5f7fa;
  border: 1px dashed #cfd3e2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #8d99a5;
  font-size: 16px;
  text-align: center;

  &.dragable {
    border-color: #2390ff;
  }

  .icon {
    width: 64px;
    height: 64px;
    background: url(./images/upload_cloud.png);
    display: inline-block;
  }

  .icon-file {
    width: 50px;
    height: 50px;
    background: url(./images/upload_file.png);
    display: inline-block;
  }

  .main {
    color: #00244a;
    padding: 5px 0;
  }

  a {
    color: #2390ff;
  }

  .uploader-preview-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .file {
      width: 200px;
      height: 126px;
      background: #ffffff;
      box-shadow: 0px 2px 12px 0px rgba(11, 40, 74, 0.15);
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      p {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        color: #00244a;
        width: 100%;
        padding: 0 16px;
      }
    }
  }
}
</style>
