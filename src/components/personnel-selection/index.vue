<template>
  <!-- 被监管人员 -->
  <div>
    <div class="flex-box"><p class="detail-title">被监管人员</p>
      <Button type="primary" style="margin-top: -16px;" @click='openPrison' v-if="formData.jgrybm ">
        重新选择
      </Button>
    </div>
    <div class='jgrySelect' @click='openPrison' v-if="!formData.jgrybm">
      <p class='jgrySelect-addIcon'>
        <Icon type="ios-people" color='#fff' size='36' style='position:relative;left:-10px;top:10px;'/>
        <Icon style='position:relative;right:-10px;bottom:-10px;' size='36' type="md-add-circle" color='#fff'/>
      </p>
      <p class='jgrySelect-text'>点击选择被监管人员</p>
    </div>
    <ryxx :jgrybm="formData.jgrybm" v-if="formData.jgrybm" />
    <!-- 被监管人员选择组件 -->
    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
           title="人员列表">
      <div class="select-use">
        <prisonSelect v-if="openModal" ref="prisonSelect" ryzt="ALL" :isMultiple='false'
                      :selectUseIds="formData.jgrybm"/>
      </div>
      <div slot="footer">
        <Button type="primary" @click="useSelect" class="save">确 定</Button>
        <Button @click="openModal=false" class="save">关 闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import ryxx from "./ryxx.vue"
import { prisonSelect } from "sd-prison-select"

export default {
  components: {
    ryxx,
    prisonSelect
  },
  props: {
    curData: Object,
    saveType: String
  },
  data () {
    return {
      openModal: false,
      formData: this.curData ? this.curData : {}
    }
  },
  watch: {
    'curData': {
      handler(n, o) {
        if (this.saveType != 'add' && n) {
          this.formData = n
        }
      }, deep: true, immediate: true
    }
  },
  methods: {
    openPrison() {
      this.openModal = true
    },
    useSelect() {
      if (this.$refs.prisonSelect.checkedUse && this.$refs.prisonSelect.checkedUse.length > 0) {
        this.formData = this.$refs.prisonSelect.checkedUse[0]
        // console.log(this.formData, 'this.formData')
        this.$emit('selectUser', this.formData, this.formData.jgrybm)
        this.openModal = false
      } else {
        this.$Notice.warning({
          title: '提示',
          desc: '请选择人员!'
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.flex-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-content: center;
  border: none;
}

.jgrySelect {
  border: 1px solid #dcdee2;
  height: 140px;
  border-radius: 6px;
  background: #f7fbfd;
  margin-right: 16px;
  text-align: center;
  display: flex;
  cursor: pointer;
  flex-wrap: wrap;
  padding-top: 16px;

  .jgrySelect-addIcon {
    width: 110px;
    height: 60px;
    background: #50a6f9;
    border-radius: 6px;
    margin: auto;
  }

  .jgrySelect-text {
    width: 100%;
    // margin-top:-20px;
    color: #2d8cf0;
  }
}
</style>
