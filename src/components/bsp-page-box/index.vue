<template>
	<div class="bsp-page-box" :style="{ height: `${height}px` }">
		<div class="bsp-page-content">
			<template v-if="tableData.length">
				<slot :rows="tableData"></slot>
			</template>
			<bsp-empty v-else height="100%"></bsp-empty>
		</div>
    <div class="bsp-page-footer">
      <Page :total="tableTotal" size="small"  :page-size-opts="tablePageList"  :page-size="tableParam.size" :current.sync="tableParam.current"   show-elevator show-sizer @on-page-size-change="changeSize"  @on-change="change"/>
      <span class="total">共&nbsp;&nbsp;{{ tableTotal }}&nbsp;&nbsp;条</span>
    </div>
	</div>
</template>
<script>
import bspEmpty from "../bsp-empty/index.vue"
export default {
  components: {bspEmpty},
	data() {
		return {
			tableParam: {
				size: 10,
				current: 1
			},
			tableTotal: 0,
			tableData: []
		}
	},
	props: {
		getDataMethod: {
			type: Function,
			required: true
		},
		getDataParam: {
			type: Object,
			default: () => {
				return {}
			}
		},
		height: {
			type: Number,
		},
		isInitLoad: {
			type: Boolean,
			default: false
    },
    tablePageList: {
			type: Array,
			default: () => {
				return [10, 15, 20]
			}
		}
	},
	created() {
		this.tableParam.size = this.tablePageList[0] || 10
		this.isInitLoad && this.query_grid_data(1)
	},
	methods: {
    query_grid_data(page) {
			this.tableParam['current'] = page || this.tableParam.current || this.getDataParam.current || 1
			this.tableParam['size'] = this.getDataParam.size || this.tableParam.size || 20
			return this.getData()
		},
		getData() {
			let param = Object.assign({}, this.getDataParam, this.tableParam)
			return this.getDataMethod(param)
				.then((res) => {
					let { total, rows } = res.data
					this.tableData = rows
					this.tableTotal = total
					this.$emit('on-update', this.tableData)
				})
				.catch((err) => {
					this.$emit('on-error', err)
				})
		},
		change() {
			this.query_grid_data()
		},
		changeSize(e) {
			this.tableParam.current = 1
			this.tableParam.size = e
			this.query_grid_data()
		}
	}
}
</script>
<style lang="less">
.bsp-page-box {
	position: relative;
	background: #fff;
	overflow: hidden;
	display: flex;
	flex-direction: column;
  border: 1px solid #ddd;

	.bsp-page-content {
		flex: 1;
		overflow-y: auto;
		height: ~"calc(100% - 48px)";
	}

  .bsp-page-footer {
    border-top: 1px solid #ddd;
    width: 100%;
		height: 54px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .total {
      display: inline-block;
   
    margin: 10px 0;
    padding: 0 15px;
 
    cursor: pointer;
 
    }
  }
}
</style>
