<template>
    <div class="download_box">
        <div class="download_box_one" v-for="(item,index) in files" :key="index">
            <div v-if="photoFormat.indexOf(item.ext)>-1" class="download_box_one_icon_left download_pp"></div>
            <div v-else-if="item.ext=='pdf'" class="download_box_one_icon_left download_pdf"></div>
            <div v-else-if="item.ext=='doc' || item.ext=='docx'" class="download_box_one_icon_left download_word"></div>
            <div v-else-if="audioFormat.indexOf(item.ext)>-1" class="download_box_one_icon_left download_music"></div>
            <div v-else-if="excelFormat.indexOf(item.ext)>-1" class="download_box_one_icon_left download_excel"></div>
            <div v-else-if="item.ext=='txt'" class="download_box_one_icon_left download_txt"></div>
            <div v-else class="download_box_one_icon_left download_unknow"></div>
            <div class="download_box_one_right " >
                <div class="download_box_one_text" :title="item.name||item.fileName||''">{{ item.name||item.fileName||'' }}</div>
                <div class="item_right ">
                    <Icon type="md-eye" title="预览" style="font-size:0.2rem;margin-right:0.1rem;" @click="see(item,index)" v-if="item.isImgOrPdf"/>
                    <div class="download_box_one_icon_right"  @click="downloadUrl(item)"></div>
                </div>
            </div>
        </div>
           <!-- 轮播图片 -->
        <carousel v-if="showCarousel" :isDeleteEv="false" :visible.sync='showCarousel' :imgPhotoList="imgPhotoList"  @values="imgValues" :imgIndex="wpxxIndex"/>
        
    </div>
</template>
<script>
//轮播图片
import carousel from '@/view/components/carouselImg/carousel-img.vue';
import axios from 'axios'
export default {
    components:{
        carousel,
    },
    props:{
        uploadFiles:{
            type:Array
        }
    },
    watch: {
        uploadFiles:{
            handler(n,o){
                this.files = []
                function isFormat(name) {
                    var format = ['.jpg','.jpeg','.png','.bmp','.gif','.tif','.pdf'].filter(item => {
                        return name.indexOf(item) > -1;
                    })
                    return format
                }
                n.forEach((item,index)=>{
                    if(isFormat(item.url).length>0){
                        item.isImgOrPdf = true
                    } else {
                        item.isImgOrPdf = false
                    }
                    this.files.push(item)
                })

            },
            deep: true,
            immediate: true
        }
    },
    data(){
        return{
            files: [],
            url:serverConfig.uploadUrl,
            photoFormat:['bmp', 'jpg', 'png', 'tif', 'gif', 'pcx', 'tga', 'exif', 'fpx', 'svg', 'psd', 'cdr', 'pcd', 'dxf',
            'ufo', 'eps', 'ai', 'raw', 'WMF', 'webp'],
            audioFormat:['wmv', 'asf', 'asx', 'rm', 'rmvb', 'mp4', '3pg', 'mov','m4v', 'avi', 'dat', 'mkv', 'flv', 'vob', 'mp3'],
            excelFormat:['xlsx', 'xlsm', 'xltx', 'xltm', 'xlsb', 'xlam'],
            showCarousel:false,
            wpxxIndex:0,
            imgPhotoList:[],
        }
    },
    created(){
        
    },
    methods:{
        downloadUrl(item){
            // var url=item.path!= undefined ? this.url+ item.path:'';
            // window.open(url.replace(serverConfig.uploadUrl,serverConfig.downloadUrl));
            // let a = document.createElement('a')
            // a.href = item.url
            // a.click();
            axios({
                url: item.url,
                method: "get",
                responseType: "blob"
            }).then(response => {
                const link = document.createElement("a");
                let blob = new Blob([response.data], { type: response.data.type });
                let url = URL.createObjectURL(blob);
                link.href = url;
                link.download = item.fileName;
                link.click();
            });
        },
        see(item,index){
            this.imgPhotoList=[];
            if(item.url.indexOf('.pdf')>-1){
                let routeData = this.$router.resolve({
                    path:'/dms/pdfViewer',
                    query:{
                        pdfUrl:item.url
                    }
                })
                window.open(routeData.href, '_blank');

            }else{
                this.imgPhotoList=[];
                var _self=this;
                function isFormat(name) {
                    var format = ['.jpg','.jpeg','.png','.bmp','.gif','.tif','.pdf'].filter(item => {
                        return name.indexOf(item) > -1;
                    })
                    return format
                }
               
                this.files.forEach(element => {
                    var name = element.name || element.fileName
                    if(element.url.indexOf('.pdf')==-1&&isFormat(name).length>0){
                        _self.imgPhotoList.push(element)
                        if(item.id==element.id){
                            _self.wpxxIndex=_self.imgPhotoList.length-1;
                        }
                    }
                });
                this.showCarousel=true;
            }
        },
        imgValues(data,index,flag){
            this.showCarousel=false;
        },
    }
}
</script>
<style lang="less" scoped>
.download_box_one_text {
    width: 60%;
    overflow: hidden;
    text-overflow : ellipsis;
    white-space: nowrap;
    line-height: 0.24rem;
}
</style>