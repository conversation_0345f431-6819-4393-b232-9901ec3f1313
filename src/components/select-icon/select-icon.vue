<template>
  <div class="select-icon">
    <div class="picture-card" @click="handleModal" :style="{ width: width, height: height }">
      <img :src="selectUrl" alt="" v-if="selectUrl" />
      <Icon type="md-add" size="40" v-else />
    </div>
    <Button type="primary" @click="handleModal">{{ title }}</Button>
    <!-- 弹窗 -->
    <Modal v-model="openStatus" :mask-closable="false" :closable="false" class-name="bsp-role-assign" :width="1024">
      <div class="flow-modal-title" slot="header">
        <span style="font-size: 18px; font-weight: 400">选择图标</span>
        <span @click="cancel" style="position: absolute; right: 10px; font-size: 36px; cursor: pointer">
          <i class="ivu-icon ivu-icon-ios-close" style="position: relative; top: -3px"></i>
        </span>
      </div>
      <div class="img-box">
        <div v-for="(item, index) in imgArr" :key="index" :class="activeIndex == index ? 'active' : ''" @click="handleIcon(item, index)">
          <!-- <img :src="require(`../../../public/image/${item}`)" :class="activeIndex == index ? '' : 'rubberBand'" class="animated" /> -->
          <img :src="item.icon" :class="activeIndex == index ? 'active' : 'rubberBand'" class="animated" />

        </div>
      </div>
      <div slot="footer">
        <Button @click="cancel">取消</Button>
        <Button style="margin: 0 10px" @click="handleComfirm" type="primary">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'SelectIcon',
  props: {
    fileName: {
      type: String,
      default: 'appIcon'
    },
    title: {
      type: String,
      default: '选择图标'
    },
    width: {
      type: String,
      default: '130px'
    },
    height: {
      type: String,
      default: '130px'
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      openStatus: false,
      imgArr: [],
      activeIndex: null,
      selectUrl: '',
      selectUrlName: '',
      appId: serverConfig.APP_ID,
    }
  },
  watch: {
    value() {
        this.selectUrl =this.value  // require(`../../../public/image/${this.value}`)
        this.imgArr.forEach((ele,index)=>{
            if(ele.icon == this.value){
              this.activeIndex =index
            }
        })
      // this.activeIndex = this.imgArr.indexOf(this.value)
    }
  },
  created() {
    // var pattern = new RegExp(this.fileName)
    // const context = require.context('../../../public/image/', true, /$/)
    // context.keys().forEach(ele => {
    //   if (ele.indexOf(this.fileName) !== -1 && ele.indexOf('active') == -1) {
    //     this.imgArr.push(ele.split('./')[1])
    //   }
    // })
    if (this.value) {
      this.selectUrl =this.value  // require(`../../../public/image/${this.value}`)
      // this.activeIndex = this.imgArr.indexOf(this.value)
        this.imgArr.forEach((ele,index)=>{
            if(ele.icon == this.value){
              this.activeIndex =index
            }
        })
    }
    this.getIconData()
  },
  methods: {
    getIconData(){
        this.$store.dispatch('postRequest',{ url: this.$path.com_getTbByappId_url,params: {appId:this.appId} }).then(data => {
          if (data.success) {
            // this.original_data=JSON.parse(JSON.stringify(data.data))
            this.imgArr=data.data
            
          } else {
            this.$Notice.error({
              title: '错误提示',
              desc: data.msg
            })
          }
        })
    },
    // 打开弹窗
    handleModal() {
      this.openStatus = true
    },
    // 关闭
    cancel() {
      this.openStatus = false
      this.$emit('on_cancel')
    },
    // 选中图标
    handleIcon(item, index) {
      this.activeIndex = index
      this.selectUrlName = item.icon//name
    },
    // 确认
    handleComfirm() {
      this.openStatus = false
      this.selectUrl =this.selectUrlName  //require(`../../../public/image/${this.selectUrlName}`)
      this.$emit('choice_confirm', this.selectUrlName)
    }
  }
}
</script>

<style lang="less" scoped>
.bsp-role-assign .flow-modal-title {
  height: 40px;
  background: #2b5fda;
  width: 100%;
  text-indent: 1em;
  color: #fff;
  line-height: 40px;
}

/**重置对话框 */
/deep/ .bsp-role-assign .ivu-modal-content .ivu-modal-body,
/deep/ .bsp-role-assign .ivu-modal-header {
  padding: 0px;
}

/deep/ .bsp-role-assign .ivu-modal-footer {
  border-top: 1px solid #cee0f0;
  background: #f7faff;
}
</style>
<style lang="less" scoped>
@import url('~@/assets/style/animate.min.css');
.select-icon {
  display: flex;
  flex-flow: column;
  align-items: center;
  .picture-card {
    background-color: #fbfdff;
    border: 1px dashed #c0ccda;
    border-radius: 6px;
    cursor: pointer;
    line-height: 146px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      border-color: #409eff;
      color: #409eff;
    }
    > img {
      width: 100%;
      height: 100%;
      padding: 15px;
    }
  }
  .ivu-btn {
    line-height: 30px;
    margin-top: 15px;
  }
}
.img-box {
  padding: 5px 0px 5px 5px;
  display: flex;
  flex-wrap: wrap;
  max-height: 500px;
  overflow: auto;
  > div {
    padding: 10px 10px 5px 10px;
    border-radius: 5px;
    // height: fit-content;
    height: 95px;
    width: 100px;
    &:hover,
    &.active {
      background: #ddd;
      > img {
        animation: myfirst 0.8s; // 动画名称，播放时间
        animation-iteration-count: infinite; //无限播放
      }
    }
    img {
      cursor: pointer;
      width: 100%;
      height: 100%;
    }
    @keyframes myfirst {
      0% {
        transform: rotate(-7deg;);
      }
      25% {
        transform: rotate(7deg;);
      }
      50% {
        transform: rotate(-7deg;);
      }
      75% {
        transform: rotate(7deg;);
      }
      100% {
        transform: rotate(-7deg;);
      }
    }
  }
}
</style>
