<!--表单  -->
<template>
    <div class="fm-content-wrap">
        <Form ref="formData" :model="formData" :label-colon="true"  @on-validate="formOnValidate" label-position="right" :label-width="130">
            <div class="fm-content-form">
          <p class="fm-content-wrap-title"><Icon type="md-list-box" size="24" color="#2b5fda" />基础信息</p>
            <Row>
                <Col span="8">
                  <FormItem label="药品名称">
                    <Input v-model="formData.input" placeholder="请输入" style="width: 100%;"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="药品类别">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="是否毒麻品">
                    <RadioGroup v-model="formData.disabledGroup">
                        <Radio label="是" disabled></Radio>
                        <Radio label="否"></Radio>
                    </RadioGroup>
                  </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                  <FormItem label="生产单位">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="药品本位码">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="剂型">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                  <FormItem label="计量单位">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="单位换算系数">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="最小计量单位">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                  <FormItem label="规格">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="每天用量">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="批准文号">
                    <Input v-model="formData.input" placeholder="请输入"></Input>

                  </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                  <FormItem prop='spwh' label="原批准文号" :rules="[{ trigger: 'blur,change', message: '必填', required: true }]">
                    <Input v-model="formData.spwh" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="是否可报损"  :rules="{required: true, message: '必填', trigger: 'blur'}">
                    <RadioGroup v-model="formData.disabledGroup">
                        <Radio label="是" disabled></Radio>
                        <Radio label="否"></Radio>
                    </RadioGroup>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="入库是否需审批">
                    <RadioGroup v-model="formData.disabledGroup">
                        <Radio label="是" disabled></Radio>
                        <Radio label="否"></Radio>
                    </RadioGroup>
                  </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                  <FormItem label="所属药库">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="药品类型">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>

            </Row>
          </div>
          <div class="fm-content-form">
            <p class="fm-content-wrap-title"><Icon type="md-list-box" size="24" color="#2b5fda" />其他信息</p>
            <Row>
                <Col span="8">
                  <FormItem label="库存预警量">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="英文名称">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="药品别名">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                  <FormItem label="商品名称">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="批准日期">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="上市许可持有人">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                  <FormItem label="上市许可持有人地址">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="生产地址">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="药品本位码备注">
                    <Input v-model="formData.input" placeholder="请输入"></Input>

                  </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="8">
                  <FormItem label="包装单位">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                </Row>
              </div>
        </Form>
    </div>
</template>

<script>
export default {
  data(){
    return{
        formData:{}
    }
  },
  methods:{
    formOnValidate(){

    }
  }
}
</script>

<style>
@import url('~@/components/fm/styles/formForm.css')
</style>