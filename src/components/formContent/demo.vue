<!--表单  -->
<template>
    <div class="fm-content-wrap">
        <Form ref="formData" :model="formData" :label-colon="true"  @on-validate="formOnValidate" label-position="right" :label-width="130">
            <div class="fm-content-form">
            <p class="fm-content-wrap-title"><Icon type="md-list-box" size="24" color="#2b5fda" />基础信息</p>
            <Row>
                <Col span="8">
                  <FormItem label="药品名称">
                    <Input v-model="formData.input" placeholder="请输入" style="width: 100%;"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="药品类别">
                    <Input v-model="formData.input" placeholder="请输入"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="是否毒麻品">
                    <RadioGroup v-model="formData.disabledGroup">
                        <Radio label="是" disabled></Radio>
                        <Radio label="否"></Radio>
                    </RadioGroup>
                  </FormItem>
                </Col>
            </Row>
            </div>
        </Form>
    </div>
 </template>
     
     <script>
     export default {
       data(){
         return{
             formData:{}
         }
       },
       methods:{
         formOnValidate(){
     
         }
       }
     }
     </script>
     
     <style></style>