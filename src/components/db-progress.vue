/** 实现百分比占比条效果 */
<template>
  <div class="about">
    <div class="step">
      <!-- 左边100%的时候不显示斜边三角形，并且增加右边角 -->
      <div
        class="left"
        v-show="percent"
        :class="[{ 'full-left': !rightPercent }, { tringle: rightPercent }]"
        :style="{ width: percent + '%', background: leftBg, color: leftColor }"
        @mouseover="onMouseTooltip(LEFT_BAR, SHOW_TIP)"
        @mouseleave="onMouseTooltip(LEFT_BAR, HIDE_TIP)"
      >
        <div class="bar-tip-box" v-show="leftBar.isShowTip && isShowTip">
          <p>总数：{{ totalNum }}</p>
          <p>绿色所占比例：{{ percent }}%</p>
        </div>
        <div class="tip-arrow" v-show="leftBar.isShowTip && isShowTip"></div>
        {{ leftValue ? leftValue : percent + '%' }}
      </div>
      <div
        class="right"
        v-show="rightPercent"
        :class="[{ 'full-right': !percent }]"
        :style="{ background: rightBg, color: rightColor }"
        @mouseover="onMouseTooltip(RIGHT_BAR, SHOW_TIP)"
        @mouseleave="onMouseTooltip(RIGHT_BAR, HIDE_TIP)"
      >
        <div class="bar-tip-box" v-show="rightBar.isShowTip && isShowTip">
          <p>总数：{{ totalNum }}</p>
          <p>灰色所占比例：{{ rightPercent }}%</p>
        </div>
        <div class="tip-arrow" v-show="rightBar.isShowTip && isShowTip"></div>
        {{ rightValue ? rightValue : rightPercent + '%' }}
      </div>
    </div>
  </div>
</template>

<script>
const LEFT_BAR = 'left'
const RIGHT_BAR = 'right'
const SHOW_TIP = 'show'
const HIDE_TIP = 'hide'

export default {
  name: 'progressView',
  props: {
    percent: {
      type: [String, Number],
      default: 0
    },
    leftValue: {
      type: [String, Number],
      default: ''
    },
    leftColor: {
      type: String,
      default: '#fff'
    },
    leftBg: {
      type: String,
      default: '#62c87f'
    },
    rightValue: {
      type: [String, Number],
      default: ''
    },
    rightColor: {
      type: String,
      default: '#7A8399'
    },
    rightBg: {
      type: String,
      default: '#d0d4dc'
    },
    isShowTip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      LEFT_BAR: LEFT_BAR,
      RIGHT_BAR: RIGHT_BAR,
      SHOW_TIP: SHOW_TIP,
      HIDE_TIP: HIDE_TIP,
      totalNum: 1000,
      leftBar: {
        isShowTip: false,
        delayOut: null
      },
      rightBar: {
        isShowTip: false,
        delayOut: null
      }
    }
  },
  methods: {
    onMouseTooltip(tipType, actionType) {
      let bar = null
      if (tipType == LEFT_BAR) {
        bar = this.leftBar
      } else if (tipType == RIGHT_BAR) {
        bar = this.rightBar
      } else {
        return
      }
      if (actionType === SHOW_TIP) {
        this.showBarTooltip(bar)
      } else if (actionType === HIDE_TIP) {
        this.hideBarTooltip(bar)
      } else {
        return
      }
    },
    showBarTooltip(bar) {
      if (bar.delayOut != null) {
        clearTimeout(bar.delayOut)
      }
      bar.delayOut = null
      bar.isShowTip = true
    },
    hideBarTooltip(bar) {
      clearTimeout(bar.delayOut)
      bar.delayOut = setTimeout(function () {
        bar.isShowTip = false
      }, 100)
    }
  },
  computed: {
    rightPercent: function () {
      return 100 - Number(this.percent)
    }
  }
}
</script>

<style lang="less" scoped>
.step {
  position: relative;
  display: flex;
  width: 100%;
  font-size: 0;
  .left {
    flex-grow: 0;
    position: relative;
    display: inline-block;
    background: #62c87f;
    color: #fff;
    text-align: right;
    font-weight: 400;
    width: 70%;
    font-size: 15px;
    line-height: 30px;
    height: 30px;
    min-width: 30px;
    padding-right: 8px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  // 百分百的时候不显示该伪类
  .tringle::after {
    // content: ' ';
    // position: absolute;
    // top: 0;
    // right: -8px;
    // border-width: 30px 8px;
    // border-style: solid;
    // border-color: #62c87f transparent transparent transparent;
    // z-index: 10;
  }

  .right {
    flex-grow: 1;
    position: relative;
    display: inline-block;
    /* width:30%; */
    background: #d0d4dc;
    color: #7a8399;
    font-weight: 400;
    text-align: right;
    font-size: 15px;
    line-height: 30px;
    height: 30px;
    min-width: 35px;
    padding-right: 8px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .full-left {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .full-right {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }

  .tip-arrow {
    position: absolute;
    left: 50%;
    top: -10px;
    display: inline-block;
    width: 7px;
    height: 7px;
    transform: rotateZ(45deg);
    -webkit-transform: rotateZ(45deg);
    background-color: #7f7f7f;
    z-index: 10;
  }

  .bar-tip-box {
    position: absolute;
    top: -5px;
    right: 50%;
    transform: translate(50%, -100%);
    text-align: left;
    padding: 5px 10px;
    width: max-content;
    color: #fff;
    font-size: 12px;
    font-weight: 400;
    border-radius: 4px;
    background-color: #7f7f7f;
    z-index: 10;

    p {
      margin: 0;
      padding-bottom: 5px;
    }
  }
}
</style>