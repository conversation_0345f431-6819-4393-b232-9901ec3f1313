/*
 * @Author: 丁永泽 <EMAIL>
 * @Date: 2025-07-23 11:09:38
 * @LastEditors: 丁永泽 <EMAIL>
 * @LastEditTime: 2025-07-23 11:10:02
 * @FilePath: \rs-acp-web\src\components\bsp-upload\index.js
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import FileUpload from './FileUpload.vue'

const Plugin = {
  install (Vue) {
    Vue.component('bsp-file-upload', FileUpload)
  }
}

export default Plugin