<template>
  <div>
    <p class="detail-title">监室选择</p>
    <Tabs :value="tabValue" @on-click="getAttribute">
      <TabPane label="主协管监室" name="mainAssistantManager"></TabPane>
      <TabPane label="全部监室" name="all"></TabPane>
    </Tabs>
    <div class="prison-select">
      <div class="prison-select-left">
        <div class="prisonData-wrap">
          <p :class="['prisonData-wrap-child', curAreaCode == '' ? 'prisonData-wrap-child-active' : '']"
            @click="selectPrison()">
            <Icon type="ios-at" /><span class="prisonName">全部</span>
          </p>
          <p :class="['prisonData-wrap-child', curAreaCode == item.areaCode ? 'prisonData-wrap-child-active' : '']"
            v-for="(item, index) in prisonData" :key="index" @click="selectPrison(item)">
            <Icon type="md-home" /><span class="prisonName">{{ item.areaName }}</span>
          </p>
        </div>
      </div>
      <div class="prison-select-center">
        <div style="display: flex;padding:0 16px 16px 16px;align-items:center">
          <span class="use-form-title">监室号：</span>
          <Input v-model="page.roomName" style="width: 160px;height: 30px;" @input="search"></Input>
          <div>
            &nbsp;&nbsp;&nbsp;<Button @click="reset" size="small">重置</Button>
            &nbsp;&nbsp;<Button type="primary" @click="search" size="small">搜索</Button>
          </div>
        </div>
        <div class="use-list" v-if="roomList && roomList.length > 0">
          <div v-for="(item, index) in roomList" :key="index" :class="['use-list-box', item.checked ? 'checked' : '']"
            @click="selectCur(item)">
            <div class="user-flex-sm">
              <img
                :src="item.roomSex == '1' || item.roomSex == '5' ? require('@/assets/icons/man.svg') : require('@/assets/icons/woman.svg')"
                style="padding-right: 5px;" />
              <div style="width: 100%;">
                <p>{{ item.roomName }}</p>
                <p>
                  <Icon type="md-person" />&nbsp;{{ item.imprisonmentAmount }}
                  <Tag color="primary" v-if="item.roomType == '19'">单独关押</Tag>
                </p>
              </div>
            </div>
            <Icon type="md-checkmark-circle" :size="26" color="#2da94f" v-if="item.checked" class="checkedIcon" />
            <Spin size="large" fix v-if="spinShow"></Spin>
          </div>
        </div>
        <div class="use-list" v-else style="">
          <div style="margin:auto">
            <img src="@/assets/images/noData.png" />
            <p class="noUseData">暂无监室数据</p>
          </div>
        </div>
        <div class="pageWrap">
          <Page :total="total" class="pageWrap" size="small" show-elevator show-sizer :page-size-opts='pageSizeOpts'
            show-total :page-size="page.pageSize" @on-prev="getNo" @on-next="getNo" :current="page.pageNo"
            @on-change="getNo" @on-page-size-change="getSize" />
        </div>
      </div>
      <div class="prison-select-right">
        <p class="prison-select-right-title">已选择监室({{ checkedRoom.length }})</p>
        <div class="selectedUseWrap" v-if="checkedRoom && checkedRoom.length > 0">
          <template>
            <p :key="index + 'roomList'" class="selectedUse" v-for="(item, index) in checkedRoom">
              <span style="width: 100px; display: inline-block; text-align: left;">{{ item.roomName }}</span>&nbsp;
              <Icon style="cursor: pointer;" type="md-close-circle" :size="20" color="red"
                @click="cancelSelect(item, index)" />
            </p>
          </template>
        </div>
        <p v-else class="noData">暂未选择监室</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    isMultiple: { // 是否多选
      type: Boolean,
      default: true
    },
    selectRoomIds: {
      type: String, // 已选择监室
      default: ''
    }
  },
  watch: {
    roomList: {
      handler(n, o) {
        if (this.checkedRoom && this.checkedRoom.length > 0) {
          this.checkedRoom.forEach((item, j) => {
            n.forEach((ele, i) => {
              if (item.roomCode == ele.roomCode) {
                this.$set(this.roomList[i], "checked", true)
              }
            })
          })
        }
      }
    },
    selectRoomIds: {
      handler(n, o) {
        if (!n) {
          this.checkedRoom = []
          this.roomList.forEach(item => {
            item.checked = false

          })
        }
      }
    }
  },
  data() {
    return {
      tabValue: 'mainAssistantManager',
      searchForm: {
        mainAssistantManager: true
      },
      prisonData: [],
      roomList: [],
      curAreaCode: '',
      checkedRoom: [],
      singleRoom: '',
      page: {
        pageNo: 1,
        pageSize: 12,
        roomName: ''
      },
      total: 0,
      pageSizeOpts: [12, 24, 36, 48, 60, 72],
      spinShow: false,
      defaultImg: require('@/assets/images/main.png'),
    }
  },
  mounted() {
    this.getPrison()
    this.getRoomData()
  },
  methods: {
    getAttribute(name) {
      if (this.tabValue != name) {
        this.$set(this, 'tabValue', name)
        if (name == 'mainAssistantManager') {
          this.$set(this.searchForm, 'mainAssistantManager', true)
        } else {
          this.$set(this.searchForm, 'mainAssistantManager', false)
        }
        this.curAreaCode = ''
        this.page.pageNo = 1
        this.getPrison()
        this.getRoomData()
      }
    },
    getPrison() {
      let params = {
        orgCode: this.$store.state.common.orgCode
      }
      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/area/getAreaListByOrgCode',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.prisonData = resp.data ? resp.data : []
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    selectPrison(item) {
      if (item) {
        if (item.areaCode != this.curAreaCode) {
          this.curAreaCode = item.areaCode
          this.getRoomData(item)
        }
      }
      else {
        if (this.curAreaCode != '') {
          this.curAreaCode = ''
          this.getRoomData(item)
        }
      }
    },
    getRoomData(item) {
      this.spinShow = true
      let params = {
        orgCode: this.$store.state.common.orgCode,
        roomName: this.page.roomName,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
      }

      //监区过滤
      if (item) {
        params.areaId = item.areaCode
      }

      //主协管过滤
      params = Object.assign({}, params, this.searchForm)

      this.$store.dispatch('authPostRequest', {
        url: '/acp-com/base/pm/areaPrisonRoom/page',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.roomList = resp.data.list ? resp.data.list : [],
            this.total = resp.data.total
          this.spinShow = false
          if (this.selectRoomIds && this.roomList && this.roomList.length > 0) {
            let selectRoomIdsArr = this.selectRoomIds.split(',')
            this.roomList.forEach(item => {
              this.$set(item, 'checked', false)
              selectRoomIdsArr.forEach(ele => {
                if (item.roomCode == ele) {
                  this.$set(item, 'checked', true)
                  // this.selectCur(item)  
                  return;
                }
              })
            })
          }
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
          this.spinShow = false
        }
      })
    },
    search() {
      this.getRoomData()
    },
    reset() {
      this.$set(this.page, 'roomName', '')
      this.getRoomData()
    },
    getNo(pageNo) {
      this.$set(this.page, 'pageNo', pageNo)
      this.getRoomData()
    },
    getSize(pageSize) {
      this.$set(this.page, 'pageSize', pageSize)
      this.getRoomData()
    },
    selectCur(item) {
      if (!this.isMultiple) {
        this.checkedRoom = []
        this.roomList.forEach(ele => {
          this.$set(ele, 'checked', false)
        })
      }
      if (this.roomList.length > 0) {
        this.roomList.forEach(ele => {
          if (ele.roomCode == item.roomCode) {
            this.$set(ele, 'checked', !ele.checked)
            if (ele.checked) {
              this.checkedRoom.push(ele)
            } else {
              this.checkedRoom.forEach((item, i) => {
                if (item.roomCode == ele.roomCode) {
                  this.checkedRoom.splice(i, 1)
                }
              })
            }
          }
        })
      }
    },
    cancelSelect(item, index) {
      if (this.roomList.length > 0) {
        this.roomList.forEach(ele => {
          if (ele.roomCode == item.roomCode) {
            this.$set(ele, 'checked', false)
          }
        })
      }
      this.checkedRoom.splice(index, 1)
    },
    getCheckedRoom(roomId) {

      let params = {
        orgCode: this.$store.state.common.orgCode,
        pageNo: 1,
        pageSize: 99,
      }
      this.$store.dispatch('authPostRequest', {
        url: '/acp-com/base/pm/areaPrisonRoom/page',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.checkedRoom = resp.data.list.filter(item => this.selectRoomIds.split(',').includes(item.id))
          if (this.selectRoomIds && this.roomList && this.roomList.length > 0) {
            let selectRoomIdsArr = this.selectRoomIds.split(',')
            this.roomList.forEach(item => {
              this.$set(item, 'checked', false)
              selectRoomIdsArr.forEach(ele => {
                if (item.roomCode == ele) {
                  this.$set(item, 'checked', true)
                  // this.selectCur(item)  
                  return;
                }
              })
            })
          }
        }
      })
    },
  }
}
</script>
<style lang="less">
@import url('./index.less');
</style>
<style lang="less" scoped>
.prisonData-wrap {
  height: 340px;
  overflow-y: auto;

  .prisonData-wrap-child {
    width: 150px;
    display: flex;
    align-items: center;
    padding: 6px 16px;
    cursor: pointer;

    &:hover {
      background: #e6f6ff;
      border-radius: 6px;
      color: #4094f0;
    }

    .prisonName {
      margin-left: 16px;
      display: inline-block;
    }
  }
}

.prisonData-box {
  display: flex;
  justify-content: space-between;
}

.prisonData-wrap {
  border-right: 1px solid #efefef;
  padding-right: 10px;
}

.prisonData-wrap-child-active {
  background: #e6f6ff;
  border-radius: 6px;
  color: #4094f0;
}
</style>