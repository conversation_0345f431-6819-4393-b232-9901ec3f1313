<template>
  <div class="global-component-test">
    <h2>PersonnelSelector 全局组件注册测试</h2>
    <p class="description">
      测试 PersonnelSelector 组件是否已成功注册为全局组件，可以在任何地方直接使用。
    </p>
    
    <Row :gutter="20">
      <Col span="12">
        <Card title="全局组件使用">
          <div class="test-info">
            <h4>✅ 全局组件注册成功！</h4>
            <p>现在可以在任何Vue组件中直接使用 <code>&lt;PersonnelSelector&gt;</code> 标签，无需import。</p>
          </div>
          
          <!-- 直接使用全局组件，无需import -->
          <PersonnelSelector
            v-model="selectedPersonnel"
            :title="'选择人员'"
            :placeholder="'点击选择人员进行测试'"
            @change="handlePersonnelChange"
          />
          
          <div v-if="selectedPersonnel" class="selected-info">
            <h4>已选择人员信息：</h4>
            <pre>{{ JSON.stringify(selectedPersonnel, null, 2) }}</pre>
          </div>
        </Card>
      </Col>
      
      <Col span="12">
        <Card title="全局方法测试">
          <div class="method-test">
            <h4>全局工具方法测试</h4>
            
            <div class="method-item">
              <strong>$formatPersonnelInfo:</strong>
              <pre>{{ formattedPersonnel }}</pre>
            </div>
            
            <div class="method-item">
              <strong>$isValidPersonnel:</strong>
              <span>{{ isValidPersonnel }}</span>
            </div>
            
            <div class="method-item">
              <strong>$getPersonnelDisplayName:</strong>
              <span>{{ displayName }}</span>
            </div>
            
            <div class="method-item">
              <strong>$getPersonnelAvatar:</strong>
              <span>{{ avatarUrl }}</span>
            </div>
          </div>
        </Card>
      </Col>
    </Row>
    
    <Row style="margin-top: 20px;">
      <Col span="24">
        <Card title="使用代码示例">
          <Tabs>
            <TabPane label="模板代码" name="template">
              <pre class="code-block">{{ templateCode }}</pre>
            </TabPane>
            <TabPane label="全局方法" name="methods">
              <pre class="code-block">{{ methodsCode }}</pre>
            </TabPane>
            <TabPane label="配置选项" name="config">
              <pre class="code-block">{{ configCode }}</pre>
            </TabPane>
          </Tabs>
        </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
export default {
  name: 'PersonnelSelectorGlobalTest',
  data() {
    return {
      selectedPersonnel: null
    }
  },
  
  computed: {
    // 测试全局方法
    formattedPersonnel() {
      return this.$formatPersonnelInfo(this.selectedPersonnel, ['xm', 'roomName', 'jgrybm'])
    },
    
    isValidPersonnel() {
      return this.$isValidPersonnel(this.selectedPersonnel)
    },
    
    displayName() {
      return this.$getPersonnelDisplayName(this.selectedPersonnel)
    },
    
    avatarUrl() {
      return this.$getPersonnelAvatar(this.selectedPersonnel, '/default-avatar.png')
    },
    
    templateCode() {
      return `<!-- 直接使用全局组件，无需import -->
<template>
  <PersonnelSelector
    v-model="selectedPersonnel"
    :title="'选择人员'"
    :placeholder="'点击选择人员'"
    @change="handlePersonnelChange"
  />
</template>

<script>
export default {
  // 无需在components中注册PersonnelSelector
  data() {
    return {
      selectedPersonnel: null
    }
  },
  methods: {
    handlePersonnelChange(personnel) {
      console.log('选择的人员:', personnel)
    }
  }
}
</script>`
    },
    
    methodsCode() {
      return `// 使用全局方法
export default {
  methods: {
    testGlobalMethods() {
      // 格式化人员信息
      const formatted = this.$formatPersonnelInfo(personnel, ['xm', 'roomName'])
      
      // 检查人员是否有效
      const isValid = this.$isValidPersonnel(personnel)
      
      // 获取显示名称
      const name = this.$getPersonnelDisplayName(personnel)
      
      // 获取头像URL
      const avatar = this.$getPersonnelAvatar(personnel, '/default.png')
    }
  }
}`
    },
    
    configCode() {
      return `// 全局配置选项
Vue.use(PersonnelSelectorPlugin, {
  defaultConfig: {
    title: '默认标题',
    placeholder: '默认占位符',
    showAvatar: true,
    showRoomName: true
  }
})

// 在组件中访问默认配置
export default {
  mounted() {
    console.log(this.$personnelSelectorDefaults)
  }
}`
    }
  },
  
  methods: {
    handlePersonnelChange(personnel) {
      console.log('人员选择变化:', personnel)
      this.selectedPersonnel = personnel
    }
  }
}
</script>

<style scoped>
.global-component-test {
  padding: 20px;
}

.description {
  color: #666;
  margin-bottom: 20px;
}

.test-info {
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.test-info h4 {
  color: #1890ff;
  margin: 0 0 10px 0;
}

.selected-info {
  margin-top: 20px;
  padding: 15px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.method-test {
  padding: 10px 0;
}

.method-item {
  margin-bottom: 15px;
  padding: 10px;
  background: #fafafa;
  border-radius: 4px;
}

.method-item strong {
  color: #1890ff;
}

.method-item pre {
  margin: 5px 0 0 0;
  font-size: 12px;
}

.code-block {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
}
</style>
