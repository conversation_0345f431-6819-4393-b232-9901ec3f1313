# 人员选择组件 (PersonnelSelector)

人员选择组件已全局注册为 `PersonnelSelector`，可以在任何 Vue 组件中直接使用，无需导入。

## 特性

- 🚀 **全局注册**: 已注册为全局组件，可在任何地方直接使用
- 👥 **人员选择**: 支持从在押人员中选择
- 📱 **扫码支持**: 支持扫码快速选择人员
- 🎨 **信息展示**: 完整展示人员基本信息和案件信息
- 🔧 **多种模式**: 支持单选模式，多选模式开发中
- ✅ **数据验证**: 内置人员数据有效性验证
- 🎯 **事件丰富**: 提供完整的选择和变化事件

## 安装使用

### 全局注册（推荐）

组件已在 `main.js` 中全局注册，可直接使用：

```vue
<template>
  <div>
    <!-- 直接使用，无需import -->
    <PersonnelSelector
      v-model="selectedPersonnelCode"
      @change="onPersonnelChange"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedPersonnelCode: ''
    }
  },
  methods: {
    onPersonnelChange(personnelData, jgrybm) {
      console.log('选中的人员:', personnelData)
      console.log('人员编码:', jgrybm)
    }
  }
}
</script>
```

## 属性 (Props)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value (v-model) | String | '' | 人员编码，支持双向绑定 |
| title | String | '被监管人员' | 组件标题 |
| placeholder | String | '点击选择在押人员' | 占位符文本 |
| personnelType | String | 'ZS' | 人员类型过滤，ZS-在所，ALL-全部 |
| isMultiple | Boolean | false | 是否多选（暂未完全实现） |
| showCaseInfo | Boolean | true | 是否显示案件信息 |
| enableScan | Boolean | true | 是否启用扫码功能 |
| showScanTip | Boolean | true | 是否显示扫码提示 |

## 事件 (Events)

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | jgrybm (String) | v-model 绑定事件，返回人员编码 |
| change | personnelData (Object), jgrybm (String) | 人员选择变化事件，返回完整人员信息和编码 |

## 全局方法

组件注册时会自动添加以下全局方法到 Vue 原型上：

### $formatPersonnelInfo(personnel, fields)

格式化人员信息显示

```javascript
// 格式化指定字段
const formatted = this.$formatPersonnelInfo(personnel, ['xm', 'roomName', 'jgrybm'])
console.log(formatted) // { xm: '张三', roomName: '101室', jgrybm: 'P001' }
```

### $isValidPersonnel(personnel)

检查人员数据是否有效

```javascript
const isValid = this.$isValidPersonnel(personnel)
console.log(isValid) // true/false
```

### $getPersonnelDisplayName(personnel)

获取人员显示名称

```javascript
const name = this.$getPersonnelDisplayName(personnel)
console.log(name) // '张三' 或 '-'
```

### $getPersonnelAvatar(personnel, defaultImg)

获取人员头像URL

```javascript
const avatar = this.$getPersonnelAvatar(personnel, '/default-avatar.png')
console.log(avatar) // 头像URL或默认图片
```

## 全局配置

可以在注册时提供默认配置：

```javascript
// main.js
Vue.use(PersonnelSelectorPlugin, {
  defaultConfig: {
    title: '选择人员',
    placeholder: '点击选择人员',
    showAvatar: true,
    showRoomName: true
  }
})
```

在组件中访问默认配置：

```javascript
export default {
  mounted() {
    console.log(this.$personnelSelectorDefaults)
  }
}
```

## 使用示例

### 基础用法
```vue
<personnel-selector v-model="jgrybm" @change="handleChange" />
```

### 自定义标题和占位符
```vue
<personnel-selector
  v-model="jgrybm"
  title="选择在押人员"
  placeholder="请选择需要操作的在押人员"
  @change="handleChange"
/>
```

### 不显示案件信息
```vue
<personnel-selector
  v-model="jgrybm"
  :show-case-info="false"
  @change="handleChange"
/>
```

### 禁用扫码功能
```vue
<personnel-selector
  v-model="jgrybm"
  :enable-scan="false"
  :show-scan-tip="false"
  @change="handleChange"
/>
```

### 选择全部人员（包括已出所）
```vue
<personnel-selector
  v-model="jgrybm"
  personnel-type="ALL"
  @change="handleChange"
/>
```

## 功能特性

1. **人员信息展示**：选中人员后显示详细的人员信息，包括照片、基本信息等
2. **案件信息展示**：可选择是否显示案件相关信息
3. **扫码支持**：支持扫码枪扫描条形码自动识别人员
4. **响应式设计**：适配不同屏幕尺寸
5. **数据验证**：自动处理空值和 null 字符串
6. **全局注册**：无需在每个组件中单独导入

## 测试页面

可以访问 `/demo/personnelSelectorTest.vue` 查看完整的使用示例和测试用例。

## 注意事项

1. 组件依赖 `sd-prison-select` 包，确保项目中已安装
2. 扫码功能需要硬件扫码枪支持
3. 人员数据来源于 `/acp-com/base/pm/prisoner/getPrisonerSelectCompomenOne` 接口
4. 组件已全局注册，直接使用 `<personnel-selector>` 标签即可，无需导入
