<template>
  <div class="test-page">
    <div class="page-header">
      <h1>人员选择组件 - 新布局展示</h1>
      <p>展示优化后的div布局，更美观、更符合用户审美的人员信息展示</p>
    </div>

    <!-- 基础使用示例 -->
    <div class="test-section">
      <h2>基础使用（完整信息展示）</h2>
      <personnel-selector
        v-model="testData.jgrybm"
        title="被监管人员"
        placeholder="点击选择在押人员"
        :show-case-info="true"
        @change="handleChange"
      />
    </div>

    <!-- 仅基本信息示例 -->
    <div class="test-section">
      <h2>仅显示基本信息</h2>
      <personnel-selector
        v-model="testData2.jgrybm"
        title="选择人员（仅基本信息）"
        placeholder="点击选择人员"
        :show-case-info="false"
        @change="handleChange2"
      />
    </div>

    <!-- 禁用扫码功能示例 -->
    <div class="test-section">
      <h2>禁用扫码功能</h2>
      <personnel-selector
        v-model="testData3.jgrybm"
        title="人员选择（无扫码）"
        placeholder="点击选择人员"
        :enable-scan="false"
        :show-scan-tip="false"
        @change="handleChange3"
      />
    </div>

    <!-- 详情模式示例 -->
    <div class="test-section">
      <h2>详情模式（仅展示）</h2>
      <div class="detail-mode-controls">
        <Button @click="setDetailPersonnel" type="primary" size="small">设置测试人员</Button>
        <Button @click="clearDetailPersonnel" type="warning" size="small">清空人员</Button>
        <Input
          v-model="detailPersonnelCode"
          placeholder="输入人员编码测试"
          style="width: 200px; margin-left: 10px;"
        />
      </div>
      <personnel-selector
        v-model="detailPersonnelCode"
        mode="detail"
        title="人员详情信息"
        :show-case-info="true"
        @change="handleDetailChange"
      />
    </div>

    <!-- 显示测试结果 -->
    <div class="test-section" v-if="lastSelectedPersonnel">
      <h2>最后选择的人员信息</h2>
      <div class="test-result">
        <div class="result-item">
          <span class="result-label">人员编码:</span>
          <span class="result-value">{{ lastSelectedPersonnel.jgrybm || '未选择' }}</span>
        </div>

        <div v-if="selectedPersonnel" class="personnel-detail">
          <h4>人员信息:</h4>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">姓名:</span>
              <span class="info-value">{{ formatValue(selectedPersonnel.xm) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">房间:</span>
              <span class="info-value">{{ formatValue(selectedPersonnel.roomName) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">证件号码:</span>
              <span class="info-value">{{ formatValue(selectedPersonnel.zjhm) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">出生日期:</span>
              <span class="info-value">{{ formatValue(selectedPersonnel.csrq) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">籍贯:</span>
              <span class="info-value">{{ formatValue(selectedPersonnel.jgName) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">民族:</span>
              <span class="info-value">{{ formatValue(selectedPersonnel.mzName) }}</span>
            </div>
          </div>

          <div v-if="selectedPersonnel.xszm || selectedPersonnel.ajbh" class="case-detail">
            <h4>案件信息:</h4>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">涉嫌罪名:</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.xszm) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">案件编号:</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.ajbh) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">诉讼环节:</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.sshjName) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">入所时间:</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.rssj) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">关押期限:</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.gyqx) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">办案单位:</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.basj) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 测试按钮 -->
      <div class="test-buttons">
        <Button @click="clearSelection" type="warning">清空选择</Button>
        <Button @click="setTestData" type="primary">设置测试数据</Button>
        <Button @click="toggleScanTip" type="default">
          {{ showScanTip ? '隐藏' : '显示' }}扫码提示
        </Button>
      </div>
    </div>

    <!-- 扫码测试区域 -->
    <div class="test-section">
      <h2>扫码测试</h2>
      <p>在下面的输入框中输入人员编码模拟扫码枪输入：</p>
      <div class="scan-test-area">
        <Input
          v-model="scanTestCode"
          placeholder="输入人员编码后按回车模拟扫码"
          @on-enter="simulateScan"
          style="width: 300px;"
        />
        <Button @click="simulateScan" type="primary" style="margin-left: 10px;">
          模拟扫码
        </Button>
      </div>
    </div>

    <!-- 统一宽度测试 -->
    <div class="test-section">
      <h2>统一宽度测试</h2>
      <p>测试不同长度内容的卡片宽度统一效果：</p>

      <div class="width-test-container">
        <div class="width-test-item">
          <h4>短证件号码测试</h4>
          <personnel-selector
            v-model="shortIdTest"
            title="短证件号码人员"
            :show-case-info="true"
            @change="handleShortIdChange"
          />
        </div>

        <div class="width-test-item">
          <h4>长证件号码测试</h4>
          <personnel-selector
            v-model="longIdTest"
            title="长证件号码人员"
            :show-case-info="true"
            @change="handleLongIdChange"
          />
        </div>
      </div>

      <div class="test-buttons">
        <Button @click="setShortIdData" type="primary">设置短证件号码数据</Button>
        <Button @click="setLongIdData" type="success">设置长证件号码数据</Button>
        <Button @click="clearWidthTest" type="warning">清空测试</Button>
      </div>
    </div>

    <!-- 智能扫码测试 -->
    <div class="test-section">
      <h2>智能扫码功能测试</h2>
      <div class="scan-test-info">
        <p><strong>测试原理：</strong>基于输入时间间隔和回车键的智能判断</p>
        <p><strong>时间阈值：</strong>{{ timeThreshold }}ms（扫码枪 < 50ms，人工输入 > 50ms）</p>
        <p><strong>测试方法：</strong>在下面的表单中正常输入，不会触发扫码；使用模拟器测试扫码功能</p>
      </div>

      <!-- 表单输入测试 -->
      <div class="form-test-area">
        <h3>表单输入测试（不会触发扫码）</h3>
        <Form :model="formTestData" :label-width="100">
          <Row :gutter="16">
            <Col span="12">
              <FormItem label="数字输入">
                <Input
                  v-model="formTestData.number"
                  placeholder="输入数字：123456789"
                  @on-change="handleFormInput('数字', $event)"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="文本输入">
                <Input
                  v-model="formTestData.text"
                  placeholder="输入文本：ABCDEFG"
                  @on-change="handleFormInput('文本', $event)"
                />
              </FormItem>
            </Col>
          </Row>
        </Form>
      </div>

      <!-- 扫码模拟器 -->
      <div class="scan-simulator">
        <h3>扫码模拟器</h3>
        <div class="simulator-controls">
          <Input
            v-model="mockScanContent"
            placeholder="bar:USER123456"
            style="width: 300px; margin-right: 12px;"
          />
          <Button @click="simulateFastScan" type="primary">模拟扫码枪（快速）</Button>
          <Button @click="simulateSlowInput" type="default">模拟人工输入（慢速）</Button>
        </div>

        <div class="simulator-settings">
          <Row :gutter="16">
            <Col span="8">
              <FormItem label="时间阈值(ms)">
                <InputNumber v-model="timeThreshold" :min="10" :max="500" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="扫码标识符">
                <Input v-model="scanPrefix" placeholder="bar:" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="快速输入间隔(ms)">
                <InputNumber v-model="fastInputDelay" :min="1" :max="100" />
              </FormItem>
            </Col>
          </Row>
        </div>
      </div>

      <!-- 智能扫码组件测试 -->
      <div class="smart-scan-component">
        <h3>智能扫码组件</h3>
        <personnel-selector
          v-model="smartScanTest"
          title="智能扫码测试"
          :time-threshold="timeThreshold"
          :scan-prefix="scanPrefix"
          @change="handleSmartScanChange"
        />
      </div>
    </div>

    <!-- 布局特点说明 -->
    <div class="test-section">
      <h2>组件特点说明</h2>
      <div class="feature-list">
        <div class="feature-item">
          <h4>🎨 现代化卡片布局</h4>
          <p>使用div布局替代传统table表格，采用flexbox弹性布局，响应式更好</p>
        </div>
        <div class="feature-item">
          <h4>🖼️ 左右分栏设计</h4>
          <p>左侧头像区域采用渐变背景，右侧信息区域清晰分组展示</p>
        </div>
        <div class="feature-item">
          <h4>✨ 视觉层次优化</h4>
          <p>重要信息（监室名称、涉嫌罪名）使用渐变背景突出显示</p>
        </div>
        <div class="feature-item">
          <h4>📱 响应式适配</h4>
          <p>移动端自动切换为垂直布局，字体大小自适应</p>
        </div>
        <div class="feature-item">
          <h4>📏 统一宽度设计</h4>
          <p>所有卡片（人员卡片、案件卡片）自动保持统一宽度，整体更美观</p>
        </div>
        <div class="feature-item">
          <h4>🚀 智能扫码功能</h4>
          <p>基于时间间隔智能判断，完美解决表单输入冲突，用户无感知</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import personnelSelector from './index.vue'

export default {
  name: 'PersonnelSelectorTest',
  components: {
    personnelSelector
  },
  data() {
    return {
      testData: {
        jgrybm: ''
      },
      testData2: {
        jgrybm: ''
      },
      testData3: {
        jgrybm: ''
      },
      selectedPersonnel: null,
      lastSelectedPersonnel: null,
      scanTestCode: '',
      showScanTip: true,
      // 统一宽度测试数据
      shortIdTest: '',
      longIdTest: '',
      // 智能扫码测试数据
      formTestData: {
        number: '',
        text: ''
      },
      mockScanContent: 'bar:USER123456',
      timeThreshold: 50,
      scanPrefix: 'bar:',
      fastInputDelay: 10,
      smartScanTest: '',
      // 详情模式测试数据
      detailPersonnelCode: ''
    }
  },
  methods: {
    handleChange(personnelData, jgrybm) {
      this.selectedPersonnel = personnelData
      this.lastSelectedPersonnel = personnelData
      console.log('人员选择变化:', personnelData, jgrybm)
      this.$Message.success(`已选择人员: ${personnelData.xm || '未知'}`)
    },

    handleChange2(personnelData, jgrybm) {
      this.lastSelectedPersonnel = personnelData
      console.log('人员选择变化2:', personnelData, jgrybm)
      this.$Message.success(`已选择人员: ${personnelData.xm || '未知'}`)
    },

    handleChange3(personnelData, jgrybm) {
      this.lastSelectedPersonnel = personnelData
      console.log('人员选择变化3:', personnelData, jgrybm)
      this.$Message.success(`已选择人员: ${personnelData.xm || '未知'}`)
    },

    // 统一宽度测试方法
    handleShortIdChange(personnelData, jgrybm) {
      console.log('短证件号码人员选择:', personnelData, jgrybm)
      this.$Message.success(`短证件号码人员: ${personnelData.xm || '未知'}`)
    },

    handleLongIdChange(personnelData, jgrybm) {
      console.log('长证件号码人员选择:', personnelData, jgrybm)
      this.$Message.success(`长证件号码人员: ${personnelData.xm || '未知'}`)
    },

    setShortIdData() {
      this.shortIdTest = 'SHORT_ID_123456'
      this.$Message.info('已设置短证件号码测试数据')
    },

    setLongIdData() {
      this.longIdTest = 'VERY_LONG_ID_NUMBER_123456789012345678'
      this.$Message.info('已设置长证件号码测试数据')
    },

    clearWidthTest() {
      this.shortIdTest = ''
      this.longIdTest = ''
      this.$Message.info('已清空宽度测试数据')
    },

    // 智能扫码测试方法
    handleFormInput(type, value) {
      console.log(`表单${type}输入:`, value)
      this.$Message.info(`表单${type}输入: ${value}`)
    },

    handleSmartScanChange(personnelData, jgrybm) {
      console.log('智能扫码识别:', personnelData, jgrybm)
      this.$Message.success(`智能扫码成功: ${personnelData.xm || '未知'} (${jgrybm})`)
    },

    // 模拟扫码枪的快速输入
    simulateFastScan() {
      const content = this.mockScanContent.trim()
      if (!content) {
        this.$Message.warning('请输入扫码内容')
        return
      }

      this.$Message.info(`开始模拟扫码枪快速输入: ${content}`)

      // 模拟快速连续按键
      let index = 0
      const inputChar = () => {
        if (index < content.length) {
          // 创建键盘事件
          const event = new KeyboardEvent('keydown', {
            key: content[index],
            bubbles: true,
            cancelable: true
          })
          document.dispatchEvent(event)
          index++
          setTimeout(inputChar, this.fastInputDelay) // 快速输入间隔
        } else {
          // 输入完成后发送回车键
          setTimeout(() => {
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              bubbles: true,
              cancelable: true
            })
            document.dispatchEvent(enterEvent)
          }, this.fastInputDelay)
        }
      }

      inputChar()
    },

    // 模拟人工的慢速输入
    simulateSlowInput() {
      const content = this.mockScanContent.trim()
      if (!content) {
        this.$Message.warning('请输入扫码内容')
        return
      }

      this.$Message.info(`开始模拟人工慢速输入: ${content}`)

      // 模拟慢速输入
      let index = 0
      const inputChar = () => {
        if (index < content.length) {
          const event = new KeyboardEvent('keydown', {
            key: content[index],
            bubbles: true,
            cancelable: true
          })
          document.dispatchEvent(event)
          index++
          setTimeout(inputChar, 200) // 慢速输入间隔200ms
        } else {
          // 输入完成后发送回车键
          setTimeout(() => {
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              bubbles: true,
              cancelable: true
            })
            document.dispatchEvent(enterEvent)
          }, 200)
        }
      }

      inputChar()
    },
    
    clearSelection() {
      this.testData.jgrybm = ''
      this.testData2.jgrybm = ''
      this.testData3.jgrybm = ''
      this.selectedPersonnel = null
      this.lastSelectedPersonnel = null
      this.$Message.info('已清空选择')
    },

    refreshPage() {
      location.reload()
    },
    
    setTestData() {
      // 设置一个测试用的人员编码
      this.testData.jgrybm = 'TEST_PERSONNEL_001'
      this.$Message.info('已设置测试数据')
    },
    
    simulateScan() {
      if (this.scanTestCode.trim()) {
        this.testData.jgrybm = this.scanTestCode.trim()
        this.scanTestCode = ''
        this.$Message.success('模拟扫码成功')
      } else {
        this.$Message.warning('请输入人员编码')
      }
    },
    
    toggleScanTip() {
      this.showScanTip = !this.showScanTip
    },

    // 详情模式相关方法
    handleDetailChange(personnelData, jgrybm) {
      console.log('详情模式加载的人员:', personnelData, jgrybm)
      this.$Message.success(`详情模式加载人员: ${personnelData.xm || '未知'}`)
    },

    setDetailPersonnel() {
      this.detailPersonnelCode = 'TEST_DETAIL_001'
      this.$Message.info('已设置详情模式测试人员')
    },

    clearDetailPersonnel() {
      this.detailPersonnelCode = ''
      this.$Message.info('已清空详情模式人员')
    },

    /**
     * 检测可能的硬件加速支持
     */
    detectHardwareAcceleration() {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

      if (!gl) return { supported: false, reason: 'WebGL not available' };

      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      if (debugInfo) {
          const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
          return {
              supported: true,
              renderer: renderer,
              // 检测是否包含硬件解码相关关键词
              likelyHardwareDecoding: /nvidia|amd|intel|apple|qualcomm/i.test(renderer)
          };
      }

      return { supported: false, reason: 'Debug info not available' };
    },

    /**
     * 实际测试H.265视频播放（推荐方法）
     * @param {string} testVideoUrl H.265测试视频URL
     * @returns {Promise<boolean>} 是否能成功播放
     */
    async testH265Playback(testVideoUrl) {
      return new Promise((resolve) => {
          const video = document.createElement('video');
          video.muted = true; // 避免自动播放策略问题
          video.preload = 'metadata';

          const timeout = setTimeout(() => {
              cleanup();
              resolve(false);
          }, 10000); // 10秒超时

          const cleanup = () => {
              clearTimeout(timeout);
              video.removeEventListener('loadedmetadata', onSuccess);
              video.removeEventListener('error', onError);
              video.src = '';
          };

          const onSuccess = () => {
              cleanup();
              resolve(true);
          };

          const onError = () => {
              cleanup();
              resolve(false);
          };

          video.addEventListener('loadedmetadata', onSuccess);
          video.addEventListener('error', onError);
          video.src = testVideoUrl;
      });
    },

    /**
     * 简化的H.265支持检测（兼容原方法）
     */
    isH265Supported() {
      return this.checkH265Support().supported;
    },

    /**
     * 完整的H.265支持测试
     */
    async performH265Test() {
      console.log('=== H.265/HEVC 支持检测 ===');

      // 1. 基础MIME类型检测
      const supportInfo = this.checkH265Support();
      console.log('基础支持检测:', supportInfo);

      // 2. 如果有测试视频URL，进行实际播放测试
      const testVideoUrl = 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4'; // 替换为实际的H.265测试视频

      if (supportInfo.supported) {
          console.log('浏览器声称支持H.265，进行实际播放测试...');
          try {
              const canActuallyPlay = await this.testH265Playback(testVideoUrl);
              console.log('实际播放测试结果:', canActuallyPlay);
              return {
                  mimeSupport: true,
                  actualPlayback: canActuallyPlay,
                  recommendation: canActuallyPlay ? '完全支持H.265' : '声称支持但实际播放可能有问题'
              };
          } catch (error) {
              console.error('播放测试出错:', error);
              return {
                  mimeSupport: true,
                  actualPlayback: false,
                  recommendation: '声称支持但测试失败'
              };
          }
      } else {
          console.log('浏览器不支持H.265');
          return {
              mimeSupport: false,
              actualPlayback: false,
              recommendation: '不支持H.265，建议使用H.264'
          };
      }
    },

    /**
     * 格式化显示值，处理 null 字符串
     */
    formatValue(value) {
      if (!value || value === 'null' || value === null || value === undefined) {
        return '-'
      }
      return value
    }
  }
}
</script>

<style lang="less">
/* 全局样式覆盖，确保测试页面可以滚动 */
html, body {
  overflow: auto !important;
  height: auto !important;
}

#app {
  overflow: auto !important;
  height: auto !important;
}
</style>

<style lang="less" scoped>
.test-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    text-align: center;
    margin-bottom: 32px;
    padding: 24px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #1c4e80;
      margin: 0 0 12px 0;
    }

    p {
      font-size: 14px;
      color: #666;
      margin: 0;
      line-height: 1.6;
    }
  }

  .test-section {
    margin-bottom: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      font-size: 18px;
      font-weight: 600;
      color: #1c4e80;
      margin: 0 0 20px 0;
      padding-bottom: 10px;
      border-bottom: 1px solid #e8e8e8;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        bottom: -1px;
        width: 40px;
        height: 1px;
        background: #1c4e80;
      }
    }
  }
  
  .test-result {
    margin-top: 30px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 6px;

    h3, h4 {
      color: #333;
      margin-bottom: 15px;
    }

    .result-item {
      margin-bottom: 15px;
      padding: 10px;
      background: #fff;
      border-radius: 4px;
      border-left: 4px solid #2d8cf0;

      .result-label {
        color: #666;
        font-weight: 600;
        margin-right: 10px;
      }

      .result-value {
        color: #333;
        font-weight: 500;
      }
    }

    .personnel-detail {
      margin-top: 20px;

      h4 {
        color: #2d8cf0;
        border-bottom: 2px solid #e8e8e8;
        padding-bottom: 8px;
        margin-bottom: 15px;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 12px;
      margin-bottom: 20px;
    }

    .info-item {
      display: flex;
      padding: 8px 12px;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #e8e8e8;

      .info-label {
        color: #666;
        min-width: 80px;
        font-weight: 500;
        margin-right: 10px;
      }

      .info-value {
        color: #333;
        font-weight: 500;
        flex: 1;
      }
    }

    .case-detail {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #e8e8e8;

      h4 {
        color: #f56c6c;
      }
    }
  }
  
  .test-buttons {
    margin-top: 20px;
    
    .ivu-btn {
      margin-right: 10px;
    }
  }
  
  .scan-test-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #d9edf7;
    border-radius: 8px;
    background: #f4f8fa;
    
    h3 {
      color: #31708f;
      margin-bottom: 15px;
    }
    
    p {
      color: #666;
      margin-bottom: 15px;
    }
  }
  
  .usage-section {
    padding: 20px;
    margin-bottom: 50px; // 确保底部有足够空间
    border: 1px solid #dff0d8;
    border-radius: 8px;
    background: #f9fff9;
    
    h3 {
      color: #3c763d;
      margin-bottom: 15px;
    }
    
    ol {
      color: #666;
      
      li {
        margin-bottom: 8px;
        line-height: 1.5;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-page {
    padding: 10px;

    .test-result {
      padding: 15px;

      .info-grid {
        grid-template-columns: 1fr;
        gap: 8px;
      }

      .info-item {
        flex-direction: column;

        .info-label {
          min-width: auto;
          margin-bottom: 4px;
          font-size: 12px;
        }

        .info-value {
          font-size: 14px;
        }
      }

      .result-item {
        padding: 8px;

        .result-label, .result-value {
          font-size: 14px;
        }
      }
    }

    .scan-test-section {
      padding: 15px;

      .ivu-input {
        width: 100% !important;
        margin-bottom: 10px;
      }

      .ivu-btn {
        width: 100%;
        margin-left: 0 !important;
      }
    }
  }
}

/* 新增样式 */
.test-page {
  .width-test-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 24px;
    margin: 20px 0;

    .width-test-item {
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      h4 {
        font-size: 14px;
        font-weight: 600;
        color: #495057;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #dee2e6;
      }
    }
  }

  // 智能扫码测试样式
  .scan-test-info {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;

    p {
      margin: 0 0 8px 0;
      color: #0369a1;
      font-size: 14px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .form-test-area {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;

    h3 {
      color: #374151;
      margin-bottom: 12px;
      font-size: 15px;
    }
  }

  .scan-simulator {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;

    h3 {
      color: #374151;
      margin-bottom: 12px;
      font-size: 15px;
    }

    .simulator-controls {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;
    }

    .simulator-settings {
      h3 {
        font-size: 14px;
        margin-bottom: 8px;
      }
    }
  }

  .smart-scan-component {
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 16px;

    h3 {
      color: #374151;
      margin-bottom: 12px;
      font-size: 15px;
    }
  }

  .feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    margin-top: 16px;

    .feature-item {
      padding: 16px;
      background: #fafafa;
      border-radius: 4px;
      border-left: 3px solid #1c4e80;
      border: 1px solid #e8e8e8;

      h4 {
        font-size: 15px;
        font-weight: 600;
        color: #1c4e80;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 13px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .scan-test-area {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 16px;
    flex-wrap: wrap;

    .ivu-input {
      border-radius: 6px;
      transition: all 0.3s ease;

      &:focus {
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .ivu-btn {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }

    // 详情模式控制区域样式
    .detail-mode-controls {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e8e8e8;
    }
  }

  .test-buttons {
    .ivu-btn {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }
}
</style>
