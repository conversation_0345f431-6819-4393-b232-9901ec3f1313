<template>
  <div class="personnel-selector-example">
    <h2>人员选择组件使用示例</h2>
    
    <!-- 基础用法 -->
    <div class="example-section">
      <h3>基础用法</h3>
      <personnel-selector 
        v-model="formData.jgrybm" 
        @change="handlePersonnelChange" 
      />
      <p>当前选择的人员编码: {{ formData.jgrybm }}</p>
    </div>

    <!-- 自定义配置 -->
    <div class="example-section">
      <h3>自定义配置</h3>
      <personnel-selector 
        v-model="formData2.jgrybm"
        title="选择被监管人员"
        placeholder="点击选择或扫码识别人员"
        personnel-type="ZS"
        :show-case-info="true"
        :enable-scan="true"
        :show-scan-tip="true"
        @change="handlePersonnelChange2"
      />
      <p>当前选择的人员编码: {{ formData2.jgrybm }}</p>
    </div>

    <!-- 不显示案件信息 -->
    <div class="example-section">
      <h3>不显示案件信息</h3>
      <personnel-selector 
        v-model="formData3.jgrybm"
        title="选择人员"
        :show-case-info="false"
        @change="handlePersonnelChange3"
      />
      <p>当前选择的人员编码: {{ formData3.jgrybm }}</p>
    </div>

    <!-- 禁用扫码功能 -->
    <div class="example-section">
      <h3>禁用扫码功能</h3>
      <personnel-selector 
        v-model="formData4.jgrybm"
        title="选择人员"
        :enable-scan="false"
        :show-scan-tip="false"
        @change="handlePersonnelChange4"
      />
      <p>当前选择的人员编码: {{ formData4.jgrybm }}</p>
    </div>

    <!-- 人员信息展示 -->
    <div class="example-section" v-if="selectedPersonnelInfo">
      <h3>选择的人员信息</h3>
      <pre>{{ JSON.stringify(selectedPersonnelInfo, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import personnelSelector from './index.vue'

export default {
  name: 'PersonnelSelectorExample',
  components: {
    personnelSelector
  },
  data() {
    return {
      formData: {
        jgrybm: '',
        prison: {}
      },
      formData2: {
        jgrybm: '',
        prison: {}
      },
      formData3: {
        jgrybm: '',
        prison: {}
      },
      formData4: {
        jgrybm: '',
        prison: {}
      },
      selectedPersonnelInfo: null
    }
  },
  methods: {
    handlePersonnelChange(personnelData, jgrybm) {
      this.formData.prison = personnelData
      this.selectedPersonnelInfo = personnelData
      console.log('基础用法 - 选择的人员:', personnelData)
    },
    
    handlePersonnelChange2(personnelData, jgrybm) {
      this.formData2.prison = personnelData
      console.log('自定义配置 - 选择的人员:', personnelData)
    },
    
    handlePersonnelChange3(personnelData, jgrybm) {
      this.formData3.prison = personnelData
      console.log('不显示案件信息 - 选择的人员:', personnelData)
    },
    
    handlePersonnelChange4(personnelData, jgrybm) {
      this.formData4.prison = personnelData
      console.log('禁用扫码功能 - 选择的人员:', personnelData)
    }
  }
}
</script>

<style lang="less" scoped>
.personnel-selector-example {
  padding: 20px;
  
  h2 {
    color: #333;
    margin-bottom: 20px;
  }
  
  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    
    h3 {
      color: #666;
      margin-bottom: 16px;
      font-size: 16px;
    }
    
    p {
      margin-top: 16px;
      color: #999;
      font-size: 14px;
    }
    
    pre {
      background: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      overflow-x: auto;
    }
  }
}
</style>
