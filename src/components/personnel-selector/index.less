// 人员选择组件样式 - 公安风格设计
.personnel-selector {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  position: relative;
  display: inline-block;
  max-width: 100%;

  // 标题区域
  .personnel-header {
    margin-bottom: 16px;

    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      h4 {
        color: #1c4e80;
        position: relative;
        padding-left: 12px;
        margin: 0;
        font-size: 14px;
        font-weight: 600;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: #1c4e80;
          border-radius: 1px;
        }
      }

      .reselect-btn {
        margin-left: auto;
        padding: 0 15px;
        flex-shrink: 0;
      }
    }
  }

  // 选择区域样式
  .personnel-select-area {
    border: 2px dashed #bfbfbf;
    height: 160px;
    border-radius: 4px;
    background: #fafafa;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1c4e80;
      background: #f0f5ff;

      .personnel-select-icon {
        background: #1c4e80;

        .ivu-icon {
          color: #fff;
        }
      }

      .personnel-select-text {
        color: #1c4e80;
      }
    }

    .personnel-select-icon {
      width: 56px;
      height: 56px;
      background: #e8f4fd;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;
      transition: all 0.3s ease;

      .ivu-icon {
        font-size: 24px;
        color: #1c4e80;
        transition: color 0.3s ease;
      }
    }

    .personnel-select-text {
      color: #666;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
    }
  }

  // 统一宽度的卡片容器
  .unified-cards-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;

    // 使用CSS变量来统一管理卡片宽度
    --card-width: auto;

    // 当容器内容确定后，动态计算最适合的宽度
    min-width: fit-content;

    // 所有卡片都使用统一的宽度
    .personnel-card,
    .case-card {
      width: 100%;
      min-width: 370px; // 设置一个合理的最小宽度
      max-width: 100%;
    }
  }

  // 人员信息卡片
  .personnel-card {
    display: flex;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    // 头像区域
    .avatar-section {
      flex: 0 0 140px;
      background: #f5f5f5;
      border-right: 1px solid #d9d9d9;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px 12px;

      .avatar-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;

        .personnel-avatar {
          width: 100px;
          height: 120px;
          object-fit: cover;
          border-radius: 4px;
          border: 2px solid #d9d9d9;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .personnel-name {
          font-size: 14px;
          font-weight: 600;
          color: #1c4e80;
          background: #fff;
          padding: 4px 8px;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          min-width: 70px;
          text-align: center;
        }
      }
    }

    // 信息区域
    .info-section {
      flex: 1;
      padding: 14px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      // min-width: 280px;

      .info-item {
        background: #fafafa;
        border-radius: 4px;
        padding: 8px 12px;
        border: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        transition: background-color 0.2s ease;

        &:hover {
          background: #f0f5ff;
        }

        &.highlight-item {
          background: #f8f9fa;
          border-color: #d0d7de;

          &:hover {
            background: #e6f3ff;
          }
        }

        .info-label {
          font-size: 12px;
          font-weight: 500;
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .info-value {
          font-size: 12px;
          font-weight: 500;
          color: #333;
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &.highlight-value {
            color: #1c4e80;
            font-weight: 600;
            font-size: 13px;
          }
        }
      }
    }
  }

  // 案件信息区域
  .case-info-section {
    // 移除上边距和边框，因为现在在统一容器中
    .section-title h4 {
      color: #1c4e80;
      position: relative;
      padding-left: 12px;
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 16px;
        background: #1c4e80;
        border-radius: 1px;
      }
    }
  }

  // 案件信息卡片
  .case-card {
    display: block;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 14px;

    .case-item {
      background: #fafafa;
      border-radius: 4px;
      padding: 8px 12px;
      border: 1px solid #e8e8e8;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      transition: background-color 0.2s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        background: #f0f5ff;
      }

      &.highlight-item {
        background: #f8f9fa;
        border-color: #d0d7de;

        &:hover {
          background: #e6f3ff;
        }
      }

      .case-label {
        font-size: 12px;
        font-weight: 500;
        color: #666;
        width: 80px;
        flex-shrink: 0;
      }

      .case-value {
        font-size: 12px;
        font-weight: 500;
        color: #333;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.highlight-value {
          color: #1c4e80;
          font-weight: 600;
          font-size: 13px;
        }
      }
    }
  }

  // 扫码提示区域
  .scan-tip {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 12px;
    padding: 8px 12px;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 4px;
    color: #0369a1;
    font-size: 12px;

    .ivu-icon {
      color: #0369a1;
    }
  }

  // 隐藏的扫码输入框
  .scan-input {
    position: absolute;
    left: -9999px;
    opacity: 0;
    pointer-events: none;
  }

  // 移动端响应式样式
  @media (max-width: 768px) {
    .unified-cards-container {
      gap: 12px;

      .personnel-card,
      .case-card {
        min-width: auto;
        width: 100%;
      }
    }

    .personnel-card {
      flex-direction: column;

      .avatar-section {
        flex: none;
        padding: 16px;
        border-right: none;
        border-bottom: 1px solid #d9d9d9;

        .avatar-container {
          gap: 10px;

          .personnel-avatar {
            width: 90px;
            height: 110px;
          }

          .personnel-name {
            font-size: 14px;
            padding: 4px 8px;
            min-width: 60px;
          }
        }
      }

      .info-section {
        padding: 12px;
        gap: 6px;

        .info-item {
          padding: 6px 8px;

          .info-label {
            width: 70px;
            font-size: 11px;
          }

          .info-value {
            font-size: 11px;

            &.highlight-value {
              font-size: 12px;
            }
          }
        }
      }
    }

    .case-card {
      padding: 12px;

      .case-item {
        padding: 6px 8px;
        margin-bottom: 6px;

        .case-label {
          width: 70px;
          font-size: 11px;
        }

        .case-value {
          font-size: 11px;

          &.highlight-value {
            font-size: 12px;
          }
        }
      }
    }

    .case-info-section {
      .section-title h4 {
        font-size: 13px;
        margin-bottom: 8px;
      }
    }

    .scan-tip {
      margin-top: 8px;
      padding: 6px 10px;
      font-size: 11px;
    }
  }
}