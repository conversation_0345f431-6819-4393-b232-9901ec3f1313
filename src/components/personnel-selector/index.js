/**
 * PersonnelSelector 人员选择组件
 * 全局组件注册插件
 */

import PersonnelSelector from './index.vue'

// Vue插件安装方法
function install(Vue, options = {}) {
  // 检查是否已经安装
  if (install.installed) {
    return
  }
  install.installed = true

  // 注册全局组件
  Vue.component('PersonnelSelector', PersonnelSelector)

  // 全局混入人员选择工具方法
  Vue.mixin({
    methods: {
      // 格式化人员信息显示
      $formatPersonnelInfo(personnel, fields = ['xm', 'roomName']) {
        if (!personnel) return {}
        
        const formatted = {}
        fields.forEach(field => {
          formatted[field] = personnel[field] || '-'
        })
        return formatted
      },

      // 检查人员是否有效
      $isValidPersonnel(personnel) {
        return personnel && personnel.xm && personnel.jgrybm
      },

      // 获取人员显示名称
      $getPersonnelDisplayName(personnel) {
        if (!personnel) return '-'
        return personnel.xm || personnel.name || '-'
      },

      // 获取人员头像URL
      $getPersonnelAvatar(personnel, defaultImg = '') {
        if (!personnel) return defaultImg
        return personnel.frontPhoto ? (this.http || '') + personnel.frontPhoto : defaultImg
      }
    }
  })

  // 处理插件选项
  if (options.defaultConfig) {
    Vue.prototype.$defaultPersonnelConfig = options.defaultConfig
  }

  // 设置默认配置
  Vue.prototype.$personnelSelectorDefaults = {
    title: '人员选择',
    placeholder: '点击选择人员',
    showAvatar: true,
    showRoomName: true,
    ...options
  }
}

// 兼容旧版本的安装方法
PersonnelSelector.install = install

// 导出安装函数
export { install }

// 默认导出插件对象
const PersonnelSelectorPlugin = {
  install,
  PersonnelSelector
}

// 如果在浏览器环境中且Vue可用，自动安装
if (typeof window !== 'undefined' && window.Vue) {
  window.Vue.use(PersonnelSelectorPlugin)
}

// 导出插件对象
export { PersonnelSelectorPlugin }

// 默认导出组件（保持向后兼容）
export default PersonnelSelector
