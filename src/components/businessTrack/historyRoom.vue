<template>
   <div>
    <ul  style="background: #F5F7FA;border-radius: 6px;padding: 16px  16px ;height: 100%;overflow-y: auto;" >
                  <p class="detail-title">羁押监所</p>
                  <li
                    :class="['track-item-room pointer',item.jgrybm==jgrybm?'track-item-room-active':'' ]"
                    v-for="(item, idx2) in jsList"
                    :key="idx2"
                    @click="lookInfo(item)"
                  >
                    <p class="head">
                      <label>{{ item.org_name }}</label>
                    </p>

                    <div class="content-box">
                      <div class="content-box-list" >
                          <b>关押期限：{{ item.gyqx }}</b>
                          <p>涉嫌罪名：{{ item.sxzm }}</p>
                          <p>jgrybm：{{ item.jgrybm }}</p>
                          <p style="padding-bottom:16px;">案件编号：{{ item.ajbh }}</p>
                      </div>
                    </div>
                  </li>
            </ul>
   </div>
</template>
<script>
  export default {
      props:{
        jgrybm:String,
      },
      data(){
        return{
          zjhm:this.$route.query.zjhm ? this.$route.query.zjhm : '',
          orgName:'',
          curOrg:'',
          spinShow:false,
          jsList:[],
        }
      },
      mounted(){
        if(this.jgrybm){
          this.getOrgData()
        }
      },
      methods:{
        getOrgData() {
          this.spinShow = true
          this.selectedOrg = []
          this.$store.dispatch('postRequest', {
            url: this.$path.get_query_grid,
            params: {
              condis:`[{"name":"zjhm","op":"=","value":"${this.zjhm}","valueType":"string"}]`,
              modelId: "yryd-rsjllb"
            },
          }).then((res) => {
            this.spinShow = false
            if (res.success) {
              this.jsList = res.rows.map(item => {
                return {
                  id: item.id,
                  jgrybm: item.jgrybm,
                  orgName: item.org_name,
                  org_name: item.org_name,
                  org_code: item.org_code,
                  gyqx: item.gyqx,
                  sxzm: item.sxzm,
                  ajbh: item.ajbh,
                };
              })
              this.curOrg=this.jsList[0].org_code
              this.orgName=this.jsList[0].org_name
              let jgrybm=this.jsList[0].jgrybm
              let id = this.jsList[0].id
              this.$emit('getTrast',this.curOrg,this.orgName, jgrybm, id)
            }
          });
          // let params={
          //   group:this.group,
          //   jgrybm:this.jgrybm
          // }
          // this.$store.dispatch('postRequest', { url: this.$path.getJgryTraceOrgList_getData ,params:params}).then(resp => {
          //   this.spinShow = false
          //   if(resp.success && resp.data && resp.data.length>0) {
          //     this.jsList=resp.data
          //     this.curOrg=this.jsList[0].org_code
          //     this.orgName=this.jsList[0].org_name
          //     let jgrybm=this.jsList[0].jgrybm
          //     this.$emit('getTrast',this.curOrg,this.orgName, jgrybm)
          //   }else{
          //     // this.jsList=resp.data
          //   }
          // })
        },
        lookInfo(item){
          this.curOrg=item.org_code
          this.orgName=item.org_name
          this.$emit('selectRoom',item)
        }
      }
  }
</script>
