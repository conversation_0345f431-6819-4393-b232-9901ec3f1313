<template>
  <div class="track-wrapper" style="margin: 0 16px">
    <template>
      <Spin v-if="loading" fix>
        <Icon type="ios-loading" size="50" class="spin-icon-load"></Icon>
        <div>正在加载数据...</div>
      </Spin>

                <!-- 分页器 :page-size-opts="pageSizeOpts"-->
      <div class="track-wrap">
      <ul class="track-box" style="height: 590px;padding-right: 16px;overflow: auto;">
        <li class="track-item prison" v-if="trackList.list.length != 0">
          <p class="head">
            <label>羁押记录--{{ total }}条</label>
          </p>
          <div class="content">羁押地点：{{ orgName }}</div>
        </li>
        <li
            class="track-item pointer"
            v-for="(item, idx2) in trackList.list"
            :key="idx2"
            @click="showDetail(item)"
            >
            <p class="head">
                <label>{{ item.busName }}</label>
                <span>{{ item.addTime }}</span>
            </p>
            <div class="content-box">
                <div class="content-box-list" v-if="item.content">
                <p
                    class="content"
                    v-for="(field,key,index) in JSON.parse(item.content)"
                    :key="index"
                >
                    <span class="title">{{ key }}</span>
                    <span>{{ field || "-" }}</span>
                </p>
                </div>
            </div>
        </li>
         <div class="tips" v-if="trackList.list.length == 0" style="width:410px;text-align: center;margin-top: 10%;">
         <img
          src="@/assets/images/noData.png"
          alt=""
        />
          <p style="line-height:40px;color: black;" >暂无数据</p>
        </div>
      </ul>
        <div class="pageWrap" style="margin: 10px 12% 16px;" v-if="trackList.list !=0">
            <Page :total="total" class="pageWrap" size="small" show-elevator show-sizer :page-size="page.pageSize" @on-prev="getNo" @on-next="getNo" :current="page.pageNo"  @on-change="getNo" @on-page-size-change="getSize" />
        </div>
    </div>
    </template>
   
  </div>
</template>

<script>
export default {
  props:{
    group:String,
    jgrybm:String,
    curOrgCode:String,
    trackParam:Object,
    orgName:String,
  },
  data() {
    return {
        page:{
            pageSize:10,
            pageNo:1
        },
      total:0,
      trackList: {
          // detaiNum: 0,
          // detaiPlace: "88888888",
          total: 0,
          list: [],
      },
      loading: false,
    };
  },
  watch:{
     trackParam:{
        handler(n,o){
              this.getData()
        },deep:true,immediate:true
     }
  },
  mounted(){
    this.getData()
  },
  methods: {
    getNo(pageNo){
        //console.log(this.page,pageNo,'search')
        this.$set(this.page,'pageNo',pageNo)
        this.getData()
    },
    getSize(pageSize){
        //console.log(this.page,'search')
        this.$set(this.page,'pageSize',pageSize)
        this.getData()
    },
    showDetail(item){
       this.$emit('showDetail',item)
    },
    getData() {
      this.loading = true;
      let param={
         group:this.group,
         jgrybm:this.jgrybm,
         orgCode:this.curOrgCode
      }

      if(this.trackParam.happenStartTime && this.trackParam.happenStartTime.length>0){
          param.startTime=this.trackParam.happenStartTime[0]
          param.endTime=this.trackParam.happenStartTime[1]
      }
      Object.assign(param,this.page,this.trackParam)

      this.$store.dispatch('authPostRequest',{
        url: this.$path.busTrace_getData,
        params: param,
      }).then((res) => {
        if (res.success) {
           this.total=res.data.total
           this.$set(this.trackList, 'list', res.data.list)
           console.log('trackList',this.trackList)
        } else {
          this.$Message.error({ content: res.msg });
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    getBusinessKey(businessCode){
        this.$emit('getBusinessKey',businessCode)
    },
    search() {
      this.$set(this.page,'pageNo', 1)
      this.$set(this.page,'pageSize', 10)
    }
  },
};
</script>

<style lang="less" scoped>
.trackItem-box{
    height: 300px ;
    overflow: hidden;
}
.spin-icon-load {
  animation: ani-spin 1s linear infinite;
}

@keyframes ani-spin {
  from { transform: rotate(0deg); }
  50%  { transform: rotate(180deg); }
  to   { transform: rotate(360deg); }
}
</style>