<template>
    <Modal
        width="80%"
        class-name="progressModalWrap"
        v-model="progressModal_ZH"
        :footer-hide="true"
        :mask-closable="false"
        title="转换进度">
        <div class="conversionProgressBox">
            <div class="progressModal">
                <template v-if="uploadSuccessList.length < 1 ? uploadImgList.length < 1 ? true : false : false">
                    <p class="inTime uploadNoData">暂无转换数据</p>
                </template>
                <template v-else>
                    <div class="allUploadMsgBox">
                        <div class="uploadFileListBox">
                            <p><span class=" inTime">已上传材料</span>，共{{uploadLength}}份</p>
                            <Button @click="ReconvertList" type="success" >批量转换</Button>
                            <!-- <Button @click="reZj"   type="primary" style="margin-left: 20px">重新组卷</Button> -->

                            <Dropdown style="margin-left: 20px">
                                <Button type="info">
                                    排序<Icon type="ios-arrow-down"></Icon>
                                </Button>
                                <DropdownMenu slot="list">
                                    <DropdownItem @click.native="getConvertPdfList({timeOrder: 'asc' })">时间顺序</DropdownItem>
                                    <DropdownItem @click.native="getConvertPdfList({timeOrder: 'desc'}  )">时间倒序</DropdownItem>
                                    <DropdownItem @click.native="getConvertPdfList({nameOrder: 'asc'})">名称顺序A-Z</DropdownItem>
                                    <DropdownItem @click.native="getConvertPdfList({nameOrder: 'desc'})">名称倒序Z-A</DropdownItem>
                                </DropdownMenu>
                            </Dropdown>

                            <div class="uploadFileListsmallBox" style="margin-top: 20px;padding-right:0;height:80%;">  
                                <CheckboxGroup v-model="check_AllGroup" @on-change="check_AllGroupChange">
                                    <Collapse v-model="CollapseActiveId">
                                        <Panel :name="item.id"  v-for="(item,index) in uploadSuccessList" :key="index+'us'">
                                                {{item.catalogName}}
                                                <div slot="content">
                                                    <div v-for="(item2 ,index2) in item.pdfList" :key="item2.id + '--pdf'" class="uploadFileList">
                                                        <Checkbox  :label="item2.id" class="hideLabel" :disabled="userInfo.isAdmin ? false : (isConverting.length > 0 && isConverting.indexOf(item2.id) !== -1)"></Checkbox>
                                                        <p :title="item2.pdfName">{{index2+1}}、{{item2.pdfName}}</p>
                                                        <span style="font-size: 0.14rem;" :class="['statusName',item2.statusClass]">{{item2.statusName}}</span>
                                                        <Button type="primary" style="margin-left:0.1rem;" size="large" @click="ViewOriginalFile(item2)">查看原文件</Button>
                                                        <Button title="当且仅当pdf转换失败或转换的图片出现空白时使用" 
                                                        :disabled="userInfo.isAdmin ? false : (isConverting.length > 0 && 
                                                        isConverting.indexOf(item2.id) !== -1)" size="large" style="margin-left:0.1rem;" 
                                                        type="success" @click="Reconvert(item2.id,item2.batchId,item2.status)">{{item2.status=='6'?'继续转换':'重新转换'}}</Button>
                                                        <Button v-if="userInfo.isAdmin ? true : (item2.status == '0')" size="large" style="margin-left:0.1rem;" type="default" @click="Reconvert_cancel(item2.id)">取消转换</Button>
                                                    </div>
                                                </div>
                                        </Panel>
                                    </Collapse>   
                                </CheckboxGroup>
                            </div>
                            <!-- <div class="uploadFileListsmallBox" style="margin-top: 20px;padding-right:0;height:80%;">  
                                <CheckboxGroup v-model="check_AllGroup" @on-change="check_AllGroupChange">
                                    <div :name="item.id"  v-for="(item,index) in uploadSuccessList" :key="index+'us'">
                                        <div v-for="(item2 ,index2) in item.pdfList" :key="item2.id + '--pdf'" class="uploadFileList">
                                            <Checkbox  :label="item2.id" class="hideLabel" :disabled="userInfo.isAdmin ? false : (isConverting.length > 0 && isConverting.indexOf(item2.id) !== -1)"></Checkbox>
                                            <p :title="item2.pdfName">{{index2+1}}、{{item2.pdfName}}</p>
                                            <span :class="['statusName',item2.statusClass]">{{item2.statusName}}</span>
                                            <Button type="primary" style="margin-left:0.1rem;" size="large" @click="ViewOriginalFile(item2)">查看原文件</Button>
                                            <Button title="当且仅当pdf转换失败或转换的图片出现空白时使用" 
                                         
                                            :disabled="userInfo.isAdmin ? false : (isConverting.length > 0 && 
                                            isConverting.indexOf(item2.id) !== -1)" size="large" style="margin-left:0.1rem;" 
                                            type="success" @click="Reconvert(item2.id,item2.batchId)">{{item2.status == 6 ? '继续转换':'重新转换'}}</Button>
                                            <Button v-if="userInfo.isAdmin ? true : (item2.status == '0')" size="large" style="margin-left:0.1rem;" type="default" @click="Reconvert_cancel(item2.id)">取消转换</Button>
                                        </div>
                                    </div>
                                </CheckboxGroup>
                            </div> -->
                        </div>
                        <div class="percentValBox">
                            <p><span class=" inTime">转换进度</span>，转换耗时：<span >{{inTime2}}</span></p>
                            <div class="uploadFileListsmallBox" style="height:80%;">
                                <div v-for="(item,index) in PdfConvertRateList" :key="index+'pdf'">
                                    <p class="pdfFileName inTime" :title="item.pdfName">材料名称：{{item.pdfName}}</p>
                                    <div class="progressBox">
                                        <Progress  :percent="item.percentVal" :stroke-width="32" status="success" text-inside />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
            <div class="isFinishBtn">
                <Button size="large" type="primary" class="close_btn" @click="isFinishAction">关闭</Button>
            </div>
        </div>
    </Modal>
</template>
<script>
export default {
    name:'ConversionProgress',
    data() {
        return {
            orderRule: {},
            jgrybm:'',
            progressModal_ZH: false,
            check_AllGroup: [],
            CollapseActiveId: '',
            PdfConvertRateList: [],
            uploadSuccessList: [],
            uploadImgList: [],
            isConverting:[],
            uploadLength: 0,
            inTime2: '',
            allPdfList: [],
            uploadTimer: null,
            uploadTimer2: null,
            isFirstIntoPage: true,
            websocketInitSendTimes: 10,
        }
    },
    watch:{
        'progressModal_ZH':{
            handler(n,o){
              if(n){
                this.getConvertPdfList();
              }
            },deep:true,immediate:true
        }
    },
    created(){
        this.jgrybm = this.$route.query.jgrybm;
        var userInfo = localStorage.getItem('usreInfo');
        if(userInfo){
            this.userInfo = JSON.parse(userInfo);
        }
        this.getResServerUrl(this.jgrybm)

        
    },
    beforeDestroy(){
        clearInterval(this.uploadTimer)
        clearInterval(this.uploadTimer2)
    },
    methods:{
        ViewOriginalFile(item){ //查看原文件
            console.log(item,item.pdfUrl)
            let routeData = this.$router.resolve({
                path:'/dms/pdfViewer',
                query:{
                    pdfUrl:item.pdfUrl
                }
            })
            window.open(routeData.href, '_blank');
            // this.$router.push({
            //     path:'/dms/pdfViewer',
            //     query:{
            //         pdfUrl:item.pdfUrl
            //     }
            // })
        },
        check_AllGroupChange(arr){
            this.check_AllGroup = arr
        },
        getConvertPdfList(val){ // 获取上传列表记录
            if(val){
                this.check_AllGroup = []
            }
            var obj = val ? val : {}
            let params = {
                jgrybm: this.jgrybm,
                type: '0,1',
                materialSource: '1',
                ...obj
            }
            this.$Post(this.$path.GET_CONVERT_PDF_LIST,params).then(res=>{
                if(res.success){
                    this.uploadSuccessList = res.data
                    var count = 0
                    this.uploadSuccessList.forEach(item1=>{
                        item1.pdfList.forEach(item => {
                            count++
                            item.statusName = item.status == 0 ? "已上传未处理" : item.status == 1 ? "已下载处理中" : item.status == 2 ? 
                            "已拆分完成" : item.status == 5 ? "下载失败" : item.status == 4 ? "已取消" : item.status == 6 ? "已暂停" : "";
                            item.statusClass = item.status == 0 ? 'yscwcl' : item.status == 1 ? 'yxzclz' : item.status == 2 ? 'ycfwc' : item.status == 5 ? 'xzsb' : 'ycfwc';
                        })
                    })
                    this.uploadLength = count
                }
            });
        },
        ReconvertList() {
        // 批量重新转换
            if (this.check_AllGroup.length == 0) {
                this.$Message.warning("请先勾选要转换的文件");
                return;
            }
            var pdfList = []
            var arr = []
            this.uploadSuccessList.forEach(item=>{
                item.pdfList.forEach(item2=>{
                  pdfList.push(item2)
                })
            })
            console.log(this.uploadSuccessList,'this.uploadSuccessList')
            pdfList.forEach(item=>{
                this.check_AllGroup.forEach(item2=>{
                    if(item2==item.id&&item.status=='2'){
                        // 已拆分完成的才可以再次转换
                        arr.push(1)
                    }
                })
            })
            // console.log(pdfList,'pdfList')
            if(arr.length==0){
                // 都不是已拆分完成
                this.$Message.warning('选中的文件暂未拆分完成，暂不可转换')
                return
            }
            var check_AllGroup = [...this.check_AllGroup]
            if(!this.userInfo.isAdmin) {
                if(this.isConverting.length > 0){
                    var isConverting = [...this.isConverting]
                    isConverting.forEach(item => {
                        this.check_AllGroup.forEach((item1,index)=>{
                            if(item == item1) {
                                check_AllGroup.splice(check_AllGroup.indexOf(item1),1)
                            }
                        })
                    })
                }
                if(check_AllGroup.length == 0) {
                    this.$Message.warning('选中的文件都在转换中，暂不可转换')
                    return
                }
            }
            this.check_AllGroup = [...check_AllGroup]
            let params = {
                jgrybm : this.jgrybm,
                pdfIdList: JSON.stringify(check_AllGroup),
                materialSource: '1',
            }
            this.$Post(this.API.DmsConvertApi.BATCH_CONVERT_PDF,params).then(res=>{
                if(res.success){
                    this.getResServerUrl(this.jgrybm);
                    setTimeout(()=>{
                        this.getConvertPdfList()
                    },3000)
                }
            })
        },
        Reconvert(pdfId,batchId,status){ // 重新转换
            console.log(pdfId,batchId,status,'pdfId,batchId,status')
            if(status=='2'||status=='6'){
                let params = {
                    jgrybm: this.jgrybm,
                    pdfId: pdfId,
                    materialSource: '1',
                }
                this.$Post(this.$path.RE_CONVERT_PDF,params).then(res=>{
                    if(res.success){
                        this.getResServerUrl(this.jgrybm);
                        setTimeout(()=>{
                            this.getConvertPdfList()
                        },3000)
                    }
                })
            }
        },
        Reconvert_cancel(pdfId){ // 取消转换
            this.$Get(this.$path.PDF_cancelConvert,{pdfId:pdfId}).then(res=>{
                if(res.success){
                    this.$Message.success(res.msg)
                    setTimeout(()=>{
                        this.getConvertPdfList()
                    },3000)
                }else{
                    this.$Message.warning(res.msg)
                }
            })
        },
        getResServerUrl(jgrybm){
            this.$Get(this.$path.GET_CONVERT_IP_PORT).then(data=>{
                if (data.success) {
                    this.serverUrl = data.data;
                    this.websocketInitSendTimes = 10;
                    this.upload_zh_time();
                    // this.getConvertPdfList();
                    this.$emit('getData')
                    this.initWebSocket(jgrybm);
                }
            })
        },
        initWebSocket(jgrybm){  // type 1: 编目页面,  3: 卷宗导入页面
            // websocket 通用连接方法
            var urlHead = this.serverUrl?"ws://" + this.serverUrl:serverConfig.PDF_CONVERT_RATE;
            var usreInfo = JSON.parse(localStorage.getItem('usreInfo'));
            var sid = jgrybm + "-" + '1';
            var socketUrl = urlHead + "/webSocketConvert/" + sid + "?access_token=" + usreInfo.access_token;
            var _this = this;
            if ('WebSocket' in window) {
                if (socketUrl) {
                    var websocket = new WebSocket(socketUrl)
                    // 连接成功建立的回调方法
                    websocket.onopen = function(e) {
                        console.log('WebSocket连接成功')
                    }
                    // 连接发生错误的回调方法
                    websocket.onerror = function() {
                        console.log('WebSocket连接失败')
                    }
                    websocket.onclose = function (event) {
                        _this.websocketInitSendTimes --
                        console.log("WebSocket:已关闭");
                        clearTimeout(timer)
                        clearTimeout(connectTimer)
                        if(_this.websocketInitSendTimes > 0){
                            _this.initWebSocket(_this.jgrybm);
                        }else{
                            _this.websocketInitSendTimes = 10
                        }
                    };
                    
                    
                    _this.is_pdf_zh = true

                    var sendText = {
                        "source":"convertRate",
                        "jgrybm": jgrybm,
                        "type": '1'
                    }
                    var timer = setTimeout(()=>{
                        websocket.send(JSON.stringify(sendText))
                        clearTimeout(timer)
                    },1500)
                    var connectTimer = null, sendTimer = null
                    
                    websocket.onmessage = function(e) {
                        var res = JSON.parse(e.data)
                        console.log('获取数据转换',res,'')
                        if(res.refreshStatus=='1'){
                            _this.getConvertPdfList(_this.orderRule);
                        }
                        _this.PdfConvertRateList = res.data
                        if(res.data.length > 0){
                            _this.websocketInitSendTimes = 10
                            res.data.forEach(item=>{
                                if(item.pause == '1'){
                                    websocket.close();
                                }
                                if(item.convertCount == item.pageCount){
                                    if(_this.isConverting.length > 0){
                                        _this.isConverting.forEach((item2,index2)=>{
                                            if(item2 == item.pdfId){
                                                _this.isConverting.splice(index2,1)
                                            }
                                        })
                                    }
                                }else{
                                    _this.isConverting.push(item.pdfId)
                                }
                                item.percentVal = Number(parseInt((item.convertCount / item.pageCount)*100))
                            })
                        }else{
                            clearTimeout(sendTimer)
                            clearTimeout(connectTimer)
                            clearInterval(_this.uploadTimer)
                        }
                        if(res.refresh=='1'){
                            clearTimeout(connectTimer)
                            clearTimeout(sendTimer)
                            _this.$Message.success('PDF转换成功！')
                            _this.websocketInitSendTimes = 10
                            _this.isConverting = []
                            setTimeout(()=>{
                                clearInterval(_this.uploadTimer2)
                                _this.uploadTimer2 = null;
                            },3000)
                            clearInterval(_this.uploadTimer)
                            _this.uploadTimer2 = null
                            _this.is_pdf_zh = false
                            _this.uploadLoadingType = 2;
                            _this.isFinishAction();
                            _this.$parent.initFiles();
                            return
                        }else{
                            _this.websocketInitSendTimes --

                            if(_this.websocketInitSendTimes > 0){
                                sendTimer = setTimeout(()=>{
                                    clearTimeout(connectTimer)
                                    websocket.send(JSON.stringify(sendText))
                                    clearTimeout(sendTimer)
                                },3000)
                                if(!connectTimer){
                                    connectTimer = setTimeout(()=>{
                                        clearTimeout(connectTimer)
                                        websocket.close();
                                    },10000)
                                }
                            }else{
                                _this.websocketInitSendTimes = 10
                            }
                        }
                        if(_this.isFirstIntoPage){
                            _this.isFirstIntoPage = false;
                            _this.inTime2 = "00:00:00"
                        //    console.log('第一次进入页面，pdf全部已经转换完了,重置计时')
                        }
                        

                    }
                }
            } else {
                alert('当前浏览器 Not support websocket')
            }
        },
        upload_zh_time(){
            clearInterval(this.uploadTimer)
            this.uploadTimer = null
            var startTime = new Date().getTime();
            this.uploadTimer = setInterval(()=>{
                let nowTime = new Date().getTime();
                let time = nowTime - startTime;
                var h = parseInt(time/3600000),
                    m = parseInt((time - (h*3600000))/60000),
                    s = parseInt((time - (h*3600000) - (m*60000))/1000);
                    h = h < 10 ? '0'+h : h;
                    m = m < 10 ? '0'+m : m;
                    s = s < 10 ? '0'+s : s;
                this.inTime2 = h+':'+m+':'+s;
                // console.log(this.inTime2,'time')
            },1000)
        },
        isFinishAction(){
            this.progressModal_ZH = false;
        },
    },
}
</script>
<style lang="less" scoped>
    .uploadNoData{
        text-align: center;
        line-height: 0.8rem;
    }
    .isFinishBtn {
        display: flex;
        justify-content: center;
    }
    .isFinishBtn .close_btn {
        width: 0.7rem;
        height: 0.3rem;
        background-color: #ffffff;
        color: #087EFF;
        border: 1px solid #087EFF;
    }
    /deep/.isFinishBtn .ivu-btn {
        min-width:  0.7rem;
    }
</style>
<style>
    .fileCheckBox span:last-child {
        display: none;
    }
    .hideLabel span:nth-child(2) {
        display: none !important;
    }
</style>