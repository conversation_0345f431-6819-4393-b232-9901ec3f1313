<template>
    <div class="row file-romove">
        <div class="search_stuff">
            <div style="width:90%;position:relative">
                <Input v-model="stuffName"  placeholder="搜索材料" />
                <Icon type="ios-search" id="stuffSearch" @click="stuffSearch" color="#087EFF"/>
            </div>
            <div class="resetBtn" @click="resetBtn">重置</div>
        </div>
        <div class="top-panel">
            <div class="back-btn" @click="backOne" data-parent-id="545"><i class="icon-panel back-up-icon"></i>返回上一级</div>

            <div class="crumbs-panel">
                <p id="parentCatalog" style="display: flex">
                    <span v-for="(item,index) in parentCatalogData" :key="index" class="item-panel" :title="item.name">{{item.name}}<i v-show="index !== parentCatalogData.length -1">></i></span>
                </p>
            </div>
        </div>
        <div v-show="catalogSelectData.length < 1" class="content-panel">
            <i class="air_secrch"></i>
        </div>
        <div class="content-panel" v-show="catalogSelectData.length > 0">
            <ul id="catalogSelect">
                <template v-for="(item,index) in catalogSelectData">
                    <li :key="index" v-if="item.isParent" class="isParent" @click="parentNodeAction(item)">
                        <i class="icon-panel wjj-icon"></i>{{item.newName}}
                    </li>
                    <li :key="index"  v-else :class="seleteIndex == index ? 'isLast active' : 'isLast'" @click="lastNodeAction(item,index)">
                        <i class="icon-panel wj-icon"></i>{{item.newName}}
                    </li>
                </template>
            </ul>
        </div>
        <div class="bottom-panel">
            <div class="remove-btn select" @click="closeModal"> 关闭 </div>
            <div class="remove-btn select" @click="moveAction"> 移动 </div>
        </div>
    </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
export default {
    name:'MoveFiles',
    props:{
        jzTreeObj:{
            type:Object
        },
        seletedFiles:{
            type:Array
        },
        photoItem: {
            type:Object,
            default: () => ({})
        },
    },
    computed:{
        ZtreeObj () {
            return require('@/view/DMS/bmzj/ztreeFn.js')
        }
    },
    watch:{
        seletedFiles:{
            handler(newData,oldData){
                this.stuffName = '';
                this.parentCatalogData = [{id : "0",name : "卷宗目录"}]
                this.initNodes();
            },
            deep:true
        }
    },
    data() {
        return {
            stuffName:'',
            seleteIndex:-1,
            parentCatalogData:[
                {
                    id : "0",
                    name : "卷宗目录"
                },
            ],
            catalogSelectData:[],
            currentCatalogId:'',
        }
    },
    mounted(){
        this.initNodes(); 
    },
    methods:{
        ...mapActions([
            'changeOptionFile', 
        ]),
        stuffSearch(){
            this.autoMatch();
        },
        //关闭窗口
        closeModal() {
             this.$parent.$parent.modal4 = false;
        },
        resetBtn(){
            this.stuffName = '';
            this.parentCatalogData = [{id : "0",name : "卷宗目录"}]
            this.initNodes();
        },
        autoMatch() {
            //搜索文本
            if (this.stuffName != "") {
                var matchNodes = []; //匹配到的单位树形数据
                var initNodes = this.jzTreeObj.getNodesByFilter((node)=>{
                    if(node.name.indexOf(this.stuffName) > -1 && !node.isParent ){
                        matchNodes.push(node);
                    }else{
                        this.catalogSelectData = [];
                    }
                });
                var parentNodes = [];
                for(var i = 0 ; i < matchNodes.length ; i ++ ){
                    if(!matchNodes[i].isParent){
                        var obj = {
                        id : matchNodes[i].parentId,
                        name : matchNodes[i].name,
                        isParent : "true"
                        }
                        parentNodes.push(obj);
                    }
                }
                parentNodes = this.unique(parentNodes);
                var newArr = [];
                for(i = 0 ; i < parentNodes.length; i ++){
                    newArr.push(parentNodes[i]);
                    for(var j = 0 ; j < matchNodes.length ; j ++ ){
                        if(parentNodes[i].id == matchNodes[j].parentId &&matchNodes[j].isParent != undefined && !matchNodes[j].isParent){
                            newArr.push(matchNodes[j]);
                        }
                    }
                }
                // this.catalogSelectData = this.unique(newArr);
                // console.log(newArr)
                this.nodeData(this.unique(newArr));
               
            }else {
                this.resetBtn();
            }
        },
        selectDefaultNode() {
            var selectNode = this.jzTreeObj.getNodesByFilter(function (node) {
                return node.isDoc;
            }, true);

            if (selectNode) {
                $('#' + selectNode.tId + '_a').click();
            }
        },
        unique(arr) {
            for (var i = 0, len = arr.length; i < len; i++) {
                for (var j = i + 1, len = arr.length; j < len; j++) {
                    if (arr[i].id === arr[j].id) {
                        arr.splice(j, 1);
                        j--;        // 每删除一个数j的值就减1
                        len--;      // j值减小时len也要相应减1（减少循环次数，节省性能）   
                        // console.log(j,len)
                    }
                }
            }
            return arr;
        },
        //获取所有上级名称
        getParentCatalogName(currentNode, parentCatalogName) {
            var node = currentNode.getParentNode();
            if (node) {
                if (parentCatalogName == "") {
                    parentCatalogName = node.realName ? node.realName : node.name;
                } else {
                    parentCatalogName = (node.realName ? node.realName : node.name) + " > " + parentCatalogName;
                }
                return this.getParentCatalogName(node, parentCatalogName);
            } else {
                return parentCatalogName;
            }
        },
        initNodes(){
            var initNodes = this.jzTreeObj.getNodesByFilter(function(node){
                return node.level == 0 && !node.isDoc;
            });
            this.nodeData(initNodes);
        },
        nodeData(data){
            if(data && data.length > 0){
                data.forEach(item => {
                    item.newName = item.realName ? item.realName : item.name;
                });
                this.catalogSelectData = data;
            }
        },
        parentNodeAction(item){
            var id = item.id;
            var catalogNodes = this.jzTreeObj.getNodesByFilter(function(node){
                return node.parentId == id
            });
            this.nodeData(catalogNodes);
            var obj = {
                id : id,
                name :item.newName
            }
            this.parentCatalogData.push(obj);
        },
        lastNodeAction(item,index){
            this.seleteIndex = index;
            this.currentCatalogId = item.id;
        },
        backOne(){
            this.currentCatalogId = '';
			if(this.parentCatalogData.length <= 1){
				return;
			}
			this.parentCatalogData = this.parentCatalogData.slice(0,this.parentCatalogData.length-1);
			var currentParentCatalogId = this.parentCatalogData[this.parentCatalogData.length-1].id;
			var catalogNodes = this.jzTreeObj.getNodesByFilter(function(node){
			    return (node.parentId == currentParentCatalogId) && !node.isDoc;
            });
            this.nodeData(catalogNodes);
        },
        moveAction(){
            if(this.currentCatalogId !== ''){
                var params = JSON.parse(JSON.stringify(this.ZtreeObj.pageFunc.moveFiles(this.currentCatalogId,this.seletedFiles)));
                this.ZtreeObj.pageFunc.getNewXh();
                if(params){
                    //单选移动
                    if(this.photoItem && this.photoItem.id) {
                        let obj = {};
                        Object.assign(obj, this.photoItem);
                        obj.xh = obj.reXh;
                        delete obj.reXh;
                        params[1].operateType = '04';
                        params[1].deleteMaterialFile = [obj]
                    }
                    //多选移动
                    let obj = this.$parent.$parent.allPhotoItem;
                    if(obj && Object.keys(obj).length > 0) {
                        for(let i = 1; i<params.length; i++) {
                            if(obj[params[i].id]) {
                                params[i].deleteMaterialFile = obj[params[i].id];
                            }
                            params[i].operateType = '04';
                        }
                    }
                    //未编目移动到已编目，去除unIdentify属性
                    if(this.$parent.$parent.fromWbm) {
                        params[0].materialFile.forEach(item => {
                            delete item.unIdentify
                            delete item.repeat;
                            delete item.repeatMark;
                            delete item.simScore;
                        })
                        

                        let arrList = [];
                        for(let i=1; i<params.length; i++) {
                           arrList = [...arrList, ...params[i].deleteMaterialFile];
                        }

                        let idList = [];    //存放所有有重复标记的图片id
                        arrList.forEach(item => {
                            if(item.repeat == '1') {
                                idList.push(item.id);
                            }
                        })
                        let repeatObjArr = {};
                        let wbmList = JSON.parse(JSON.stringify(this.$parent.$parent.newDocDataAll_wbm));    //未编目去除重复标记
                        let removeList = [];
                        let isClearMark = false;
                        for(let i=0; i<wbmList.length; i++) {
                            for(let j=0; j<wbmList[i].materialFile.length; j++) {
                                if(idList.includes(wbmList[i].materialFile[j].id) && wbmList[i].materialFile[j].repeat == '1') {  //返回材料时需要手动清除重复页标记
                                    //  let materialObj = JSON.parse(JSON.stringify(wbmList[i].materialFile));
                                    isClearMark = true;
                                    let materialObj = wbmList[i].materialFile;
                                    let repeatObj = JSON.parse(JSON.stringify(materialObj[j]));
                                    delete materialObj[j].repeat;
                                    delete materialObj[j].repeatMark;
                                    delete materialObj[j].simScore;

                                    let clearMarkInfo = {
                                       ignoreTypeList: ['5'],
                                       imageId: materialObj[j].id,
                                       repeatMark: repeatObj.repeatMark
                                    }
                                    let dossierMaterialInfo = JSON.parse(JSON.stringify(wbmList[i]));
                                    dossierMaterialInfo.materialFile = JSON.stringify(materialObj);
                                    removeList.push({
                                       clearMarkInfo,
                                       dossierMaterialInfo
                                    })
                                }
                                if(wbmList[i].materialFile[j].repeat == '1') {            //统计某个重复页有多少张图片
                                    isClearMark = true;
                                    if(repeatObjArr[wbmList[i].materialFile[j].repeatMark]) {
                                       repeatObjArr[wbmList[i].materialFile[j].repeatMark].num++;
                                       repeatObjArr[wbmList[i].materialFile[j].repeatMark].idList.push(wbmList[i].materialFile[j].id);
                                    } else {
                                       repeatObjArr[wbmList[i].materialFile[j].repeatMark] ={
                                           num: 1,
                                           idList: [wbmList[i].materialFile[j].id]
                                       }
                                    }
                                }
                            }
                        }
                        params[0].type = '1';
                        if(params.length >= 2) {
                            for(let i=1; i<params.length; i++) {
                                params[i].type = this.$parent.$parent.selectIndex == 1 ? '1' : '0';
                            }
                        }
                        // params.forEach(item => {
                        //     item.type = this.$parent.$parent.selectIndex == 1 ? '1' : '0';
                        // })
                        for(let i=1; i<params.length; i++) {
                            if(params[i].materialFile) {
                                params[i].materialFile.forEach(item => {
                                    if(item.repeatMark && repeatObjArr[item.repeatMark] && repeatObjArr[item.repeatMark].num == 1) {
                                        delete item.repeatMark;
                                        delete item.repeat;
                                        delete item.simScore;
                                    }
                                })
                            }
                        }

                        if(isClearMark) {
                            this.$Post_RP(this.API.DmsApi.CLEAR_MARK, removeList).then(res => {
                                if(res.success) {
                                    // this.$Message.success('操作成功');
                                    this.$parent.$parent.operatePost(params,true,'move');
                                } else {
                                    this.$Message.warning(res.msg)
                                }
                            })
                        } else {
                            this.$parent.$parent.operatePost(params,true,'move');
                        }


                    }
                    params.forEach(item => {
                        item.type = this.$parent.$parent.selectIndex == 1 ? '1' : '0';
                    })
                    params[0].type = '1';
                    if(!this.$parent.$parent.fromWbm) {
                        this.$parent.$parent.operatePost(params,true,'move');
                    }  
                    this.$parent.$parent.firstInitMore = true;
                    this.$parent.$parent.checkAllGroup = [];
                    this.$parent.$parent.checkAllGroup2 = []
                    this.$parent.$parent.modal4 = false;
                    this.$parent.$parent.ischeckAll = [];
                    this.$parent.$parent.newDocDataAll_ybm2 = [];
                    this.$parent.$parent.newDocDataAll_wbm2 = [];
                    this.$parent.$parent.photoItem = null;
                    this.$parent.$parent.allPhotoItem = null;

                    this.currentCatalogId = '';
                }
                // 将移动的图片id放vux中保存
                var arr = []
                this.seletedFiles.map(item=>{
                    arr.push({
                        id:item
                    })
                })
                this.changeOptionFile(arr)
            }else{
                this.$Message.warning('请选择目录！');
            }
        }
    }
}
</script>
<style lang="less" scoped>
@assets:'../assets/images';
    #parentCatalog{
        // height: 0.35rem;
    }
    #stuffSearch{
        font-size: 0.2rem;
        cursor: pointer;
        font-weight: bold;
        position: absolute;
        right: 0.07rem;
        top: 0.05rem
    }
    .air_secrch{
        display: inline-block;
        width: 1.9rem;
        height: 1.5rem;
        background: url('@{assets}/znaj/noMore.png') no-repeat;
        background-size: 100% 100%;
        margin-left: calc(~'50% - 0.95rem');
        margin-top: 0.4rem;
    }
    .search_stuff{
        margin: 0.1rem 0.28rem;
        /* width: 100%; */
        width: calc(~'100% - 0.56rem');
        height: 0.4rem;
        position: relative;  
        display: flex;
        align-items: center;
    }
    .search_stuff input {
        width: 100%;
        height: 100%;
        border: 1px solid #93c5f5;
        padding-left: 0.1rem;
        box-sizing: border-box;
        font-size: 0.18rem;
    }
    .resetBtn{
        width: 0.7rem;
        height: 0.3rem;
        line-height: 0.3rem;
        text-align: center;
        margin-left: 0.08rem;
        cursor: pointer;
        border: solid 1px #087EFF;
        color: #0870ff;
        box-sizing: border-box;
        border-radius: 0.02rem;
        font-size: 0.16rem;
        border-radius: 0.04rem;
    }
    #catalogSelect > li.isLast {
        margin-left: 0.3rem;
    }
    .search_stuff .layui-icon-search {
        position: absolute;
        right: 12%;
        top: 0.08rem;
        font-size: 0.18rem;
        color: #087EFF;
        font-weight: bold;
    }
    .file-romove .top-panel .crumbs-panel{
        margin: 0.08rem 0.28rem;
        padding-right: 2rem;
        border-bottom: 1px solid #D8E2EB;
        font-size: 16px;
        font-family: 'Microsoft YaHei Regular, Microsoft YaHei Regular-Regular';
        font-weight: 400;
        text-align: left;
        color: #666666;
        // line-height: 18px;
    }
    .file-romove .top-panel{
        position:relative;
    }
    .file-romove .top-panel .back-btn{
        height: 100%;
        position:absolute;
        right:0.2rem;
        top:0;
        cursor:pointer;
        font-size: 0.18rem;
        display: flex;
        align-items: center;
    }
    .file-romove .top-panel .item-panel{
        cursor:pointer;
        font-size: 0.18rem;
        display: inline-block;
        max-width: 3.2rem;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }
    .file-romove .top-panel .back-btn .back-up-icon{
        display: inline-block;
        height: 0.25rem;
        width: 0.25rem;
        background: url('@{assets}/dzjz/bmzj/back_up.png') no-repeat center;
        background-size: 100%;
        margin-right: 0.1rem;
    }

    .file-romove .content-panel{
        overflow: auto;
        height: 4.8rem;
    }
    .file-romove .content-panel ul{
        padding: 0.1rem 0.4rem;
    }
    .file-romove .content-panel ul li{
        padding:0.05rem 0;
    }
    .file-romove .content-panel ul li:hover{
        color:#317af7;
        cursor:pointer;
    }
    .file-romove .content-panel ul li.active{
        color:#317af7;
    }
    .file-romove .content-panel ul li .icon-panel{
        display: inline-block;
        margin-bottom: -0.05rem;
    }
    .file-romove .content-panel ul li .icon-panel.wjj-icon{
        height: 0.2rem;
        width: 0.24rem;
        background: url('@{assets}/ztree/img/ztree-clous-wjj-isDoc.png') no-repeat center;
        background-size: 100%;
    }
    .file-romove .content-panel ul li .icon-panel.wj-icon{
        width: 0.2rem;
        height: 0.2rem;
        background: url('@{assets}/ztree/img/ztree-wdh-isDoc.png') no-repeat center;
        background-size: 100%;
    }

    .file-romove .bottom-panel{
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align:center;
    }
    .file-romove .bottom-panel .remove-btn{
        width: 0.7rem;
        height: 0.3rem;
        line-height: 0.3rem;
        margin: 0 0.06rem;
        background: #c0c4cc;
        color: #fff;
        // padding: 0.1rem 0rem;
        border-radius: 4px;
        cursor:pointer;
    }
    .file-romove .bottom-panel .remove-btn.select{
        background: #087eff;
    }
    .file-romove .bottom-panel .remove-btn:first-child{
        width: 0.7rem;
        margin: 0 0.06rem;
        line-height: 0.3rem;
        background: #ffffff;
        color: #0870ff;
        border: 1px solid #0870ff;
        cursor:pointer;
    }

    .file-romove .content-panel ul li .icon-panel.wjj-icon {
        margin-right: 0.1rem;
    }
    #catalogSelect > li.isLast {
        margin-left: 0;
    }
    .file-romove .content-panel ul li .icon-panel.wj-icon {
        margin-right: 0.1rem;
    }
/deep/ .search_stuff .ivu-input {
   height: 0.3rem;
}
</style>