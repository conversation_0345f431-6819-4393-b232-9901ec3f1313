/**
 * 动态表单工具函数
 */

import { DEFAULT_FIELD_CONFIG, DEFAULT_FORM_CONFIG, DEFAULT_SECTION_CONFIG, COMPONENT_DEFAULT_PROPS, COMMON_RULES } from './types'

/**
 * 合并字段配置
 * @param {Object} fieldConfig - 字段配置
 * @param {Object} sectionConfig - 节配置（用于继承节级别的只读设置）
 * @returns {Object} 合并后的配置
 */
export function mergeFieldConfig(fieldConfig, sectionConfig = {}) {
  // 保持原始字段配置的响应性，只对容易共享引用的属性进行深拷贝
  const merged = { ...fieldConfig }

  // 设置默认值（只设置未定义的属性，保持响应性）
  Object.keys(DEFAULT_FIELD_CONFIG).forEach(key => {
    if (merged[key] === undefined) {
      // 对于对象和数组类型，创建新的实例避免共享引用
      if (key === 'props' || key === 'events' || key === 'style') {
        merged[key] = {}
      } else if (key === 'rules') {
        merged[key] = []
      } else {
        merged[key] = DEFAULT_FIELD_CONFIG[key]
      }
    }
  })

  // 确保关键的对象和数组属性是独立的副本，避免共享引用
  merged.props = { ...DEFAULT_FIELD_CONFIG.props, ...(fieldConfig.props || {}) }
  merged.events = { ...DEFAULT_FIELD_CONFIG.events, ...(fieldConfig.events || {}) }
  merged.rules = [...(fieldConfig.rules || [])]
  merged.style = { ...DEFAULT_FIELD_CONFIG.style, ...(fieldConfig.style || {}) }

  // 如果有 options 数组，也要创建独立副本
  if (fieldConfig.options && Array.isArray(fieldConfig.options)) {
    merged.options = [...fieldConfig.options]
  }

  // 合并组件默认属性
  if (COMPONENT_DEFAULT_PROPS[merged.type]) {
    merged.props = { ...COMPONENT_DEFAULT_PROPS[merged.type], ...merged.props }
  }

  // 处理只读控制的优先级
  // 优先级：字段级别 > 节级别 > 默认值
  if (fieldConfig.readonly === undefined && sectionConfig.readonly === true) {
    merged.readonly = true
  }

  // 处理禁用控制的优先级（保持向后兼容）
  // 优先级：字段级别 > 节级别 > 默认值
  if (fieldConfig.disabled === undefined && sectionConfig.readonly === true) {
    merged.disabled = true
  }

  // 处理必填规则 - 只有明确设置 required: true 的字段才添加验证规则
  // 修改：移除自动触发器，只在手动校验时触发
  if (merged.required === true && !merged.rules.some(rule => rule.required)) {
    merged.rules.unshift({
      required: true,
      message: `${merged.label}为必填项`
      // 不设置trigger属性，避免自动触发校验
    })
  }

  return merged
}

/**
 * 计算字段的最终只读状态
 * @param {Object} field - 字段配置
 * @param {Object} section - 节配置
 * @param {boolean} componentDisabled - 组件级别禁用状态
 * @param {string} mode - 表单模式
 * @returns {boolean} 最终的只读/禁用状态
 */
export function calculateFieldDisabledState(field, section, componentDisabled, mode) {
  // 优先级从高到低：
  // 1. 字段级别的 disabled 属性（最高优先级）
  if (field.disabled !== undefined) {
    return field.disabled
  }

  // 2. 字段级别的 readonly 属性
  if (field.readonly === true) {
    return true
  }

  // 3. 节级别的 readonly 属性
  if (section && section.readonly === true) {
    return true
  }

  // 4. 组件级别的 disabled 属性
  if (componentDisabled === true) {
    return true
  }

  // 5. 查看模式（最低优先级）
  if (mode === 'view') {
    return true
  }

  return false
}

/**
 * 合并表单配置（兼容旧格式和新的节配置）
 * @param {Object} formConfig - 表单配置或节配置
 * @returns {Object} 合并后的配置
 */
export function mergeFormConfig(formConfig) {
  // 如果包含fields字段，说明是节配置，使用DEFAULT_SECTION_CONFIG
  if (formConfig && formConfig.fields) {
    return { ...DEFAULT_SECTION_CONFIG, ...formConfig }
  }
  // 否则使用旧的DEFAULT_FORM_CONFIG
  return { ...DEFAULT_FORM_CONFIG, ...formConfig }
}

/**
 * 合并节配置
 * @param {Object} sectionConfig - 节配置
 * @returns {Object} 合并后的配置
 */
export function mergeSectionConfig(sectionConfig) {
  return { ...DEFAULT_SECTION_CONFIG, ...sectionConfig }
}

/**
 * 根据列数计算字段span
 * @param {number} columns - 列数
 * @returns {number} span值
 */
export function getDefaultSpan(columns) {
  const spanMap = {
    1: 24,
    2: 12,
    3: 8,
    4: 6
  }
  return spanMap[columns] || 8
}

/**
 * 生成表单初始数据
 * @param {Array} fields - 字段配置数组
 * @returns {Object} 初始数据对象
 */
export function generateInitialData(fields) {
  const data = {}
  fields.forEach(field => {
    if (field.key) {
      data[field.key] = field.defaultValue !== undefined ? field.defaultValue : getDefaultValue(field.type)
    }
  })
  return data
}

/**
 * 获取字段类型的默认值
 * @param {string} type - 字段类型
 * @returns {*} 默认值
 */
export function getDefaultValue(type) {
  const defaultValues = {
    input: '',
    textarea: '',
    select: '',
    radio: '',
    checkbox: [],
    'date-picker': '',
    'time-picker': '',
    'datetime-picker': '',
    'date-time-picker': '',
    number: null,
    'input-number': null,
    switch: false,
    rate: 0,
    slider: 0,
    upload: []
  }
  return defaultValues[type] !== undefined ? defaultValues[type] : ''
}

/**
 * 验证字段配置
 * @param {Object} field - 字段配置
 * @returns {Array} 错误信息数组
 */
export function validateFieldConfig(field) {
  const errors = []

  if (!field.key) {
    errors.push('字段必须包含key属性')
  }

  if (!field.label) {
    errors.push('字段必须包含label属性')
  }

  if (!field.type) {
    errors.push('字段必须包含type属性')
  }

  return errors
}

/**
 * 深度克隆对象
 * @param {*} obj - 要克隆的对象
 * @returns {*} 克隆后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }

  if (typeof obj === 'object') {
    const cloned = {}
    Object.keys(obj).forEach(key => {
      cloned[key] = deepClone(obj[key])
    })
    return cloned
  }

  return obj
}

/**
 * 格式化选项数据
 * @param {Array} options - 原始选项数据
 * @returns {Array} 格式化后的选项数据
 */
export function formatOptions(options) {
  if (!Array.isArray(options)) {
    return []
  }

  return options.map(option => {
    if (typeof option === 'string' || typeof option === 'number') {
      return { label: option, value: option }
    }

    if (typeof option === 'object' && option !== null) {
      return {
        label: option.label || option.text || option.name || option.value,
        value: option.value !== undefined ? option.value : option.key || option.id,
        disabled: option.disabled || false,
        ...option
      }
    }

    return option
  })
}

/**
 * 获取常用验证规则
 * @param {string} ruleName - 规则名称
 * @param {Object} options - 规则选项
 * @returns {Object} 验证规则
 */
export function getCommonRule(ruleName, options = {}) {
  const rule = COMMON_RULES[ruleName]
  if (!rule) {
    console.warn(`未找到验证规则: ${ruleName}`)
    return null
  }

  return { ...rule, ...options }
}

/**
 * 创建自定义验证规则
 * @param {Function} validator - 验证函数
 * @param {string} message - 错误信息
 * @returns {Object} 验证规则
 * 注意：移除了trigger参数，校验只在手动调用validate时触发
 */
export function createCustomRule(validator, message) {
  return {
    validator: (rule, value, callback) => {
      const result = validator(value)
      if (result === true) {
        callback()
      } else {
        callback(new Error(typeof result === 'string' ? result : message))
      }
    },
    message
    // 不设置trigger属性，避免自动触发校验
  }
}

/**
 * 处理字段依赖关系
 * @param {Object} field - 字段配置
 * @param {Object} formData - 表单数据
 * @returns {Object} 处理后的字段配置
 */
export function processDependency(field, formData) {
  if (!field.dependsOn) {
    return field
  }

  const { key: dependKey, value: dependValue, action } = field.dependsOn
  const dependFieldValue = formData[dependKey]

  const processedField = { ...field }

  switch (action) {
    case 'show':
      processedField.hidden = dependFieldValue !== dependValue
      break
    case 'hide':
      processedField.hidden = dependFieldValue === dependValue
      break
    case 'enable':
      processedField.disabled = dependFieldValue !== dependValue
      break
    case 'disable':
      processedField.disabled = dependFieldValue === dependValue
      break
    case 'require':
      processedField.required = dependFieldValue === dependValue
      break
    default:
      break
  }

  return processedField
}

/**
 * 过滤隐藏字段的数据
 * @param {Object} formData - 表单数据
 * @param {Array} fields - 字段配置数组
 * @returns {Object} 过滤后的数据
 */
export function filterHiddenFields(formData, fields) {
  const filtered = {}
  fields.forEach(field => {
    if (!field.hidden && formData.hasOwnProperty(field.key)) {
      filtered[field.key] = formData[field.key]
    }
  })
  return filtered
}

/**
 * 生成唯一ID
 * @param {string} prefix - 前缀
 * @returns {string} 唯一ID
 */
export function generateId(prefix = 'dynamic-form') {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 判断是否为空值
 * @param {*} value - 值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
  if (value === null || value === undefined || value === '') {
    return true
  }

  if (Array.isArray(value)) {
    return value.length === 0
  }

  if (typeof value === 'object') {
    return Object.keys(value).length === 0
  }

  return false
}

/**
 * 验证数组配置格式
 * @param {Array} config - 配置数组
 * @returns {Array} 错误信息数组
 */
export function validateArrayConfig(config) {
  const errors = []

  if (!Array.isArray(config)) {
    errors.push('配置必须是数组格式')
    return errors
  }

  config.forEach((section, index) => {
    if (!section.fields || !Array.isArray(section.fields)) {
      errors.push(`第${index + 1}个节必须包含fields数组`)
    }

    if (section.fields) {
      section.fields.forEach((field, fieldIndex) => {
        const fieldErrors = validateFieldConfig(field)
        if (fieldErrors.length > 0) {
          errors.push(`第${index + 1}个节的第${fieldIndex + 1}个字段: ${fieldErrors.join(', ')}`)
        }
      })
    }
  })

  return errors
}

/**
 * 从数组配置中提取所有字段
 * @param {Array} config - 配置数组
 * @returns {Array} 所有字段的数组
 */
export function extractAllFields(config) {
  const fields = []
  if (Array.isArray(config)) {
    config.forEach(section => {
      if (section.fields && Array.isArray(section.fields)) {
        fields.push(...section.fields)
      }
    })
  }
  return fields
}

/**
 * 格式化表单数据用于提交
 * @param {Object} formData - 表单数据
 * @param {Array} fields - 字段配置数组
 * @returns {Object} 格式化后的数据
 */
export function formatSubmitData(formData, fields) {
  const formatted = {}

  fields.forEach(field => {
    if (field.hidden) return

    const value = formData[field.key]

    // 处理不同类型的数据格式化
    switch (field.type) {
      case 'date-picker':
      case 'datetime-picker':
      case 'time-picker':
        formatted[field.key] = value ? (typeof value === 'string' ? value : value.toISOString()) : ''
        break
      case 'number':
        formatted[field.key] = value !== null && value !== undefined ? Number(value) : null
        break
      case 'checkbox':
        formatted[field.key] = Array.isArray(value) ? value : []
        break
      default:
        formatted[field.key] = value
        break
    }
  })

  return formatted
}
