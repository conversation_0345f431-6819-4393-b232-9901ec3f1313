/**
 * 简单的Vue模板语法检查工具
 */

const fs = require('fs')
const path = require('path')

function checkVueTemplate(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8')
  const lines = content.split('\n')
  
  const errors = []
  const tagStack = []
  
  // 自闭合标签
  const selfClosingTags = new Set([
    'input', 'img', 'br', 'hr', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr'
  ])
  
  // Vue特殊标签
  const vueSpecialTags = new Set(['template', 'slot', 'component'])
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    const lineNum = i + 1
    
    // 跳过注释和空行
    if (!line || line.startsWith('<!--') || line.startsWith('//') || line.startsWith('*')) {
      continue
    }
    
    // 查找标签
    const tagMatches = line.match(/<\/?[^>]+>/g)
    if (tagMatches) {
      for (const tagMatch of tagMatches) {
        const isClosingTag = tagMatch.startsWith('</')
        const isSelfClosing = tagMatch.endsWith('/>')
        
        if (isClosingTag) {
          // 结束标签
          const tagName = tagMatch.match(/<\/([^>]+)>/)[1]
          
          if (tagStack.length === 0) {
            errors.push(`第${lineNum}行: 找到多余的结束标签 ${tagMatch}`)
          } else {
            const lastTag = tagStack.pop()
            if (lastTag !== tagName) {
              errors.push(`第${lineNum}行: 标签不匹配，期望 </${lastTag}>，但找到 ${tagMatch}`)
            }
          }
        } else if (!isSelfClosing) {
          // 开始标签
          const tagName = tagMatch.match(/<([^\s>]+)/)[1]
          
          if (!selfClosingTags.has(tagName.toLowerCase())) {
            tagStack.push(tagName)
          }
        }
      }
    }
  }
  
  // 检查未闭合的标签
  if (tagStack.length > 0) {
    errors.push(`未闭合的标签: ${tagStack.join(', ')}`)
  }
  
  return errors
}

// 检查DynamicForm.vue
const dynamicFormPath = path.join(__dirname, 'DynamicForm.vue')

try {
  const errors = checkVueTemplate(dynamicFormPath)
  
  if (errors.length === 0) {
    console.log('✅ DynamicForm.vue 语法检查通过')
  } else {
    console.log('❌ DynamicForm.vue 发现语法错误:')
    errors.forEach(error => console.log('  ', error))
  }
} catch (error) {
  console.error('检查失败:', error.message)
}
