# 内置提交功能使用指南

## 概述

动态表单组件的内置提交功能可以大幅简化表单提交的处理逻辑，将原本需要几十行代码的提交处理压缩到几行配置。

## 🚀 快速开始

### 最简单的用法

```vue
<template>
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    :submit-config="{ text: '保存' }"
    @submit="handleSubmit"
  />
</template>

<script>
export default {
  methods: {
    handleSubmit(data) {
      console.log('提交数据:', data)
      // 返回Promise或直接处理
      return this.saveData(data)
    }
  }
}
</script>
```

### HTTP自动提交

```vue
<DynamicForm
  v-model="formData"
  :config="formConfig"
  submit-url="/api/users"
  submit-method="POST"
  :submit-config="{
    text: '创建用户',
    successMessage: '用户创建成功！'
  }"
/>
```

## 📋 配置选项详解

### submitConfig 配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| text | String | '提交' | 按钮文本 |
| type | String | 'primary' | 按钮类型 |
| showInActions | Boolean | true | 是否显示在操作区域 |
| validateBeforeSubmit | Boolean | true | 提交前是否验证 |
| successMessage | String | '提交成功' | 成功提示消息 |
| errorMessage | String | '提交失败' | 失败提示消息 |
| beforeSubmit | Function | null | 提交前钩子 |
| transformData | Function | null | 数据转换函数 |
| afterSubmit | Function | null | 提交后钩子 |

### 其他Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| submitUrl | String | '' | 提交URL，设置后自动发送HTTP请求 |
| submitMethod | String | 'POST' | HTTP请求方法 |
| autoSubmit | Boolean | false | 是否自动提交（暂未实现） |

## 🔧 高级用法

### 1. 数据转换

```javascript
{
  transformData: (formData, originalData, formInstance) => {
    return {
      ...formData,
      timestamp: new Date().toISOString(),
      userId: this.$store.state.user.id
    }
  }
}
```

### 2. 提交前验证

```javascript
{
  beforeSubmit: (formData, formInstance) => {
    if (!formData.name || formData.name.length < 2) {
      this.$Message.warning('姓名至少需要2个字符')
      return false // 阻止提交
    }
    return true
  }
}
```

### 3. 提交后处理

```javascript
{
  afterSubmit: (result, submitData, formInstance) => {
    // 提交成功后的处理
    if (result.success) {
      // 清空表单
      formInstance.resetFields()
      // 跳转页面
      this.$router.push('/list')
    }
  }
}
```

## 📝 实际案例

### 案例1：用户注册表单

```vue
<template>
  <DynamicForm
    v-model="userForm"
    :config="userConfig"
    :submit-config="userSubmitConfig"
    submit-url="/api/auth/register"
    @submit-success="handleRegisterSuccess"
  />
</template>

<script>
export default {
  data() {
    return {
      userForm: {},
      userConfig: {
        title: '用户注册',
        fields: [
          { key: 'username', label: '用户名', type: 'input', required: true },
          { key: 'email', label: '邮箱', type: 'input', required: true },
          { key: 'password', label: '密码', type: 'input', props: { type: 'password' } }
        ]
      },
      userSubmitConfig: {
        text: '注册',
        successMessage: '注册成功，请登录！',
        transformData: (data) => ({
          ...data,
          registerTime: Date.now()
        })
      }
    }
  },
  methods: {
    handleRegisterSuccess() {
      this.$router.push('/login')
    }
  }
}
</script>
```

### 案例2：病号登记表单重构

**重构前（原代码）：**
```javascript
// 54行复杂的提交逻辑
handleSubmit() {
  this.$refs.formData.validate((valid) => {
    if (valid) {
      this.loading = true;
      const submitData = {
        manageIdFk: this.localFormData.manageIdFk,
        prisonId: this.localFormData.prisonId,
        jgrybm: this.localFormData.jgrybm,
        businessStatus: this.localFormData.businessStatus || '1',
        severelySickType: this.localFormData.severelySickType,
        patientSituation: this.localFormData.patientSituation,
        treatmentSituation: this.localFormData.treatmentSituation,
      };
      
      this.$emit('update:formData', this.localFormData);
      
      this.$store.dispatch('authPostRequest', {
        url: this.$path.sick_create,
        params: submitData
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.$Notice.success({
            title: '成功提示',
            desc: '重病号登记成功'
          });
          this.toback();
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: res.msg || '重病号登记失败'
          });
        }
      }).catch((error) => {
        this.loading = false;
        this.$Notice.error({
          title: '错误提示',
          desc: '网络异常，请稍后重试'
        });
        console.error('提交失败：', error);
      });
    } else {
      this.$Message.error('请填写完整的表单信息！');
    }
  });
}
```

**重构后（使用内置提交）：**
```vue
<DynamicForm
  v-model="formData"
  :config="formConfig"
  :submit-config="{
    text: '提交',
    successMessage: '重病号登记成功',
    transformData: (data) => ({
      manageIdFk: data.manageIdFk,
      prisonId: data.prisonId,
      jgrybm: data.jgrybm,
      businessStatus: data.businessStatus || '1',
      severelySickType: data.severelySickType,
      patientSituation: data.patientSituation,
      treatmentSituation: data.treatmentSituation
    })
  }"
  :submit-url="$path.sick_create"
  @submit-success="$emit('toback')"
/>
```

**效果对比：**
- 代码行数：54行 → 15行（减少72%）
- 复杂度：高 → 低
- 可维护性：差 → 优
- 错误处理：手动 → 自动

## 🎯 最佳实践

### 1. 合理使用钩子函数

```javascript
{
  beforeSubmit: (data, form) => {
    // 只做必要的验证，不要重复表单验证
    return this.checkBusinessLogic(data)
  },
  transformData: (data) => {
    // 只做数据格式转换，不要做业务逻辑
    return this.formatSubmitData(data)
  },
  afterSubmit: (result, data, form) => {
    // 只做提交后的UI操作，不要做数据处理
    this.handleUIUpdate(result)
  }
}
```

### 2. 错误处理策略

```javascript
{
  errorMessage: '操作失败，请重试',
  beforeSubmit: (data, form) => {
    // 客户端验证
    if (!this.validateBusinessRules(data)) {
      return false
    }
  }
}

// 在组件中监听错误
@submit-error="handleSubmitError"

handleSubmitError(error, data, form) {
  // 根据错误类型做不同处理
  if (error.code === 'VALIDATION_ERROR') {
    this.handleValidationError(error)
  } else if (error.code === 'NETWORK_ERROR') {
    this.handleNetworkError(error)
  }
}
```

### 3. 性能优化

```javascript
{
  // 避免在transformData中做重复计算
  transformData: (data) => {
    // ❌ 错误：每次都重新计算
    // return { ...data, hash: this.calculateHash(data) }
    
    // ✅ 正确：缓存计算结果
    if (!this._cachedHash) {
      this._cachedHash = this.calculateHash(data)
    }
    return { ...data, hash: this._cachedHash }
  }
}
```

## 🔍 调试技巧

### 1. 开启调试模式

```javascript
{
  beforeSubmit: (data, form) => {
    console.log('提交前数据:', data)
    return true
  },
  transformData: (data) => {
    const transformed = this.transform(data)
    console.log('数据转换:', { before: data, after: transformed })
    return transformed
  },
  afterSubmit: (result, data, form) => {
    console.log('提交结果:', result)
  }
}
```

### 2. 错误追踪

```vue
<DynamicForm
  @before-submit="console.log('before-submit:', $event)"
  @submit="console.log('submit:', $event)"
  @submit-success="console.log('submit-success:', $event)"
  @submit-error="console.log('submit-error:', $event)"
/>
```

## 🚨 常见问题

### Q: 如何阻止表单提交？
A: 在 `beforeSubmit` 钩子中返回 `false`

### Q: 如何自定义错误处理？
A: 监听 `@submit-error` 事件

### Q: 如何在提交成功后跳转页面？
A: 在 `afterSubmit` 钩子或 `@submit-success` 事件中处理

### Q: 如何处理文件上传？
A: 在 `transformData` 中处理FormData格式转换

## 📚 相关文档

- [完整API文档](./README.md)
- [快速开始指南](./QUICK_START.md)
- [示例代码](./examples/BuiltinSubmitExample.vue)
