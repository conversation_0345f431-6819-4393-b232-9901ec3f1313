/**
 * 动态表单组件入口文件
 */

import DynamicForm from './DynamicForm.vue'
import * as types from './types'
import * as utils from './utils'

// 导出组件
export default DynamicForm

// 导出类型和工具函数
export { types, utils }

// 导出常用的类型和工具
export const {
  FORM_MODES,
  FIELD_TYPES,
  COMMON_RULES,
  DEFAULT_FIELD_CONFIG,
  DEFAULT_FORM_CONFIG,
  COMPONENT_DEFAULT_PROPS
} = types

export const {
  mergeFieldConfig,
  mergeFormConfig,
  getDefaultSpan,
  generateInitialData,
  getDefaultValue,
  validateFieldConfig,
  deepClone,
  formatOptions,
  getCommonRule,
  createCustomRule,
  processDependency,
  filterHiddenFields,
  generateId,
  isEmpty,
  formatSubmitData
} = utils

// Vue插件安装方法
function install(Vue, options = {}) {
  // 检查是否已经安装
  if (install.installed) {
    return
  }
  install.installed = true

  // 注册全局组件
  Vue.component('DynamicForm', DynamicForm)

  // 注册全局属性
  Vue.prototype.$FIELD_TYPES = FIELD_TYPES
  Vue.prototype.$FORM_MODES = FORM_MODES

  // 全局混入表单工具方法
  Vue.mixin({
    methods: {
      // 创建表单配置
      $createFormConfig(fields, options = {}) {
        return mergeFormConfig({
          fields,
          ...options
        })
      },

      // 生成初始数据
      $generateFormData(config) {
        return generateInitialData(config)
      },

      // 格式化选项
      $formatOptions(options, labelKey = 'label', valueKey = 'value') {
        return formatOptions(options, labelKey, valueKey)
      },

      // 检查值是否为空
      $isEmpty(value) {
        return isEmpty(value)
      }
    }
  })

  // 处理插件选项
  if (options.defaultConfig) {
    Vue.prototype.$defaultFormConfig = options.defaultConfig
  }
}

// 兼容旧版本的安装方法
DynamicForm.install = install

// 导出安装函数
export { install }

// 默认导出插件对象
const DynamicFormPlugin = {
  install,
  DynamicForm,
  FIELD_TYPES,
  FORM_MODES
}

// 如果在浏览器环境中且Vue可用，自动安装
if (typeof window !== 'undefined' && window.Vue) {
  window.Vue.use(DynamicFormPlugin)
}

// 导出插件对象
export { DynamicFormPlugin }
