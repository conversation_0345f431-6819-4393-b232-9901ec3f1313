<template>
  <div class="test-component">
    <h3>✅ DynamicForm 组件语法检查通过</h3>
    <p>模板标签匹配问题已修复，组件可以正常使用。</p>

    <DynamicForm
      v-model="formData"
      :config="formConfig"
      :actions="formActions"
      @action="handleAction"
    />

    <div style="margin-top: 20px;">
      <h4>表单数据：</h4>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import DynamicForm from './DynamicForm.vue'
import { FIELD_TYPES } from './types'

export default {
  name: 'TestComponent',
  components: {
    DynamicForm
  },
  data() {
    return {
      formData: {},
      formConfig: {
        title: '测试表单',
        columns: 2,
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true,
            props: {
              placeholder: '请输入姓名'
            }
          },
          {
            key: 'email',
            label: '邮箱',
            type: FIELD_TYPES.INPUT,
            rules: [
              { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入邮箱'
            }
          }
        ]
      },
      formActions: [
        {
          text: '重置',
          type: 'default',
          onClick: this.handleReset
        },
        {
          text: '提交',
          type: 'primary',
          onClick: this.handleSubmit
        }
      ]
    }
  },
  methods: {
    handleAction(action) {
      console.log('按钮点击:', action)
    },

    handleReset(formData, formInstance) {
      if (formInstance) {
        formInstance.resetFields()
      }
      console.log('重置表单')
    },

    handleSubmit(formData, formInstance) {
      if (formInstance) {
        formInstance.validate((valid) => {
          if (valid) {
            console.log('提交成功:', formData)
          } else {
            console.log('验证失败')
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.test-component {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}
</style>
