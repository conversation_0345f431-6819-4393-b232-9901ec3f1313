<template>
  <div class="validation-debug-test">
    <h2>验证规则调试测试</h2>
    
    <DynamicForm
      ref="testForm"
      v-model="formData"
      :config="formConfig"
      :mode="'create'"
      :loading="false"
    />
    
    <div class="debug-info">
      <h3>调试信息：</h3>
      <div class="debug-section">
        <h4>生成的验证规则：</h4>
        <pre>{{ JSON.stringify(formRules, null, 2) }}</pre>
      </div>
      
      <div class="debug-section">
        <h4>所有字段信息：</h4>
        <pre>{{ JSON.stringify(allFields, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="test-actions">
      <Button type="primary" @click="validateForm">验证表单</Button>
      <Button @click="showDebugInfo">显示调试信息</Button>
      <Button @click="resetForm">重置表单</Button>
    </div>
    
    <div class="validation-result" v-if="validationResult">
      <h3>验证结果：</h3>
      <pre>{{ validationResult }}</pre>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES } from '../types'

export default {
  name: 'ValidationDebugTest',
  data() {
    return {
      formData: {
        field1: '',
        field2: '',
        field3: '',
        field4: ''
      },
      validationResult: null,
      formRules: {},
      allFields: []
    }
  },
  computed: {
    formConfig() {
      return [
        {
          title: '验证规则调试',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          fields: [
            {
              key: 'field1',
              label: '必填输入框',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入内容'
              }
            },
            {
              key: 'field2',
              label: '必填字典选择',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_BHLB',
              props: {
                placeholder: '请选择字典项',
                multiple: false
              }
            },
            {
              key: 'field3',
              label: '必填日期选择',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期时间',
                format: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            {
              key: 'field4',
              label: '可选输入框',
              type: FIELD_TYPES.INPUT,
              required: false,
              span: 12,
              props: {
                placeholder: '可选输入'
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    validateForm() {
      this.$refs.testForm.validate().then(() => {
        this.validationResult = '✅ 表单验证通过！'
        this.$Message.success('表单验证通过')
      }).catch((errors) => {
        console.log('验证错误详情:', errors)
        this.validationResult = `❌ 表单验证失败：\n${JSON.stringify(errors, null, 2)}`
        this.$Message.error('表单验证失败，请检查必填项')
      })
    },
    
    showDebugInfo() {
      // 获取表单组件的内部数据
      const formComponent = this.$refs.testForm
      if (formComponent) {
        this.formRules = formComponent.formRules || {}
        this.allFields = formComponent.allFields || []
        console.log('表单验证规则:', this.formRules)
        console.log('所有字段:', this.allFields)
      }
    },
    
    resetForm() {
      this.formData = {
        field1: '',
        field2: '',
        field3: '',
        field4: ''
      }
      this.validationResult = null
      this.$refs.testForm.resetFields()
    }
  },
  
  mounted() {
    // 延迟获取调试信息
    this.$nextTick(() => {
      setTimeout(() => {
        this.showDebugInfo()
      }, 1000)
    })
  }
}
</script>

<style lang="less" scoped>
.validation-debug-test {
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
  
  .debug-info {
    margin: 20px 0;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 4px;
    
    .debug-section {
      margin-bottom: 20px;
      
      h4 {
        margin-bottom: 10px;
        color: #333;
      }
      
      pre {
        background: #fff;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #ddd;
        font-size: 12px;
        overflow-x: auto;
        line-height: 1.5;
        max-height: 300px;
        overflow-y: auto;
      }
    }
  }
  
  .test-actions {
    margin: 20px 0;
    text-align: center;
    
    .ivu-btn {
      margin: 0 10px;
    }
  }
  
  .validation-result {
    margin-top: 30px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 4px;
    
    h3 {
      margin-bottom: 10px;
      color: #333;
    }
    
    pre {
      background: #fff;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 12px;
      overflow-x: auto;
      line-height: 1.5;
    }
  }
}
</style>
