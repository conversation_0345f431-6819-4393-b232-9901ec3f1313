<template>
  <div class="dictionary-type-test">
    <h2>字典类型测试</h2>
    
    <DynamicForm
      v-model="formData"
      :config="formConfig"
      :mode="'create'"
      :loading="false"
    />
    
    <div class="test-data">
      <h3>表单数据：</h3>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </div>
    
    <div class="usage-example">
      <h3>使用示例：</h3>
      <pre>{{ usageExample }}</pre>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES } from '../types'

export default {
  name: 'DictionaryTypeTest',
  data() {
    return {
      formData: {
        diseaseType: '',
        screeningMethod: '',
        screeningResult: '',
        normalSelect: '',
        customDict: ''
      }
    }
  },
  computed: {
    formConfig() {
      return [
        {
          title: '字典类型使用示例',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          fields: [
            {
              key: 'diseaseType',
              label: '传染病类型',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_BHLB',
              props: {
                placeholder: '请选择传染病类型',
                multiple: false
              }
            },
            {
              key: 'screeningMethod',
              label: '初筛操作方式',
              type: FIELD_TYPES.DICTIONARY,
              span: 12,
              dicName: 'ZD_CYB_CSCZFS',
              props: {
                placeholder: '请选择初筛方式',
                multiple: false
              }
            },
            {
              key: 'screeningResult',
              label: '初筛结果',
              type: FIELD_TYPES.DICTIONARY,
              span: 12,
              dicName: 'ZD_CYB_CSJG_YXYX',
              props: {
                placeholder: '请选择初筛结果',
                multiple: false
              }
            },
            {
              key: 'normalSelect',
              label: '普通下拉选择',
              type: FIELD_TYPES.SELECT,
              span: 12,
              props: {
                placeholder: '请选择'
              },
              options: [
                { value: '1', label: '选项1' },
                { value: '2', label: '选项2' },
                { value: '3', label: '选项3' }
              ]
            },
            {
              key: 'customDict',
              label: '自定义字典组件',
              type: FIELD_TYPES.CUSTOM,
              span: 12,
              render: (h, value, formData, field) => {
                return h('s-dicgrid', {
                  props: { 
                    value, 
                    multiple: false, 
                    dicName: 'ZD_BHLB',
                    placeholder: '自定义渲染的字典组件'
                  }
                })
              }
            }
          ]
        }
      ]
    },
    
    usageExample() {
      return `// 新的字典类型使用方式
{
  key: 'diseaseType',
  label: '传染病类型',
  type: FIELD_TYPES.DICTIONARY,  // 使用新的字典类型
  dicName: 'ZD_BHLB',           // 字典名称
  props: {
    placeholder: '请选择传染病类型',
    multiple: false             // 是否多选
  }
}

// 对比原来的写法
{
  key: 'diseaseType',
  label: '传染病类型',
  type: FIELD_TYPES.CUSTOM,
  render: (h, value, formData, field) => {
    return h('s-dicgrid', {
      props: { value, multiple: false, dicName: 'ZD_BHLB' }
    })
  }
}`
    }
  }
}
</script>

<style lang="less" scoped>
.dictionary-type-test {
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
  
  .test-data,
  .usage-example {
    margin-top: 30px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 4px;
    
    h3 {
      margin-bottom: 10px;
      color: #333;
    }
    
    pre {
      background: #fff;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 12px;
      overflow-x: auto;
      line-height: 1.5;
    }
  }
}
</style>
