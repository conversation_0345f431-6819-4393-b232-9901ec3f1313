<template>
  <div class="submit-only-validation-test">
    <h2>仅提交时校验测试</h2>
    <p>此测试验证表单只在提交时触发校验，而不在字段变化或失焦时自动触发</p>
    
    <DynamicForm
      ref="testForm"
      v-model="formData"
      :config="formConfig"
      :mode="'create'"
      :loading="false"
      @field-change="handleFieldChange"
    />
    
    <div class="test-actions">
      <Button type="primary" @click="validateForm">手动验证表单</Button>
      <Button @click="resetForm">重置表单</Button>
      <Button @click="clearValidation">清除验证</Button>
    </div>
    
    <div class="debug-info">
      <h3>测试说明：</h3>
      <ul>
        <li>在必填字段中输入内容后删除，不应该立即显示验证错误</li>
        <li>失焦时不应该触发验证</li>
        <li>只有点击"手动验证表单"按钮时才应该显示验证错误</li>
      </ul>
      
      <h3>表单数据：</h3>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
      
      <h3>字段变化日志：</h3>
      <div class="change-log">
        <div v-for="(log, index) in changeLog" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
    
    <div class="validation-result" v-if="validationResult">
      <h3>验证结果：</h3>
      <pre>{{ validationResult }}</pre>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES } from '../types'

export default {
  name: 'SubmitOnlyValidationTest',
  data() {
    return {
      formData: {
        requiredField1: '',
        requiredField2: '',
        optionalField: '',
        emailField: ''
      },
      changeLog: [],
      validationResult: null,
      formConfig: [
        {
          title: '仅提交时校验测试表单',
          columns: 2,
          labelWidth: 120,
          fields: [
            {
              key: 'requiredField1',
              label: '必填字段1',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入必填字段1'
              }
            },
            {
              key: 'requiredField2',
              label: '必填字段2',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入必填字段2'
              }
            },
            {
              key: 'optionalField',
              label: '可选字段',
              type: FIELD_TYPES.INPUT,
              required: false,
              span: 12,
              props: {
                placeholder: '可选字段'
              }
            },
            {
              key: 'emailField',
              label: '邮箱字段',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              rules: [
                { type: 'email', message: '请输入正确的邮箱地址' }
              ],
              props: {
                placeholder: '请输入邮箱地址'
              }
            }
          ]
        }
      ]
    }
  },
  
  methods: {
    handleFieldChange(key, value, formData) {
      const timestamp = new Date().toLocaleTimeString()
      this.changeLog.unshift(`[${timestamp}] 字段 ${key} 变化: ${value}`)
      
      // 限制日志条数
      if (this.changeLog.length > 10) {
        this.changeLog = this.changeLog.slice(0, 10)
      }
    },
    
    validateForm() {
      this.validationResult = null
      
      this.$refs.testForm.validate().then((valid) => {
        this.validationResult = {
          success: true,
          message: '表单验证通过',
          timestamp: new Date().toLocaleString()
        }
        this.$Message.success('表单验证通过')
      }).catch((error) => {
        this.validationResult = {
          success: false,
          message: '表单验证失败',
          error: error,
          timestamp: new Date().toLocaleString()
        }
        this.$Message.error('表单验证失败')
      })
    },
    
    resetForm() {
      this.$refs.testForm.resetFields()
      this.validationResult = null
      this.changeLog = []
    },
    
    clearValidation() {
      this.$refs.testForm.clearValidate()
      this.validationResult = null
    }
  }
}
</script>

<style lang="less" scoped>
.submit-only-validation-test {
  padding: 20px;
  
  .test-actions {
    margin: 20px 0;
    
    .ivu-btn {
      margin-right: 10px;
    }
  }
  
  .debug-info {
    margin-top: 20px;
    padding: 15px;
    background: #f8f8f9;
    border-radius: 4px;
    
    h3 {
      margin-top: 20px;
      margin-bottom: 10px;
      
      &:first-child {
        margin-top: 0;
      }
    }
    
    ul {
      margin: 10px 0;
      padding-left: 20px;
      
      li {
        margin: 5px 0;
        color: #666;
      }
    }
    
    pre {
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .change-log {
      max-height: 150px;
      overflow-y: auto;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
      
      .log-item {
        padding: 5px 10px;
        border-bottom: 1px solid #f0f0f0;
        font-size: 12px;
        font-family: monospace;
        
        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
  
  .validation-result {
    margin-top: 20px;
    padding: 15px;
    border-radius: 4px;
    
    &:has(pre:contains('"success": true')) {
      background: #f6ffed;
      border: 1px solid #b7eb8f;
    }
    
    &:has(pre:contains('"success": false')) {
      background: #fff2f0;
      border: 1px solid #ffccc7;
    }
    
    pre {
      background: transparent;
      border: none;
      padding: 0;
      margin: 10px 0 0 0;
    }
  }
}
</style>
