<template>
  <div class="bsp-file-upload-test">
    <h2>BSP File Upload 测试</h2>
    <p>测试 BSP_FILE_UPLOAD 组件的 defaultList 属性处理</p>
    
    <div class="test-sections">
      <!-- 测试1：空数组 -->
      <div class="test-section">
        <h3>测试1：空数组</h3>
        <DynamicForm
          ref="testForm1"
          v-model="formData1"
          :config="formConfig1"
          :mode="'create'"
        />
        <p>formData1.attachments: {{ JSON.stringify(formData1.attachments) }}</p>
      </div>
      
      <!-- 测试2：字符串格式的数组 -->
      <div class="test-section">
        <h3>测试2：字符串格式的数组</h3>
        <DynamicForm
          ref="testForm2"
          v-model="formData2"
          :config="formConfig2"
          :mode="'create'"
        />
        <p>formData2.attachments: {{ JSON.stringify(formData2.attachments) }}</p>
      </div>
      
      <!-- 测试3：正常数组 -->
      <div class="test-section">
        <h3>测试3：正常数组</h3>
        <DynamicForm
          ref="testForm3"
          v-model="formData3"
          :config="formConfig3"
          :mode="'create'"
        />
        <p>formData3.attachments: {{ JSON.stringify(formData3.attachments) }}</p>
      </div>
    </div>
    
    <div class="test-actions">
      <Button @click="updateStringData" type="primary">更新字符串数据</Button>
      <Button @click="updateArrayData" type="success">更新数组数据</Button>
      <Button @click="clearData" type="warning">清空数据</Button>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES, FORM_MODES } from '@/components/dynamic-form/types'
import { getToken } from '@/libs/util'

export default {
  name: 'BspFileUploadTest',
  data() {
    return {
      // 测试1：空数组
      formData1: {
        attachments: []
      },
      
      // 测试2：字符串格式的数组
      formData2: {
        attachments: '[{"raw":{"uid":1754319556296},"uid":1754319556291.8787,"name":"test.tar.gz","size":50921996,"type":"application/x-gzip","url":"http://*************:9010/acp/test.tar.gz","status":"finished","percentage":100,"id":"1952376796922253312"}]'
      },
      
      // 测试3：正常数组
      formData3: {
        attachments: [{
          uid: 1754319556291.8787,
          name: "test.tar.gz",
          size: 50921996,
          type: "application/x-gzip",
          url: "http://*************:9010/acp/test.tar.gz",
          status: "finished",
          percentage: 100,
          id: "1952376796922253312"
        }]
      }
    }
  },
  
  computed: {
    formConfig1() {
      return this.getFormConfig()
    },
    
    formConfig2() {
      return this.getFormConfig()
    },
    
    formConfig3() {
      return this.getFormConfig()
    }
  },
  
  methods: {
    getFormConfig() {
      return [
        {
          title: '文件上传测试',
          columns: 1,
          labelWidth: 160,
          labelColon: true,
          collapsible: false,
          collapsed: false,
          showHeader: true,
          fields: [
            {
              key: "attachments",
              label: "上传附件",
              type: FIELD_TYPES.BSP_FILE_UPLOAD,
              span: 24,
              required: false,
              props: {
                uploadUrl: this.$path.terminal_version_management_file_upload + '?access_token=' + getToken(),
                mode: 'edit',
                width: 100,
                height: 100,
                autoUpload: true,
                format: ["gz", "tar", "zip"],
                maxCount: 3,
              },
            }
          ]
        }
      ]
    },
    
    updateStringData() {
      this.formData2.attachments = '[{"uid":' + Date.now() + ',"name":"updated.tar.gz","size":1024,"type":"application/x-gzip","url":"http://example.com/updated.tar.gz","status":"finished","percentage":100}]'
    },
    
    updateArrayData() {
      this.formData3.attachments = [{
        uid: Date.now(),
        name: "updated.tar.gz",
        size: 1024,
        type: "application/x-gzip",
        url: "http://example.com/updated.tar.gz",
        status: "finished",
        percentage: 100
      }]
    },
    
    clearData() {
      this.formData1.attachments = []
      this.formData2.attachments = '[]'
      this.formData3.attachments = []
    }
  }
}
</script>

<style lang="less" scoped>
.bsp-file-upload-test {
  padding: 20px;
  
  .test-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e8eaec;
    border-radius: 4px;
    
    h3 {
      margin-top: 0;
      color: #2d8cf0;
    }
    
    p {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8f8f9;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      word-break: break-all;
    }
  }
  
  .test-actions {
    margin-top: 20px;
    
    .ivu-btn {
      margin-right: 10px;
    }
  }
}
</style>
