<template>
  <div class="date-time-picker-test">
    <h2>DATE_TIME_PICKER 类型测试</h2>
    
    <div class="test-section">
      <h3>1. 基础测试</h3>
      <DynamicForm
        ref="basicForm"
        :mode="'create'"
        :form-data="basicFormData"
        :form-config="basicFormConfig"
        @change="handleBasicChange"
      />
      <div class="data-display">
        <strong>表单数据：</strong>
        <pre>{{ JSON.stringify(basicFormData, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 验证测试</h3>
      <DynamicForm
        ref="validationForm"
        :mode="'create'"
        :form-data="validationFormData"
        :form-config="validationFormConfig"
        @change="handleValidationChange"
      />
      <Button @click="validateForm" type="primary">验证表单</Button>
      <div class="data-display">
        <strong>表单数据：</strong>
        <pre>{{ JSON.stringify(validationFormData, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>3. 不同格式测试</h3>
      <DynamicForm
        ref="formatForm"
        :mode="'create'"
        :form-data="formatFormData"
        :form-config="formatFormConfig"
        @change="handleFormatChange"
      />
      <div class="data-display">
        <strong>表单数据：</strong>
        <pre>{{ JSON.stringify(formatFormData, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES } from '../types'

export default {
  name: 'DateTimePickerTest',
  data() {
    return {
      // 基础测试数据
      basicFormData: {},
      basicFormConfig: {
        title: '基础 DATE_TIME_PICKER 测试',
        columns: 1,
        fields: [
          {
            key: 'basicDateTime',
            label: '基础日期时间',
            type: FIELD_TYPES.DATE_TIME_PICKER,
            span: 24,
            props: {
              placeholder: '请选择日期时间'
            }
          }
        ]
      },

      // 验证测试数据
      validationFormData: {},
      validationFormConfig: {
        title: '验证 DATE_TIME_PICKER 测试',
        columns: 1,
        fields: [
          {
            key: 'requiredDateTime',
            label: '必填日期时间',
            type: FIELD_TYPES.DATE_TIME_PICKER,
            required: true,
            span: 24,
            props: {
              placeholder: '请选择日期时间（必填）'
            }
          }
        ]
      },

      // 格式测试数据
      formatFormData: {},
      formatFormConfig: {
        title: '格式 DATE_TIME_PICKER 测试',
        columns: 2,
        fields: [
          {
            key: 'fullDateTime',
            label: '完整格式',
            type: FIELD_TYPES.DATE_TIME_PICKER,
            span: 12,
            props: {
              placeholder: '完整日期时间',
              format: 'yyyy-MM-dd HH:mm:ss'
            }
          },
          {
            key: 'shortDateTime',
            label: '简短格式',
            type: FIELD_TYPES.DATE_TIME_PICKER,
            span: 12,
            props: {
              placeholder: '简短日期时间',
              format: 'yyyy-MM-dd HH:mm'
            }
          }
        ]
      }
    }
  },
  methods: {
    handleBasicChange(key, value, formData) {
      console.log('基础测试变化:', { key, value, formData })
    },

    handleValidationChange(key, value, formData) {
      console.log('验证测试变化:', { key, value, formData })
    },

    handleFormatChange(key, value, formData) {
      console.log('格式测试变化:', { key, value, formData })
    },

    validateForm() {
      this.$refs.validationForm.validate((valid) => {
        if (valid) {
          this.$Message.success('验证通过！')
        } else {
          this.$Message.error('验证失败！')
        }
      })
    }
  }
}
</script>

<style scoped>
.date-time-picker-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.data-display {
  margin-top: 15px;
  padding: 10px;
  background: #f8f8f9;
  border-radius: 4px;
}

.data-display pre {
  margin: 5px 0 0 0;
  font-size: 12px;
  line-height: 1.4;
}
</style>
