<template>
  <div class="input-change-test">
    <h2>输入框变化事件测试</h2>

    <DynamicForm
      ref="testForm"
      v-model="formData"
      :config="formConfig"
      @field-change="handleFieldChange"
    />

    <div class="test-info">
      <h3>变化日志：</h3>
      <div class="change-log">
        <div v-for="(log, index) in changeLog" :key="index" class="log-item">
          {{ log }}
        </div>
        <div v-if="changeLog.length === 0" class="no-log">
          暂无变化记录，请在输入框中输入内容...
        </div>
      </div>

      <h3>当前表单数据：</h3>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FIELD_TYPES } from '../types'

export default {
  name: 'InputChangeTest',
  components: {
    DynamicForm
  },
  data() {
    return {
      formData: {},
      changeLog: [],
      formConfig: [
        {
          title: '输入类组件测试',
          columns: 2,
          fields: [
            {
              key: 'textInput',
              label: '文本输入框',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入文本'
              }
            },
            {
              key: 'numberInput',
              label: '数字输入框',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入数字',
                type: 'number'
              }
            },
            {
              key: 'cd4tlbz',
              label: 'CD4T淋巴值',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入CD4T淋巴值',
                type: 'number'
              }
            },
            {
              key: 'viralLoadValue',
              label: '病毒载量值',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入病毒载量值',
                type: 'number'
              }
            },
            {
              key: 'textareaInput',
              label: '文本域',
              type: FIELD_TYPES.TEXTAREA,
              span: 24,
              props: {
                placeholder: '请输入多行文本',
                rows: 3
              }
            },
            {
              key: 'numberComponent',
              label: '数字组件',
              type: FIELD_TYPES.NUMBER,
              span: 12,
              props: {
                placeholder: '请输入数字',
                min: 0,
                max: 1000
              }
            }
          ]
        },
        {
          title: '选择类组件测试',
          columns: 2,
          fields: [
            {
              key: 'selectField',
              label: '下拉选择',
              type: FIELD_TYPES.SELECT,
              span: 12,
              props: {
                placeholder: '请选择'
              },
              options: [
                { label: '选项1', value: '1' },
                { label: '选项2', value: '2' },
                { label: '选项3', value: '3' }
              ]
            },
            {
              key: 'radioField',
              label: '单选框',
              type: FIELD_TYPES.RADIO,
              span: 12,
              options: [
                { label: '男', value: 'male' },
                { label: '女', value: 'female' }
              ]
            },
            {
              key: 'checkboxField',
              label: '多选框',
              type: FIELD_TYPES.CHECKBOX,
              span: 12,
              options: [
                { label: '爱好1', value: 'hobby1' },
                { label: '爱好2', value: 'hobby2' },
                { label: '爱好3', value: 'hobby3' }
              ]
            },
            {
              key: 'switchField',
              label: '开关',
              type: FIELD_TYPES.SWITCH,
              span: 12
            }
          ]
        },
        {
          title: '日期时间组件测试',
          columns: 2,
          fields: [
            {
              key: 'dateField',
              label: '日期选择',
              type: FIELD_TYPES.DATE_PICKER,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'datetimeField',
              label: '日期时间选择',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              span: 12,
              props: {
                placeholder: '请选择日期时间',
                format: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            {
              key: 'timeField',
              label: '时间选择',
              type: FIELD_TYPES.TIME_PICKER,
              span: 12,
              props: {
                placeholder: '请选择时间',
                format: 'HH:mm:ss'
              }
            }
          ]
        },
        {
          title: '其他组件测试',
          columns: 2,
          fields: [
            {
              key: 'rateField',
              label: '评分',
              type: FIELD_TYPES.RATE,
              span: 12
            },
            {
              key: 'sliderField',
              label: '滑块',
              type: FIELD_TYPES.SLIDER,
              span: 12,
              props: {
                min: 0,
                max: 100
              }
            }
          ]
        }
      ]
    }
  },

  methods: {
    handleFieldChange(key, value, formData) {
      const timestamp = new Date().toLocaleTimeString()
      const logMessage = `[${timestamp}] 字段 "${key}" 变化: "${value}" (类型: ${typeof value})`

      console.log('字段变化事件触发:', key, value, typeof value)
      console.log('完整表单数据:', formData)

      this.changeLog.unshift(logMessage)

      // 限制日志条数
      if (this.changeLog.length > 20) {
        this.changeLog = this.changeLog.slice(0, 20)
      }
    },

    clearLog() {
      this.changeLog = []
    },

    resetForm() {
      this.$refs.testForm.resetFields()
      this.changeLog = []
    }
  }
}
</script>

<style scoped>
.input-change-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-info {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.change-log {
  max-height: 300px;
  overflow-y: auto;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
}

.log-item {
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.no-log {
  color: #999;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

pre {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  overflow-x: auto;
  font-size: 12px;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}

h3 {
  color: #666;
  margin: 15px 0 10px 0;
}
</style>
