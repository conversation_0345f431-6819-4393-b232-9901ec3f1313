<template>
  <div class="required-validation-test">
    <h2>必填项验证测试</h2>

    <DynamicForm
      ref="testForm"
      v-model="formData"
      :config="formConfig"
      :mode="'create'"
      :loading="false"
    />

    <div class="test-actions">
      <Button type="primary" @click="validateForm">验证表单</Button>
      <Button @click="resetForm">重置表单</Button>
    </div>

    <div class="test-data">
      <h3>表单数据：</h3>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </div>

    <div class="validation-result" v-if="validationResult">
      <h3>验证结果：</h3>
      <pre>{{ validationResult }}</pre>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES } from '../types'

export default {
  name: 'RequiredValidationTest',
  data() {
    return {
      formData: {
        requiredInput: '',
        requiredSelect: '',
        requiredDict: '',
        requiredDate: '',
        optionalInput: '',
        optionalSelect: ''
      },
      validationResult: null
    }
  },
  computed: {
    formConfig() {
      return [
        {
          title: '必填项验证测试',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          fields: [
            {
              key: 'requiredInput',
              label: '必填输入框',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入内容（必填）'
              }
            },
            {
              key: 'requiredSelect',
              label: '必填下拉选择',
              type: FIELD_TYPES.SELECT,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择（必填）'
              },
              options: [
                { value: '1', label: '选项1' },
                { value: '2', label: '选项2' },
                { value: '3', label: '选项3' }
              ]
            },
            {
              key: 'requiredDict',
              label: '必填字典选择',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              span: 12,
              dicName: 'ZD_BHLB',
              props: {
                placeholder: '请选择传染病类型（必填）',
                multiple: false
              }
            },
            {
              key: 'requiredDate',
              label: '必填日期选择',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择日期时间（必填）',
                format: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            {
              key: 'optionalInput',
              label: '可选输入框',
              type: FIELD_TYPES.INPUT,
              required: false,
              span: 12,
              props: {
                placeholder: '请输入内容（可选）'
              }
            },
            {
              key: 'optionalSelect',
              label: '可选下拉选择',
              type: FIELD_TYPES.SELECT,
              required: false,
              span: 12,
              props: {
                placeholder: '请选择（可选）'
              },
              options: [
                { value: 'a', label: '选项A' },
                { value: 'b', label: '选项B' },
                { value: 'c', label: '选项C' }
              ]
            }
          ]
        }
      ]
    }
  },
  methods: {
    validateForm() {
      this.$refs.testForm.validate().then(() => {
        this.validationResult = '✅ 表单验证通过！'
        this.$Message.success('表单验证通过')
      }).catch((errors) => {
        console.log('验证错误详情:', errors)
        this.validationResult = `❌ 表单验证失败：\n${JSON.stringify(errors, null, 2)}`
        this.$Message.error('表单验证失败，请检查必填项')
      })
    },

    resetForm() {
      this.formData = {
        requiredInput: '',
        requiredSelect: '',
        requiredDict: '',
        requiredDate: '',
        optionalInput: '',
        optionalSelect: ''
      }
      this.validationResult = null
      this.$refs.testForm.resetFields()
    }
  }
}
</script>

<style lang="less" scoped>
.required-validation-test {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    color: #333;
  }

  .test-actions {
    margin: 20px 0;
    text-align: center;

    .ivu-btn {
      margin: 0 10px;
    }
  }

  .test-data,
  .validation-result {
    margin-top: 30px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 4px;

    h3 {
      margin-bottom: 10px;
      color: #333;
    }

    pre {
      background: #fff;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 12px;
      overflow-x: auto;
      line-height: 1.5;
    }
  }

  .validation-result {
    &.success {
      border-left: 4px solid #19be6b;
    }

    &.error {
      border-left: 4px solid #ed4014;
    }
  }
}
</style>
