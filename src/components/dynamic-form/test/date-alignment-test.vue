<template>
  <div class="date-alignment-test">
    <h2>日期组件对齐测试</h2>
    
    <DynamicForm
      v-model="formData"
      :config="formConfig"
      :mode="'create'"
      :loading="false"
    />
    
    <div class="test-data">
      <h3>表单数据：</h3>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES } from '../types'

export default {
  name: 'DateAlignmentTest',
  data() {
    return {
      formData: {
        normalInput: '',
        dateOnly: '',
        dateTime1: '',
        dateTime2: '',
        timeOnly: '',
        selectField: '',
        anotherInput: ''
      }
    }
  },
  computed: {
    formConfig() {
      return [
        {
          title: '日期组件对齐测试',
          columns: 2,
          labelWidth: 160,
          labelColon: true,
          fields: [
            {
              key: 'normalInput',
              label: '普通输入框',
              type: FIELD_TYPES.INPUT,
              span: 12,
              props: {
                placeholder: '请输入文本'
              }
            },
            {
              key: 'dateOnly',
              label: '日期选择器',
              type: FIELD_TYPES.DATE_PICKER,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'dateTime1',
              label: '日期时间选择器(DATETIME)',
              type: FIELD_TYPES.DATETIME_PICKER,
              span: 12,
              props: {
                placeholder: '请选择日期时间',
                format: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            {
              key: 'dateTime2',
              label: '日期时间选择器(DATE_TIME)',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              span: 12,
              props: {
                placeholder: '请选择日期时间',
                format: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            {
              key: 'timeOnly',
              label: '时间选择器',
              type: FIELD_TYPES.TIME_PICKER,
              span: 12,
              props: {
                placeholder: '请选择时间',
                format: 'HH:mm:ss'
              }
            },
            {
              key: 'selectField',
              label: '下拉选择框',
              type: FIELD_TYPES.SELECT,
              span: 12,
              props: {
                placeholder: '请选择'
              },
              options: [
                { value: '1', label: '选项1' },
                { value: '2', label: '选项2' },
                { value: '3', label: '选项3' }
              ]
            },
            {
              key: 'anotherInput',
              label: '另一个输入框',
              type: FIELD_TYPES.INPUT,
              span: 12,
              props: {
                placeholder: '请输入文本'
              }
            }
          ]
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.date-alignment-test {
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
    color: #333;
  }
  
  .test-data {
    margin-top: 30px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 4px;
    
    h3 {
      margin-bottom: 10px;
      color: #333;
    }
    
    pre {
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-size: 12px;
      overflow-x: auto;
    }
  }
}
</style>
