<template>
  <div class="array-config-test">
    <h1>DynamicForm 数组配置测试</h1>
    
    <div class="test-section">
      <h2>测试1: 基础数组配置</h2>
      <DynamicForm
        v-model="testData1"
        :config="testConfig1"
        :mode="mode"
        @field-change="handleFieldChange"
      />
      
      <div class="data-display">
        <h3>表单数据:</h3>
        <pre>{{ JSON.stringify(testData1, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>测试2: 多节配置（参考 /docs/config.json）</h2>
      <DynamicForm
        v-model="testData2"
        :config="testConfig2"
        :mode="mode"
        @field-change="handleFieldChange"
      />
      
      <div class="data-display">
        <h3>表单数据:</h3>
        <pre>{{ JSON.stringify(testData2, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="controls">
      <h3>控制面板</h3>
      <Button @click="mode = 'create'" :type="mode === 'create' ? 'primary' : 'default'">创建模式</Button>
      <Button @click="mode = 'edit'" :type="mode === 'edit' ? 'primary' : 'default'">编辑模式</Button>
      <Button @click="mode = 'view'" :type="mode === 'view' ? 'primary' : 'default'">查看模式</Button>
      <Button @click="resetData" type="warning">重置数据</Button>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES } from '../types'

export default {
  name: 'ArrayConfigTest',
  data() {
    return {
      mode: 'create',
      testData1: {},
      testData2: {},
      
      // 测试配置1: 简单的数组配置
      testConfig1: [
        {
          title: '用户信息',
          columns: 2,
          labelWidth: 100,
          fields: [
            {
              key: 'name',
              label: '姓名',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入姓名'
              }
            },
            {
              key: 'age',
              label: '年龄',
              type: FIELD_TYPES.NUMBER,
              required: true,
              span: 12,
              props: {
                min: 0,
                max: 120
              }
            }
          ]
        }
      ],
      
      // 测试配置2: 完整的多节配置（参考 /docs/config.json）
      testConfig2: [
        {
          title: '业务登记信息',
          columns: 2,
          labelWidth: 120,
          labelColon: true,
          fields: [
            {
              key: 'registrationDate',
              label: '登记日期',
              type: FIELD_TYPES.DATE_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择登记日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'abnormalType',
              label: '精神异常类型',
              type: FIELD_TYPES.SELECT,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择异常类型'
              },
              options: [
                { value: 'depression', label: '抑郁症状' },
                { value: 'anxiety', label: '焦虑症状' },
                { value: 'psychosis', label: '精神病性症状' },
                { value: 'bipolar', label: '双相情感障碍' },
                { value: 'other', label: '其他' }
              ]
            },
            {
              key: 'discoveryTime',
              label: '发现时间',
              type: FIELD_TYPES.DATETIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择发现时间',
                format: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            {
              key: 'discoveryLocation',
              label: '发现地点',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入发现地点'
              }
            },
            {
              key: 'severityLevel',
              label: '严重程度',
              type: FIELD_TYPES.RADIO,
              required: true,
              span: 24,
              options: [
                { value: 'mild', label: '轻微' },
                { value: 'moderate', label: '中等' },
                { value: 'severe', label: '严重' }
              ]
            },
            {
              key: 'needMedicalIntervention',
              label: '是否需要医疗干预',
              type: FIELD_TYPES.SWITCH,
              span: 12,
              props: {
                trueValue: true,
                falseValue: false
              }
            },
            {
              key: 'abnormalManifestations',
              label: '异常表现',
              type: FIELD_TYPES.TEXTAREA,
              required: true,
              span: 24,
              props: {
                placeholder: '请详细描述异常表现',
                autosize: { minRows: 3, maxRows: 5 }
              }
            }
          ]
        },
        {
          title: '经办信息',
          columns: 2,
          labelWidth: 120,
          labelColon: true,
          showTitle: false, // 测试隐藏标题
          fields: [
            {
              key: 'handler',
              label: '经办人',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入经办人'
              }
            },
            {
              key: 'handlingTime',
              label: '经办时间',
              type: FIELD_TYPES.DATETIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择经办时间',
                format: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            {
              key: 'handlingDepartment',
              label: '经办部门',
              type: FIELD_TYPES.SELECT,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择经办部门'
              },
              options: [
                { value: 'security', label: '安全科' },
                { value: 'medical', label: '医务科' },
                { value: 'education', label: '教育科' },
                { value: 'management', label: '管理科' }
              ]
            },
            {
              key: 'processingStatus',
              label: '处理状态',
              type: FIELD_TYPES.SELECT,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择处理状态'
              },
              options: [
                { value: '待处理', label: '待处理' },
                { value: '处理中', label: '处理中' },
                { value: '已处理', label: '已处理' }
              ]
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
    },
    
    resetData() {
      this.testData1 = {}
      this.testData2 = {}
    }
  }
}
</script>

<style scoped>
.array-config-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  padding: 20px;
}

.test-section h2 {
  margin-top: 0;
  color: #2b5fda;
}

.data-display {
  margin-top: 20px;
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
}

.data-display h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.data-display pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  max-height: 200px;
  overflow-y: auto;
}

.controls {
  text-align: center;
  padding: 20px;
  background: #f8f8f9;
  border-radius: 4px;
}

.controls .ivu-btn {
  margin: 0 5px;
}
</style>
