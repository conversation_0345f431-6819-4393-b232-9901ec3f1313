# 动态表单组件 (DynamicForm)

一个基于 Vue 2 + View Design (iView) 的通用动态表单组件，支持通过配置驱动表单渲染，提供丰富的字段类型和灵活的布局选项。

## 特性

- 🚀 **配置驱动**: 通过JSON配置快速生成表单
- 📱 **响应式布局**: 支持1-4列自适应布局
- 🎨 **多种字段类型**: 支持input、select、date-picker等常用组件
- 🔧 **三种模式**: 新增、编辑、查看模式切换
- ✅ **表单验证**: 内置常用验证规则，支持自定义验证
- 📋 **分组支持**: 支持表单分组和折叠功能
- 🎯 **字段依赖**: 支持字段间的依赖关系
- 🔌 **高度可扩展**: 支持自定义组件和渲染函数
- 🏗️ **BSP Layout集成**: 完美配合项目现有的布局组件系统
- 🎛️ **内置提交**: 强大的内置提交功能，大幅简化提交逻辑

## 安装使用

### 1. 引入组件

```javascript
// 全局注册
import DynamicForm from '@/components/dynamic-form'
Vue.component('DynamicForm', DynamicForm)

// 或者局部引入
import DynamicForm from '@/components/dynamic-form/DynamicForm.vue'

export default {
  components: {
    DynamicForm
  }
}
```

### 2. 基础用法

#### 新的数组配置格式（推荐）

```vue
<template>
  <!-- 独立使用 -->
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    :mode="mode"
    @field-change="handleFieldChange"
  />

  <!-- 与BSP Layout集成使用（推荐） -->
  <EasyLayout :actions="actions" @action="handleAction">
    <template #right>
      <EasyCard title="表单标题">
        <DynamicForm
          ref="dynamicForm"
          v-model="formData"
          :config="formConfig"
          :submit-config="submitConfig"
        />
      </EasyCard>
    </template>
  </EasyLayout>
</template>

<script>
import { FORM_MODES, FIELD_TYPES } from '@/components/dynamic-form/types'

export default {
  data() {
    return {
      mode: FORM_MODES.CREATE,
      formData: {},
      // 新的数组配置格式 - 支持多节配置
      formConfig: [
        {
          title: '基本信息',
          columns: 2,
          labelWidth: 120,
          labelColon: true,
          fields: [
            {
              key: 'name',
              label: '姓名',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入姓名'
              }
            },
            {
              key: 'email',
              label: '邮箱',
              type: FIELD_TYPES.INPUT,
              span: 12,
              rules: [
                { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }
              ]
            }
          ]
        },
        {
          title: '联系信息',
          columns: 2,
          labelWidth: 120,
          labelColon: true,
          showTitle: false, // 可以隐藏某个节的标题
          fields: [
            {
              key: 'phone',
              label: '手机号',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入手机号'
              }
            },
            {
              key: 'address',
              label: '地址',
              type: FIELD_TYPES.TEXTAREA,
              span: 24,
              props: {
                placeholder: '请输入详细地址'
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
    }
  }
}
</script>
```

#### 兼容旧的对象配置格式

```javascript
// 旧格式仍然支持，但推荐使用新的数组格式
formConfig: {
  title: '用户信息',
  columns: 2,
  fields: [
    {
      key: 'name',
      label: '姓名',
      type: FIELD_TYPES.INPUT,
      required: true
    }
  ]
}
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| config | 表单配置（支持数组和对象格式） | Array/Object | [] |
| value / v-model | 表单数据 | Object | {} |
| mode | 表单模式 | String | 'create' |
| generateActions | 是否生成BSP Layout操作按钮配置 | Boolean | false |
| forceShowActions | 是否强制显示内部操作按钮 | Boolean | false |
| actions | 自定义内部操作按钮 | Array | [] |
| disabled | 是否禁用表单 | Boolean | false |
| loading | 是否显示加载状态 | Boolean | false |
| submitConfig | 提交配置 | Object | {} |
| submitUrl | 提交URL | String | '' |
| submitMethod | 提交方法 | String | 'POST' |
| autoSubmit | 是否自动提交 | Boolean | false |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| input | 表单数据变化 | (formData) |
| field-change | 字段值变化 | (key, value, formData) |
| validate | 表单验证 | (prop, valid, message) |
| action | BSP Layout操作按钮点击 | (action) |
| layout-detected | 布局环境检测完成 | ({ detected, hasActions, shouldShowInternal }) |
| before-submit | 提交前触发 | (data, formInstance) |
| submit | 自定义提交处理 | (data, formInstance) |
| submit-success | 提交成功 | (result, data, formInstance) |
| submit-error | 提交失败 | (error, data, formInstance) |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| validate | 表单验证 | (callback) |
| validateField | 验证指定字段 | (prop, callback) |
| resetFields | 重置表单 | - |
| clearValidate | 清除验证 | (props) |
| getFormData | 获取表单数据 | - |
| setFormData | 设置表单数据 | (data) |
| setFieldValue | 设置字段值 | (key, value) |
| getFieldValue | 获取字段值 | (key) |
| submit | 触发表单提交 | - |

## 配置说明

### 新的数组配置格式（推荐）

DynamicForm 现在支持数组配置格式，可以更直观地定义多个表单节，避免了 config 和 sections 重复定义的问题。

```javascript
// 数组格式配置 - 每个元素代表一个表单节
[
  {
    title: '业务登记信息',      // 节标题
    showTitle: true,           // 是否显示标题（可选）
    columns: 2,                // 列数 (1-4)
    labelWidth: 120,           // 标签宽度
    labelPosition: 'right',    // 标签位置
    labelColon: true,          // 是否显示冒号
    collapsible: false,        // 是否可折叠
    collapsed: false,          // 默认是否折叠
    titleIcon: {               // 标题图标
      type: 'md-list-box',
      size: 24,
      color: '#2b5fda'
    },
    fields: [                  // 字段配置数组
      {
        key: 'registrationDate',
        label: '登记日期',
        type: 'date-picker',
        required: true,
        span: 12
      }
      // ... 更多字段
    ]
  },
  {
    title: '经办信息',
    showTitle: false,          // 不显示这个节的标题
    columns: 2,
    labelWidth: 120,
    fields: [
      {
        key: 'handler',
        label: '经办人',
        type: 'input',
        required: true,
        span: 12
      }
      // ... 更多字段
    ]
  }
]
```

### 兼容的对象配置格式

```javascript
// 旧的对象格式仍然支持
{
  title: '表单标题',           // 表单标题
  showTitle: true,            // 是否显示标题
  columns: 3,                 // 列数 (1-4)
  labelWidth: 130,            // 标签宽度
  labelPosition: 'right',     // 标签位置
  labelColon: true,           // 是否显示冒号
  titleIcon: {                // 标题图标
    type: 'md-list-box',
    size: 24,
    color: '#2b5fda'
  },
  fields: [],                 // 字段配置数组
  sections: []                // 分组配置数组（已废弃）
}
```

### 字段配置 (FieldConfig)

```javascript
{
  key: 'fieldName',           // 字段唯一标识
  label: '字段标签',          // 字段标签
  type: 'input',              // 字段类型
  span: 8,                    // 栅格占位 (1-24)
  offset: 0,                  // 栅格偏移
  required: false,            // 是否必填
  disabled: false,            // 是否禁用
  hidden: false,              // 是否隐藏
  defaultValue: '',           // 默认值
  placeholder: '请输入',      // 占位符
  props: {},                  // 组件属性
  events: {},                 // 组件事件
  rules: [],                  // 验证规则
  options: [],                // 选项数据
  style: {},                  // 自定义样式
  className: '',              // CSS类名
  help: '',                   // 帮助文本
  dependsOn: {},              // 依赖配置
  render: null                // 自定义渲染函数
}
```

## 支持的字段类型

| 类型 | 说明 | 对应组件 |
|------|------|----------|
| input | 输入框 | Input |
| textarea | 文本域 | Input (type="textarea") |
| select | 选择器 | Select |
| radio | 单选框 | RadioGroup |
| checkbox | 复选框 | CheckboxGroup |
| date-picker | 日期选择 | DatePicker |
| time-picker | 时间选择 | TimePicker |
| datetime-picker | 日期时间选择 | DatePicker (type="datetime") |
| date-time-picker | 日期时间选择 | DatePicker (type="datetime") |
| number | 数字输入 | InputNumber |
| switch | 开关 | Switch |
| rate | 评分 | Rate |
| slider | 滑块 | Slider |
| upload | 上传 | Upload |
| file-upload | 文件上传 | file-upload |
| dictionary | 字典选择 | s-dicgrid |
| custom | 自定义 | 自定义渲染 |

### DATE_TIME_PICKER 使用示例

```javascript
{
  key: 'appointmentTime',
  label: '预约时间',
  type: FIELD_TYPES.DATE_TIME_PICKER,
  required: true,
  span: 12,
  props: {
    placeholder: '请选择预约时间',
    format: 'yyyy-MM-dd HH:mm:ss',
    clearable: true
  }
}
```

### DICTIONARY 字典类型使用示例

字典类型是对 `s-dicgrid` 组件的封装，使用更加简洁：

```javascript
// 新的字典类型写法
{
  key: 'diseaseType',
  label: '传染病类型',
  type: FIELD_TYPES.DICTIONARY,
  required: true,
  span: 12,
  dicName: 'ZD_BHLB',  // 字典名称
  props: {
    placeholder: '请选择传染病类型',
    multiple: false,    // 是否多选，默认 false
    clearable: true     // 是否可清空，默认 true
  }
}

// 对比原来的写法（仍然支持）
{
  key: 'diseaseType',
  label: '传染病类型',
  type: FIELD_TYPES.CUSTOM,
  render: (h, value, formData, field) => {
    return h('s-dicgrid', {
      props: { value, multiple: false, dicName: 'ZD_BHLB' }
    })
  }
}
```

### FILE_UPLOAD 文件上传类型使用示例

文件上传类型是对 `file-upload` 组件的封装，支持多种文件类型上传：

```javascript
{
  key: 'attachments',
  label: '附件上传',
  type: FIELD_TYPES.FILE_UPLOAD,
  required: false,
  span: 24,  // 文件上传通常占满一行
  props: {
    // 核心属性（根据官方文档）
    accept: '.pdf,.doc,.docx,.xls,.xlsx', // 接受文件类型
    expireTime: '3600',      // 过期时间
    filePath: '',            // 文件路径：minio服务器中文件存储的具体位置
    serviceMark: 'DOC_SERVICE', // 服务标识
    bucketName: 'document-bucket', // 桶名
    multiple: true,          // 是否支持多选
    maxSize: 10 * 1024,      // 最大size (KB)
    maxFiles: 20,            // 最多文件
    defaultList: [],         // 已上传列表
    isDetail: false,         // 标识是否可以上传删除文件

    // 扩展属性（保持兼容）
    fileType: 'file',        // 文件类型
    fileTag: 'document',     // 文件标签
    searchFileType: 'all',   // 搜索文件类型
    showHistory: true,       // 是否显示历史文件
    showFiles: true,         // 是否显示文件列表
    businessId: 'BIZ001',    // 业务ID
    tableName: 'documents',  // 表名
    postData: {              // 额外的POST数据
      category: 'attachment'
    },
    delCallback: (fileId, file) => { // 删除回调函数
      console.log('删除文件:', fileId, file)
    },
    customBtn: {             // 自定义上传按钮
      type: 'primary',
      icon: 'md-add',
      text: '上传附件',
      color: '#fff',
      size: '20'
    }
  },
  events: {
    // 根据官方文档的事件名称
    'beforeUpload': (file) => { // 上传之前产生的回调
      console.log('准备上传:', file)
      return true // 返回 false 可阻止上传
    },
    'onExceededSize': () => { // 文件超出指定大小回调
      console.log('文件大小超出限制')
    },
    'onExceededFile': () => { // 超出文件最大数回调
      console.log('文件数量超出限制')
    },
    'fileRemove': (file) => { // 文件删除回调
      console.log('文件删除:', file)
    },
    'fileError': (error) => { // 文件上传失败回调
      console.log('文件上传失败:', error)
    },
    'fileSuccess': (file) => { // 单个文件上传成功回调
      console.log('文件上传成功:', file)
    },
    'fileComplete': (files) => { // 文件全部上传成功回调
      console.log('所有文件上传完成:', files)
    }
  }
}
```

#### 完整的使用示例（严格对应官方文档）

```javascript
// 严格按照官方使用手册配置
{
  key: 'meetingDocuments',
  label: '会议文档',
  type: FIELD_TYPES.FILE_UPLOAD,
  span: 24,
  props: {
    // 官方文档属性
    accept: '.pdf,.doc,.docx', // 接受文件类型
    expireTime: '3600', // 过期时间
    filePath: '/meeting/docs', // 文件路径：minio服务器中文件存储的具体位置
    serviceMark: 'MEETING_SERVICE', // 服务标识
    bucketName: 'meeting-bucket', // 桶名
    multiple: true, // 是否支持多选
    maxSize: 10 * 1024, // 最大size (KB)
    maxFiles: 20, // 最多文件
    defaultList: [], // 已上传列表
    isDetail: false // 标识是否可以上传删除文件
  },
  events: {
    // 官方文档事件
    'beforeUpload': (file) => { // 上传之前产生的回调
      console.log('准备上传:', file)
      return true
    },
    'onExceededSize': () => { // 文件超出指定大小回调
      console.log('文件大小超出限制')
    },
    'onExceededFile': () => { // 超出文件最大数回调
      console.log('文件数量超出限制')
    },
    'fileRemove': (file) => { // 文件删除回调
      console.log('文件删除:', file)
    },
    'fileError': (error) => { // 文件上传失败回调
      console.log('文件上传失败:', error)
    },
    'fileSuccess': (file) => { // 单个文件上传成功回调
      console.log('文件上传成功:', file)
    },
    'fileComplete': (files) => { // 文件全部上传成功回调
      console.log('所有文件上传完成:', files)
    }
  }
}
```

#### 对应您原始使用示例的配置

```html
<!-- 原始使用方式 -->
<file-upload
  :defaultList="meetingDocumentsUrl"
  :serviceMark="serviceMark"
  :bucketName="bucketName"
  :beforeUpload="beforeUpload"
  @fileSuccess="fileSuccessFile"
  @fileRemove="fileRemoveFile"
  @fileComplete="fileCompleteFileCertUrl" />
```

```javascript
// 在动态表单中的对应配置
{
  key: 'meetingDocuments',
  label: '会议文档',
  type: FIELD_TYPES.FILE_UPLOAD,
  span: 24,
  props: {
    defaultList: this.meetingDocumentsUrl, // 对应 :defaultList
    serviceMark: this.serviceMark, // 对应 :serviceMark
    bucketName: this.bucketName, // 对应 :bucketName
    // 其他属性...
  },
  events: {
    'beforeUpload': this.beforeUpload, // 对应 :beforeUpload
    'fileSuccess': this.fileSuccessFile, // 对应 @fileSuccess
    'fileRemove': this.fileRemoveFile, // 对应 @fileRemove
    'fileComplete': this.fileCompleteFileCertUrl // 对应 @fileComplete
  }
}
```

**字典类型的优势：**
- 配置更简洁，无需写 render 函数
- 统一的属性配置方式
- 更好的类型支持和代码提示
- 自动处理常用属性（placeholder、clearable 等）

## 表单模式

- **create**: 新增模式 - 所有字段可编辑
- **edit**: 编辑模式 - 所有字段可编辑
- **view**: 查看模式 - 所有字段只读显示

## 内置提交功能

动态表单组件提供了强大的内置提交功能，可以大幅简化表单提交的处理逻辑。

### 基础用法

```vue
<template>
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    :submit-config="submitConfig"
    @submit="handleSubmit"
    @submit-success="handleSuccess"
    @submit-error="handleError"
  />
</template>

<script>
export default {
  data() {
    return {
      formData: {},
      formConfig: { /* 表单配置 */ },
      submitConfig: {
        text: '提交',
        successMessage: '提交成功！',
        errorMessage: '提交失败，请重试'
      }
    }
  },
  methods: {
    handleSubmit(data, formInstance) {
      // 自定义提交逻辑
      console.log('提交数据:', data)
      return Promise.resolve({ success: true })
    },
    handleSuccess(result, data, formInstance) {
      console.log('提交成功:', result)
    },
    handleError(error, data, formInstance) {
      console.error('提交失败:', error)
    }
  }
}
</script>
```

### HTTP自动提交

配置 `submitUrl` 后，组件会自动发送HTTP请求：

```vue
<DynamicForm
  v-model="formData"
  :config="formConfig"
  :submit-config="submitConfig"
  submit-url="/api/user/create"
  submit-method="POST"
  @submit-success="handleSuccess"
/>
```

### 提交配置选项

```javascript
{
  text: '提交',                    // 按钮文本
  type: 'primary',                // 按钮类型
  showInActions: true,            // 是否显示在操作区域
  validateBeforeSubmit: true,     // 提交前是否验证
  successMessage: '提交成功',      // 成功提示
  errorMessage: '提交失败',        // 失败提示
  beforeSubmit: (data, form) => { // 提交前钩子
    // 返回false可阻止提交
    return true
  },
  transformData: (data) => {      // 数据转换
    return { ...data, timestamp: Date.now() }
  },
  afterSubmit: (result, data, form) => { // 提交后钩子
    // 提交完成后的处理
  }
}
```

### 重构示例：病号登记表单

**原代码（复杂的提交逻辑）：**
```javascript
handleSubmit() {
  this.$refs.formData.validate((valid) => {
    if (valid) {
      this.loading = true
      const submitData = { /* 构造数据 */ }

      this.$store.dispatch('authPostRequest', {
        url: this.$path.sick_create,
        params: submitData
      }).then((res) => {
        this.loading = false
        if (res.success) {
          this.$Notice.success({ /* 成功处理 */ })
          this.toback()
        } else {
          this.$Notice.error({ /* 错误处理 */ })
        }
      }).catch((error) => {
        this.loading = false
        this.$Notice.error({ /* 异常处理 */ })
      })
    }
  })
}
```

**重构后（使用内置提交）：**
```vue
<DynamicForm
  v-model="formData"
  :config="formConfig"
  :submit-config="{
    text: '提交',
    successMessage: '重病号登记成功',
    transformData: (data) => ({
      manageIdFk: data.manageIdFk,
      jgrybm: data.jgrybm,
      businessStatus: data.businessStatus || '1',
      severelySickType: data.severelySickType,
      patientSituation: data.patientSituation,
      treatmentSituation: data.treatmentSituation
    })
  }"
  submit-url="/api/sick/create"
  @submit-success="() => $emit('toback')"
/>
```

**优势对比：**
- 代码量减少 80%+
- 自动处理加载状态
- 统一的错误处理
- 内置数据转换
- 更好的可维护性

## BSP Layout 集成

动态表单组件专为与项目现有的 `bsp-layout` 组件系统集成而设计。**表单组件本身不包含操作按钮**，而是依赖布局组件的统一底部按钮系统。

### 设计理念

- ✅ **职责分离** - 表单负责数据处理，布局负责操作控制
- ✅ **统一体验** - 所有页面的操作按钮样式和行为一致
- ✅ **架构清晰** - 避免组件间的职责重叠
- ✅ **易于维护** - 按钮逻辑集中在布局组件中

### EasyLayout 集成

```vue
<template>
  <EasyLayout :actions="actions" @action="handleAction">
    <template #left>
      <!-- 左侧内容 -->
    </template>
    <template #right>
      <EasyCard title="业务登记">
        <DynamicForm
          ref="dynamicForm"
          v-model="formData"
          :config="formConfig"
          :submit-config="submitConfig"
        />
      </EasyCard>
    </template>
  </EasyLayout>
</template>

<script>
export default {
  computed: {
    actions() {
      return [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'submit', label: '提交', icon: 'ios-send', type: 'primary' }
      ]
    }
  },
  methods: {
    handleAction(action) {
      if (action.name === 'submit') {
        this.$refs.dynamicForm.submit()
      } else if (action.name === 'back') {
        this.$router.go(-1)
      }
    }
  }
}
</script>
```

### SimpleDetailCardLayout 集成

```vue
<template>
  <SimpleDetailCardLayout
    :actions="actions"
    :cards="cards"
    @action="handleAction"
  >
    <template #form-card>
      <DynamicForm ref="dynamicForm" v-model="formData" :config="formConfig" />
    </template>
  </SimpleDetailCardLayout>
</template>
```

### DetailCardLayout 集成

```vue
<template>
  <DetailCardLayout
    :bottom-actions="bottomActions"
    @bottom-action="handleBottomAction"
  >
    <template #form-card>
      <DynamicForm ref="dynamicForm" v-model="formData" :config="formConfig" />
    </template>
  </DetailCardLayout>
</template>

<script>
export default {
  methods: {
    handleBottomAction({ action }) {
      if (action.name === 'submit') {
        this.$refs.dynamicForm.submit()
      }
    }
  }
}
</script>
```

详细的集成指南请参考：[BSP Layout 集成指南](./BSP_LAYOUT_INTEGRATION.md)

## 简化设计

为了便于开发人员使用和排查问题，动态表单组件采用了**简化设计**：

### 移除的复杂功能
- ❌ 自动布局环境检测
- ❌ 复杂的样式兼容性检测
- ❌ DOM结构自动分析
- ❌ 复杂的按钮显示逻辑

### 简化后的优势
- ✅ **代码更清晰** - 移除了复杂的检测逻辑
- ✅ **调试更容易** - 问题定位更直接
- ✅ **性能更好** - 无复杂的DOM检测
- ✅ **维护成本低** - 代码结构简单明了

### 使用方式
```vue
<!-- 独立使用 -->
<DynamicForm v-model="data" :config="config" :actions="actions" />

<!-- 在布局中使用 -->
<DynamicForm v-model="data" :config="config" in-layout />
```

详细的简化使用指南请参考：[简化使用指南](./SIMPLE_USAGE_GUIDE.md)

## 操作按钮配置

动态表单组件的操作按钮**完全通过外部配置传入**，组件本身不生成任何默认按钮。这种设计确保了最大的灵活性和可控性。

### 设计原则

- ✅ **外部配置** - 所有操作按钮都通过 `actions` 属性传入
- ✅ **无默认按钮** - 组件不生成任何默认的操作按钮
- ✅ **完全可控** - 按钮的样式、行为、显示逻辑完全由外部控制
- ✅ **智能显示** - 根据布局环境智能判断是否显示内部按钮

### 基础用法

```vue
<template>
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    :actions="formActions"
    @action="handleAction"
  />
</template>

<script>
export default {
  data() {
    return {
      formActions: [
        {
          text: '重置',
          type: 'default',
          icon: 'ios-refresh',
          onClick: this.handleReset
        },
        {
          text: '提交',
          type: 'primary',
          icon: 'ios-send',
          onClick: this.handleSubmit
        }
      ]
    }
  },
  methods: {
    handleReset(formData, formInstance) {
      formInstance.resetFields()
    },
    handleSubmit(formData, formInstance) {
      formInstance.validate((valid) => {
        if (valid) {
          console.log('提交数据:', formData)
        }
      })
    }
  }
}
</script>
```

## 智能操作按钮

在外部配置按钮的基础上，组件具有智能的按钮显示机制，能够自动判断是否需要显示内部操作按钮。

### 智能判断逻辑

```
如果 (强制显示内部按钮) {
  显示内部按钮
} 否则如果 (未检测到布局环境) {
  显示内部按钮  // 独立使用
} 否则如果 (检测到布局环境 && 布局没有配置操作按钮) {
  显示内部按钮  // 布局无按钮，需要内部按钮
} 否则 {
  隐藏内部按钮  // 使用布局的操作按钮
}
```

### 使用场景

#### 1. 独立使用 - 显示外部配置的按钮
```vue
<DynamicForm
  v-model="data"
  :config="config"
  :actions="actions"
/>
<!-- ✅ 显示外部配置的操作按钮 -->
```

#### 2. 有按钮的布局 - 自动隐藏内部按钮
```vue
<EasyLayout :actions="actions" @action="handleAction">
  <template #right>
    <EasyCard title="表单">
      <DynamicForm v-model="data" :config="config" />
      <!-- ✅ 自动隐藏内部按钮，使用布局按钮 -->
    </EasyCard>
  </template>
</EasyLayout>
```

#### 3. 无按钮的布局 - 显示外部配置的按钮
```vue
<EasyLayout>
  <!-- 注意：没有配置 :actions 属性 -->
  <template #right>
    <EasyCard title="表单">
      <DynamicForm
        v-model="data"
        :config="config"
        :actions="actions"
      />
      <!-- ✅ 显示外部配置的按钮，因为布局没有按钮 -->
    </EasyCard>
  </template>
</EasyLayout>
```

#### 4. 强制显示内部按钮
```vue
<EasyLayout :actions="layoutActions">
  <template #right>
    <EasyCard title="表单">
      <DynamicForm
        v-model="data"
        :config="config"
        :actions="formActions"
        :force-show-actions="true"
      />
      <!-- ✅ 强制显示表单内部按钮，即使布局有按钮 -->
    </EasyCard>
  </template>
</EasyLayout>
```

### 自定义内部按钮

```vue
<DynamicForm
  v-model="data"
  :config="config"
  :actions="customActions"
  @action="handleAction"
/>

<script>
export default {
  data() {
    return {
      customActions: [
        {
          text: '重置',
          type: 'default',
          icon: 'ios-refresh',
          onClick: this.handleReset
        },
        {
          text: '保存草稿',
          type: 'warning',
          onClick: this.handleSaveDraft
        },
        {
          text: '提交审核',
          type: 'primary',
          onClick: this.handleSubmit
        }
      ]
    }
  }
}
</script>
```

### 布局检测事件

```vue
<DynamicForm
  @layout-detected="handleLayoutDetected"
/>

<script>
export default {
  methods: {
    handleLayoutDetected({ detected, hasActions, shouldShowInternal }) {
      console.log('布局环境:', detected)
      console.log('布局有按钮:', hasActions)
      console.log('显示内部按钮:', shouldShowInternal)
    }
  }
}
</script>
```

### 优势

- **智能化** - 自动判断是否需要显示操作按钮
- **灵活性** - 支持强制显示和自定义按钮
- **一致性** - 确保任何情况下都有可用的操作按钮
- **无缝集成** - 与BSP Layout系统完美配合

## 高级功能

### 1. 表单分组

```javascript
{
  sections: [
    {
      title: '基本信息',
      titleIcon: { type: 'md-person', size: 24, color: '#2b5fda' },
      collapsible: true,
      collapsed: false,
      fields: ['name', 'age', 'gender']
    }
  ]
}
```

### 2. 字段依赖

```javascript
{
  key: 'city',
  label: '城市',
  type: 'select',
  dependsOn: {
    key: 'province',      // 依赖字段
    value: 'guangdong',   // 依赖值
    action: 'show'        // 动作: show/hide/enable/disable/require
  }
}
```

### 3. 自定义渲染

```javascript
{
  key: 'custom',
  label: '自定义字段',
  type: 'custom',
  render: (h, value, formData, field) => {
    return h('div', '自定义内容')
  }
}
```

### 4. 自定义验证

```javascript
{
  rules: [
    {
      validator: (rule, value, callback) => {
        if (value < 18) {
          callback(new Error('年龄必须大于18岁'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}
```

## 样式定制

组件使用了项目现有的样式规范，主要样式类：

- `.dynamic-form` - 组件根容器
- `.bsp-base-form` - 表单容器
- `.fm-content-wrap` - 内容包装器
- `.fm-content-form` - 表单内容区域
- `.view-mode` - 查看模式样式

可以通过覆盖这些样式类来自定义外观。

## 示例

查看 `examples` 目录下的示例文件：

- `BasicExample.vue` - 基础用法示例
- `AdvancedExample.vue` - 高级功能示例
- `SickPersonForm.vue` - 重构的病号登记表单示例

## 兼容性

- Vue 2.6+
- View Design 4.6+
- 现代浏览器 (IE11+)

## 完整示例

### 病号登记表单重构示例

```vue
<template>
  <DynamicForm
    ref="sickForm"
    v-model="formData"
    :config="sickFormConfig"
    :mode="mode"
    :actions="actions"
    @field-change="handleFieldChange"
  />
</template>

<script>
import { FORM_MODES, FIELD_TYPES } from '@/components/dynamic-form/types'

export default {
  data() {
    return {
      mode: FORM_MODES.CREATE,
      formData: {
        severelySickType: '',
        patientSituation: '',
        treatmentSituation: ''
      },
      sickFormConfig: {
        title: '业务登记',
        columns: 1,
        labelWidth: 130,
        fields: [
          {
            key: 'severelySickType',
            label: '病号类别',
            type: FIELD_TYPES.SELECT,
            span: 16,
            required: true,
            options: [
              { label: '重病号', value: '1' },
              { label: '普通病号', value: '2' }
            ],
            props: {
              multiple: true,
              placeholder: '请选择病号类别'
            }
          },
          {
            key: 'patientSituation',
            label: '病情情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 16,
            required: true,
            props: {
              placeholder: '请输入病情情况',
              autosize: { minRows: 3, maxRows: 5 }
            }
          },
          {
            key: 'treatmentSituation',
            label: '治疗情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 16,
            required: true,
            props: {
              placeholder: '请输入治疗情况',
              autosize: { minRows: 3, maxRows: 5 }
            }
          }
        ]
      },
      actions: [
        {
          text: '返回',
          type: 'default',
          onClick: this.handleBack
        },
        {
          text: '提交',
          type: 'primary',
          onClick: this.handleSubmit
        }
      ]
    }
  },
  methods: {
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
    },

    handleBack() {
      this.$emit('toback')
    },

    handleSubmit(formData, formInstance) {
      formInstance.validate((valid) => {
        if (valid) {
          // 提交逻辑
          console.log('提交数据:', formInstance.getFormData())
        }
      })
    }
  }
}
</script>
```

### 多列布局示例

```javascript
// 3列布局配置
{
  columns: 3,
  fields: [
    { key: 'field1', label: '字段1', type: 'input', span: 8 },  // 占1列
    { key: 'field2', label: '字段2', type: 'input', span: 8 },  // 占1列
    { key: 'field3', label: '字段3', type: 'input', span: 8 },  // 占1列
    { key: 'field4', label: '字段4', type: 'textarea', span: 24 } // 占满行
  ]
}
```

### 复杂验证示例

```javascript
{
  key: 'password',
  label: '密码',
  type: 'input',
  props: { type: 'password' },
  rules: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20位', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
          callback(new Error('密码必须包含大小写字母和数字'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}
```

## 常见问题

### Q: 如何实现字段的动态显示/隐藏？

A: 使用 `dependsOn` 配置：

```javascript
{
  key: 'detailInfo',
  label: '详细信息',
  type: 'textarea',
  dependsOn: {
    key: 'needDetail',
    value: true,
    action: 'show'  // 当needDetail为true时显示
  }
}
```

### Q: 如何自定义字段组件？

A: 使用 `render` 函数：

```javascript
{
  key: 'customField',
  label: '自定义字段',
  type: 'custom',
  render: (h, value, formData, field) => {
    return h('MyCustomComponent', {
      props: { value },
      on: {
        input: field.events.change
      }
    })
  }
}
```

### Q: 如何处理异步数据加载？

A: 在组件的 `created` 或 `mounted` 钩子中加载数据：

```javascript
async created() {
  // 加载选项数据
  const options = await this.loadOptions()

  // 更新字段配置
  const field = this.formConfig.fields.find(f => f.key === 'category')
  if (field) {
    field.options = options
  }
}
```

## 只读/禁用状态优先级控制

动态表单组件支持多层级的只读/禁用状态控制，优先级从高到低如下：

1. **字段级别禁用** (`FieldConfig.disabled`) - 最高优先级
2. **字段级别只读** (`FieldConfig.readonly`) - 次高优先级
3. **节级别只读** (`FormSectionConfig.readonly`) - 中高优先级
4. **组件级别禁用** (`DynamicForm.disabled`) - 中等优先级
5. **查看模式** (`mode === 'view'`) - 最低优先级

### 优先级规则说明

- **字段级别 `disabled` 优先级最高**：如果字段配置中明确设置了 `disabled` 属性（无论是 `true` 还是 `false`），将直接使用该值，忽略其他所有设置
- **字段级别 `readonly` 次之**：如果字段没有设置 `disabled` 属性，但设置了 `readonly: true`，则该字段会被禁用
- **节级别 `readonly` 再次之**：如果字段没有设置 `disabled` 和 `readonly` 属性，但所在节设置了 `readonly: true`，则该节下所有字段都会被禁用
- **组件级别 `disabled` 再次之**：如果字段和节都没有设置相关属性，但组件设置了 `disabled: true`，则整个表单都会被禁用
- **查看模式最低**：在查看模式下，所有字段默认禁用，但可以被更高优先级的设置覆盖

### 使用示例

```javascript
// 示例配置
[
  {
    title: '基本信息',
    readonly: true,  // 节级别只读 - 影响本节所有字段
    fields: [
      {
        key: 'field1',
        label: '字段1',
        type: 'input',
        disabled: false  // 字段级别启用 - 优先级最高，覆盖节级别只读
      },
      {
        key: 'field2',
        label: '字段2',
        type: 'input'
        // 没有设置disabled或readonly，跟随节级别只读
      },
      {
        key: 'field3',
        label: '字段3',
        type: 'input',
        readonly: true  // 字段级别只读 - 会被禁用
      }
    ]
  },
  {
    title: '详细信息',
    // 没有设置readonly，跟随组件级别或模式设置
    fields: [
      {
        key: 'field4',
        label: '字段4',
        type: 'input',
        disabled: true  // 字段级别禁用 - 无论其他设置如何都会被禁用
      }
    ]
  }
]
```

```vue
<template>
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    :disabled="componentDisabled"  <!-- 组件级别禁用 -->
    :mode="formMode"              <!-- 查看模式禁用 -->
  />
</template>
```

在上述示例中：
- `field1` 会被启用（字段级别 `disabled: false` 优先级最高）
- `field2` 会被禁用（跟随节级别 `readonly: true`）
- `field3` 会被禁用（字段级别 `readonly: true`）
- `field4` 会被禁用（字段级别 `disabled: true` 优先级最高）

## 最佳实践

1. **合理使用列数**: 移动端建议1-2列，桌面端可使用3-4列
2. **统一验证规则**: 将常用验证规则提取为常量
3. **组件复用**: 将常用的字段配置提取为可复用的配置对象
4. **性能优化**: 大表单建议使用分组功能，避免一次渲染过多字段
5. **用户体验**: 合理使用帮助文本和占位符，提供清晰的操作指引
6. **只读控制**: 优先使用节级别的 `readonly` 控制整节只读，字段级别的 `disabled` 或 `readonly` 用于特殊情况覆盖

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础表单渲染
- 支持三种表单模式
- 支持表单验证和分组
- 支持字段依赖和自定义渲染
