/**
 * 表单迁移辅助工具
 * 帮助从现有表单代码迁移到动态表单组件
 */

import { FIELD_TYPES } from './types'

/**
 * 从现有表单数据和规则生成动态表单配置
 * @param {Object} formData - 现有表单数据
 * @param {Object} formRules - 现有表单验证规则
 * @param {Object} options - 配置选项
 * @returns {Object} 动态表单配置
 */
export function generateConfigFromExisting(formData, formRules = {}, options = {}) {
  const {
    title = '表单',
    columns = 2,
    labelWidth = 130,
    fieldMappings = {}
  } = options

  const fields = []

  Object.keys(formData).forEach(key => {
    const value = formData[key]
    const rules = formRules[key] || []
    const mapping = fieldMappings[key] || {}

    // 根据值类型推断字段类型
    let fieldType = inferFieldType(value, key, mapping)

    // 生成字段配置
    const fieldConfig = {
      key,
      label: mapping.label || formatLabel(key),
      type: mapping.type || fieldType,
      defaultValue: value,
      required: rules.some(rule => rule.required),
      rules: rules,
      ...mapping
    }

    // 处理特殊属性
    if (fieldConfig.type === FIELD_TYPES.SELECT && !fieldConfig.options) {
      fieldConfig.options = mapping.options || []
    }

    fields.push(fieldConfig)
  })

  return {
    title,
    columns,
    labelWidth,
    fields
  }
}

/**
 * 推断字段类型
 * @param {*} value - 字段值
 * @param {string} key - 字段键名
 * @param {Object} mapping - 字段映射配置
 * @returns {string} 字段类型
 */
function inferFieldType(value, key, mapping) {
  if (mapping.type) {
    return mapping.type
  }

  // 根据键名推断
  const keyLower = key.toLowerCase()

  if (keyLower.includes('email')) {
    return FIELD_TYPES.INPUT
  }

  if (keyLower.includes('phone') || keyLower.includes('mobile')) {
    return FIELD_TYPES.INPUT
  }

  if (keyLower.includes('datetime') || keyLower.includes('date_time') || keyLower.includes('date-time')) {
    return FIELD_TYPES.DATE_TIME_PICKER
  }

  if (keyLower.includes('date') || keyLower.includes('time')) {
    return FIELD_TYPES.DATE_PICKER
  }

  if (keyLower.includes('password')) {
    return FIELD_TYPES.INPUT
  }

  if (keyLower.includes('remark') || keyLower.includes('desc') || keyLower.includes('content')) {
    return FIELD_TYPES.TEXTAREA
  }

  // 根据值类型推断
  if (typeof value === 'number') {
    return FIELD_TYPES.NUMBER
  }

  if (typeof value === 'boolean') {
    return FIELD_TYPES.SWITCH
  }

  if (Array.isArray(value)) {
    return FIELD_TYPES.CHECKBOX
  }

  if (typeof value === 'string') {
    if (value.length > 100) {
      return FIELD_TYPES.TEXTAREA
    }
    return FIELD_TYPES.INPUT
  }

  return FIELD_TYPES.INPUT
}

/**
 * 格式化标签名
 * @param {string} key - 字段键名
 * @returns {string} 格式化后的标签
 */
function formatLabel(key) {
  // 常用字段名映射
  const labelMap = {
    name: '姓名',
    age: '年龄',
    gender: '性别',
    phone: '手机号',
    email: '邮箱',
    address: '地址',
    remark: '备注',
    description: '描述',
    title: '标题',
    content: '内容',
    status: '状态',
    type: '类型',
    category: '分类',
    date: '日期',
    time: '时间',
    createTime: '创建时间',
    updateTime: '更新时间'
  }

  if (labelMap[key]) {
    return labelMap[key]
  }

  // 驼峰转换
  return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
}

/**
 * 从iView表单规则转换为动态表单规则
 * @param {Array} iviewRules - iView表单规则
 * @returns {Array} 动态表单规则
 */
export function convertIViewRules(iviewRules) {
  if (!Array.isArray(iviewRules)) {
    return []
  }

  return iviewRules.map(rule => {
    const converted = { ...rule }

    // 处理触发时机
    if (!converted.trigger) {
      converted.trigger = 'blur'
    }

    return converted
  })
}

/**
 * 生成表单迁移代码
 * @param {Object} config - 动态表单配置
 * @returns {string} Vue组件代码
 */
export function generateMigrationCode(config) {
  const configStr = JSON.stringify(config, null, 2)

  return `<template>
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    :mode="mode"
    @field-change="handleFieldChange"
  />
</template>

<script>
import DynamicForm from '@/components/dynamic-form/DynamicForm.vue'
import { FORM_MODES } from '@/components/dynamic-form/types'

export default {
  components: {
    DynamicForm
  },
  data() {
    return {
      mode: FORM_MODES.CREATE,
      formData: {},
      formConfig: ${configStr}
    }
  },
  methods: {
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
    }
  }
}
</script>`
}

/**
 * 批量迁移表单字段
 * @param {Array} fields - 字段数组
 * @param {Object} options - 选项
 * @returns {Array} 动态表单字段配置
 */
export function batchMigrateFields(fields, options = {}) {
  const { defaultSpan = 8, defaultRequired = false } = options

  return fields.map(field => {
    if (typeof field === 'string') {
      // 简单字段名
      return {
        key: field,
        label: formatLabel(field),
        type: FIELD_TYPES.INPUT,
        span: defaultSpan,
        required: defaultRequired
      }
    }

    if (typeof field === 'object') {
      // 字段对象
      return {
        span: defaultSpan,
        required: defaultRequired,
        ...field,
        label: field.label || formatLabel(field.key),
        type: field.type || FIELD_TYPES.INPUT
      }
    }

    return field
  })
}

/**
 * 从HTML表单元素提取配置
 * @param {string} htmlString - HTML字符串
 * @returns {Array} 字段配置数组
 */
export function extractFromHTML(htmlString) {
  // 这是一个简化的实现，实际项目中可能需要更复杂的解析
  const fields = []

  // 匹配FormItem
  const formItemRegex = /<FormItem[^>]*label="([^"]*)"[^>]*prop="([^"]*)"[^>]*>/g
  let match

  while ((match = formItemRegex.exec(htmlString)) !== null) {
    const label = match[1]
    const prop = match[2]

    // 简单推断字段类型
    let type = FIELD_TYPES.INPUT
    if (htmlString.includes(`<Input[^>]*v-model="[^"]*${prop}[^"]*"[^>]*type="textarea"`)) {
      type = FIELD_TYPES.TEXTAREA
    } else if (htmlString.includes(`<Select[^>]*v-model="[^"]*${prop}[^"]*"`)) {
      type = FIELD_TYPES.SELECT
    } else if (htmlString.includes(`<InputNumber[^>]*v-model="[^"]*${prop}[^"]*"`)) {
      type = FIELD_TYPES.NUMBER
    }

    fields.push({
      key: prop,
      label: label,
      type: type
    })
  }

  return fields
}

/**
 * 生成迁移报告
 * @param {Object} oldConfig - 原配置
 * @param {Object} newConfig - 新配置
 * @returns {Object} 迁移报告
 */
export function generateMigrationReport(oldConfig, newConfig) {
  const report = {
    summary: {
      totalFields: newConfig.fields.length,
      migratedFields: 0,
      warnings: [],
      suggestions: []
    },
    details: []
  }

  newConfig.fields.forEach(field => {
    const detail = {
      key: field.key,
      label: field.label,
      type: field.type,
      status: 'success',
      notes: []
    }

    // 检查是否需要手动配置
    if (field.type === FIELD_TYPES.SELECT && (!field.options || field.options.length === 0)) {
      detail.status = 'warning'
      detail.notes.push('需要手动配置选项数据')
      report.summary.warnings.push(`${field.label}(${field.key}) 需要配置选项数据`)
    }

    if (field.type === FIELD_TYPES.CUSTOM) {
      detail.status = 'warning'
      detail.notes.push('需要实现自定义渲染函数')
      report.summary.warnings.push(`${field.label}(${field.key}) 需要实现自定义渲染`)
    }

    if (detail.status === 'success') {
      report.summary.migratedFields++
    }

    report.details.push(detail)
  })

  // 生成建议
  if (newConfig.fields.length > 6) {
    report.summary.suggestions.push('建议使用分组功能组织表单字段')
  }

  if (newConfig.columns > 2) {
    report.summary.suggestions.push('移动端建议使用1-2列布局')
  }

  return report
}
