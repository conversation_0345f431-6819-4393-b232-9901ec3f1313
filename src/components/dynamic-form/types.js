/**
 * 动态表单组件类型定义
 */

/**
 * 表单状态枚举
 */
export const FORM_MODES = {
  CREATE: 'create',    // 新增
  EDIT: 'edit',       // 编辑
  VIEW: 'view'        // 查看
}

/**
 * 支持的表单组件类型
 */
export const FIELD_TYPES = {
  INPUT: 'input',
  TEXTAREA: 'textarea',
  SELECT: 'select',
  RADIO: 'radio',
  CHECKBOX: 'checkbox',
  DATE_PICKER: 'date-picker',
  TIME_PICKER: 'time-picker',
  DATETIME_PICKER: 'datetime-picker',
  DATE_TIME_PICKER: 'date-time-picker',
  NUMBER: 'number',
  INPUT_NUMBER: 'input-number',
  SWITCH: 'switch',
  RATE: 'rate',
  SLIDER: 'slider',
  UPLOAD: 'upload',
  FILE_UPLOAD: 'file-upload',
  BSP_FILE_UPLOAD: 'bsp-file-upload',
  DICTIONARY: 'dictionary',
  CUSTOM: 'custom'
}

/**
 * 表单字段配置结构
 * @typedef {Object} FieldConfig
 * @property {string} key - 字段唯一标识
 * @property {string} label - 字段标签
 * @property {string} type - 字段类型，参考 FIELD_TYPES
 * @property {*} defaultValue - 默认值
 * @property {number} [span=8] - 栅格占位格数，默认8（3列布局）
 * @property {number} [offset=0] - 栅格左侧间隔格数
 * @property {Object} [props={}] - 组件属性
 * @property {Object} [events={}] - 组件事件
 * @property {Array} [rules=[]] - 验证规则
 * @property {boolean} [required=false] - 是否必填
 * @property {boolean} [disabled=false] - 是否禁用
 * @property {boolean} [readonly=false] - 是否只读
 * @property {boolean} [hidden=false] - 是否隐藏
 * @property {string} [placeholder] - 占位符
 * @property {Array} [options=[]] - 选项数据（用于select、radio、checkbox）
 * @property {Function} [render] - 自定义渲染函数
 * @property {Object} [style={}] - 自定义样式
 * @property {string} [className] - 自定义CSS类名
 * @property {string} [help] - 帮助文本
 * @property {Object} [dependsOn] - 依赖字段配置
 */

/**
 * 表单节配置结构（新的数组格式）
 * @typedef {Object} FormSectionConfig
 * @property {string} [title] - 节标题
 * @property {boolean} [showTitle=true] - 是否显示标题
 * @property {Object} [titleIcon] - 标题图标配置
 * @property {Array<FieldConfig>} fields - 字段配置数组
 * @property {number} [columns=3] - 表单列数，默认3列
 * @property {number} [labelWidth=130] - 标签宽度
 * @property {string} [labelPosition='right'] - 标签位置
 * @property {boolean} [labelColon=true] - 是否显示冒号
 * @property {Object} [formProps={}] - Form组件额外属性
 * @property {boolean} [collapsible=false] - 是否可折叠
 * @property {boolean} [collapsed=false] - 默认是否折叠
 * @property {boolean} [readonly=false] - 节级别只读控制
 */

/**
 * 表单配置结构（兼容旧格式）
 * @typedef {Object} FormConfig
 * @property {Array<FieldConfig>} fields - 字段配置数组
 * @property {number} [columns=3] - 表单列数，默认3列
 * @property {number} [labelWidth=130] - 标签宽度
 * @property {string} [labelPosition='right'] - 标签位置
 * @property {boolean} [labelColon=true] - 是否显示冒号
 * @property {Object} [formProps={}] - Form组件额外属性
 * @property {string} [title] - 表单标题
 * @property {boolean} [showTitle=true] - 是否显示标题
 * @property {Object} [titleIcon] - 标题图标配置
 * @property {Array} [sections=[]] - 分组配置（已废弃，使用数组格式）
 */

/**
 * 表单分组配置（已废弃，使用FormSectionConfig）
 * @typedef {Object} SectionConfig
 * @property {string} title - 分组标题
 * @property {Array<string>} fields - 包含的字段key数组
 * @property {boolean} [collapsible=false] - 是否可折叠
 * @property {boolean} [collapsed=false] - 默认是否折叠
 * @property {Object} [titleIcon] - 标题图标配置
 */

/**
 * 提交配置
 * @typedef {Object} SubmitConfig
 * @property {string} [text='提交'] - 提交按钮文本
 * @property {string} [type='primary'] - 按钮类型
 * @property {boolean} [loading=false] - 是否显示加载状态
 * @property {boolean} [showInActions=true] - 是否在操作按钮区域显示
 * @property {boolean} [validateBeforeSubmit=true] - 提交前是否验证
 * @property {string} [successMessage='提交成功'] - 成功提示消息
 * @property {string} [errorMessage='提交失败'] - 失败提示消息
 * @property {Function} [beforeSubmit] - 提交前钩子，返回false可阻止提交
 * @property {Function} [afterSubmit] - 提交后钩子
 * @property {Function} [transformData] - 数据转换函数
 */

/**
 * 操作按钮配置
 * @typedef {Object} ActionConfig
 * @property {string} text - 按钮文本
 * @property {string} [type='default'] - 按钮类型
 * @property {string} [icon] - 按钮图标
 * @property {boolean} [loading=false] - 是否显示加载状态
 * @property {boolean} [disabled=false] - 是否禁用
 * @property {Function} onClick - 点击处理函数
 * @property {Object} [props={}] - 按钮额外属性
 */

/**
 * 默认字段配置
 */
export const DEFAULT_FIELD_CONFIG = {
  span: 8,
  offset: 0,
  props: {},
  events: {},
  rules: [],
  required: false,
  disabled: false,
  readonly: false,
  hidden: false,
  style: {},
  className: ''
}

/**
 * 默认表单节配置
 */
export const DEFAULT_SECTION_CONFIG = {
  columns: 3,
  labelWidth: 130,
  labelPosition: 'right',
  labelColon: true,
  formProps: {},
  showTitle: true,
  titleIcon: {
    type: 'md-list-box',
    size: 24,
    color: '#2b5fda'
  },
  collapsible: false,
  collapsed: false,
  readonly: false,
  fields: []
}

/**
 * 默认表单配置（兼容旧格式）
 */
export const DEFAULT_FORM_CONFIG = {
  columns: 3,
  labelWidth: 130,
  labelPosition: 'right',
  labelColon: true,
  formProps: {},
  showTitle: true,
  titleIcon: {
    type: 'md-list-box',
    size: 24,
    color: '#2b5fda'
  },
  sections: []
}

/**
 * 常用验证规则
 * 注意：移除了trigger属性，校验只在手动调用validate时触发
 */
export const COMMON_RULES = {
  required: { required: true, message: '此项为必填项' },
  email: { type: 'email', message: '请输入正确的邮箱地址' },
  phone: { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
  idCard: { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号码' },
  number: { type: 'number', message: '请输入数字' },
  integer: { type: 'integer', message: '请输入整数' },
  url: { type: 'url', message: '请输入正确的URL地址' }
}

/**
 * 组件默认属性配置
 */
export const COMPONENT_DEFAULT_PROPS = {
  [FIELD_TYPES.INPUT]: {
    placeholder: '请输入',
    clearable: true
  },
  [FIELD_TYPES.TEXTAREA]: {
    placeholder: '请输入',
    autosize: { minRows: 3, maxRows: 5 }
  },
  [FIELD_TYPES.SELECT]: {
    placeholder: '请选择',
    clearable: true,
    filterable: true
  },
  [FIELD_TYPES.DATE_PICKER]: {
    placeholder: '请选择日期',
    clearable: true,
    format: 'yyyy-MM-dd'
  },
  [FIELD_TYPES.DATETIME_PICKER]: {
    placeholder: '请选择日期时间',
    clearable: true,
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  [FIELD_TYPES.DATE_TIME_PICKER]: {
    placeholder: '请选择日期时间',
    clearable: true,
    format: 'yyyy-MM-dd HH:mm:ss'
  },
  [FIELD_TYPES.TIME_PICKER]: {
    placeholder: '请选择时间',
    clearable: true,
    format: 'HH:mm:ss'
  },
  [FIELD_TYPES.NUMBER]: {
    placeholder: '请输入数字',
    min: 0
  },
  [FIELD_TYPES.INPUT_NUMBER]: {
    placeholder: '请输入数字',
    min: 0,
    style: { width: '100%' }
  },
  [FIELD_TYPES.DICTIONARY]: {
    placeholder: '请选择',
    clearable: true,
    multiple: false
  },
  [FIELD_TYPES.FILE_UPLOAD]: {

  }
}
