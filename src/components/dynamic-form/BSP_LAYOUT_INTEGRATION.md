# BSP Layout 集成指南

动态表单组件专为与项目现有的 `bsp-layout` 组件系统集成而设计，完美配合底部操作按钮系统。

## 🎯 设计理念

动态表单组件**不包含**自己的操作按钮，而是依赖 `bsp-layout` 组件的统一底部按钮系统，确保：

- ✅ **统一的用户体验** - 所有页面的操作按钮样式和行为一致
- ✅ **架构清晰** - 表单负责数据，布局负责操作
- ✅ **易于维护** - 按钮逻辑集中在布局组件中
- ✅ **无缝升级** - 可在不同布局组件间平滑迁移

## 📋 支持的布局组件

| 布局组件 | 按钮配置属性 | 事件名 | 状态 |
|----------|-------------|--------|------|
| EasyLayout | `actions` | `@action` | ✅ 完全支持 |
| SimpleDetailCardLayout | `actions` | `@action` | ✅ 完全支持 |
| DetailCardLayout | `bottom-actions` | `@bottom-action` | ✅ 完全支持 |

## 🚀 快速开始

### 1. EasyLayout 集成

```vue
<template>
  <EasyLayout 
    :actions="actions"
    @action="handleAction"
  >
    <template #left>
      <!-- 左侧内容 -->
    </template>
    
    <template #right>
      <EasyCard title="业务登记">
        <DynamicForm
          ref="dynamicForm"
          v-model="formData"
          :config="formConfig"
          :submit-config="submitConfig"
          @submit-success="handleSubmitSuccess"
        />
      </EasyCard>
    </template>
  </EasyLayout>
</template>

<script>
export default {
  data() {
    return {
      formData: {},
      formConfig: { /* 表单配置 */ },
      submitConfig: {
        text: '提交',
        successMessage: '提交成功！'
      }
    }
  },
  computed: {
    actions() {
      return [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'submit', label: '提交', icon: 'ios-send', type: 'primary' }
      ]
    }
  },
  methods: {
    handleAction(action) {
      if (action.name === 'submit') {
        this.$refs.dynamicForm.submit()
      } else if (action.name === 'back') {
        this.$router.go(-1)
      }
    },
    handleSubmitSuccess() {
      this.$router.go(-1)
    }
  }
}
</script>
```

### 2. SimpleDetailCardLayout 集成

```vue
<template>
  <SimpleDetailCardLayout
    left-title="病号信息"
    :data="patientData"
    :cards="cards"
    :actions="actions"
    @action="handleAction"
  >
    <template #left="{ data }">
      <!-- 左侧病号信息 -->
    </template>
    
    <template #form-card>
      <DynamicForm
        ref="dynamicForm"
        v-model="formData"
        :config="formConfig"
        :submit-config="submitConfig"
      />
    </template>
  </SimpleDetailCardLayout>
</template>
```

### 3. DetailCardLayout 集成

```vue
<template>
  <DetailCardLayout
    :left-config="leftConfig"
    :right-cards="rightCards"
    :bottom-actions="bottomActions"
    @bottom-action="handleBottomAction"
  >
    <template #form-card>
      <DynamicForm
        ref="dynamicForm"
        v-model="formData"
        :config="formConfig"
        :submit-config="submitConfig"
      />
    </template>
  </DetailCardLayout>
</template>

<script>
export default {
  computed: {
    bottomActions() {
      return [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'submit', label: '提交', icon: 'ios-send', type: 'primary' }
      ]
    }
  },
  methods: {
    handleBottomAction({ action }) {
      if (action.name === 'submit') {
        this.$refs.dynamicForm.submit()
      }
    }
  }
}
</script>
```

## 🎨 按钮配置规范

### 标准按钮配置

```javascript
// 返回按钮
{
  name: 'back',
  label: '返回',
  icon: 'ios-arrow-back',
  type: 'default'
}

// 取消按钮
{
  name: 'cancel',
  label: '取消',
  icon: 'ios-close',
  type: 'default'
}

// 提交按钮
{
  name: 'submit',
  label: '提交',
  icon: 'ios-send',
  type: 'primary'
}

// 保存按钮
{
  name: 'save',
  label: '保存',
  icon: 'ios-checkmark',
  type: 'primary'
}

// 审批按钮
{
  name: 'approve',
  label: '审批',
  icon: 'ios-checkmark-circle',
  type: 'primary'
}
```

### 动态按钮状态

```javascript
computed: {
  actions() {
    return [
      { name: 'back', label: '返回', icon: 'ios-arrow-back' },
      {
        name: 'submit',
        label: '提交',
        icon: 'ios-send',
        type: 'primary',
        loading: this.isSubmitting,    // 动态加载状态
        disabled: !this.formValid     // 动态禁用状态
      }
    ]
  }
}
```

## 🔧 表单提交集成

### 方式1：手动触发提交

```javascript
methods: {
  handleAction(action) {
    switch (action.name) {
      case 'submit':
        // 手动触发表单提交
        this.$refs.dynamicForm.submit()
        break
      case 'save':
        // 手动触发表单验证和保存
        this.$refs.dynamicForm.validate((valid) => {
          if (valid) {
            this.saveForm()
          }
        })
        break
    }
  }
}
```

### 方式2：自动提交集成

```javascript
// 在表单配置中设置自动提交
submitConfig: {
  text: '提交',
  successMessage: '提交成功！',
  transformData: (data) => ({
    ...data,
    timestamp: Date.now()
  })
}

// 按钮点击时自动触发提交
handleAction(action) {
  if (action.name === 'submit') {
    this.$refs.dynamicForm.submit()
  }
}
```

## 📱 响应式适配

所有布局组件的底部按钮都自动适配移动端：

```less
// 移动端自动适配
@media (max-width: 768px) {
  .bottom-actions {
    .actions-container {
      justify-content: center;
      flex-wrap: wrap;
      gap: 8px;
      
      .ivu-btn {
        flex: 1;
        min-width: 120px;
      }
    }
  }
}
```

## 🎯 最佳实践

### 1. 按钮命名规范

使用标准的按钮名称，确保在不同布局组件间的一致性：

```javascript
// ✅ 推荐的按钮名称
const STANDARD_ACTIONS = {
  BACK: 'back',
  CANCEL: 'cancel', 
  SAVE: 'save',
  SUBMIT: 'submit',
  APPROVE: 'approve',
  REJECT: 'reject',
  EDIT: 'edit',
  DELETE: 'delete'
}
```

### 2. 状态管理

```javascript
export default {
  data() {
    return {
      isSubmitting: false,
      formValid: false
    }
  },
  
  computed: {
    actions() {
      return [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        {
          name: 'submit',
          label: this.isSubmitting ? '提交中...' : '提交',
          icon: 'ios-send',
          type: 'primary',
          loading: this.isSubmitting,
          disabled: !this.formValid
        }
      ]
    }
  },
  
  methods: {
    async handleSubmit() {
      this.isSubmitting = true
      try {
        await this.$refs.dynamicForm.submit()
      } finally {
        this.isSubmitting = false
      }
    }
  }
}
```

### 3. 错误处理

```javascript
methods: {
  handleAction(action) {
    try {
      switch (action.name) {
        case 'submit':
          this.handleSubmit()
          break
        case 'back':
          this.handleBack()
          break
      }
    } catch (error) {
      console.error('操作失败:', error)
      this.$Message.error('操作失败，请重试')
    }
  }
}
```

## 🔄 无缝升级路径

在不同布局组件间迁移时，按钮配置保持一致：

```javascript
// 阶段1: EasyLayout
const actions = [
  { name: 'back', label: '返回', icon: 'ios-arrow-back' },
  { name: 'submit', label: '提交', icon: 'ios-send', type: 'primary' }
]

// 阶段2: SimpleDetailCardLayout (配置完全相同)
const actions = [
  { name: 'back', label: '返回', icon: 'ios-arrow-back' },
  { name: 'submit', label: '提交', icon: 'ios-send', type: 'primary' }
]

// 阶段3: DetailCardLayout (只需改属性名)
const bottomActions = [
  { name: 'back', label: '返回', icon: 'ios-arrow-back' },
  { name: 'submit', label: '提交', icon: 'ios-send', type: 'primary' }
]
```

## 📋 完整示例

查看完整的集成示例：

- **BspLayoutIntegrationExample.vue** - 三种布局组件的完整集成示例
- **病号登记表单** - 实际业务场景的集成案例
- **测试页面** - 可运行的演示和测试

## 🎉 总结

通过与 `bsp-layout` 组件系统的深度集成，动态表单组件实现了：

- **架构清晰** - 表单专注数据处理，布局负责操作控制
- **体验统一** - 所有页面的操作按钮保持一致
- **开发高效** - 减少重复代码，提升开发效率
- **维护简单** - 集中式的按钮管理和样式控制

这种设计确保了动态表单组件与项目现有架构的完美融合！
