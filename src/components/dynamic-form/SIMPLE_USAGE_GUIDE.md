# 动态表单组件 - 简化使用指南

## 🎯 设计理念

动态表单组件采用**简单直接**的设计理念，避免复杂的自动检测和兼容性处理，让开发人员能够清晰地控制组件行为。

## 🚀 基础用法

### 1. 独立使用
```vue
<template>
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    :actions="formActions"
    @action="handleAction"
  />
</template>

<script>
export default {
  data() {
    return {
      formData: {},
      formConfig: {
        title: '用户信息',
        fields: [
          { key: 'name', label: '姓名', type: 'input', required: true },
          { key: 'email', label: '邮箱', type: 'input' }
        ]
      },
      formActions: [
        { text: '重置', type: 'default', onClick: this.handleReset },
        { text: '提交', type: 'primary', onClick: this.handleSubmit }
      ]
    }
  }
}
</script>
```

### 2. 在布局组件中使用

#### 方式1：使用布局的操作按钮
```vue
<template>
  <EasyLayout :actions="layoutActions" @action="handleLayoutAction">
    <template #right>
      <EasyCard title="表单">
        <DynamicForm
          v-model="formData"
          :config="formConfig"
          in-layout
        />
      </EasyCard>
    </template>
  </EasyLayout>
</template>

<script>
export default {
  computed: {
    layoutActions() {
      return [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'submit', label: '提交', icon: 'ios-send', type: 'primary' }
      ]
    }
  },
  methods: {
    handleLayoutAction(action) {
      if (action.name === 'submit') {
        this.$refs.dynamicForm.submit()
      }
    }
  }
}
</script>
```

#### 方式2：使用表单内部按钮
```vue
<template>
  <EasyLayout>
    <template #right>
      <EasyCard title="表单">
        <DynamicForm
          v-model="formData"
          :config="formConfig"
          :actions="formActions"
          in-layout
          force-show-actions
        />
      </EasyCard>
    </template>
  </EasyLayout>
</template>
```

## 📋 Props 说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| config | Object | {} | 表单配置 |
| value/v-model | Object | {} | 表单数据 |
| mode | String | 'create' | 表单模式：create/edit/view |
| actions | Array | [] | 操作按钮配置 |
| forceShowActions | Boolean | false | 强制显示内部按钮 |
| in-layout | - | - | 标记表单在布局组件中使用 |

## 🎨 样式控制

### CSS 类名说明
- `.dynamic-form` - 根容器
- `.dynamic-form.in-layout` - 在布局中使用时的样式
- `.dynamic-form.force-show-actions` - 强制显示按钮时的样式

### 自定义样式
```vue
<template>
  <DynamicForm class="custom-form" />
</template>

<style scoped>
.custom-form {
  /* 自定义样式 */
}

.custom-form .dynamic-form-fields {
  padding: 20px;
  border-radius: 8px;
}
</style>
```

## 🔧 常见场景

### 场景1：独立表单页面
```vue
<DynamicForm
  v-model="formData"
  :config="formConfig"
  :actions="[
    { text: '重置', type: 'default', onClick: handleReset },
    { text: '提交', type: 'primary', onClick: handleSubmit }
  ]"
/>
```

### 场景2：弹窗中的表单
```vue
<Modal v-model="showModal" title="编辑信息">
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    in-layout
  />
  
  <template #footer>
    <Button @click="showModal = false">取消</Button>
    <Button type="primary" @click="handleSubmit">确定</Button>
  </template>
</Modal>
```

### 场景3：卡片中的表单
```vue
<Card title="用户信息">
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    :actions="formActions"
    in-layout
  />
</Card>
```

### 场景4：多步骤表单
```vue
<Steps :current="currentStep">
  <Step title="基本信息" />
  <Step title="详细信息" />
  <Step title="确认提交" />
</Steps>

<DynamicForm
  v-model="formData"
  :config="stepConfigs[currentStep]"
  :actions="stepActions"
/>
```

## 🎯 最佳实践

### 1. 明确使用场景
```javascript
// ✅ 推荐：明确标记使用场景
<DynamicForm in-layout />          // 在布局中使用
<DynamicForm />                    // 独立使用

// ❌ 避免：依赖自动检测
// 不再支持复杂的自动检测逻辑
```

### 2. 简单的按钮控制
```javascript
// ✅ 推荐：简单直接的按钮配置
const actions = [
  { text: '取消', type: 'default', onClick: this.handleCancel },
  { text: '保存', type: 'primary', onClick: this.handleSave }
]

// ❌ 避免：复杂的按钮逻辑
// 不再需要复杂的按钮检测和自动显示逻辑
```

### 3. 清晰的样式控制
```css
/* ✅ 推荐：使用明确的CSS类名 */
.dynamic-form.in-layout .dynamic-form-title {
  display: none;
}

/* ❌ 避免：复杂的嵌套选择器 */
/* 不再使用复杂的布局检测样式 */
```

## 🚨 注意事项

### 1. 移除的复杂功能
- ❌ 自动布局检测
- ❌ 复杂的样式兼容性检测
- ❌ DOM结构分析
- ❌ 自动按钮显示/隐藏

### 2. 简化后的优势
- ✅ 代码更简洁，易于理解
- ✅ 调试更容易，问题定位清晰
- ✅ 性能更好，无复杂检测逻辑
- ✅ 维护成本更低

### 3. 迁移指南
如果您之前使用了复杂的自动检测功能，请按以下方式迁移：

```javascript
// 旧方式（复杂）
<DynamicForm v-model="data" :config="config" />
// 依赖自动检测布局环境

// 新方式（简单）
<DynamicForm 
  v-model="data" 
  :config="config"
  :actions="actions"
  in-layout
/>
// 明确指定使用场景
```

## 📚 相关文档

- [完整API文档](./README.md)
- [快速开始指南](./QUICK_START.md)
- [外部配置按钮示例](./examples/ExternalActionsExample.vue)

## 🎉 总结

简化后的动态表单组件：
- **更简单** - 移除复杂的自动检测逻辑
- **更清晰** - 明确的使用方式和样式控制
- **更可靠** - 减少了潜在的bug和兼容性问题
- **更易维护** - 代码结构清晰，便于开发和调试

这种设计让开发人员能够更好地控制组件行为，同时保持了组件的强大功能！
