# 动态表单组件快速开始

## 5分钟快速上手

### 1. 引入组件

```javascript
import DynamicForm from '@/components/dynamic-form/DynamicForm.vue'
```

### 2. 基础使用

```vue
<template>
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    @field-change="handleChange"
  />
</template>

<script>
import { FIELD_TYPES } from '@/components/dynamic-form/types'

export default {
  data() {
    return {
      formData: {},
      formConfig: {
        title: '用户信息',
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true
          }
        ]
      }
    }
  },
  methods: {
    handleChange(key, value) {
      console.log('字段变化:', key, value)
    }
  }
}
</script>
```

### 3. 常用配置

#### 多列布局
```javascript
{
  columns: 3,  // 3列布局
  fields: [
    { key: 'field1', label: '字段1', type: 'input' },
    { key: 'field2', label: '字段2', type: 'input' },
    { key: 'field3', label: '字段3', type: 'input' }
  ]
}
```

#### 字段跨列
```javascript
{
  key: 'description',
  label: '描述',
  type: 'textarea',
  span: 24  // 占满整行
}
```

#### 必填验证
```javascript
{
  key: 'email',
  label: '邮箱',
  type: 'input',
  required: true,  // 简单必填
  rules: [         // 复杂验证
    { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }
  ]
}
```

#### 选择器选项
```javascript
{
  key: 'gender',
  label: '性别',
  type: 'select',
  options: [
    { label: '男', value: 'male' },
    { label: '女', value: 'female' }
  ]
}
```

### 4. 表单模式

```javascript
// 新增模式
mode: 'create'

// 编辑模式  
mode: 'edit'

// 查看模式（只读）
mode: 'view'
```

### 5. 操作按钮

```javascript
actions: [
  {
    text: '保存',
    type: 'primary',
    onClick: (formData, formInstance) => {
      formInstance.validate((valid) => {
        if (valid) {
          console.log('保存数据:', formData)
        }
      })
    }
  }
]
```

### 6. 表单分组

```javascript
{
  sections: [
    {
      title: '基本信息',
      fields: ['name', 'age', 'gender']
    },
    {
      title: '联系信息', 
      fields: ['phone', 'email', 'address']
    }
  ]
}
```

## 重构现有表单

### 原表单代码
```vue
<Form :model="formData" :rules="rules">
  <FormItem label="姓名" prop="name">
    <Input v-model="formData.name" placeholder="请输入姓名" />
  </FormItem>
  <FormItem label="年龄" prop="age">
    <InputNumber v-model="formData.age" :min="1" :max="120" />
  </FormItem>
</Form>
```

### 重构后代码
```vue
<DynamicForm
  v-model="formData"
  :config="{
    fields: [
      {
        key: 'name',
        label: '姓名',
        type: 'input',
        required: true,
        props: { placeholder: '请输入姓名' }
      },
      {
        key: 'age', 
        label: '年龄',
        type: 'number',
        props: { min: 1, max: 120 }
      }
    ]
  }"
/>
```

## 常用字段类型

| 类型 | 说明 | 示例 |
|------|------|------|
| input | 输入框 | `{ type: 'input' }` |
| textarea | 文本域 | `{ type: 'textarea' }` |
| select | 选择器 | `{ type: 'select', options: [...] }` |
| radio | 单选框 | `{ type: 'radio', options: [...] }` |
| checkbox | 复选框 | `{ type: 'checkbox', options: [...] }` |
| date-picker | 日期选择 | `{ type: 'date-picker' }` |
| number | 数字输入 | `{ type: 'number' }` |
| switch | 开关 | `{ type: 'switch' }` |

## 下一步

- 查看 [完整API文档](./README.md)
- 运行 [测试页面](../../view/test/dynamic-form-test.vue)
- 查看 [示例代码](./examples/)

## 技术支持

如有问题，请查看：
1. [README.md](./README.md) - 完整文档
2. [examples/](./examples/) - 示例代码
3. 项目内搜索 `DynamicForm` 查看使用案例
