<template>
  <div class="global-component-test">
    <h2>全局组件注册测试</h2>
    <p class="description">
      测试DynamicForm组件是否已成功注册为全局组件，可以在任何地方直接使用。
    </p>
    
    <Row :gutter="20">
      <Col span="12">
        <Card title="全局组件使用">
          <div class="test-info">
            <h4>✅ 全局组件注册成功！</h4>
            <p>现在可以在任何Vue组件中直接使用 <code>&lt;DynamicForm&gt;</code> 标签，无需import。</p>
          </div>
          
          <!-- 直接使用全局组件，无需import -->
          <DynamicForm
            v-model="formData"
            :config="formConfig"
            :actions="formActions"
            @action="handleAction"
          />
        </Card>
      </Col>
      
      <Col span="12">
        <Card title="全局属性和方法">
          <div class="global-methods">
            <h4>全局属性：</h4>
            <ul>
              <li><code>this.$FIELD_TYPES</code> - 字段类型常量</li>
              <li><code>this.$FORM_MODES</code> - 表单模式常量</li>
            </ul>
            
            <h4>全局方法：</h4>
            <ul>
              <li><code>this.$createFormConfig()</code> - 创建表单配置</li>
              <li><code>this.$generateFormData()</code> - 生成初始数据</li>
              <li><code>this.$formatOptions()</code> - 格式化选项</li>
              <li><code>this.$isEmpty()</code> - 检查值是否为空</li>
            </ul>
            
            <div class="test-buttons">
              <Button @click="testGlobalMethods" type="primary">测试全局方法</Button>
              <Button @click="testGlobalProperties" type="info">测试全局属性</Button>
            </div>
          </div>
        </Card>
      </Col>
    </Row>
    
    <!-- 使用全局方法创建的表单 -->
    <Card title="使用全局方法创建的表单" style="margin-top: 20px;">
      <div style="margin-bottom: 16px;">
        <Button @click="createQuickForm" type="success">使用全局方法创建快速表单</Button>
        <Button @click="createUserForm" type="warning">创建用户信息表单</Button>
        <Button @click="resetForm" type="default">重置表单</Button>
      </div>
      
      <DynamicForm
        v-if="globalMethodForm"
        v-model="globalMethodFormData"
        :config="globalMethodForm"
        :actions="globalMethodActions"
        @action="handleGlobalMethodAction"
      />
    </Card>
    
    <!-- 测试结果 -->
    <Card title="测试结果" style="margin-top: 20px;">
      <div class="test-results">
        <div v-for="(result, index) in testResults" :key="index" class="test-result">
          <Tag :color="result.success ? 'green' : 'red'">
            {{ result.success ? '✅' : '❌' }}
          </Tag>
          <span class="result-text">{{ result.message }}</span>
        </div>
      </div>
      <Button @click="clearResults" size="small" style="margin-top: 10px;">清空结果</Button>
    </Card>
    
    <!-- 代码示例 -->
    <Card title="使用示例" style="margin-top: 20px;">
      <Tabs value="template">
        <TabPane label="模板使用" name="template">
          <pre class="code-block">{{ templateCode }}</pre>
        </TabPane>
        
        <TabPane label="全局方法" name="methods">
          <pre class="code-block">{{ methodsCode }}</pre>
        </TabPane>
        
        <TabPane label="全局属性" name="properties">
          <pre class="code-block">{{ propertiesCode }}</pre>
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<script>
// 注意：这里没有import DynamicForm，因为它已经是全局组件了

export default {
  name: 'GlobalComponentTest',
  data() {
    return {
      formData: {
        name: '',
        email: '',
        phone: ''
      },
      
      formConfig: {
        title: '全局组件测试表单',
        columns: 2,
        showTitle: false,
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: 'input', // 可以直接使用字符串，因为有全局属性
            required: true,
            props: {
              placeholder: '请输入姓名'
            }
          },
          {
            key: 'email',
            label: '邮箱',
            type: 'input',
            rules: [
              { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入邮箱'
            }
          },
          {
            key: 'phone',
            label: '手机号',
            type: 'input',
            props: {
              placeholder: '请输入手机号'
            }
          }
        ]
      },
      
      formActions: [
        {
          text: '重置',
          type: 'default',
          onClick: this.handleReset
        },
        {
          text: '提交',
          type: 'primary',
          onClick: this.handleSubmit
        }
      ],
      
      // 全局方法创建的表单
      globalMethodForm: null,
      globalMethodFormData: {},
      globalMethodActions: [
        {
          text: '保存',
          type: 'primary',
          onClick: this.handleGlobalSave
        }
      ],
      
      // 测试结果
      testResults: []
    }
  },
  
  computed: {
    templateCode() {
      return `<!-- 直接使用全局组件，无需import -->
<template>
  <DynamicForm
    v-model="formData"
    :config="formConfig"
    :actions="formActions"
    @action="handleAction"
  />
</template>

<script>
export default {
  // 无需在components中注册DynamicForm
  data() {
    return {
      formData: {},
      formConfig: { /* 配置 */ },
      formActions: [ /* 按钮 */ ]
    }
  }
}
</script>`
    },
    
    methodsCode() {
      return `// 使用全局方法
export default {
  methods: {
    createForm() {
      // 创建表单配置
      const config = this.$createFormConfig([
        { key: 'name', label: '姓名', type: this.$FIELD_TYPES.INPUT }
      ])
      
      // 生成初始数据
      const data = this.$generateFormData(config)
      
      // 格式化选项
      const options = this.$formatOptions([
        { name: '选项1', id: 1 },
        { name: '选项2', id: 2 }
      ], 'name', 'id')
      
      // 检查值是否为空
      const isEmpty = this.$isEmpty(data.name)
    }
  }
}`
    },
    
    propertiesCode() {
      return `// 使用全局属性
export default {
  data() {
    return {
      formConfig: {
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: this.$FIELD_TYPES.INPUT  // 使用全局字段类型
          },
          {
            key: 'email',
            label: '邮箱',
            type: this.$FIELD_TYPES.INPUT
          }
        ]
      }
    }
  },
  
  computed: {
    currentMode() {
      return this.$FORM_MODES.CREATE  // 使用全局表单模式
    }
  }
}`
    }
  },
  
  methods: {
    // 测试全局方法
    testGlobalMethods() {
      try {
        // 测试 $createFormConfig
        const config = this.$createFormConfig([
          { key: 'test', label: '测试', type: this.$FIELD_TYPES.INPUT }
        ])
        this.addTestResult(true, '$createFormConfig 方法测试成功')
        
        // 测试 $generateFormData
        const data = this.$generateFormData(config)
        this.addTestResult(true, '$generateFormData 方法测试成功')
        
        // 测试 $formatOptions
        const options = this.$formatOptions([
          { name: '选项1', id: 1 },
          { name: '选项2', id: 2 }
        ], 'name', 'id')
        this.addTestResult(true, '$formatOptions 方法测试成功')
        
        // 测试 $isEmpty
        const isEmpty = this.$isEmpty('')
        this.addTestResult(true, '$isEmpty 方法测试成功')
        
      } catch (error) {
        this.addTestResult(false, `全局方法测试失败: ${error.message}`)
      }
    },
    
    // 测试全局属性
    testGlobalProperties() {
      try {
        // 测试 $FIELD_TYPES
        if (this.$FIELD_TYPES && this.$FIELD_TYPES.INPUT) {
          this.addTestResult(true, '$FIELD_TYPES 全局属性可用')
        } else {
          this.addTestResult(false, '$FIELD_TYPES 全局属性不可用')
        }
        
        // 测试 $FORM_MODES
        if (this.$FORM_MODES && this.$FORM_MODES.CREATE) {
          this.addTestResult(true, '$FORM_MODES 全局属性可用')
        } else {
          this.addTestResult(false, '$FORM_MODES 全局属性不可用')
        }
        
      } catch (error) {
        this.addTestResult(false, `全局属性测试失败: ${error.message}`)
      }
    },
    
    // 创建快速表单
    createQuickForm() {
      this.globalMethodForm = this.$createFormConfig([
        { key: 'title', label: '标题', type: this.$FIELD_TYPES.INPUT, required: true },
        { key: 'content', label: '内容', type: this.$FIELD_TYPES.TEXTAREA }
      ], {
        title: '快速表单',
        columns: 1
      })
      this.globalMethodFormData = this.$generateFormData(this.globalMethodForm)
      this.addTestResult(true, '使用全局方法创建快速表单成功')
    },
    
    // 创建用户表单
    createUserForm() {
      this.globalMethodForm = this.$createFormConfig([
        { key: 'username', label: '用户名', type: this.$FIELD_TYPES.INPUT, required: true },
        { key: 'password', label: '密码', type: this.$FIELD_TYPES.INPUT, props: { type: 'password' } },
        { key: 'role', label: '角色', type: this.$FIELD_TYPES.SELECT, options: [
          { label: '管理员', value: 'admin' },
          { label: '用户', value: 'user' }
        ]}
      ], {
        title: '用户信息表单',
        columns: 2
      })
      this.globalMethodFormData = this.$generateFormData(this.globalMethodForm)
      this.addTestResult(true, '使用全局方法创建用户表单成功')
    },
    
    // 重置表单
    resetForm() {
      this.globalMethodForm = null
      this.globalMethodFormData = {}
      this.addTestResult(true, '表单已重置')
    },
    
    // 处理操作
    handleAction(action) {
      this.addTestResult(true, `全局组件操作: ${action.text}`)
    },
    
    handleGlobalMethodAction(action) {
      this.addTestResult(true, `全局方法表单操作: ${action.text}`)
    },
    
    handleReset(formData, formInstance) {
      if (formInstance) {
        formInstance.resetFields()
      }
      this.addTestResult(true, '表单重置成功')
    },
    
    handleSubmit(formData, formInstance) {
      if (formInstance) {
        formInstance.validate((valid) => {
          if (valid) {
            this.addTestResult(true, '表单提交成功')
          } else {
            this.addTestResult(false, '表单验证失败')
          }
        })
      }
    },
    
    handleGlobalSave(formData, formInstance) {
      this.addTestResult(true, '全局方法表单保存成功')
    },
    
    // 添加测试结果
    addTestResult(success, message) {
      this.testResults.unshift({
        success,
        message,
        time: new Date().toLocaleTimeString()
      })
      
      if (this.testResults.length > 10) {
        this.testResults = this.testResults.slice(0, 10)
      }
    },
    
    // 清空结果
    clearResults() {
      this.testResults = []
    }
  }
}
</script>

<style scoped>
.global-component-test {
  padding: 20px;
}

.description {
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-left: 4px solid #28a745;
  color: #666;
  line-height: 1.6;
}

.test-info {
  margin-bottom: 16px;
  
  h4 {
    color: #28a745;
    margin-bottom: 8px;
  }
  
  p {
    color: #666;
    line-height: 1.6;
  }
  
  code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 12px;
  }
}

.global-methods {
  h4 {
    margin-bottom: 8px;
    color: #333;
  }
  
  ul {
    margin: 8px 0 16px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
      color: #666;
      font-size: 13px;
      
      code {
        background: #f1f1f1;
        padding: 2px 4px;
        border-radius: 2px;
        font-size: 11px;
      }
    }
  }
}

.test-buttons {
  margin-top: 16px;
  
  .ivu-btn {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}

.test-results {
  .test-result {
    display: flex;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
    
    .result-text {
      margin-left: 8px;
      font-size: 13px;
    }
  }
}

.code-block {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  margin: 0;
}
</style>
