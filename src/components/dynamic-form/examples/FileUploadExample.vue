<template>
  <div class="file-upload-example">
    <h2>文件上传组件示例</h2>

    <Tabs value="basic" type="card">
      <!-- 基础文件上传 -->
      <TabPane label="基础上传" name="basic">
        <Card title="基础文件上传示例">
          <p>基础的文件上传功能，支持多文件上传：</p>

          <DynamicForm
            ref="basicForm"
            v-model="basicFormData"
            :config="basicFormConfig"
            :mode="mode"
            @field-change="handleFieldChange"
          />

          <div style="margin-top: 20px;">
            <h4>表单数据：</h4>
            <pre class="data-display">{{ JSON.stringify(basicFormData, null, 2) }}</pre>
          </div>
        </Card>
      </TabPane>

      <!-- 限制文件类型 -->
      <TabPane label="限制类型" name="restricted">
        <Card title="限制文件类型示例">
          <p>限制只能上传特定类型的文件：</p>

          <DynamicForm
            ref="restrictedForm"
            v-model="restrictedFormData"
            :config="restrictedFormConfig"
            :mode="mode"
            @field-change="handleFieldChange"
          />
        </Card>
      </TabPane>

      <!-- 自定义配置 -->
      <TabPane label="自定义配置" name="custom">
        <Card title="自定义配置示例">
          <p>自定义按钮样式和上传限制：</p>

          <DynamicForm
            ref="customForm"
            v-model="customFormData"
            :config="customFormConfig"
            :mode="mode"
            @field-change="handleFieldChange"
          />
        </Card>
      </TabPane>
    </Tabs>

    <div style="margin-top: 20px;">
      <Button @click="toggleMode">
        切换模式: {{ mode === 'view' ? '查看' : '编辑' }}
      </Button>
    </div>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FORM_MODES, FIELD_TYPES } from '../types'

export default {
  name: 'FileUploadExample',
  components: {
    DynamicForm
  },
  data() {
    return {
      mode: FORM_MODES.EDIT,

      // 基础示例数据
      basicFormData: {
        title: '测试文档',
        description: '这是一个测试文档的描述',
        attachments: []
      },

      // 限制类型示例数据
      restrictedFormData: {
        projectName: '项目文档',
        documents: [],
        images: []
      },

      // 自定义配置示例数据
      customFormData: {
        reportTitle: '报告标题',
        reportFiles: []
      }
    }
  },
  computed: {
    // 基础文件上传配置
    basicFormConfig() {
      return [
        {
          title: '基础文件上传',
          fields: [
            {
              key: 'title',
              label: '标题',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入标题'
              }
            },
            {
              key: 'description',
              label: '描述',
              type: FIELD_TYPES.TEXTAREA,
              span: 12,
              props: {
                placeholder: '请输入描述',
                autosize: { minRows: 3, maxRows: 5 }
              }
            },
            {
              key: 'attachments',
              label: '附件上传',
              type: FIELD_TYPES.FILE_UPLOAD,
              span: 24,
              props: {
                multiple: true,
                maxFiles: 10,
                maxSize: 51200, // 50MB
                fileType: 'file',
                fileTag: 'attachment',
                showHistory: true,
                showFiles: true,
                businessId: 'TEST_BIZ_001',
                serviceMark: 'TEST_SERVICE',
                bucketName: 'test-bucket',
                defaultList: [],
                beforeUpload: (file) => {
                  console.log('准备上传文件:', file.name)
                  return true
                }
              },
              events: {
                'fileSuccess': this.handleFileSuccess,
                'fileRemove': this.handleFileRemove,
                'fileComplete': this.handleFileComplete
              }
            }
          ]
        }
      ]
    },

    // 限制文件类型配置
    restrictedFormConfig() {
      return [
        {
          title: '限制文件类型上传',
          fields: [
            {
              key: 'projectName',
              label: '项目名称',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 24,
              props: {
                placeholder: '请输入项目名称'
              }
            },
            {
              key: 'documents',
              label: '文档上传',
              type: FIELD_TYPES.FILE_UPLOAD,
              span: 12,
              props: {
                multiple: true,
                maxFiles: 5,
                maxSize: 20480, // 20MB
                accept: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx',
                fileType: 'document',
                fileTag: 'project-doc',
                customBtn: {
                  type: 'primary',
                  icon: 'md-document',
                  text: '上传文档',
                  color: '#fff',
                  size: '18'
                }
              }
            },
            {
              key: 'images',
              label: '图片上传',
              type: FIELD_TYPES.FILE_UPLOAD,
              span: 12,
              props: {
                multiple: true,
                maxFiles: 10,
                maxSize: 10240, // 10MB
                accept: '.jpg,.jpeg,.png,.gif,.bmp',
                fileType: 'image',
                fileTag: 'project-img',
                customBtn: {
                  type: 'success',
                  icon: 'md-images',
                  text: '上传图片',
                  color: '#fff',
                  size: '18'
                }
              }
            }
          ]
        }
      ]
    },

    // 自定义配置
    customFormConfig() {
      return [
        {
          title: '自定义文件上传配置',
          fields: [
            {
              key: 'reportTitle',
              label: '报告标题',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 24,
              props: {
                placeholder: '请输入报告标题'
              }
            },
            {
              key: 'reportFiles',
              label: '报告文件',
              type: FIELD_TYPES.FILE_UPLOAD,
              span: 24,
              props: {
                multiple: false, // 单文件上传
                maxFiles: 1,
                maxSize: 102400, // 100MB
                fileType: 'report',
                fileTag: 'monthly-report',
                accept: '.pdf,.doc,.docx',
                showHistory: false,
                customBtn: {
                  type: 'warning',
                  icon: 'md-cloud-upload',
                  text: '选择报告文件',
                  color: '#fff',
                  size: '20'
                }
              },
              events: {
                'file-success': (file) => {
                  this.$Message.success(`文件 ${file.name} 上传成功`)
                },
                'file-remove': (file) => {
                  this.$Message.info(`文件 ${file.name} 已删除`)
                }
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
    },

    handleFileSuccess(file) {
      console.log('文件上传成功:', file)
      this.$Message.success(`文件 ${file.name} 上传成功`)
    },

    handleFileRemove(file) {
      console.log('文件删除:', file)
      this.$Message.info(`文件已删除`)
    },

    handleFileComplete(files) {
      console.log('文件上传完成:', files)
      this.$Message.success('所有文件上传完成')
    },

    toggleMode() {
      this.mode = this.mode === FORM_MODES.VIEW ? FORM_MODES.EDIT : FORM_MODES.VIEW
    }
  }
}
</script>

<style scoped>
.file-upload-example {
  padding: 20px;
}

.data-display {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
