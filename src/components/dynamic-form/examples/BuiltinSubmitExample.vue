<template>
  <div class="builtin-submit-example">
    <h2>内置提交功能示例</h2>
    
    <Tabs value="basic" type="card">
      <!-- 基础内置提交 -->
      <TabPane label="基础提交" name="basic">
        <Card title="基础内置提交示例">
          <p>使用内置的提交功能，只需要配置submitConfig即可：</p>
          
          <DynamicForm
            ref="basicForm"
            v-model="basicFormData"
            :config="basicFormConfig"
            :submit-config="basicSubmitConfig"
            @submit="handleBasicSubmit"
            @submit-success="handleSubmitSuccess"
            @submit-error="handleSubmitError"
          />
          
          <div style="margin-top: 20px;">
            <h4>表单数据：</h4>
            <pre class="data-display">{{ JSON.stringify(basicFormData, null, 2) }}</pre>
          </div>
        </Card>
      </TabPane>
      
      <!-- HTTP提交 -->
      <TabPane label="HTTP提交" name="http">
        <Card title="HTTP提交示例">
          <p>配置submitUrl后，组件会自动发送HTTP请求：</p>
          
          <DynamicForm
            ref="httpForm"
            v-model="httpFormData"
            :config="httpFormConfig"
            :submit-config="httpSubmitConfig"
            submit-url="/api/user/create"
            submit-method="POST"
            @before-submit="handleBeforeSubmit"
            @submit-success="handleSubmitSuccess"
            @submit-error="handleSubmitError"
          />
        </Card>
      </TabPane>
      
      <!-- 高级配置 -->
      <TabPane label="高级配置" name="advanced">
        <Card title="高级提交配置示例">
          <p>使用钩子函数和数据转换：</p>
          
          <DynamicForm
            ref="advancedForm"
            v-model="advancedFormData"
            :config="advancedFormConfig"
            :submit-config="advancedSubmitConfig"
            @submit="handleAdvancedSubmit"
            @submit-success="handleSubmitSuccess"
            @submit-error="handleSubmitError"
          />
        </Card>
      </TabPane>
      
      <!-- 病号登记重构 -->
      <TabPane label="病号登记" name="sick">
        <Card title="重构后的病号登记表单">
          <p>使用内置提交功能重构的病号登记表单：</p>
          
          <DynamicForm
            ref="sickForm"
            v-model="sickFormData"
            :config="sickFormConfig"
            :submit-config="sickSubmitConfig"
            submit-url="/api/sick/create"
            @before-submit="handleSickBeforeSubmit"
            @submit-success="handleSickSuccess"
            @submit-error="handleSickError"
          />
        </Card>
      </TabPane>
    </Tabs>
    
    <!-- 提交日志 -->
    <Card title="提交日志" style="margin-top: 20px;">
      <div class="submit-logs">
        <div v-for="(log, index) in submitLogs" :key="index" class="log-item">
          <Tag :color="log.type === 'success' ? 'green' : log.type === 'error' ? 'red' : 'blue'">
            {{ log.type }}
          </Tag>
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <Button @click="clearLogs" size="small" style="margin-top: 10px;">清空日志</Button>
    </Card>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FIELD_TYPES } from '../types'

export default {
  name: 'BuiltinSubmitExample',
  components: {
    DynamicForm
  },
  data() {
    return {
      // 基础提交示例
      basicFormData: {},
      basicFormConfig: {
        title: '用户注册',
        columns: 2,
        fields: [
          {
            key: 'username',
            label: '用户名',
            type: FIELD_TYPES.INPUT,
            required: true,
            props: {
              placeholder: '请输入用户名'
            }
          },
          {
            key: 'email',
            label: '邮箱',
            type: FIELD_TYPES.INPUT,
            required: true,
            rules: [
              { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入邮箱'
            }
          },
          {
            key: 'phone',
            label: '手机号',
            type: FIELD_TYPES.INPUT,
            rules: [
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入手机号'
            }
          },
          {
            key: 'remark',
            label: '备注',
            type: FIELD_TYPES.TEXTAREA,
            span: 24,
            props: {
              placeholder: '请输入备注信息',
              autosize: { minRows: 3, maxRows: 5 }
            }
          }
        ]
      },
      basicSubmitConfig: {
        text: '注册',
        successMessage: '注册成功！',
        errorMessage: '注册失败，请重试'
      },
      
      // HTTP提交示例
      httpFormData: {},
      httpFormConfig: {
        title: '用户信息',
        columns: 2,
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true
          },
          {
            key: 'age',
            label: '年龄',
            type: FIELD_TYPES.NUMBER,
            props: { min: 1, max: 120 }
          }
        ]
      },
      httpSubmitConfig: {
        text: '保存用户',
        successMessage: '用户信息保存成功！',
        transformData: (data) => {
          // 数据转换示例
          return {
            ...data,
            timestamp: new Date().toISOString()
          }
        }
      },
      
      // 高级配置示例
      advancedFormData: {},
      advancedFormConfig: {
        title: '高级表单',
        columns: 2,
        fields: [
          {
            key: 'title',
            label: '标题',
            type: FIELD_TYPES.INPUT,
            required: true
          },
          {
            key: 'priority',
            label: '优先级',
            type: FIELD_TYPES.SELECT,
            options: [
              { label: '低', value: 'low' },
              { label: '中', value: 'medium' },
              { label: '高', value: 'high' }
            ]
          }
        ]
      },
      advancedSubmitConfig: {
        text: '提交任务',
        validateBeforeSubmit: true,
        beforeSubmit: (formData, formInstance) => {
          // 提交前验证
          if (!formData.title || formData.title.length < 3) {
            this.$Message.warning('标题至少需要3个字符')
            return false
          }
          return true
        },
        transformData: (data, originalData, formInstance) => {
          // 数据转换
          return {
            ...data,
            createdAt: new Date().toISOString(),
            status: 'pending'
          }
        },
        afterSubmit: (result, submitData, formInstance) => {
          // 提交后处理
          this.addLog('info', '提交后处理完成')
          // 可以在这里做一些清理工作
          setTimeout(() => {
            formInstance.resetFields()
          }, 2000)
        }
      },
      
      // 病号登记示例
      sickFormData: {
        jgrybm: 'test123',
        manageIdFk: 'test456'
      },
      sickFormConfig: {
        title: '业务登记',
        columns: 1,
        fields: [
          {
            key: 'severelySickType',
            label: '病号类别',
            type: FIELD_TYPES.SELECT,
            span: 16,
            required: true,
            options: [
              { label: '重病号', value: '1' },
              { label: '普通病号', value: '2' },
              { label: '慢性病号', value: '3' }
            ],
            props: {
              multiple: true,
              placeholder: '请选择病号类别'
            }
          },
          {
            key: 'patientSituation',
            label: '病情情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 16,
            required: true,
            props: {
              placeholder: '请输入病情情况',
              autosize: { minRows: 3, maxRows: 5 }
            }
          },
          {
            key: 'treatmentSituation',
            label: '治疗情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 16,
            required: true,
            props: {
              placeholder: '请输入治疗情况',
              autosize: { minRows: 3, maxRows: 5 }
            }
          }
        ]
      },
      sickSubmitConfig: {
        text: '提交登记',
        successMessage: '重病号登记成功',
        errorMessage: '重病号登记失败',
        transformData: (data) => {
          // 构造提交数据，与原来的逻辑保持一致
          return {
            manageIdFk: data.manageIdFk,
            prisonId: data.prisonId,
            jgrybm: data.jgrybm,
            businessStatus: data.businessStatus || '1',
            severelySickType: data.severelySickType,
            patientSituation: data.patientSituation,
            treatmentSituation: data.treatmentSituation
          }
        }
      },
      
      // 提交日志
      submitLogs: []
    }
  },
  methods: {
    // 基础提交处理
    handleBasicSubmit(data, formInstance) {
      this.addLog('info', `基础提交: ${JSON.stringify(data)}`)
      
      // 模拟异步处理
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true, id: Date.now() })
        }, 1000)
      })
    },
    
    // HTTP提交前处理
    handleBeforeSubmit(data, formInstance) {
      this.addLog('info', `HTTP提交前: ${JSON.stringify(data)}`)
    },
    
    // 高级提交处理
    handleAdvancedSubmit(data, formInstance) {
      this.addLog('info', `高级提交: ${JSON.stringify(data)}`)
      
      // 模拟可能失败的情况
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (Math.random() > 0.3) {
            resolve({ success: true, taskId: Date.now() })
          } else {
            reject(new Error('模拟的网络错误'))
          }
        }, 1500)
      })
    },
    
    // 病号登记提交前处理
    handleSickBeforeSubmit(data, formInstance) {
      this.addLog('info', `病号登记提交前: ${JSON.stringify(data)}`)
    },
    
    // 病号登记成功处理
    handleSickSuccess(result, data, formInstance) {
      this.addLog('success', '病号登记成功，准备返回列表页面')
      // 这里可以触发路由跳转等操作
      // this.$emit('toback')
    },
    
    // 病号登记失败处理
    handleSickError(error, data, formInstance) {
      this.addLog('error', `病号登记失败: ${error.message || '未知错误'}`)
    },
    
    // 提交成功处理
    handleSubmitSuccess(result, data, formInstance) {
      this.addLog('success', `提交成功: ${JSON.stringify(result)}`)
    },
    
    // 提交失败处理
    handleSubmitError(error, data, formInstance) {
      this.addLog('error', `提交失败: ${error.message || '未知错误'}`)
    },
    
    // 添加日志
    addLog(type, message) {
      this.submitLogs.unshift({
        type,
        message,
        time: new Date().toLocaleTimeString()
      })
      
      // 限制日志数量
      if (this.submitLogs.length > 20) {
        this.submitLogs = this.submitLogs.slice(0, 20)
      }
    },
    
    // 清空日志
    clearLogs() {
      this.submitLogs = []
    }
  }
}
</script>

<style scoped>
.builtin-submit-example {
  padding: 20px;
}

.data-display {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.submit-logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  margin: 0 10px;
  color: #999;
  font-size: 12px;
  min-width: 80px;
}

.log-message {
  flex: 1;
  font-size: 13px;
}

/deep/ .ivu-tabs-content {
  background: white;
  border-radius: 4px;
}
</style>
