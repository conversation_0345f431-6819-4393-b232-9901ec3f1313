<template>
  <div class="date-time-picker-example">
    <h2>DATE_TIME_PICKER 类型示例</h2>
    
    <DynamicForm
      ref="dynamicForm"
      :mode="mode"
      :form-data="formData"
      :form-config="formConfig"
      @submit="handleSubmit"
      @change="handleChange"
    />
    
    <div class="example-actions">
      <Button @click="switchMode('create')" :type="mode === 'create' ? 'primary' : 'default'">
        新增模式
      </Button>
      <Button @click="switchMode('edit')" :type="mode === 'edit' ? 'primary' : 'default'">
        编辑模式
      </Button>
      <Button @click="switchMode('view')" :type="mode === 'view' ? 'primary' : 'default'">
        查看模式
      </Button>
      <Button @click="resetForm" style="margin-left: 10px;">重置表单</Button>
    </div>
    
    <div class="form-data-display">
      <h3>表单数据：</h3>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES } from '../types'

export default {
  name: 'DateTimePickerExample',
  data() {
    return {
      mode: 'create',
      formData: {},
      formConfig: {
        title: 'DATE_TIME_PICKER 类型测试',
        columns: 2,
        labelWidth: 150,
        labelColon: true,
        fields: [
          {
            key: 'appointmentTime',
            label: '预约时间',
            type: FIELD_TYPES.DATE_TIME_PICKER,
            required: true,
            span: 12,
            props: {
              placeholder: '请选择预约时间',
              format: 'yyyy-MM-dd HH:mm:ss'
            }
          },
          {
            key: 'startTime',
            label: '开始时间',
            type: FIELD_TYPES.DATE_TIME_PICKER,
            required: true,
            span: 12,
            props: {
              placeholder: '请选择开始时间',
              format: 'yyyy-MM-dd HH:mm'
            }
          },
          {
            key: 'endTime',
            label: '结束时间',
            type: FIELD_TYPES.DATE_TIME_PICKER,
            span: 12,
            props: {
              placeholder: '请选择结束时间',
              format: 'yyyy-MM-dd HH:mm'
            }
          },
          {
            key: 'reminderTime',
            label: '提醒时间',
            type: FIELD_TYPES.DATE_TIME_PICKER,
            span: 12,
            props: {
              placeholder: '请选择提醒时间',
              format: 'yyyy-MM-dd HH:mm:ss',
              clearable: true
            }
          },
          {
            key: 'description',
            label: '描述',
            type: FIELD_TYPES.TEXTAREA,
            span: 24,
            props: {
              placeholder: '请输入描述信息',
              autosize: { minRows: 3, maxRows: 5 }
            }
          }
        ]
      }
    }
  },
  methods: {
    handleSubmit(formData) {
      console.log('表单提交:', formData)
      this.$Message.success('表单提交成功！')
    },
    
    handleChange(key, value, formData) {
      console.log('字段变化:', { key, value, formData })
    },
    
    switchMode(newMode) {
      this.mode = newMode
      console.log('切换模式:', newMode)
    },
    
    resetForm() {
      this.formData = {}
      this.$Message.info('表单已重置')
    }
  }
}
</script>

<style scoped>
.date-time-picker-example {
  padding: 20px;
}

.example-actions {
  margin: 20px 0;
  padding: 15px;
  background: #f8f8f9;
  border-radius: 4px;
}

.form-data-display {
  margin-top: 20px;
  padding: 15px;
  background: #f8f8f9;
  border-radius: 4px;
}

.form-data-display h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.form-data-display pre {
  background: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
}
</style>
