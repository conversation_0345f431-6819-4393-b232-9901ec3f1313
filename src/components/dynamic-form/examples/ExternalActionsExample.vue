<template>
  <div class="external-actions-example">
    <h2>外部配置按钮示例</h2>
    <p class="description">
      动态表单组件的操作按钮完全通过外部配置传入，组件本身不生成任何默认按钮。
    </p>
    
    <Tabs value="basic" type="card">
      <!-- 基础配置 -->
      <TabPane label="基础配置" name="basic">
        <Row :gutter="20">
          <Col span="12">
            <Card title="表单配置">
              <DynamicForm
                ref="basicForm"
                v-model="basicFormData"
                :config="basicFormConfig"
                :actions="basicActions"
                @action="handleBasicAction"
              />
            </Card>
          </Col>
          
          <Col span="12">
            <Card title="按钮配置代码">
              <pre class="code-block">{{ basicActionsCode }}</pre>
            </Card>
          </Col>
        </Row>
      </TabPane>
      
      <!-- 复杂配置 -->
      <TabPane label="复杂配置" name="complex">
        <Row :gutter="20">
          <Col span="12">
            <Card title="表单配置">
              <DynamicForm
                ref="complexForm"
                v-model="complexFormData"
                :config="complexFormConfig"
                :actions="complexActions"
                @action="handleComplexAction"
              />
            </Card>
          </Col>
          
          <Col span="12">
            <Card title="按钮配置代码">
              <pre class="code-block">{{ complexActionsCode }}</pre>
            </Card>
          </Col>
        </Row>
      </TabPane>
      
      <!-- 动态按钮 -->
      <TabPane label="动态按钮" name="dynamic">
        <Row :gutter="20">
          <Col span="12">
            <Card title="表单配置">
              <div style="margin-bottom: 16px;">
                <label>当前模式：</label>
                <RadioGroup v-model="currentMode" @on-change="updateDynamicActions">
                  <Radio label="create">新增</Radio>
                  <Radio label="edit">编辑</Radio>
                  <Radio label="view">查看</Radio>
                </RadioGroup>
              </div>
              
              <DynamicForm
                ref="dynamicForm"
                v-model="dynamicFormData"
                :config="dynamicFormConfig"
                :mode="currentMode"
                :actions="dynamicActions"
                @action="handleDynamicAction"
              />
            </Card>
          </Col>
          
          <Col span="12">
            <Card title="动态按钮逻辑">
              <div class="dynamic-info">
                <h4>当前模式：{{ currentMode }}</h4>
                <h4>按钮配置：</h4>
                <ul>
                  <li v-for="action in dynamicActions" :key="action.text">
                    {{ action.text }} ({{ action.type }})
                  </li>
                </ul>
              </div>
              <pre class="code-block">{{ dynamicActionsCode }}</pre>
            </Card>
          </Col>
        </Row>
      </TabPane>
      
      <!-- 无按钮配置 -->
      <TabPane label="无按钮" name="no-actions">
        <Row :gutter="20">
          <Col span="12">
            <Card title="没有配置按钮的表单">
              <DynamicForm
                ref="noActionsForm"
                v-model="noActionsFormData"
                :config="noActionsFormConfig"
              />
              <!-- 注意：没有传入 :actions 属性 -->
            </Card>
          </Col>
          
          <Col span="12">
            <Card title="说明">
              <div class="no-actions-info">
                <h4>没有配置按钮</h4>
                <p>当不传入 <code>:actions</code> 属性时，表单不会显示任何操作按钮。</p>
                <p>这种情况下，操作按钮应该由外部布局组件提供。</p>
                
                <h4>代码示例：</h4>
                <pre class="code-block">&lt;DynamicForm
  v-model="formData"
  :config="formConfig"
  <!-- 没有 :actions 属性 -->
/&gt;</pre>
              </div>
            </Card>
          </Col>
        </Row>
      </TabPane>
    </Tabs>
    
    <!-- 操作日志 -->
    <Card title="操作日志" style="margin-top: 20px;">
      <div class="action-logs">
        <div v-for="(log, index) in actionLogs" :key="index" class="log-item">
          <Tag :color="log.type === 'success' ? 'green' : log.type === 'error' ? 'red' : 'blue'">
            {{ log.source }}
          </Tag>
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <Button @click="clearLogs" size="small" style="margin-top: 10px;">清空日志</Button>
    </Card>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FIELD_TYPES, FORM_MODES } from '../types'

export default {
  name: 'ExternalActionsExample',
  components: {
    DynamicForm
  },
  data() {
    return {
      // 基础示例
      basicFormData: {},
      basicFormConfig: {
        title: '基础表单',
        columns: 2,
        showTitle: false,
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true
          },
          {
            key: 'email',
            label: '邮箱',
            type: FIELD_TYPES.INPUT,
            rules: [{ type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }]
          }
        ]
      },
      basicActions: [
        {
          text: '重置',
          type: 'default',
          icon: 'ios-refresh',
          onClick: this.handleReset
        },
        {
          text: '提交',
          type: 'primary',
          icon: 'ios-send',
          onClick: this.handleSubmit
        }
      ],
      
      // 复杂示例
      complexFormData: {},
      complexFormConfig: {
        title: '复杂表单',
        columns: 2,
        showTitle: false,
        fields: [
          {
            key: 'title',
            label: '标题',
            type: FIELD_TYPES.INPUT,
            required: true
          },
          {
            key: 'category',
            label: '分类',
            type: FIELD_TYPES.SELECT,
            options: [
              { label: '技术', value: 'tech' },
              { label: '产品', value: 'product' },
              { label: '设计', value: 'design' }
            ]
          },
          {
            key: 'content',
            label: '内容',
            type: FIELD_TYPES.TEXTAREA,
            span: 24
          }
        ]
      },
      complexActions: [
        {
          text: '返回',
          type: 'default',
          icon: 'ios-arrow-back',
          onClick: this.handleBack
        },
        {
          text: '保存草稿',
          type: 'warning',
          icon: 'ios-save',
          onClick: this.handleSaveDraft
        },
        {
          text: '预览',
          type: 'info',
          icon: 'ios-eye',
          onClick: this.handlePreview
        },
        {
          text: '发布',
          type: 'primary',
          icon: 'ios-send',
          onClick: this.handlePublish
        }
      ],
      
      // 动态按钮示例
      currentMode: FORM_MODES.CREATE,
      dynamicFormData: {},
      dynamicFormConfig: {
        title: '动态按钮表单',
        columns: 2,
        showTitle: false,
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true
          },
          {
            key: 'status',
            label: '状态',
            type: FIELD_TYPES.SELECT,
            options: [
              { label: '草稿', value: 'draft' },
              { label: '已发布', value: 'published' },
              { label: '已归档', value: 'archived' }
            ]
          }
        ]
      },
      
      // 无按钮示例
      noActionsFormData: {},
      noActionsFormConfig: {
        title: '无按钮表单',
        columns: 1,
        showTitle: false,
        fields: [
          {
            key: 'message',
            label: '消息',
            type: FIELD_TYPES.TEXTAREA,
            props: {
              placeholder: '这个表单没有配置操作按钮'
            }
          }
        ]
      },
      
      // 操作日志
      actionLogs: []
    }
  },
  
  computed: {
    // 动态按钮配置
    dynamicActions() {
      const actions = []
      
      switch (this.currentMode) {
        case FORM_MODES.CREATE:
          actions.push(
            { text: '重置', type: 'default', onClick: this.handleReset },
            { text: '保存', type: 'primary', onClick: this.handleSave }
          )
          break
          
        case FORM_MODES.EDIT:
          actions.push(
            { text: '取消', type: 'default', onClick: this.handleCancel },
            { text: '更新', type: 'primary', onClick: this.handleUpdate }
          )
          break
          
        case FORM_MODES.VIEW:
          actions.push(
            { text: '返回', type: 'default', onClick: this.handleBack },
            { text: '编辑', type: 'primary', onClick: this.handleEdit }
          )
          break
      }
      
      return actions
    },
    
    // 代码示例
    basicActionsCode() {
      return `actions: [
  {
    text: '重置',
    type: 'default',
    icon: 'ios-refresh',
    onClick: this.handleReset
  },
  {
    text: '提交',
    type: 'primary',
    icon: 'ios-send',
    onClick: this.handleSubmit
  }
]`
    },
    
    complexActionsCode() {
      return `actions: [
  { text: '返回', type: 'default', onClick: this.handleBack },
  { text: '保存草稿', type: 'warning', onClick: this.handleSaveDraft },
  { text: '预览', type: 'info', onClick: this.handlePreview },
  { text: '发布', type: 'primary', onClick: this.handlePublish }
]`
    },
    
    dynamicActionsCode() {
      return `computed: {
  dynamicActions() {
    switch (this.currentMode) {
      case 'create':
        return [
          { text: '重置', type: 'default', onClick: this.handleReset },
          { text: '保存', type: 'primary', onClick: this.handleSave }
        ]
      case 'edit':
        return [
          { text: '取消', type: 'default', onClick: this.handleCancel },
          { text: '更新', type: 'primary', onClick: this.handleUpdate }
        ]
      case 'view':
        return [
          { text: '返回', type: 'default', onClick: this.handleBack },
          { text: '编辑', type: 'primary', onClick: this.handleEdit }
        ]
    }
  }
}`
    }
  },
  
  methods: {
    // 基础操作处理
    handleBasicAction(action) {
      this.addLog('基础示例', `点击了按钮: ${action.text}`)
    },
    
    // 复杂操作处理
    handleComplexAction(action) {
      this.addLog('复杂示例', `点击了按钮: ${action.text}`)
    },
    
    // 动态操作处理
    handleDynamicAction(action) {
      this.addLog('动态示例', `${this.currentMode}模式下点击了: ${action.text}`)
    },
    
    // 更新动态按钮
    updateDynamicActions() {
      this.addLog('动态示例', `切换到${this.currentMode}模式，按钮已更新`)
    },
    
    // 具体操作方法
    handleReset(formData, formInstance) {
      if (formInstance) {
        formInstance.resetFields()
        this.addLog('操作', '表单已重置')
      }
    },
    
    handleSubmit(formData, formInstance) {
      if (formInstance) {
        formInstance.validate((valid) => {
          if (valid) {
            this.addLog('操作', '表单提交成功')
          } else {
            this.addLog('操作', '表单验证失败')
          }
        })
      }
    },
    
    handleBack() {
      this.addLog('操作', '执行返回操作')
    },
    
    handleSaveDraft() {
      this.addLog('操作', '保存草稿成功')
    },
    
    handlePreview() {
      this.addLog('操作', '打开预览')
    },
    
    handlePublish() {
      this.addLog('操作', '发布成功')
    },
    
    handleSave() {
      this.addLog('操作', '保存成功')
    },
    
    handleCancel() {
      this.addLog('操作', '取消操作')
    },
    
    handleUpdate() {
      this.addLog('操作', '更新成功')
    },
    
    handleEdit() {
      this.currentMode = FORM_MODES.EDIT
      this.addLog('操作', '切换到编辑模式')
    },
    
    // 添加日志
    addLog(source, message) {
      this.actionLogs.unshift({
        source,
        message,
        time: new Date().toLocaleTimeString(),
        type: 'info'
      })
      
      if (this.actionLogs.length > 10) {
        this.actionLogs = this.actionLogs.slice(0, 10)
      }
    },
    
    // 清空日志
    clearLogs() {
      this.actionLogs = []
    }
  }
}
</script>

<style scoped>
.external-actions-example {
  padding: 20px;
}

.description {
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-left: 4px solid #007bff;
  color: #666;
  line-height: 1.6;
}

.code-block {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  margin: 0;
}

.dynamic-info {
  margin-bottom: 16px;
  
  h4 {
    margin-bottom: 8px;
    color: #333;
  }
  
  ul {
    margin: 8px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
    }
  }
}

.no-actions-info {
  h4 {
    margin-bottom: 8px;
    color: #333;
  }
  
  p {
    margin-bottom: 12px;
    line-height: 1.6;
    color: #666;
  }
  
  code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 12px;
  }
}

.action-logs {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  margin: 0 10px;
  color: #999;
  font-size: 12px;
  min-width: 80px;
}

.log-message {
  flex: 1;
  font-size: 13px;
}
</style>
