<template>
  <div class="complete-file-upload-example">
    <Card title="完整的文件上传示例（对应实际使用场景）">
      <p>这个示例展示了如何在动态表单中使用 file-upload 组件，包含所有必要的属性和事件处理。</p>

      <DynamicForm
        ref="completeForm"
        v-model="formData"
        :config="formConfig"
        :mode="mode"
        :loading="loading"
        @field-change="handleFieldChange"
        @validate="handleValidate"
      />

      <div style="margin-top: 20px;">
        <Button @click="toggleMode" type="primary">
          切换模式: {{ mode === 'view' ? '查看' : '编辑' }}
        </Button>
        <Button @click="loadExistingFiles" style="margin-left: 10px;">
          加载已有文件
        </Button>
        <Button @click="clearFiles" style="margin-left: 10px;">
          清空文件
        </Button>
      </div>

      <div style="margin-top: 30px;">
        <h3>表单数据：</h3>
        <pre class="data-display">{{ JSON.stringify(formData, null, 2) }}</pre>
      </div>
    </Card>
  </div>
</template>

<script>
import { FORM_MODES, FIELD_TYPES } from '@/components/dynamic-form/types'

export default {
  name: 'CompleteFileUploadExample',
  data() {
    return {
      mode: FORM_MODES.EDIT,
      loading: false,
      formData: {
        title: '会议纪要',
        meetingDate: '2024-01-15',
        meetingDocuments: [],
        attachments: []
      },
      // 模拟已有文件数据
      existingFiles: [
        {
          fileId: 'file001',
          fileName: '会议议程.pdf',
          downUrl: '/files/agenda.pdf',
          deleteUrl: '/files/delete/file001',
          fileType: 'pdf',
          fileTag: 'meeting-doc',
          businessId: 'MEETING_001'
        },
        {
          fileId: 'file002',
          fileName: '参会人员名单.xlsx',
          downUrl: '/files/attendees.xlsx',
          deleteUrl: '/files/delete/file002',
          fileType: 'xlsx',
          fileTag: 'meeting-doc',
          businessId: 'MEETING_001'
        }
      ]
    }
  },
  computed: {
    formConfig() {
      return [
        {
          title: '会议信息',
          fields: [
            {
              key: 'title',
              label: '会议标题',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入会议标题'
              }
            },
            {
              key: 'meetingDate',
              label: '会议日期',
              type: FIELD_TYPES.DATE_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择会议日期',
                format: 'yyyy-MM-dd'
              }
            }
          ]
        },
        {
          title: '文件上传',
          fields: [
            {
              key: 'meetingDocuments',
              label: '会议文档',
              type: FIELD_TYPES.FILE_UPLOAD,
              span: 24,
              props: {
                // 核心属性（根据官方文档）
                accept: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx', // 接受文件类型
                expireTime: '3600', // 过期时间
                filePath: '/meeting/documents', // 文件路径
                serviceMark: 'MEETING_SERVICE', // 服务标识
                bucketName: 'meeting-documents', // 桶名
                multiple: true, // 是否支持多选
                maxSize: 50 * 1024, // 最大size (50MB)
                maxFiles: 10, // 最多文件
                defaultList: this.formData.meetingDocuments, // 已上传列表
                isDetail: this.mode === FORM_MODES.VIEW, // 是否可以上传删除文件

                // 扩展属性（保持兼容）
                fileType: 'document',
                fileTag: 'meeting-doc',
                searchFileType: 'all',
                showHistory: true,
                showFiles: true,
                businessId: 'MEETING_001',
                tableName: 'meeting_files',
                postData: {
                  category: 'meeting',
                  department: 'admin'
                },
                delCallback: this.handleDelCallback,
                customBtn: {
                  type: 'primary',
                  icon: 'md-document',
                  text: '上传会议文档',
                  color: '#fff',
                  size: '20'
                }
              },
              events: {
                // 根据官方文档的事件名称
                'beforeUpload': this.handleBeforeUpload, // 上传之前产生的回调
                'onExceededSize': this.handleExceededSize, // 文件超出指定大小回调
                'onExceededFile': this.handleExceededFile, // 超出文件最大数回调
                'fileRemove': this.handleFileRemove, // 文件删除回调
                'fileError': this.handleFileError, // 文件上传失败回调
                'fileSuccess': this.handleFileSuccess, // 单个文件上传成功回调
                'fileComplete': this.handleFileComplete // 文件全部上传成功回调
              }
            },
            {
              key: 'attachments',
              label: '其他附件',
              type: FIELD_TYPES.FILE_UPLOAD,
              span: 24,
              props: {
                multiple: true,
                maxFiles: 5,
                maxSize: 20480, // 20MB
                fileType: 'attachment',
                fileTag: 'meeting-attachment',
                accept: '',
                showHistory: false,
                showFiles: true,
                businessId: 'MEETING_001',
                serviceMark: 'ATTACHMENT_SERVICE',
                bucketName: 'meeting-attachments',
                defaultList: this.formData.attachments,
                beforeUpload: (file) => {
                  console.log('准备上传附件:', file.name)
                  return true
                },
                customBtn: {
                  type: 'success',
                  icon: 'md-attach',
                  text: '上传附件',
                  color: '#fff',
                  size: '18'
                }
              },
              events: {
                'fileSuccess': this.handleAttachmentSuccess,
                'fileRemove': this.handleAttachmentRemove,
                'fileComplete': this.handleAttachmentComplete
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
    },

    handleValidate(valid, errors) {
      if (valid) {
        this.$Message.success('表单验证通过')
      } else {
        this.$Message.error('表单验证失败')
      }
    },

    // 上传前回调（对应 :beforeUpload="beforeUpload"）
    handleBeforeUpload(file) {
      console.log('准备上传会议文档:', file.name)

      // 文件大小检查
      if (file.size > 50 * 1024 * 1024) {
        this.$Message.error('文件大小不能超过50MB')
        return false
      }

      // 文件类型检查
      const allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']
      const fileExt = file.name.split('.').pop().toLowerCase()
      if (!allowedTypes.includes(fileExt)) {
        this.$Message.error('不支持的文件类型')
        return false
      }

      return true
    },

    // 删除回调
    handleDelCallback(fileId, file) {
      console.log('删除文件回调:', fileId, file)
      this.$Message.info(`文件 ${file.name || '未知文件'} 删除成功`)
    },

    // 文件超出指定大小回调
    handleExceededSize() {
      console.log('文件大小超出限制')
      this.$Message.error('文件大小超出限制')
    },

    // 超出文件最大数回调
    handleExceededFile() {
      console.log('文件数量超出限制')
      this.$Message.error('文件数量超出限制')
    },

    // 文件上传失败回调
    handleFileError(error) {
      console.log('文件上传失败:', error)
      this.$Message.error('文件上传失败')
    },

    // 文件上传成功（对应 @fileSuccess="fileSuccessFile"）
    handleFileSuccess(file) {
      console.log('会议文档上传成功:', file)
      this.$Message.success(`文件 ${file.name} 上传成功`)
    },

    // 文件删除（对应 @fileRemove="fileRemoveFile"）
    handleFileRemove(file) {
      console.log('会议文档删除:', file)
      this.$Message.info('文件已删除')
    },

    // 文件上传完成（对应 @fileComplete="fileCompleteFileCertUrl"）
    handleFileComplete(files) {
      console.log('会议文档上传完成:', files)
      this.$Message.success('所有会议文档上传完成')
    },

    // 附件相关事件处理
    handleAttachmentSuccess(file) {
      console.log('附件上传成功:', file)
      this.$Message.success(`附件 ${file.name} 上传成功`)
    },

    handleAttachmentRemove(file) {
      console.log('附件删除:', file)
      this.$Message.info('附件已删除')
    },

    handleAttachmentComplete(files) {
      console.log('附件上传完成:', files)
      this.$Message.success('所有附件上传完成')
    },

    toggleMode() {
      this.mode = this.mode === FORM_MODES.VIEW ? FORM_MODES.EDIT : FORM_MODES.VIEW
    },

    loadExistingFiles() {
      // 模拟加载已有文件
      this.formData.meetingDocuments = [...this.existingFiles]
      this.$Message.success('已加载现有文件')
    },

    clearFiles() {
      this.formData.meetingDocuments = []
      this.formData.attachments = []
      this.$Message.info('已清空所有文件')
    }
  }
}
</script>

<style scoped>
.complete-file-upload-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.data-display {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e8eaec;
}
</style>
