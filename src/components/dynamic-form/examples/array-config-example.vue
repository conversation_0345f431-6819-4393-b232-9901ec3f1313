<template>
  <div class="array-config-example">
    <h2>DynamicForm 数组配置示例</h2>
    
    <!-- 使用新的数组配置格式 -->
    <DynamicForm
      v-model="formData"
      :config="formConfig"
      :mode="mode"
      @submit="handleSubmit"
      @field-change="handleFieldChange"
    />
    
    <!-- 模式切换 -->
    <div class="mode-controls" style="margin-top: 20px;">
      <Button @click="mode = 'create'" :type="mode === 'create' ? 'primary' : 'default'">创建模式</Button>
      <Button @click="mode = 'edit'" :type="mode === 'edit' ? 'primary' : 'default'">编辑模式</Button>
      <Button @click="mode = 'view'" :type="mode === 'view' ? 'primary' : 'default'">查看模式</Button>
    </div>
    
    <!-- 表单数据预览 -->
    <div class="data-preview" style="margin-top: 20px;">
      <h3>表单数据：</h3>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES } from '../types'

export default {
  name: 'ArrayConfigExample',
  data() {
    return {
      mode: 'create',
      formData: {},
      // 新的数组配置格式 - 参考 /docs/config.json
      formConfig: [
        {
          title: '业务登记信息',
          columns: 2,
          labelWidth: 120,
          labelColon: true,
          fields: [
            {
              key: 'registrationDate',
              label: '登记日期',
              type: FIELD_TYPES.DATE_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择登记日期',
                format: 'yyyy-MM-dd'
              }
            },
            {
              key: 'abnormalType',
              label: '精神异常类型',
              type: FIELD_TYPES.SELECT,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择异常类型'
              },
              options: [
                { value: 'depression', label: '抑郁症状' },
                { value: 'anxiety', label: '焦虑症状' },
                { value: 'psychosis', label: '精神病性症状' },
                { value: 'bipolar', label: '双相情感障碍' },
                { value: 'other', label: '其他' }
              ]
            },
            {
              key: 'discoveryTime',
              label: '发现时间',
              type: FIELD_TYPES.DATETIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择发现时间',
                format: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            {
              key: 'discoveryLocation',
              label: '发现地点',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入发现地点'
              }
            },
            {
              key: 'severityLevel',
              label: '严重程度',
              type: FIELD_TYPES.RADIO,
              required: true,
              span: 24,
              options: [
                { value: 'mild', label: '轻微' },
                { value: 'moderate', label: '中等' },
                { value: 'severe', label: '严重' }
              ]
            },
            {
              key: 'needMedicalIntervention',
              label: '是否需要医疗干预',
              type: FIELD_TYPES.SWITCH,
              span: 12,
              props: {
                trueValue: true,
                falseValue: false
              }
            },
            {
              key: 'abnormalManifestations',
              label: '异常表现',
              type: FIELD_TYPES.TEXTAREA,
              required: true,
              span: 24,
              props: {
                placeholder: '请详细描述异常表现',
                autosize: { minRows: 3, maxRows: 5 }
              }
            },
            {
              key: 'remarks',
              label: '备注',
              type: FIELD_TYPES.TEXTAREA,
              span: 24,
              props: {
                placeholder: '请输入备注信息',
                autosize: { minRows: 2, maxRows: 4 }
              }
            }
          ]
        },
        {
          title: '经办信息',
          columns: 2,
          labelWidth: 120,
          labelColon: true,
          showTitle: false, // 不显示标题
          fields: [
            {
              key: 'handler',
              label: '经办人',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 12,
              props: {
                placeholder: '请输入经办人',
                readonly: true
              }
            },
            {
              key: 'handlingTime',
              label: '经办时间',
              type: FIELD_TYPES.DATETIME_PICKER,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择经办时间',
                format: 'yyyy-MM-dd HH:mm:ss',
                readonly: true
              }
            },
            {
              key: 'handlingDepartment',
              label: '经办部门',
              type: FIELD_TYPES.SELECT,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择经办部门'
              },
              options: [
                { value: 'security', label: '安全科' },
                { value: 'medical', label: '医务科' },
                { value: 'education', label: '教育科' },
                { value: 'management', label: '管理科' }
              ]
            },
            {
              key: 'processingStatus',
              label: '处理状态',
              type: FIELD_TYPES.SELECT,
              required: true,
              span: 12,
              props: {
                placeholder: '请选择处理状态'
              },
              options: [
                { value: '待处理', label: '待处理' },
                { value: '处理中', label: '处理中' },
                { value: '已处理', label: '已处理' }
              ]
            },
            {
              key: 'reviewer',
              label: '审核人',
              type: FIELD_TYPES.INPUT,
              span: 12,
              props: {
                placeholder: '请输入审核人'
              }
            },
            {
              key: 'reviewTime',
              label: '审核时间',
              type: FIELD_TYPES.DATETIME_PICKER,
              span: 12,
              props: {
                placeholder: '请选择审核时间',
                format: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            {
              key: 'reviewOpinion',
              label: '审核意见',
              type: FIELD_TYPES.TEXTAREA,
              span: 24,
              props: {
                placeholder: '请输入审核意见',
                autosize: { minRows: 3, maxRows: 5 }
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleSubmit(data) {
      console.log('表单提交:', data)
      this.$Message.success('提交成功')
    },
    
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
    }
  }
}
</script>

<style scoped>
.array-config-example {
  padding: 20px;
}

.mode-controls {
  text-align: center;
}

.mode-controls .ivu-btn {
  margin: 0 5px;
}

.data-preview {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
}

.data-preview pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}
</style>
