<template>
  <div class="bsp-layout-integration-example">
    <h2>BSP Layout 集成示例</h2>
    
    <Tabs value="easy" type="card">
      <!-- EasyLayout 集成 -->
      <TabPane label="EasyLayout" name="easy">
        <EasyLayout 
          left-width="300px"
          :actions="easyActions"
          @action="handleEasyAction"
        >
          <template #left>
            <div class="left-content">
              <h3>病号信息</h3>
              <div class="info-item">
                <label>姓名：</label>
                <span>张三</span>
              </div>
              <div class="info-item">
                <label>编号：</label>
                <span>JG001</span>
              </div>
              <div class="info-item">
                <label>状态：</label>
                <Tag color="warning">待登记</Tag>
              </div>
            </div>
          </template>
          
          <template #right>
            <EasyCard title="业务登记">
              <DynamicForm
                ref="easyForm"
                v-model="easyFormData"
                :config="sickFormConfig"
                :submit-config="easySubmitConfig"
                @action="handleFormAction"
                @submit-success="handleSubmitSuccess"
                @submit-error="handleSubmitError"
              />
            </EasyCard>
          </template>
        </EasyLayout>
      </TabPane>
      
      <!-- SimpleDetailCardLayout 集成 -->
      <TabPane label="SimpleDetailCardLayout" name="simple">
        <SimpleDetailCardLayout
          left-title="病号信息"
          left-icon="ios-person"
          :data="patientData"
          :cards="simpleCards"
          :actions="simpleActions"
          @action="handleSimpleAction"
        >
          <template #left="{ data }">
            <div class="left-content">
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ data.name }}</span>
              </div>
              <div class="info-item">
                <label>编号：</label>
                <span>{{ data.code }}</span>
              </div>
              <div class="info-item">
                <label>状态：</label>
                <Tag :color="getStatusColor(data.status)">{{ data.status }}</Tag>
              </div>
            </div>
          </template>
          
          <template #form-card>
            <DynamicForm
              ref="simpleForm"
              v-model="simpleFormData"
              :config="sickFormConfig"
              :submit-config="simpleSubmitConfig"
              @action="handleFormAction"
              @submit-success="handleSubmitSuccess"
              @submit-error="handleSubmitError"
            />
          </template>
        </SimpleDetailCardLayout>
      </TabPane>
      
      <!-- DetailCardLayout 集成 -->
      <TabPane label="DetailCardLayout" name="detail">
        <DetailCardLayout
          :left-config="detailLeftConfig"
          :right-cards="detailRightCards"
          :bottom-actions="detailActions"
          @bottom-action="handleDetailAction"
        >
          <template #left="{ data }">
            <div class="left-content">
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ data.name }}</span>
              </div>
              <div class="info-item">
                <label>编号：</label>
                <span>{{ data.code }}</span>
              </div>
              <div class="info-item">
                <label>状态：</label>
                <Tag :color="getStatusColor(data.status)">{{ data.status }}</Tag>
              </div>
            </div>
          </template>
          
          <template #form-card>
            <DynamicForm
              ref="detailForm"
              v-model="detailFormData"
              :config="sickFormConfig"
              :submit-config="detailSubmitConfig"
              @action="handleFormAction"
              @submit-success="handleSubmitSuccess"
              @submit-error="handleSubmitError"
            />
          </template>
        </DetailCardLayout>
      </TabPane>
    </Tabs>
    
    <!-- 操作日志 -->
    <Card title="操作日志" style="margin-top: 20px;">
      <div class="action-logs">
        <div v-for="(log, index) in actionLogs" :key="index" class="log-item">
          <Tag :color="log.type === 'success' ? 'green' : log.type === 'error' ? 'red' : 'blue'">
            {{ log.source }}
          </Tag>
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <Button @click="clearLogs" size="small" style="margin-top: 10px;">清空日志</Button>
    </Card>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FIELD_TYPES } from '../types'

export default {
  name: 'BspLayoutIntegrationExample',
  components: {
    DynamicForm
  },
  data() {
    return {
      // 表单数据
      easyFormData: {},
      simpleFormData: {},
      detailFormData: {},
      
      // 病号信息
      patientData: {
        name: '张三',
        code: 'JG001',
        status: '待登记'
      },
      
      // 病号登记表单配置
      sickFormConfig: {
        title: '业务登记',
        columns: 1,
        showTitle: false, // 在layout中已有标题
        fields: [
          {
            key: 'severelySickType',
            label: '病号类别',
            type: FIELD_TYPES.SELECT,
            span: 24,
            required: true,
            options: [
              { label: '重病号', value: '1' },
              { label: '普通病号', value: '2' },
              { label: '慢性病号', value: '3' }
            ],
            props: {
              multiple: true,
              placeholder: '请选择病号类别'
            }
          },
          {
            key: 'patientSituation',
            label: '病情情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 24,
            required: true,
            props: {
              placeholder: '请输入病情情况',
              autosize: { minRows: 3, maxRows: 5 }
            }
          },
          {
            key: 'treatmentSituation',
            label: '治疗情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 24,
            required: true,
            props: {
              placeholder: '请输入治疗情况',
              autosize: { minRows: 3, maxRows: 5 }
            }
          }
        ]
      },
      
      // 提交配置
      easySubmitConfig: {
        text: '提交登记',
        successMessage: '重病号登记成功',
        transformData: this.transformSickData
      },
      
      simpleSubmitConfig: {
        text: '提交登记',
        successMessage: '重病号登记成功',
        transformData: this.transformSickData
      },
      
      detailSubmitConfig: {
        text: '提交登记',
        successMessage: '重病号登记成功',
        transformData: this.transformSickData
      },
      
      // SimpleDetailCardLayout 卡片配置
      simpleCards: [
        {
          title: '业务登记',
          icon: 'ios-create',
          slot: 'form-card'
        }
      ],
      
      // DetailCardLayout 配置
      detailLeftConfig: {
        title: '病号信息',
        icon: 'ios-person',
        data: {
          name: '张三',
          code: 'JG001',
          status: '待登记'
        }
      },
      
      detailRightCards: [
        {
          name: 'form-card',
          title: '业务登记',
          icon: 'ios-create',
          slot: 'form-card'
        }
      ],
      
      // 操作日志
      actionLogs: []
    }
  },
  
  computed: {
    // EasyLayout 操作按钮
    easyActions() {
      return [
        {
          name: 'back',
          label: '返回',
          icon: 'ios-arrow-back',
          type: 'default'
        },
        {
          name: 'submit',
          label: '提交登记',
          icon: 'ios-send',
          type: 'primary',
          loading: this.isSubmitting
        }
      ]
    },
    
    // SimpleDetailCardLayout 操作按钮
    simpleActions() {
      return [
        {
          name: 'back',
          label: '返回',
          icon: 'ios-arrow-back',
          type: 'default'
        },
        {
          name: 'submit',
          label: '提交登记',
          icon: 'ios-send',
          type: 'primary',
          loading: this.isSubmitting
        }
      ]
    },
    
    // DetailCardLayout 操作按钮
    detailActions() {
      return [
        {
          name: 'back',
          label: '返回',
          icon: 'ios-arrow-back',
          type: 'default'
        },
        {
          name: 'submit',
          label: '提交登记',
          icon: 'ios-send',
          type: 'primary',
          loading: this.isSubmitting
        }
      ]
    },
    
    isSubmitting() {
      return false // 可以根据实际状态设置
    }
  },
  
  methods: {
    // EasyLayout 操作处理
    handleEasyAction(action) {
      this.addLog('EasyLayout', `按钮点击: ${action.name}`)
      
      if (action.name === 'submit') {
        this.$refs.easyForm.submit()
      } else if (action.name === 'back') {
        this.handleBack()
      }
    },
    
    // SimpleDetailCardLayout 操作处理
    handleSimpleAction(action) {
      this.addLog('SimpleDetailCardLayout', `按钮点击: ${action.name}`)
      
      if (action.name === 'submit') {
        this.$refs.simpleForm.submit()
      } else if (action.name === 'back') {
        this.handleBack()
      }
    },
    
    // DetailCardLayout 操作处理
    handleDetailAction({ action }) {
      this.addLog('DetailCardLayout', `按钮点击: ${action.name}`)
      
      if (action.name === 'submit') {
        this.$refs.detailForm.submit()
      } else if (action.name === 'back') {
        this.handleBack()
      }
    },
    
    // 表单操作处理
    handleFormAction(action) {
      this.addLog('DynamicForm', `表单操作: ${action.name || action}`)
    },
    
    // 提交成功处理
    handleSubmitSuccess(result, data, formInstance) {
      this.addLog('success', '提交成功，准备返回列表页面')
      // 模拟返回操作
      setTimeout(() => {
        this.handleBack()
      }, 1500)
    },
    
    // 提交失败处理
    handleSubmitError(error, data, formInstance) {
      this.addLog('error', `提交失败: ${error.message || '未知错误'}`)
    },
    
    // 返回处理
    handleBack() {
      this.addLog('info', '返回上一页')
      this.$Message.info('返回上一页')
    },
    
    // 数据转换
    transformSickData(data) {
      return {
        manageIdFk: 'test456',
        prisonId: 'prison001',
        jgrybm: 'JG001',
        businessStatus: '1',
        severelySickType: data.severelySickType,
        patientSituation: data.patientSituation,
        treatmentSituation: data.treatmentSituation
      }
    },
    
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        '待登记': 'warning',
        '已登记': 'success',
        '已取消': 'error'
      }
      return colorMap[status] || 'default'
    },
    
    // 添加日志
    addLog(source, message) {
      this.actionLogs.unshift({
        source,
        message,
        time: new Date().toLocaleTimeString(),
        type: source === 'success' ? 'success' : source === 'error' ? 'error' : 'info'
      })
      
      // 限制日志数量
      if (this.actionLogs.length > 15) {
        this.actionLogs = this.actionLogs.slice(0, 15)
      }
    },
    
    // 清空日志
    clearLogs() {
      this.actionLogs = []
    }
  }
}
</script>

<style scoped>
.bsp-layout-integration-example {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.left-content {
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  
  label {
    font-weight: bold;
    min-width: 60px;
    color: #333;
  }
  
  span {
    color: #666;
  }
}

.action-logs {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  margin: 0 10px;
  color: #999;
  font-size: 12px;
  min-width: 80px;
}

.log-message {
  flex: 1;
  font-size: 13px;
}

/deep/ .ivu-tabs-content {
  flex: 1;
  overflow: hidden;
}

/deep/ .ivu-tabs-tabpane {
  height: 100%;
}
</style>
