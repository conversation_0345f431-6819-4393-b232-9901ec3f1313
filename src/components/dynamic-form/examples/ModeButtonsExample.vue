<template>
  <div class="mode-buttons-example">
    <h2>不同模式的底部按钮示例</h2>
    <p class="description">
      动态表单组件支持根据不同的模式（新增/编辑/查看）显示不同的底部按钮配置。
    </p>
    
    <Row :gutter="20">
      <!-- 新增模式 -->
      <Col span="8">
        <Card title="新增模式 (CREATE)">
          <div class="mode-info">
            <Tag color="green">CREATE</Tag>
            <p>新增模式通常包含：</p>
            <ul>
              <li>重置按钮 - 清空表单数据</li>
              <li>保存按钮 - 保存新数据</li>
            </ul>
          </div>
          
          <DynamicForm
            ref="createForm"
            v-model="createFormData"
            :config="formConfig"
            mode="create"
            :actions="createActions"
            @action="handleCreateAction"
          />
        </Card>
      </Col>
      
      <!-- 编辑模式 -->
      <Col span="8">
        <Card title="编辑模式 (EDIT)">
          <div class="mode-info">
            <Tag color="orange">EDIT</Tag>
            <p>编辑模式通常包含：</p>
            <ul>
              <li>取消按钮 - 取消编辑操作</li>
              <li>更新按钮 - 保存修改</li>
            </ul>
          </div>
          
          <DynamicForm
            ref="editForm"
            v-model="editFormData"
            :config="formConfig"
            mode="edit"
            :actions="editActions"
            @action="handleEditAction"
          />
        </Card>
      </Col>
      
      <!-- 查看模式 -->
      <Col span="8">
        <Card title="查看模式 (VIEW)">
          <div class="mode-info">
            <Tag color="blue">VIEW</Tag>
            <p>查看模式通常包含：</p>
            <ul>
              <li>返回按钮 - 返回列表页</li>
              <li>编辑按钮 - 切换到编辑模式</li>
            </ul>
          </div>
          
          <DynamicForm
            ref="viewForm"
            v-model="viewFormData"
            :config="formConfig"
            mode="view"
            :actions="viewActions"
            @action="handleViewAction"
          />
        </Card>
      </Col>
    </Row>
    
    <!-- 动态切换示例 -->
    <Card title="动态切换模式示例" style="margin-top: 20px;">
      <div style="margin-bottom: 16px;">
        <label>切换模式：</label>
        <RadioGroup v-model="currentMode" @on-change="handleModeChange">
          <Radio label="create">新增模式</Radio>
          <Radio label="edit">编辑模式</Radio>
          <Radio label="view">查看模式</Radio>
        </RadioGroup>
      </div>
      
      <Row :gutter="20">
        <Col span="12">
          <DynamicForm
            ref="dynamicForm"
            v-model="dynamicFormData"
            :config="formConfig"
            :mode="currentMode"
            :actions="currentModeActions"
            @action="handleDynamicAction"
          />
        </Col>
        
        <Col span="12">
          <div class="mode-explanation">
            <h4>当前模式：{{ currentMode.toUpperCase() }}</h4>
            <h4>按钮配置：</h4>
            <ul>
              <li v-for="action in currentModeActions" :key="action.text">
                <strong>{{ action.text }}</strong> ({{ action.type }}) - {{ getActionDescription(action.text) }}
              </li>
            </ul>
            
            <h4>代码示例：</h4>
            <pre class="code-block">{{ getCurrentModeCode() }}</pre>
          </div>
        </Col>
      </Row>
    </Card>
    
    <!-- 操作日志 -->
    <Card title="操作日志" style="margin-top: 20px;">
      <div class="action-logs">
        <div v-for="(log, index) in actionLogs" :key="index" class="log-item">
          <Tag :color="getLogColor(log.mode)">{{ log.mode }}</Tag>
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <Button @click="clearLogs" size="small" style="margin-top: 10px;">清空日志</Button>
    </Card>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FIELD_TYPES, FORM_MODES } from '../types'

export default {
  name: 'ModeButtonsExample',
  components: {
    DynamicForm
  },
  data() {
    return {
      // 表单数据
      createFormData: {
        name: '',
        email: '',
        phone: '',
        department: ''
      },
      editFormData: {
        name: '张三',
        email: '<EMAIL>',
        phone: '13800138000',
        department: '技术部'
      },
      viewFormData: {
        name: '李四',
        email: '<EMAIL>',
        phone: '13900139000',
        department: '产品部'
      },
      dynamicFormData: {
        name: '王五',
        email: '<EMAIL>',
        phone: '13700137000',
        department: '设计部'
      },
      
      // 当前模式
      currentMode: FORM_MODES.CREATE,
      
      // 表单配置
      formConfig: {
        title: '员工信息',
        columns: 1,
        showTitle: false,
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true,
            props: {
              placeholder: '请输入姓名'
            }
          },
          {
            key: 'email',
            label: '邮箱',
            type: FIELD_TYPES.INPUT,
            required: true,
            rules: [
              { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入邮箱'
            }
          },
          {
            key: 'phone',
            label: '手机号',
            type: FIELD_TYPES.INPUT,
            props: {
              placeholder: '请输入手机号'
            }
          },
          {
            key: 'department',
            label: '部门',
            type: FIELD_TYPES.SELECT,
            options: [
              { label: '技术部', value: '技术部' },
              { label: '产品部', value: '产品部' },
              { label: '设计部', value: '设计部' },
              { label: '运营部', value: '运营部' }
            ],
            props: {
              placeholder: '请选择部门'
            }
          }
        ]
      },
      
      // 操作日志
      actionLogs: []
    }
  },
  
  computed: {
    // 新增模式按钮
    createActions() {
      return [
        {
          text: '重置',
          type: 'default',
          icon: 'ios-refresh',
          onClick: this.handleReset
        },
        {
          text: '保存',
          type: 'primary',
          icon: 'ios-save',
          onClick: this.handleCreate
        }
      ]
    },
    
    // 编辑模式按钮
    editActions() {
      return [
        {
          text: '取消',
          type: 'default',
          icon: 'ios-close',
          onClick: this.handleCancel
        },
        {
          text: '更新',
          type: 'primary',
          icon: 'ios-checkmark',
          onClick: this.handleUpdate
        }
      ]
    },
    
    // 查看模式按钮
    viewActions() {
      return [
        {
          text: '返回',
          type: 'default',
          icon: 'ios-arrow-back',
          onClick: this.handleBack
        },
        {
          text: '编辑',
          type: 'primary',
          icon: 'ios-create',
          onClick: this.handleEdit
        }
      ]
    },
    
    // 当前模式的按钮
    currentModeActions() {
      switch (this.currentMode) {
        case FORM_MODES.CREATE:
          return this.createActions
        case FORM_MODES.EDIT:
          return this.editActions
        case FORM_MODES.VIEW:
          return this.viewActions
        default:
          return []
      }
    }
  },
  
  methods: {
    // 新增模式操作
    handleCreateAction(action) {
      this.addLog('CREATE', `新增模式 - ${action.text}`)
    },
    
    // 编辑模式操作
    handleEditAction(action) {
      this.addLog('EDIT', `编辑模式 - ${action.text}`)
    },
    
    // 查看模式操作
    handleViewAction(action) {
      this.addLog('VIEW', `查看模式 - ${action.text}`)
    },
    
    // 动态模式操作
    handleDynamicAction(action) {
      this.addLog(this.currentMode.toUpperCase(), `动态模式 - ${action.text}`)
    },
    
    // 模式切换
    handleModeChange() {
      this.addLog('SYSTEM', `切换到 ${this.currentMode.toUpperCase()} 模式`)
    },
    
    // 具体操作方法
    handleReset(formData, formInstance) {
      if (formInstance) {
        formInstance.resetFields()
        this.addLog('ACTION', '表单已重置')
      }
    },
    
    handleCreate(formData, formInstance) {
      if (formInstance) {
        formInstance.validate((valid) => {
          if (valid) {
            this.addLog('ACTION', '数据保存成功')
            console.log('新增数据:', formData)
          } else {
            this.addLog('ACTION', '表单验证失败')
          }
        })
      }
    },
    
    handleCancel() {
      this.addLog('ACTION', '取消编辑操作')
    },
    
    handleUpdate(formData, formInstance) {
      if (formInstance) {
        formInstance.validate((valid) => {
          if (valid) {
            this.addLog('ACTION', '数据更新成功')
            console.log('更新数据:', formData)
          } else {
            this.addLog('ACTION', '表单验证失败')
          }
        })
      }
    },
    
    handleBack() {
      this.addLog('ACTION', '返回列表页面')
    },
    
    handleEdit() {
      this.addLog('ACTION', '切换到编辑模式')
      this.currentMode = FORM_MODES.EDIT
    },
    
    // 获取操作描述
    getActionDescription(actionText) {
      const descriptions = {
        '重置': '清空表单数据',
        '保存': '保存新数据到数据库',
        '取消': '取消当前编辑操作',
        '更新': '保存修改到数据库',
        '返回': '返回到列表页面',
        '编辑': '切换到编辑模式'
      }
      return descriptions[actionText] || '执行操作'
    },
    
    // 获取当前模式的代码示例
    getCurrentModeCode() {
      const codes = {
        create: `// 新增模式按钮配置
actions: [
  { text: '重置', type: 'default', onClick: this.handleReset },
  { text: '保存', type: 'primary', onClick: this.handleCreate }
]`,
        edit: `// 编辑模式按钮配置
actions: [
  { text: '取消', type: 'default', onClick: this.handleCancel },
  { text: '更新', type: 'primary', onClick: this.handleUpdate }
]`,
        view: `// 查看模式按钮配置
actions: [
  { text: '返回', type: 'default', onClick: this.handleBack },
  { text: '编辑', type: 'primary', onClick: this.handleEdit }
]`
      }
      return codes[this.currentMode] || ''
    },
    
    // 获取日志颜色
    getLogColor(mode) {
      const colors = {
        'CREATE': 'green',
        'EDIT': 'orange', 
        'VIEW': 'blue',
        'SYSTEM': 'purple',
        'ACTION': 'default'
      }
      return colors[mode] || 'default'
    },
    
    // 添加日志
    addLog(mode, message) {
      this.actionLogs.unshift({
        mode,
        message,
        time: new Date().toLocaleTimeString()
      })
      
      if (this.actionLogs.length > 15) {
        this.actionLogs = this.actionLogs.slice(0, 15)
      }
    },
    
    // 清空日志
    clearLogs() {
      this.actionLogs = []
    }
  }
}
</script>

<style scoped>
.mode-buttons-example {
  padding: 20px;
}

.description {
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-left: 4px solid #007bff;
  color: #666;
  line-height: 1.6;
}

.mode-info {
  margin-bottom: 16px;
  
  p {
    margin: 8px 0;
    color: #666;
  }
  
  ul {
    margin: 8px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
      color: #666;
      font-size: 13px;
    }
  }
}

.mode-explanation {
  h4 {
    margin-bottom: 8px;
    color: #333;
  }
  
  ul {
    margin: 8px 0 16px 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 6px;
      color: #666;
      font-size: 13px;
    }
  }
}

.code-block {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  margin: 0;
}

.action-logs {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  margin: 0 10px;
  color: #999;
  font-size: 12px;
  min-width: 80px;
}

.log-message {
  flex: 1;
  font-size: 13px;
}
</style>
