<template>
  <div class="advanced-example">
    <h2>高级示例 - 分组表单</h2>
    
    <!-- 动态表单 -->
    <DynamicForm
      ref="dynamicForm"
      v-model="formData"
      :config="formConfig"
      :mode="currentMode"
      :actions="formActions"
      @field-change="handleFieldChange"
    />
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FORM_MODES, FIELD_TYPES } from '../types'

export default {
  name: 'AdvancedExample',
  components: {
    DynamicForm
  },
  data() {
    return {
      currentMode: FORM_MODES.CREATE,
      formData: {},
      formConfig: {
        title: '人员信息管理',
        columns: 3,
        labelWidth: 120,
        sections: [
          {
            title: '基本信息',
            titleIcon: {
              type: 'md-person',
              size: 24,
              color: '#2b5fda'
            },
            collapsible: true,
            collapsed: false,
            fields: ['name', 'gender', 'age', 'phone', 'email', 'idCard']
          },
          {
            title: '地址信息',
            titleIcon: {
              type: 'md-home',
              size: 24,
              color: '#2b5fda'
            },
            collapsible: true,
            collapsed: false,
            fields: ['province', 'city', 'district', 'address']
          },
          {
            title: '其他信息',
            titleIcon: {
              type: 'md-information-circle',
              size: 24,
              color: '#2b5fda'
            },
            collapsible: true,
            collapsed: false,
            fields: ['education', 'profession', 'salary', 'married', 'hobbies', 'remark']
          }
        ],
        fields: [
          // 基本信息
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true,
            props: {
              placeholder: '请输入姓名'
            }
          },
          {
            key: 'gender',
            label: '性别',
            type: FIELD_TYPES.RADIO,
            required: true,
            options: [
              { label: '男', value: 'male' },
              { label: '女', value: 'female' }
            ]
          },
          {
            key: 'age',
            label: '年龄',
            type: FIELD_TYPES.NUMBER,
            required: true,
            props: {
              min: 1,
              max: 120,
              placeholder: '请输入年龄'
            }
          },
          {
            key: 'phone',
            label: '手机号',
            type: FIELD_TYPES.INPUT,
            required: true,
            rules: [
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入手机号'
            }
          },
          {
            key: 'email',
            label: '邮箱',
            type: FIELD_TYPES.INPUT,
            rules: [
              { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入邮箱地址'
            }
          },
          {
            key: 'idCard',
            label: '身份证号',
            type: FIELD_TYPES.INPUT,
            required: true,
            rules: [
              { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号码', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入身份证号码'
            }
          },
          
          // 地址信息
          {
            key: 'province',
            label: '省份',
            type: FIELD_TYPES.SELECT,
            required: true,
            options: [
              { label: '北京市', value: 'beijing' },
              { label: '上海市', value: 'shanghai' },
              { label: '广东省', value: 'guangdong' },
              { label: '浙江省', value: 'zhejiang' }
            ],
            props: {
              placeholder: '请选择省份'
            }
          },
          {
            key: 'city',
            label: '城市',
            type: FIELD_TYPES.SELECT,
            required: true,
            dependsOn: {
              key: 'province',
              value: 'guangdong',
              action: 'show'
            },
            options: [
              { label: '广州市', value: 'guangzhou' },
              { label: '深圳市', value: 'shenzhen' },
              { label: '珠海市', value: 'zhuhai' }
            ],
            props: {
              placeholder: '请选择城市'
            }
          },
          {
            key: 'district',
            label: '区县',
            type: FIELD_TYPES.SELECT,
            options: [
              { label: '天河区', value: 'tianhe' },
              { label: '越秀区', value: 'yuexiu' },
              { label: '海珠区', value: 'haizhu' }
            ],
            props: {
              placeholder: '请选择区县'
            }
          },
          {
            key: 'address',
            label: '详细地址',
            type: FIELD_TYPES.TEXTAREA,
            span: 24,
            props: {
              placeholder: '请输入详细地址',
              autosize: { minRows: 2, maxRows: 4 }
            }
          },
          
          // 其他信息
          {
            key: 'education',
            label: '学历',
            type: FIELD_TYPES.SELECT,
            options: [
              { label: '小学', value: 'primary' },
              { label: '初中', value: 'junior' },
              { label: '高中', value: 'senior' },
              { label: '大专', value: 'college' },
              { label: '本科', value: 'bachelor' },
              { label: '硕士', value: 'master' },
              { label: '博士', value: 'doctor' }
            ],
            props: {
              placeholder: '请选择学历'
            }
          },
          {
            key: 'profession',
            label: '职业',
            type: FIELD_TYPES.INPUT,
            props: {
              placeholder: '请输入职业'
            }
          },
          {
            key: 'salary',
            label: '月薪',
            type: FIELD_TYPES.NUMBER,
            props: {
              min: 0,
              formatter: value => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
              parser: value => value.replace(/￥\s?|(,*)/g, ''),
              placeholder: '请输入月薪'
            }
          },
          {
            key: 'married',
            label: '婚姻状况',
            type: FIELD_TYPES.SWITCH,
            props: {
              'true-value': true,
              'false-value': false
            },
            help: '开启表示已婚，关闭表示未婚'
          },
          {
            key: 'hobbies',
            label: '兴趣爱好',
            type: FIELD_TYPES.CHECKBOX,
            options: [
              { label: '读书', value: 'reading' },
              { label: '运动', value: 'sports' },
              { label: '音乐', value: 'music' },
              { label: '旅游', value: 'travel' },
              { label: '摄影', value: 'photography' },
              { label: '绘画', value: 'painting' }
            ]
          },
          {
            key: 'remark',
            label: '备注',
            type: FIELD_TYPES.TEXTAREA,
            span: 24,
            props: {
              placeholder: '请输入备注信息',
              autosize: { minRows: 3, maxRows: 6 }
            }
          }
        ]
      },
      formActions: [
        {
          text: '重置',
          type: 'default',
          onClick: this.handleReset
        },
        {
          text: '保存',
          type: 'primary',
          onClick: this.handleSave
        }
      ]
    }
  },
  methods: {
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
      
      // 处理省份变化时清空城市选择
      if (key === 'province') {
        this.$refs.dynamicForm.setFieldValue('city', '')
        this.$refs.dynamicForm.setFieldValue('district', '')
      }
    },
    
    handleReset() {
      this.$refs.dynamicForm.resetFields()
      this.$Message.success('表单已重置')
    },
    
    handleSave(formData, formInstance) {
      formInstance.validate((valid) => {
        if (valid) {
          const submitData = formInstance.getFormData()
          console.log('保存数据:', submitData)
          this.$Message.success('保存成功')
        } else {
          this.$Message.error('请填写完整的表单信息')
        }
      })
    }
  }
}
</script>

<style scoped>
.advanced-example {
  padding: 20px;
}
</style>
