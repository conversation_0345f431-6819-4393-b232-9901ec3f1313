<template>
  <div class="sick-person-form">
    <!-- 与BSP Layout集成的病号登记表单 -->
    <EasyLayout
      left-width="300px"
      :actions="layoutActions"
      @action="handleLayoutAction"
    >
      <template #left>
        <div class="patient-info">
          <h3>病号信息</h3>
          <div class="info-item">
            <label>姓名：</label>
            <span>{{ patientInfo.name || '张三' }}</span>
          </div>
          <div class="info-item">
            <label>编号：</label>
            <span>{{ patientInfo.code || formData.jgrybm || 'JG001' }}</span>
          </div>
          <div class="info-item">
            <label>状态：</label>
            <Tag color="warning">待登记</Tag>
          </div>
        </div>
      </template>

      <template #right>
        <EasyCard title="业务登记">
          <DynamicForm
            ref="sickPersonForm"
            v-model="formData"
            :config="formConfig"
            :mode="mode"
            :submit-config="submitConfig"
            :submit-url="submitUrl"
            :loading="loading"
            @field-change="handleFieldChange"
            @before-submit="handleBeforeSubmit"
            @submit-success="handleSubmitSuccess"
            @submit-error="handleSubmitError"
          />
        </EasyCard>
      </template>
    </EasyLayout>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FORM_MODES, FIELD_TYPES } from '../types'

export default {
  name: 'SickPersonForm',
  components: {
    DynamicForm
  },
  props: {
    // 表单数据
    formData: {
      type: Object,
      default: () => ({
        id: '',                    // 主键id
        prisonId: '',             // 所属监所id
        jgrybm: '',               // 被监管人id
        jgryxx: {},               // 被监管人信息对象
        businessStatus: '',       // 业务状态
        severelySickType: '',     // 病号类别
        patientSituation: '',     // 病情情况
        treatmentSituation: '',   // 治疗情况
        applyUserId: '',          // 申请人id
        applyUserName: '',        // 申请人姓名
        applyTime: '',            // 申请时间
        isSeverelySick: '',       // 当前是否是重病号
        relieveApplyId: '',       // 对应的最新的一条解除申请id
        relieveApproveResult: '', // 解除申请审核结果
        relieveTime: '',          // 解除时间
        manageIdFk: ''
      })
    },
    // 表单模式
    mode: {
      type: String,
      default: FORM_MODES.CREATE
    },
    // 病号信息
    patientInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      submitUrl: '', // 将通过computed动态设置
      formConfig: {
        title: '业务登记',
        columns: 1, // 单列布局，与原表单保持一致
        labelWidth: 130,
        labelColon: true,
        fields: [
          {
            key: 'severelySickType',
            label: '病号类别',
            type: FIELD_TYPES.CUSTOM, // 使用自定义组件
            span: 16,
            required: true,
            style: { marginTop: '10px' },
            render: (h, value, formData, field) => {
              // 渲染原有的 s-dicgrid 组件
              return h('s-dicgrid', {
                props: {
                  value: value,
                  multiple: true,
                  dicName: 'ZD_BHLB',
                  appMark: 'acp'
                },
                on: {
                  input: (val) => {
                    field.events.change(val)
                  }
                },
                ref: 'dicGrid'
              })
            },
            rules: [
              { trigger: 'change', message: '病号类别为必填', required: true }
            ]
          },
          {
            key: 'patientSituation',
            label: '病情情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 16,
            required: true,
            props: {
              placeholder: '请输入病情情况',
              autosize: { minRows: 3, maxRows: 5 }
            },
            rules: [
              { trigger: 'blur', message: '病情情况为必填', required: true }
            ]
          },
          {
            key: 'treatmentSituation',
            label: '治疗情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 16,
            required: true,
            props: {
              placeholder: '请输入治疗情况',
              autosize: { minRows: 3, maxRows: 5 }
            },
            rules: [
              { trigger: 'blur', message: '治疗情况为必填', required: true }
            ]
          }
        ]
      },
      submitConfig: {
        text: '提交',
        type: 'primary',
        successMessage: '重病号登记成功',
        errorMessage: '重病号登记失败',
        transformData: (data) => {
          // 构造提交数据，与原有逻辑保持一致
          return {
            manageIdFk: data.manageIdFk,
            prisonId: data.prisonId,
            jgrybm: data.jgrybm,
            businessStatus: data.businessStatus || '1',
            severelySickType: data.severelySickType,
            patientSituation: data.patientSituation,
            treatmentSituation: data.treatmentSituation
          }
        }
      }
    }
  },
  computed: {
    // 动态设置提交URL
    submitUrl() {
      return this.$path ? this.$path.sick_create : '/api/sick/create'
    },

    // 布局操作按钮
    layoutActions() {
      return [
        {
          name: 'back',
          label: '返回',
          icon: 'ios-arrow-back',
          type: 'default'
        },
        {
          name: 'submit',
          label: '提交登记',
          icon: 'ios-send',
          type: 'primary',
          loading: this.loading
        }
      ]
    }
  },
  watch: {
    // 监听props变化，同步到表单数据
    formData: {
      handler(newVal) {
        if (newVal && this.$refs.sickPersonForm) {
          this.$refs.sickPersonForm.setFormData(newVal)
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 处理布局操作按钮点击
    handleLayoutAction(action) {
      if (action.name === 'submit') {
        // 触发表单提交
        this.$refs.sickPersonForm.submit()
      } else if (action.name === 'back') {
        // 返回上一页
        this.$emit('toback')
      }
    },

    handleFieldChange(key, value, formData) {
      // 通知父组件数据变化
      this.$emit('update:formData', formData)
      this.$emit('field-change', key, value, formData)
    },

    handleBeforeSubmit(data, formInstance) {
      // 提交前处理
      this.$emit('update:formData', data)
      console.log('病号登记提交前:', data)
    },

    handleSubmitSuccess(result, data, formInstance) {
      // 提交成功处理
      console.log('病号登记成功:', result)

      // 使用Notice组件显示成功消息（如果可用）
      if (this.$Notice) {
        this.$Notice.success({
          title: '成功提示',
          desc: '重病号登记成功'
        })
      }

      // 返回列表页面
      this.$emit('toback')
    },

    handleSubmitError(error, data, formInstance) {
      // 提交失败处理
      console.error('病号登记失败:', error)

      // 使用Notice组件显示错误消息（如果可用）
      if (this.$Notice) {
        this.$Notice.error({
          title: '错误提示',
          desc: error.message || '重病号登记失败'
        })
      }
    },

    // 表单验证
    validate(callback) {
      return this.$refs.sickPersonForm.validate(callback)
    },

    // 重置表单
    resetFields() {
      this.$refs.sickPersonForm.resetFields()
    },

    // 获取表单数据
    getFormData() {
      return this.$refs.sickPersonForm.getFormData()
    },

    // 设置表单数据
    setFormData(data) {
      this.$refs.sickPersonForm.setFormData(data)
    }
  }
}
</script>

<style scoped>
.sick-person-form {
  height: 100vh;
}

.patient-info {
  padding: 20px;
}

.patient-info h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  label {
    font-weight: bold;
    min-width: 60px;
    color: #333;
  }

  span {
    color: #666;
  }
}
</style>
