<template>
  <div class="smart-actions-example">
    <h2>智能操作按钮示例</h2>

    <!-- 检测结果显示 -->
    <Card title="智能检测结果" style="margin-bottom: 20px;">
      <div class="detection-info">
        <div class="info-item">
          <label>布局环境检测：</label>
          <Tag :color="layoutInfo.detected ? 'green' : 'default'">
            {{ layoutInfo.detected ? '已检测到布局' : '独立使用' }}
          </Tag>
        </div>

        <div class="info-item">
          <label>布局操作按钮：</label>
          <Tag :color="layoutInfo.hasActions ? 'green' : 'orange'">
            {{ layoutInfo.hasActions ? '布局有按钮' : '布局无按钮' }}
          </Tag>
        </div>

        <div class="info-item">
          <label>内部按钮显示：</label>
          <Tag :color="layoutInfo.shouldShowInternal ? 'blue' : 'default'">
            {{ layoutInfo.shouldShowInternal ? '显示内部按钮' : '隐藏内部按钮' }}
          </Tag>
        </div>

        <div class="logic-explanation">
          <h4>逻辑说明：</h4>
          <p v-if="!layoutInfo.detected">
            <Icon type="ios-information-circle" color="#2db7f5" />
            未检测到布局环境，显示内部操作按钮
          </p>
          <p v-else-if="!layoutInfo.hasActions">
            <Icon type="ios-warning" color="#ff9900" />
            检测到布局环境但布局未配置操作按钮，显示内部操作按钮
          </p>
          <p v-else>
            <Icon type="ios-checkmark-circle" color="#19be6b" />
            检测到布局环境且布局已配置操作按钮，隐藏内部操作按钮
          </p>
        </div>
      </div>
    </Card>

    <Tabs value="standalone" type="card">
      <!-- 独立使用 - 显示内部按钮 -->
      <TabPane label="独立使用" name="standalone">
        <Card title="独立使用的动态表单（通过外部配置按钮）">
          <DynamicForm
            ref="standaloneForm"
            v-model="formData"
            :config="testFormConfig"
            :submit-config="submitConfig"
            :actions="standaloneActions"
            @layout-detected="handleLayoutDetected"
            @action="handleInternalAction"
          />
        </Card>
      </TabPane>

      <!-- 有按钮的布局 - 隐藏内部按钮 -->
      <TabPane label="有按钮的布局" name="with-actions">
        <div class="layout-container">
          <EasyLayout
            left-width="250px"
            :actions="layoutActions"
            @action="handleLayoutAction"
          >
            <template #left>
              <div class="left-content">
                <h3>左侧信息</h3>
                <p>这个布局配置了操作按钮</p>
              </div>
            </template>

            <template #right>
              <EasyCard title="表单（应隐藏内部按钮）">
                <DynamicForm
                  ref="withActionsForm"
                  v-model="formData"
                  :config="testFormConfig"
                  :submit-config="submitConfig"
                  @layout-detected="handleLayoutDetected"
                  @submit="handleSubmit"
                />
              </EasyCard>
            </template>
          </EasyLayout>
        </div>
      </TabPane>

      <!-- 无按钮的布局 - 显示内部按钮 -->
      <TabPane label="无按钮的布局" name="no-actions">
        <div class="layout-container">
          <EasyLayout left-width="250px">
            <!-- 注意：这里没有配置 :actions 属性 -->
            <template #left>
              <div class="left-content">
                <h3>左侧信息</h3>
                <p>这个布局没有配置操作按钮</p>
              </div>
            </template>

            <template #right>
              <EasyCard title="表单（通过外部配置按钮）">
                <DynamicForm
                  ref="noActionsForm"
                  v-model="formData"
                  :config="testFormConfig"
                  :submit-config="submitConfig"
                  :actions="noLayoutActions"
                  @layout-detected="handleLayoutDetected"
                  @action="handleInternalAction"
                />
              </EasyCard>
            </template>
          </EasyLayout>
        </div>
      </TabPane>

      <!-- 强制显示内部按钮 -->
      <TabPane label="强制显示按钮" name="force-show">
        <div class="layout-container">
          <EasyLayout
            left-width="250px"
            :actions="layoutActions"
            @action="handleLayoutAction"
          >
            <template #left>
              <div class="left-content">
                <h3>左侧信息</h3>
                <p>即使布局有按钮，也强制显示内部按钮</p>
              </div>
            </template>

            <template #right>
              <EasyCard title="表单（强制显示内部按钮）">
                <DynamicForm
                  ref="forceShowForm"
                  v-model="formData"
                  :config="testFormConfig"
                  :submit-config="submitConfig"
                  :force-show-actions="true"
                  :actions="forceShowActions"
                  @layout-detected="handleLayoutDetected"
                  @action="handleInternalAction"
                />
              </EasyCard>
            </template>
          </EasyLayout>
        </div>
      </TabPane>

      <!-- 不同模式的按钮 -->
      <TabPane label="不同模式按钮" name="mode-buttons">
        <Row :gutter="20">
          <Col span="8">
            <Card title="新增模式">
              <div style="margin-bottom: 16px;">
                <Tag color="green">CREATE 模式</Tag>
                <p>新增模式通常有：重置、保存按钮</p>
              </div>

              <DynamicForm
                ref="createForm"
                v-model="createFormData"
                :config="modeFormConfig"
                mode="create"
                :actions="createActions"
                @action="handleModeAction"
              />
            </Card>
          </Col>

          <Col span="8">
            <Card title="编辑模式">
              <div style="margin-bottom: 16px;">
                <Tag color="orange">EDIT 模式</Tag>
                <p>编辑模式通常有：取消、更新按钮</p>
              </div>

              <DynamicForm
                ref="editForm"
                v-model="editFormData"
                :config="modeFormConfig"
                mode="edit"
                :actions="editActions"
                @action="handleModeAction"
              />
            </Card>
          </Col>

          <Col span="8">
            <Card title="查看模式">
              <div style="margin-bottom: 16px;">
                <Tag color="blue">VIEW 模式</Tag>
                <p>查看模式通常有：返回、编辑按钮</p>
              </div>

              <DynamicForm
                ref="viewForm"
                v-model="viewFormData"
                :config="modeFormConfig"
                mode="view"
                :actions="viewActions"
                @action="handleModeAction"
              />
            </Card>
          </Col>
        </Row>

        <Card title="按钮配置代码" style="margin-top: 20px;">
          <pre class="code-block">{{ modeActionsCode }}</pre>
        </Card>
      </TabPane>

      <!-- 自定义内部按钮 -->
      <TabPane label="自定义内部按钮" name="custom-actions">
        <Card title="自定义内部操作按钮">
          <DynamicForm
            ref="customActionsForm"
            v-model="formData"
            :config="testFormConfig"
            :actions="customActions"
            @layout-detected="handleLayoutDetected"
            @action="handleCustomAction"
          />
        </Card>
      </TabPane>
    </Tabs>

    <!-- 操作日志 -->
    <Card title="操作日志" style="margin-top: 20px;">
      <div class="action-logs">
        <div v-for="(log, index) in actionLogs" :key="index" class="log-item">
          <Tag :color="log.type === 'success' ? 'green' : log.type === 'error' ? 'red' : 'blue'">
            {{ log.source }}
          </Tag>
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <Button @click="clearLogs" size="small" style="margin-top: 10px;">清空日志</Button>
    </Card>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FIELD_TYPES } from '../types'

export default {
  name: 'SmartActionsExample',
  components: {
    DynamicForm
  },
  data() {
    return {
      formData: {},
      layoutInfo: {
        detected: false,
        hasActions: false,
        shouldShowInternal: true
      },

      // 测试表单配置
      testFormConfig: {
        title: '智能按钮测试表单',
        columns: 2,
        showTitle: false,
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true,
            props: {
              placeholder: '请输入姓名'
            }
          },
          {
            key: 'email',
            label: '邮箱',
            type: FIELD_TYPES.INPUT,
            rules: [
              { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入邮箱'
            }
          },
          {
            key: 'remark',
            label: '备注',
            type: FIELD_TYPES.TEXTAREA,
            span: 24,
            props: {
              placeholder: '请输入备注信息',
              autosize: { minRows: 2, maxRows: 4 }
            }
          }
        ]
      },

      // 提交配置
      submitConfig: {
        text: '提交表单',
        type: 'primary',
        successMessage: '提交成功！'
      },

      // 布局操作按钮
      layoutActions: [
        {
          name: 'back',
          label: '返回',
          icon: 'ios-arrow-back',
          type: 'default'
        },
        {
          name: 'submit',
          label: '布局提交',
          icon: 'ios-send',
          type: 'primary'
        }
      ],

      // 独立使用时的按钮配置
      standaloneActions: [
        {
          text: '重置',
          type: 'default',
          icon: 'ios-refresh',
          onClick: this.handleReset
        },
        {
          text: '提交',
          type: 'primary',
          icon: 'ios-send',
          onClick: this.handleInternalSubmit
        }
      ],

      // 无布局按钮时的配置
      noLayoutActions: [
        {
          text: '返回',
          type: 'default',
          icon: 'ios-arrow-back',
          onClick: this.handleBack
        },
        {
          text: '提交',
          type: 'primary',
          icon: 'ios-send',
          onClick: this.handleInternalSubmit
        }
      ],

      // 强制显示时的按钮配置
      forceShowActions: [
        {
          text: '取消',
          type: 'default',
          onClick: this.handleCancel
        },
        {
          text: '保存',
          type: 'primary',
          onClick: this.handleSave
        }
      ],

      // 不同模式的表单数据
      createFormData: {
        name: '',
        email: '',
        phone: ''
      },
      editFormData: {
        name: '张三',
        email: '<EMAIL>',
        phone: '13800138000'
      },
      viewFormData: {
        name: '李四',
        email: '<EMAIL>',
        phone: '13900139000'
      },

      // 模式表单配置
      modeFormConfig: {
        title: '用户信息',
        columns: 1,
        showTitle: false,
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true,
            props: {
              placeholder: '请输入姓名'
            }
          },
          {
            key: 'email',
            label: '邮箱',
            type: FIELD_TYPES.INPUT,
            rules: [
              { type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入邮箱'
            }
          },
          {
            key: 'phone',
            label: '手机号',
            type: FIELD_TYPES.INPUT,
            props: {
              placeholder: '请输入手机号'
            }
          }
        ]
      },

      // 自定义内部按钮
      customActions: [
        {
          text: '重置',
          type: 'default',
          icon: 'ios-refresh',
          onClick: this.handleReset
        },
        {
          text: '保存草稿',
          type: 'warning',
          icon: 'ios-save',
          onClick: this.handleSaveDraft
        },
        {
          text: '提交审核',
          type: 'primary',
          icon: 'ios-send',
          onClick: this.handleSubmitReview
        }
      ],

      // 操作日志
      actionLogs: []
    }
  },

  computed: {
    // 新增模式按钮
    createActions() {
      return [
        {
          text: '重置',
          type: 'default',
          icon: 'ios-refresh',
          onClick: this.handleReset
        },
        {
          text: '保存',
          type: 'primary',
          icon: 'ios-save',
          onClick: this.handleCreate
        }
      ]
    },

    // 编辑模式按钮
    editActions() {
      return [
        {
          text: '取消',
          type: 'default',
          icon: 'ios-close',
          onClick: this.handleCancel
        },
        {
          text: '更新',
          type: 'primary',
          icon: 'ios-checkmark',
          onClick: this.handleUpdate
        }
      ]
    },

    // 查看模式按钮
    viewActions() {
      return [
        {
          text: '返回',
          type: 'default',
          icon: 'ios-arrow-back',
          onClick: this.handleBack
        },
        {
          text: '编辑',
          type: 'primary',
          icon: 'ios-create',
          onClick: this.handleEdit
        }
      ]
    },

    // 模式按钮配置代码示例
    modeActionsCode() {
      return `// 新增模式按钮
createActions: [
  { text: '重置', type: 'default', icon: 'ios-refresh', onClick: this.handleReset },
  { text: '保存', type: 'primary', icon: 'ios-save', onClick: this.handleCreate }
]

// 编辑模式按钮
editActions: [
  { text: '取消', type: 'default', icon: 'ios-close', onClick: this.handleCancel },
  { text: '更新', type: 'primary', icon: 'ios-checkmark', onClick: this.handleUpdate }
]

// 查看模式按钮
viewActions: [
  { text: '返回', type: 'default', icon: 'ios-arrow-back', onClick: this.handleBack },
  { text: '编辑', type: 'primary', icon: 'ios-create', onClick: this.handleEdit }
]`
    }
  },

  methods: {
    // 处理布局检测结果
    handleLayoutDetected(info) {
      this.layoutInfo = { ...info }
      this.addLog('检测', `布局检测: 环境=${info.detected}, 有按钮=${info.hasActions}, 显示内部=${info.shouldShowInternal}`)
    },

    // 处理表单提交
    handleSubmit(data) {
      this.addLog('提交', '表单提交成功')
      console.log('表单提交:', data)
    },

    // 处理布局操作按钮
    handleLayoutAction(action) {
      this.addLog('布局按钮', `点击了布局按钮: ${action.name}`)

      if (action.name === 'submit') {
        // 触发表单提交
        const activeForm = this.getActiveForm()
        if (activeForm) {
          activeForm.submit()
        }
      } else if (action.name === 'back') {
        this.addLog('布局按钮', '执行返回操作')
      }
    },

    // 处理内部操作按钮
    handleInternalAction(action) {
      this.addLog('内部按钮', `点击了内部按钮: ${action.text}`)
    },

    // 处理模式操作按钮
    handleModeAction(action) {
      this.addLog('模式按钮', `点击了按钮: ${action.text}`)
    },

    // 处理自定义操作按钮
    handleCustomAction(action) {
      this.addLog('自定义按钮', `点击了自定义按钮: ${action.text}`)
    },

    // 新增操作
    handleCreate(formData, formInstance) {
      if (formInstance) {
        formInstance.validate((valid) => {
          if (valid) {
            this.addLog('新增', '数据保存成功')
            console.log('新增数据:', formData)
          } else {
            this.addLog('新增', '表单验证失败')
          }
        })
      }
    },

    // 更新操作
    handleUpdate(formData, formInstance) {
      if (formInstance) {
        formInstance.validate((valid) => {
          if (valid) {
            this.addLog('编辑', '数据更新成功')
            console.log('更新数据:', formData)
          } else {
            this.addLog('编辑', '表单验证失败')
          }
        })
      }
    },

    // 编辑操作（从查看模式切换到编辑模式）
    handleEdit() {
      this.addLog('查看', '切换到编辑模式')
      // 这里可以触发模式切换或路由跳转
    },

    // 内部提交处理
    handleInternalSubmit(formData, formInstance) {
      if (formInstance) {
        formInstance.validate((valid) => {
          if (valid) {
            this.addLog('内部按钮', '表单验证通过，提交成功')
            console.log('内部提交数据:', formData)
          } else {
            this.addLog('内部按钮', '表单验证失败')
          }
        })
      } else {
        this.addLog('内部按钮', '直接提交')
        console.log('直接提交数据:', formData)
      }
    },

    // 返回处理
    handleBack() {
      this.addLog('内部按钮', '执行返回操作')
    },

    // 取消处理
    handleCancel() {
      this.addLog('内部按钮', '执行取消操作')
    },

    // 保存处理
    handleSave(formData, formInstance) {
      this.addLog('内部按钮', '执行保存操作')
      console.log('保存数据:', formData)
    },

    // 重置表单
    handleReset(formData, formInstance) {
      formInstance.resetFields()
      this.addLog('自定义按钮', '表单已重置')
    },

    // 保存草稿
    handleSaveDraft(formData, formInstance) {
      this.addLog('自定义按钮', '草稿已保存')
      console.log('保存草稿:', formData)
    },

    // 提交审核
    handleSubmitReview(formData, formInstance) {
      formInstance.validate((valid) => {
        if (valid) {
          this.addLog('自定义按钮', '已提交审核')
          console.log('提交审核:', formData)
        } else {
          this.addLog('自定义按钮', '表单验证失败')
        }
      })
    },

    // 获取当前活动的表单实例
    getActiveForm() {
      // 根据当前标签页返回对应的表单实例
      const activeTab = this.$refs.tabs?.activeKey || 'standalone'
      return this.$refs[`${activeTab}Form`]
    },

    // 添加日志
    addLog(source, message) {
      this.actionLogs.unshift({
        source,
        message,
        time: new Date().toLocaleTimeString(),
        type: source === '提交' ? 'success' : source === '错误' ? 'error' : 'info'
      })

      // 限制日志数量
      if (this.actionLogs.length > 15) {
        this.actionLogs = this.actionLogs.slice(0, 15)
      }
    },

    // 清空日志
    clearLogs() {
      this.actionLogs = []
    }
  }
}
</script>

<style scoped>
.smart-actions-example {
  padding: 20px;
}

.detection-info {
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    label {
      font-weight: bold;
      min-width: 120px;
      color: #333;
    }
  }

  .logic-explanation {
    margin-top: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;

    h4 {
      margin-bottom: 8px;
      color: #333;
    }

    p {
      margin: 8px 0;
      display: flex;
      align-items: center;

      .ivu-icon {
        margin-right: 8px;
      }
    }
  }
}

.layout-container {
  height: 400px;
  border: 1px solid #e8eaec;
  border-radius: 4px;
  overflow: hidden;
}

.left-content {
  padding: 20px;

  h3 {
    margin-bottom: 12px;
    color: #333;
  }

  p {
    color: #666;
    line-height: 1.6;
  }
}

.action-logs {
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  margin: 0 10px;
  color: #999;
  font-size: 12px;
  min-width: 80px;
}

.log-message {
  flex: 1;
  font-size: 13px;
}

/deep/ .ivu-tabs-content {
  min-height: 300px;
}
</style>
