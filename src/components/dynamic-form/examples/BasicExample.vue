<template>
  <div class="basic-example">
    <h2>基础示例</h2>
    
    <!-- 模式切换 -->
    <div style="margin-bottom: 20px;">
      <RadioGroup v-model="currentMode">
        <Radio label="create">新增模式</Radio>
        <Radio label="edit">编辑模式</Radio>
        <Radio label="view">查看模式</Radio>
      </RadioGroup>
    </div>
    
    <!-- 动态表单 -->
    <DynamicForm
      ref="dynamicForm"
      v-model="formData"
      :config="formConfig"
      :mode="currentMode"
      :actions="formActions"
      @field-change="handleFieldChange"
      @validate="handleValidate"
    />
    
    <!-- 表单数据展示 -->
    <div style="margin-top: 20px;">
      <h3>表单数据：</h3>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FORM_MODES, FIELD_TYPES } from '../types'

export default {
  name: 'BasicExample',
  components: {
    DynamicForm
  },
  data() {
    return {
      currentMode: FORM_MODES.CREATE,
      formData: {
        severelySickType: '1',
        patientSituation: '测试病情情况',
        treatmentSituation: '测试治疗情况'
      },
      formConfig: {
        title: '业务登记',
        columns: 2,
        labelWidth: 130,
        fields: [
          {
            key: 'severelySickType',
            label: '病号类别',
            type: FIELD_TYPES.SELECT,
            span: 16,
            required: true,
            options: [
              { label: '重病号', value: '1' },
              { label: '普通病号', value: '2' },
              { label: '慢性病号', value: '3' }
            ],
            props: {
              placeholder: '请选择病号类别',
              multiple: true
            }
          },
          {
            key: 'patientSituation',
            label: '病情情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 16,
            required: true,
            props: {
              placeholder: '请输入病情情况',
              autosize: { minRows: 3, maxRows: 5 }
            }
          },
          {
            key: 'treatmentSituation',
            label: '治疗情况',
            type: FIELD_TYPES.TEXTAREA,
            span: 16,
            required: true,
            props: {
              placeholder: '请输入治疗情况',
              autosize: { minRows: 3, maxRows: 5 }
            }
          }
        ]
      },
      formActions: [
        {
          text: '返回',
          type: 'default',
          onClick: this.handleBack
        },
        {
          text: '重置',
          type: 'default',
          onClick: this.handleReset
        },
        {
          text: '提交',
          type: 'primary',
          onClick: this.handleSubmit
        }
      ]
    }
  },
  methods: {
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value, formData)
    },
    
    handleValidate(prop, valid, message) {
      console.log('表单验证:', prop, valid, message)
    },
    
    handleBack() {
      this.$Message.info('返回操作')
    },
    
    handleReset() {
      this.$refs.dynamicForm.resetFields()
      this.$Message.success('表单已重置')
    },
    
    handleSubmit(formData, formInstance) {
      formInstance.validate((valid) => {
        if (valid) {
          const submitData = formInstance.getFormData()
          console.log('提交数据:', submitData)
          this.$Message.success('提交成功')
        } else {
          this.$Message.error('请填写完整的表单信息')
        }
      })
    }
  }
}
</script>

<style scoped>
.basic-example {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
