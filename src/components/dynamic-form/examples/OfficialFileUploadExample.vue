<template>
  <div class="official-file-upload-example">
    <Card title="官方文档对应的文件上传示例">
      <p>这个示例严格按照官方使用手册的属性和事件进行配置</p>
      
      <DynamicForm
        ref="officialForm"
        v-model="formData"
        :config="formConfig"
        :mode="mode"
        @field-change="handleFieldChange"
      />
      
      <div style="margin-top: 20px;">
        <Button @click="toggleMode" type="primary">
          切换模式: {{ mode === 'view' ? '查看' : '编辑' }}
        </Button>
        <Button @click="showFormData" style="margin-left: 10px;">
          查看表单数据
        </Button>
      </div>
      
      <div v-if="showData" style="margin-top: 30px;">
        <h3>表单数据：</h3>
        <pre class="data-display">{{ JSON.stringify(formData, null, 2) }}</pre>
      </div>
    </Card>
  </div>
</template>

<script>
import { FORM_MODES, FIELD_TYPES } from '@/components/dynamic-form/types'

export default {
  name: 'OfficialFileUploadExample',
  data() {
    return {
      mode: FORM_MODES.EDIT,
      showData: false,
      formData: {
        title: '官方示例文档',
        meetingDocuments: []
      }
    }
  },
  computed: {
    formConfig() {
      return [
        {
          title: '基本信息',
          fields: [
            {
              key: 'title',
              label: '文档标题',
              type: FIELD_TYPES.INPUT,
              required: true,
              span: 24,
              props: {
                placeholder: '请输入文档标题'
              }
            }
          ]
        },
        {
          title: '文件上传（严格按照官方文档）',
          fields: [
            {
              key: 'meetingDocuments',
              label: '会议文档',
              type: FIELD_TYPES.FILE_UPLOAD,
              span: 24,
              props: {
                // 严格按照官方文档的属性
                accept: '.pdf,.doc,.docx,.xls,.xlsx', // 接受文件类型
                expireTime: '3600', // 过期时间
                filePath: '/meeting/documents', // 文件路径：minio服务器中文件存储的具体位置
                serviceMark: 'MEETING_SERVICE', // 服务标识
                bucketName: 'meeting-bucket', // 桶名
                multiple: true, // 是否支持多选
                maxSize: 10 * 1024, // 最大size (10MB)
                maxFiles: 20, // 最多文件
                defaultList: this.formData.meetingDocuments, // 已上传列表
                isDetail: this.mode === FORM_MODES.VIEW // 标识是否可以上传删除文件
              },
              events: {
                // 严格按照官方文档的事件
                'beforeUpload': this.handleBeforeUpload, // 上传之前产生的回调
                'onExceededSize': this.handleOnExceededSize, // 文件超出指定大小回调
                'onExceededFile': this.handleOnExceededFile, // 超出文件最大数回调
                'fileRemove': this.handleFileRemove, // 文件删除回调
                'fileError': this.handleFileError, // 文件上传失败回调
                'fileSuccess': this.handleFileSuccess, // 单个文件上传成功回调
                'fileComplete': this.handleFileComplete // 文件全部上传成功回调
              }
            }
          ]
        }
      ]
    }
  },
  methods: {
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
    },
    
    // 对应官方文档：beforeUpload - 上传之前产生的回调
    handleBeforeUpload(file) {
      console.log('beforeUpload - 上传之前产生的回调:', file)
      
      // 可以在这里进行文件验证
      if (file.size > 10 * 1024 * 1024) {
        this.$Message.error('文件大小不能超过10MB')
        return false
      }
      
      // 检查文件类型
      const allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx']
      const fileExt = file.name.split('.').pop().toLowerCase()
      if (!allowedTypes.includes(fileExt)) {
        this.$Message.error('不支持的文件类型')
        return false
      }
      
      this.$Message.info(`准备上传文件: ${file.name}`)
      return true
    },
    
    // 对应官方文档：onExceededSize - 文件超出指定大小回调
    handleOnExceededSize() {
      console.log('onExceededSize - 文件超出指定大小回调')
      this.$Message.error('文件大小超出限制！')
    },
    
    // 对应官方文档：onExceededFile - 超出文件最大数回调
    handleOnExceededFile() {
      console.log('onExceededFile - 超出文件最大数回调')
      this.$Message.error('文件数量超出限制！')
    },
    
    // 对应官方文档：fileRemove - 文件删除回调
    handleFileRemove(file) {
      console.log('fileRemove - 文件删除回调:', file)
      this.$Message.info('文件已删除')
    },
    
    // 对应官方文档：fileError - 文件上传失败回调
    handleFileError(error) {
      console.log('fileError - 文件上传失败回调:', error)
      this.$Message.error('文件上传失败！')
    },
    
    // 对应官方文档：fileSuccess - 单个文件上传成功回调
    handleFileSuccess(file) {
      console.log('fileSuccess - 单个文件上传成功回调:', file)
      this.$Message.success(`文件 ${file.name} 上传成功！`)
    },
    
    // 对应官方文档：fileComplete - 文件全部上传成功回调
    handleFileComplete(files) {
      console.log('fileComplete - 文件全部上传成功回调:', files)
      this.$Message.success('所有文件上传完成！')
    },
    
    toggleMode() {
      this.mode = this.mode === FORM_MODES.VIEW ? FORM_MODES.EDIT : FORM_MODES.VIEW
    },
    
    showFormData() {
      this.showData = !this.showData
    }
  }
}
</script>

<style scoped>
.official-file-upload-example {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.data-display {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e8eaec;
}
</style>
