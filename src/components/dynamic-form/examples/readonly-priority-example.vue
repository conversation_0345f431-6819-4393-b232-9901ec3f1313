<template>
  <div class="readonly-priority-example">
    <h2>只读/禁用状态优先级控制示例</h2>
    
    <div class="control-panel">
      <h3>控制面板</h3>
      <div class="controls">
        <label>
          <input type="checkbox" v-model="componentDisabled" />
          组件级别禁用 (disabled)
        </label>
        <label>
          <select v-model="formMode">
            <option value="create">创建模式</option>
            <option value="edit">编辑模式</option>
            <option value="view">查看模式</option>
          </select>
        </label>
      </div>
    </div>

    <div class="form-container">
      <DynamicForm
        v-model="formData"
        :config="formConfig"
        :disabled="componentDisabled"
        :mode="formMode"
      />
    </div>

    <div class="data-display">
      <h3>表单数据</h3>
      <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
    </div>

    <div class="explanation">
      <h3>优先级说明</h3>
      <ul>
        <li><strong>字段1</strong>: 字段级别 disabled: false (最高优先级) - 始终启用</li>
        <li><strong>字段2</strong>: 无特殊设置 - 跟随节级别 readonly: true</li>
        <li><strong>字段3</strong>: 字段级别 readonly: true - 会被禁用</li>
        <li><strong>字段4</strong>: 字段级别 disabled: true (最高优先级) - 始终禁用</li>
        <li><strong>字段5</strong>: 无特殊设置 - 跟随组件级别或模式设置</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES, FORM_MODES } from '../types'

export default {
  name: 'ReadonlyPriorityExample',
  data() {
    return {
      componentDisabled: false,
      formMode: FORM_MODES.CREATE,
      formData: {
        field1: '字段1的值',
        field2: '字段2的值',
        field3: '字段3的值',
        field4: '字段4的值',
        field5: '字段5的值'
      },
      formConfig: [
        {
          title: '基本信息（节级别只读）',
          readonly: true,  // 节级别只读控制
          fields: [
            {
              key: 'field1',
              label: '字段1 (disabled: false)',
              type: FIELD_TYPES.INPUT,
              disabled: false,  // 字段级别启用，优先级最高
              span: 12,
              help: '字段级别 disabled: false，优先级最高，始终启用'
            },
            {
              key: 'field2',
              label: '字段2 (跟随节设置)',
              type: FIELD_TYPES.INPUT,
              span: 12,
              help: '无特殊设置，跟随节级别 readonly: true'
            },
            {
              key: 'field3',
              label: '字段3 (readonly: true)',
              type: FIELD_TYPES.INPUT,
              readonly: true,  // 字段级别只读
              span: 12,
              help: '字段级别 readonly: true，会被禁用'
            }
          ]
        },
        {
          title: '详细信息（无节级别设置）',
          fields: [
            {
              key: 'field4',
              label: '字段4 (disabled: true)',
              type: FIELD_TYPES.INPUT,
              disabled: true,  // 字段级别禁用，优先级最高
              span: 12,
              help: '字段级别 disabled: true，优先级最高，始终禁用'
            },
            {
              key: 'field5',
              label: '字段5 (跟随组件/模式)',
              type: FIELD_TYPES.INPUT,
              span: 12,
              help: '无特殊设置，跟随组件级别或模式设置'
            }
          ]
        }
      ]
    }
  }
}
</script>

<style scoped>
.readonly-priority-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.control-panel {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.controls {
  display: flex;
  gap: 20px;
  align-items: center;
}

.controls label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-container {
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.data-display {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.data-display pre {
  margin: 0;
  font-size: 12px;
}

.explanation {
  background: #e6f7ff;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.explanation ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.explanation li {
  margin-bottom: 8px;
}
</style>
