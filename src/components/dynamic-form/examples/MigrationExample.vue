<template>
  <div class="migration-example">
    <h2>表单迁移示例</h2>
    
    <Row :gutter="20">
      <!-- 原表单 -->
      <Col span="12">
        <Card title="原表单代码">
          <div class="original-form">
            <h4>原始表单：</h4>
            <Form ref="originalForm" :model="originalFormData" :rules="originalRules" :label-width="120">
              <FormItem label="姓名" prop="name">
                <Input v-model="originalFormData.name" placeholder="请输入姓名" />
              </FormItem>
              <FormItem label="年龄" prop="age">
                <InputNumber v-model="originalFormData.age" :min="1" :max="120" />
              </FormItem>
              <FormItem label="性别" prop="gender">
                <Select v-model="originalFormData.gender" placeholder="请选择性别">
                  <Option value="male">男</Option>
                  <Option value="female">女</Option>
                </Select>
              </FormItem>
              <FormItem label="邮箱" prop="email">
                <Input v-model="originalFormData.email" placeholder="请输入邮箱" />
              </FormItem>
              <FormItem label="备注" prop="remark">
                <Input v-model="originalFormData.remark" type="textarea" :autosize="{minRows: 3}" />
              </FormItem>
              <FormItem>
                <Button type="primary" @click="handleOriginalSubmit">提交</Button>
                <Button @click="handleOriginalReset" style="margin-left: 8px">重置</Button>
              </FormItem>
            </Form>
          </div>
        </Card>
      </Col>
      
      <!-- 迁移后的动态表单 -->
      <Col span="12">
        <Card title="迁移后的动态表单">
          <div class="migrated-form">
            <h4>动态表单：</h4>
            <DynamicForm
              ref="migratedForm"
              v-model="migratedFormData"
              :config="migratedConfig"
              :actions="migratedActions"
              @field-change="handleMigratedFieldChange"
            />
          </div>
        </Card>
      </Col>
    </Row>
    
    <!-- 迁移工具 -->
    <Card title="迁移工具" style="margin-top: 20px;">
      <Row :gutter="20">
        <Col span="8">
          <h4>原表单数据：</h4>
          <Input
            v-model="originalDataJson"
            type="textarea"
            :autosize="{ minRows: 8, maxRows: 12 }"
            placeholder="请输入原表单数据JSON"
          />
        </Col>
        <Col span="8">
          <h4>原表单规则：</h4>
          <Input
            v-model="originalRulesJson"
            type="textarea"
            :autosize="{ minRows: 8, maxRows: 12 }"
            placeholder="请输入原表单验证规则JSON"
          />
        </Col>
        <Col span="8">
          <h4>生成的配置：</h4>
          <Input
            v-model="generatedConfigJson"
            type="textarea"
            :autosize="{ minRows: 8, maxRows: 12 }"
            readonly
          />
        </Col>
      </Row>
      
      <div style="margin-top: 20px; text-align: center;">
        <Button type="primary" @click="generateConfig">生成动态表单配置</Button>
        <Button @click="copyConfig" style="margin-left: 10px;">复制配置</Button>
        <Button @click="generateCode" style="margin-left: 10px;">生成代码</Button>
      </div>
      
      <!-- 迁移报告 -->
      <div v-if="migrationReport" style="margin-top: 20px;">
        <h4>迁移报告：</h4>
        <Alert type="info" show-icon>
          <div slot="desc">
            <p><strong>总字段数：</strong>{{ migrationReport.summary.totalFields }}</p>
            <p><strong>成功迁移：</strong>{{ migrationReport.summary.migratedFields }}</p>
            <p v-if="migrationReport.summary.warnings.length > 0">
              <strong>警告：</strong>
              <ul>
                <li v-for="warning in migrationReport.summary.warnings" :key="warning">{{ warning }}</li>
              </ul>
            </p>
            <p v-if="migrationReport.summary.suggestions.length > 0">
              <strong>建议：</strong>
              <ul>
                <li v-for="suggestion in migrationReport.summary.suggestions" :key="suggestion">{{ suggestion }}</li>
              </ul>
            </p>
          </div>
        </Alert>
      </div>
    </Card>
    
    <!-- 生成的代码 -->
    <Modal v-model="showCodeModal" title="生成的Vue组件代码" width="80%" :mask-closable="false">
      <Input
        v-model="generatedCode"
        type="textarea"
        :autosize="{ minRows: 20, maxRows: 30 }"
        readonly
      />
      <div slot="footer">
        <Button @click="copyCode">复制代码</Button>
        <Button type="primary" @click="showCodeModal = false">关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import DynamicForm from '../DynamicForm.vue'
import { FIELD_TYPES } from '../types'
import { 
  generateConfigFromExisting, 
  generateMigrationCode, 
  generateMigrationReport 
} from '../migration-helper'

export default {
  name: 'MigrationExample',
  components: {
    DynamicForm
  },
  data() {
    return {
      // 原表单数据
      originalFormData: {
        name: '张三',
        age: 25,
        gender: 'male',
        email: '<EMAIL>',
        remark: '这是备注信息'
      },
      
      // 原表单验证规则
      originalRules: {
        name: [
          { required: true, message: '姓名不能为空', trigger: 'blur' }
        ],
        age: [
          { required: true, type: 'number', message: '年龄必须为数字', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        email: [
          { required: true, message: '邮箱不能为空', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ]
      },
      
      // 迁移后的表单数据
      migratedFormData: {},
      
      // 迁移后的表单配置
      migratedConfig: {
        title: '用户信息表单',
        columns: 2,
        labelWidth: 120,
        fields: [
          {
            key: 'name',
            label: '姓名',
            type: FIELD_TYPES.INPUT,
            required: true,
            props: {
              placeholder: '请输入姓名'
            }
          },
          {
            key: 'age',
            label: '年龄',
            type: FIELD_TYPES.NUMBER,
            required: true,
            props: {
              min: 1,
              max: 120
            }
          },
          {
            key: 'gender',
            label: '性别',
            type: FIELD_TYPES.SELECT,
            required: true,
            options: [
              { label: '男', value: 'male' },
              { label: '女', value: 'female' }
            ],
            props: {
              placeholder: '请选择性别'
            }
          },
          {
            key: 'email',
            label: '邮箱',
            type: FIELD_TYPES.INPUT,
            required: true,
            rules: [
              { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
            ],
            props: {
              placeholder: '请输入邮箱'
            }
          },
          {
            key: 'remark',
            label: '备注',
            type: FIELD_TYPES.TEXTAREA,
            span: 24,
            props: {
              autosize: { minRows: 3 }
            }
          }
        ]
      },
      
      // 迁移后的操作按钮
      migratedActions: [
        {
          text: '重置',
          type: 'default',
          onClick: this.handleMigratedReset
        },
        {
          text: '提交',
          type: 'primary',
          onClick: this.handleMigratedSubmit
        }
      ],
      
      // 工具相关数据
      originalDataJson: '',
      originalRulesJson: '',
      generatedConfigJson: '',
      generatedCode: '',
      migrationReport: null,
      showCodeModal: false
    }
  },
  created() {
    // 初始化工具数据
    this.originalDataJson = JSON.stringify(this.originalFormData, null, 2)
    this.originalRulesJson = JSON.stringify(this.originalRules, null, 2)
    this.migratedFormData = { ...this.originalFormData }
  },
  methods: {
    // 原表单方法
    handleOriginalSubmit() {
      this.$refs.originalForm.validate((valid) => {
        if (valid) {
          this.$Message.success('原表单提交成功')
          console.log('原表单数据:', this.originalFormData)
        } else {
          this.$Message.error('请填写完整信息')
        }
      })
    },
    
    handleOriginalReset() {
      this.$refs.originalForm.resetFields()
    },
    
    // 迁移后表单方法
    handleMigratedFieldChange(key, value, formData) {
      console.log('迁移后表单字段变化:', key, value)
    },
    
    handleMigratedReset(formData, formInstance) {
      formInstance.resetFields()
      this.$Message.success('动态表单已重置')
    },
    
    handleMigratedSubmit(formData, formInstance) {
      formInstance.validate((valid) => {
        if (valid) {
          this.$Message.success('动态表单提交成功')
          console.log('动态表单数据:', formInstance.getFormData())
        } else {
          this.$Message.error('请填写完整信息')
        }
      })
    },
    
    // 工具方法
    generateConfig() {
      try {
        const formData = JSON.parse(this.originalDataJson)
        const formRules = JSON.parse(this.originalRulesJson)
        
        const config = generateConfigFromExisting(formData, formRules, {
          title: '自动生成的表单',
          columns: 2,
          fieldMappings: {
            gender: {
              type: FIELD_TYPES.SELECT,
              options: [
                { label: '男', value: 'male' },
                { label: '女', value: 'female' }
              ]
            }
          }
        })
        
        this.generatedConfigJson = JSON.stringify(config, null, 2)
        this.migrationReport = generateMigrationReport({}, config)
        
        this.$Message.success('配置生成成功')
      } catch (error) {
        this.$Message.error('JSON格式错误: ' + error.message)
      }
    },
    
    copyConfig() {
      if (this.generatedConfigJson) {
        this.copyToClipboard(this.generatedConfigJson)
        this.$Message.success('配置已复制到剪贴板')
      } else {
        this.$Message.warning('请先生成配置')
      }
    },
    
    generateCode() {
      if (this.generatedConfigJson) {
        try {
          const config = JSON.parse(this.generatedConfigJson)
          this.generatedCode = generateMigrationCode(config)
          this.showCodeModal = true
        } catch (error) {
          this.$Message.error('配置格式错误')
        }
      } else {
        this.$Message.warning('请先生成配置')
      }
    },
    
    copyCode() {
      this.copyToClipboard(this.generatedCode)
      this.$Message.success('代码已复制到剪贴板')
    },
    
    copyToClipboard(text) {
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
    }
  }
}
</script>

<style scoped>
.migration-example {
  padding: 20px;
}

.original-form,
.migrated-form {
  min-height: 400px;
}

h4 {
  margin-bottom: 10px;
  color: #333;
}

/deep/ .ivu-card-body {
  min-height: 300px;
}

/deep/ .ivu-alert-desc ul {
  margin: 0;
  padding-left: 20px;
}

/deep/ .ivu-alert-desc li {
  margin: 5px 0;
}
</style>
