# iView 组件事件触发问题修复

## 问题描述

在使用 DynamicForm 组件时，发现 `handleInspectionFieldChange` 输入框没有触发 `field-change` 事件。经过分析发现，这是因为 iView 组件库中不同组件使用不同的事件名称来触发变化事件。

## 问题原因

iView 组件库中，不同组件的事件机制不同：

### 输入类组件
- **Input**: 使用 `input` 事件实时响应输入变化，`change` 事件在失去焦点时触发
- **InputNumber**: 使用 `on-change` 事件
- **Textarea**: 使用 `input` 事件

### 选择类组件
- **Select**: 使用 `on-change` 事件
- **Radio**: 使用 `on-change` 事件
- **Checkbox**: 使用 `on-change` 事件
- **Switch**: 使用 `on-change` 事件

### 日期时间组件
- **DatePicker**: 使用 `on-change` 事件
- **TimePicker**: 使用 `on-change` 事件

### 其他组件
- **Rate**: 使用 `on-change` 事件
- **Slider**: 使用 `on-change` 事件

## 解决方案

在 `DynamicForm.vue` 的 `getFieldEvents` 方法中，为不同类型的组件添加相应的事件监听：

```javascript
// 获取字段事件
getFieldEvents(field) {
  const events = {...field.events}

  // 添加默认的change事件来更新数据
  const originalChange = events.change
  const originalInput = events.input
  
  const changeHandler = (value) => {
    // 处理数据变化逻辑
    this.$set(this.formData, field.key, processedValue)
    this.$emit('input', this.formData)
    this.$emit('field-change', field.key, processedValue, this.formData)
  }

  const inputHandler = (value) => {
    // 处理输入变化逻辑
    this.$set(this.formData, field.key, value)
    this.$emit('input', this.formData)
    this.$emit('field-change', field.key, value, this.formData)
  }

  events.change = changeHandler

  // 为输入框类型添加 input 事件（实时响应）
  if (field.type === FIELD_TYPES.INPUT || 
      field.type === FIELD_TYPES.TEXTAREA ||
      field.type === FIELD_TYPES.NUMBER) {
    events.input = inputHandler
  }

  // 为各种组件添加 on-change 事件（iView 特有）
  if (field.type === FIELD_TYPES.DATE_TIME_PICKER ||
      field.type === FIELD_TYPES.DATETIME_PICKER ||
      field.type === FIELD_TYPES.DATE_PICKER ||
      field.type === FIELD_TYPES.TIME_PICKER ||
      field.type === FIELD_TYPES.SELECT ||
      field.type === FIELD_TYPES.RADIO ||
      field.type === FIELD_TYPES.CHECKBOX ||
      field.type === FIELD_TYPES.SWITCH ||
      field.type === FIELD_TYPES.RATE ||
      field.type === FIELD_TYPES.SLIDER ||
      field.type === FIELD_TYPES.NUMBER) {
    events['on-change'] = changeHandler
  }

  return events
}
```

## 修复内容

1. **INPUT 类型**: 添加 `input` 事件监听，实现实时响应输入变化
2. **TEXTAREA 类型**: 添加 `input` 事件监听
3. **NUMBER 类型**: 同时添加 `input` 和 `on-change` 事件监听
4. **SELECT 类型**: 添加 `on-change` 事件监听
5. **RADIO 类型**: 添加 `on-change` 事件监听
6. **CHECKBOX 类型**: 添加 `on-change` 事件监听
7. **SWITCH 类型**: 添加 `on-change` 事件监听
8. **RATE 类型**: 添加 `on-change` 事件监听
9. **SLIDER 类型**: 添加 `on-change` 事件监听
10. **日期时间类型**: 添加 `on-change` 事件监听

## 测试验证

创建了 `input-change-test.vue` 测试页面，包含所有组件类型的测试，验证 `field-change` 事件是否正确触发。

## 影响范围

此修复影响所有使用 DynamicForm 组件的页面，确保所有字段类型的变化事件都能正确触发，包括：

- 艾滋病管理登记页面的输入框
- 其他业务表单的各种字段类型
- 所有使用动态表单的功能模块

## 注意事项

1. 此修复保持了向后兼容性，不会影响现有功能
2. 如果字段配置中已经定义了 `events.change` 或 `events.input`，会保留原有逻辑
3. 建议在使用时测试各种字段类型的事件触发是否正常

## 相关文件

- `src/components/dynamic-form/DynamicForm.vue` - 主要修复文件
- `src/components/dynamic-form/test/input-change-test.vue` - 测试页面
- `src/view/snjy/aids-management/addRecord.vue` - 问题发现页面
