<template>
	<span>
		<Modal v-model="openStatus" :mask-closable="false"  :closable="false" class-name="bsp-role-assign" :width="1024">
			 <div class="flow-modal-title" slot="header">
				<span  style="font-size: 17px !important;">角色分配</span>
				<span @click="cancel" style="position: absolute; right: 6px;font-size: 32px;cursor: pointer;">
					<i class="ivu-icon ivu-icon-ios-close"></i>
				</span>
			</div>
			<div class="bsp-role-assign">
                <div class="flex-box">
                    <div class="left-box">
                        <Tabs :animated="true" v-model="paneName" @on-click="switchTabs">
                            <TabPane v-for="(appItem, index) in appList" :label="appItem.name" :name="appItem.code" :key="index">
                                <div class="search-div"><!--  style="width:100%;text-align: right;height: 48px" -->
                                    <Input search placeholder="搜索角色名称" v-model="searchTxt" @on-change="selectTreeNode" @on-search="selectTreeNode" style="margin-right:10px;margin-bottom:5px;width:300px;"/>
                                </div>
                                <div  class="roles-box bsp-scroll">
                                    <Collapse simple :value="openTabsKind">
                                        <Panel v-for="(item) in appRolesMap[appItem.code]" :name="item.catName" :key="item.id">
                                            <span @click.stop.prevent="checkAllNode(item)" style="color:#333;">
                                                <Checkbox size="large" v-model="item.checkAll" :indeterminate="item.indeterminate" :key="item.id">{{item.catName}}</Checkbox>
                                            </span>
                                            <div slot="content" >
                                                <ul class="ul-box">
                                                    <li class="li-box" v-for="(subItem, index) in item.roles" :key="index">
                                                        <Tooltip :content="subItem.name" placement="bottom">
                                                            <Checkbox size="large" v-model="subItem.checked" :key="subItem.id"
                                                                      @on-change="singleCheckNode(subItem,item,index)">{{subItem.name}}</Checkbox>
                                                        </Tooltip>
                                                    </li>
                                                </ul>
                                            </div>
                                        </Panel>
                                    </Collapse>
                                </div>
                            </TabPane>
                        </Tabs>
                    </div>
                    <div class="right-box">
                        <div class="tit-box" >
                            已授权角色
                            <div style="float:right;margin-right:0px;" title="删除全部">
                                <img src="./clearorg.png" @click="clearAll()" style="cursor: pointer;margin-top:8px;"/>
                            </div>
                        </div>
                        <div>
                            <div  class="content-box bsp-scroll">
                                <table class="role-table">
                                    <tbody v-for="(app, index) in appList" :key="index">
                                        <tr v-for="(item, i) in checkedRolesMap[app.code]"  :title="app.name + '-' + item.name" :key="i">
                                            <td class="role-left">
                                                <Tooltip :content="item.name" placement="bottom">
                                                    <span>{{app.name}}</span> - {{item.name}}
                                                </Tooltip>
                                            </td>
                                            <td class="role-del">
                                                <div @click="removeRole(item,app)" class="img-box">&nbsp;</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
			</div>
            <div slot="footer">
				<Button  class="cancel-btn" @click="cancel">取&nbsp;消</Button>
				<Button type="primary" class="cancel-btn main-button" :loading="loadding" @click="comfired">确&nbsp;定</Button>
			</div>
		</Modal>
	</span>
</template>
<script>
import {mapGetters} from "vuex";

export default {
    name:"role-assign",
    props: {
        /**
         * 0：返回已选择角色ID，例如
         *      {
         *          "system":"00AA497D3F0D43F38819E13D59150345,02A627425835416584D8EB9232B7152A",
         *          "public":"18DD82D0BACF431DB6ADAEEC82E2BCA8,18DD82D0BACF431DB6ADAEEC82E2BCA9"
         *      }
         * 1：返回已选择角色ID，和已选择角色详细信息
         *      {
         *          "system":"00AA497D3F0D43F38819E13D59150345",
         *          "detailSystem":[
         *              {
         *                  addTime: "2020-12-30 10:28:02"
         *                  addUser: "CB18DA2D72A44FCEA78AC82D8E268E63"
         *                  catId: "7438d8d0913d4ff491aba46c12f58388"
         *                  catName: "法制类角色"
         *                  code: "010013"
         *                  id: "00AA497D3F0D43F38819E13D59150345"
         *                  isAdmin: "0"
         *                  isAssign: "0"
         *                  isPublic: "0"
         *                  isdel: "0"
         *                  name: "法制局长（总署）"
         *                  orderId: 18
         *                  rank: "0"
         *                  systemMark: "hgzfbh"
         *                  updateTime: "2020-12-30 11:17:44"
         *                  updateUser: "CB18DA2D72A44FCEA78AC82D8E268E63"
         *              }
         *          ]
         *          "public":"18DD82D0BACF431DB6ADAEEC82E2BCA8",
         *          "detailPublic":[
         *              {
         *                  addTime: "2021-03-12 14:58:42"
         *                  addUser: "512501196512305186"
         *                  appId: ""
         *                  catId: "7438d8d0913d4ff491aba46c12f58388"
         *                  catName: "法制类角色"
         *                  code: "564445"
         *                  id: "18DD82D0BACF431DB6ADAEEC82E2BCA8"
         *                  isAdmin: "0"
         *                  isAssign: "0"
         *                  isPublic: "1"
         *                  isdel: "0"
         *                  name: "和规范化发过"
         *                  orderId: 1
         *                  rank: "1"
         *              }
         *          ]
         *      }
         * 2：不返回值，由组件做保存，
         */
        returnVal: {
            type: String,
            default: '0'
        },
        /**
         * 用户ID
         * returnVal='2' 时必传userID
         */
        userId: {
            type: String,
            default: ''
        },
        /**
         * 区域ID
         * returnVal='2' 时必传orgId
         */
        orgId: {
            type: String,
            default: ''
        },
        /**
         * 应用ID，无条件必传
         */
        appId: {
            type:String,
            default: ''
        },
        /**
         * 已选择的系统角色ID
         */
        hasSelectedSystem: {
            type: Array,
            default: () => [],
        },
        /**
         * 已选择普通角色ID
         */
        hasSelectedPublic: {
            type: Array,
            default: () => [],
        },
    },
    data () {
        return {
            searchTxt: '',
            openStatus: true,
            loadding:false,
            openTabsKind:[],
            // 被选中角色ID
            selectedRoleIdArr: [],
            // 被选中角色组ID
            selectedPublicIdArr: [],
            appName:'',
            currentApp:{},
            paneName:'',
            appList:[],
            appMap: new Map(),
            appRolesMap:new Map(),
            originalAppRolesMap:new Map(),
            checkedRolesMap:new Map(),
        }
    },
    created() {
        if (this.returnVal === '2' && (!this.userId || !this.orgId)) {
            this.$Modal.error({
                title:"错误提示",
                content:"返回类型为2，必须传递用户ID和机构ID",
                onOk:()=>{
                    this.cancel()
                }
            })
            return;
        }
        if (this.returnVal === '2') {
            this.queryRoleByUserId()
        } else {
            if (this.hasSelectedSystem && this.hasSelectedSystem.length > 0) {
                this.hasSelectedSystem.forEach(e=>{
                    this.selectedRoleIdArr.push(e)
                })
            }
            if (this.hasSelectedPublic && this.hasSelectedPublic.length > 0) {
                this.hasSelectedPublic.forEach(e=>{
                    this.selectedPublicIdArr.push(e)
                })
            }
        }
        if ("admin" === this.appId) {
            this.getAppList();
        } else {
            this.getAppById();
        }
    },
    methods: {
        ...mapGetters([
            'isAdmin',
        ]),
        cancel () {
            this.$emit('on_cancel')
        },
        focus() {
            this.$refs['input'].focus()
        },
        checkAllNode(item) {
            item.indeterminate = false
            item.checkAll = !item.checkAll
            let arr = []
            if (this.checkedRolesMap[this.currentApp.code]) {
                arr = this.checkedRolesMap[this.currentApp.code]
            }
            item.roles.forEach(e=>{
                if (item.checkAll) {
                    e.checked = true
                    arr.push(e)
                } else {
                    e.checked = false
                    for (let x=0; x<arr.length; x++) {
                        if (arr[x].id === e.id) {
                            arr.splice(x,1)
                        }
                    }
                }
            })
            this.checkedRolesMap[this.currentApp.code] = arr
            this.$forceUpdate()
        },
        warnMsg (msg) {
            this.$Modal.warning({
                title: '温馨提示',
                content: msg
            })
        },
        removeRole(item,app) {
            let arr = this.checkedRolesMap[app.code]
            for (let x=0;x<arr.length; x++) {
                if (item.id === arr[x].id) {
                    arr.splice(x,1)
                }
            }
            this.checkedRolesMap[app.code] = arr
            this.appRolesMap[app.code].forEach(kind=>{
                let flag = false
                let checkflag = true
                let inflag = false
                kind.roles.forEach(e=>{
                    if (item.id === e.id) {
                        flag = true
                        e.checked = false
                    }
                    if (e.checked) {
                        inflag = true
                    } else {
                        checkflag = false
                    }
                })
                if (flag) {
                    kind.checkAll = false
                    kind.indeterminate = false
                    if (!checkflag && inflag) {
                        kind.indeterminate = true
                    }
                    this.$forceUpdate()
                    return;
                }
            })
        },
        queryRoleByUserId(){
            this.$store.dispatch('postRequest',{
                url: this.$path.uac_get_roles_by_user_id,
                params: {userId:this.userId,orgId:this.orgId}
            }).then(resp => {
                if (resp.success) {
                    let result = resp.data;
                    let systemList = result['01']
                    if (systemList && systemList.length > 0) {
                        systemList.forEach(e=>{
                            this.selectedRoleIdArr.push(e)
                        })
                    }
                    let normalList = result['02']
                    if (normalList && normalList.length > 0) {
                        normalList.forEach(e=>{
                            this.selectedPublicIdArr.push(e)
                        })
                    }
                } else {
                    this.$Notice.error({
                        title:"错误提示",
                        desc:"加载已选择角色异常：" + resp.msg
                    })
                }
            })
        },
        getAppById(){
            this.$store.dispatch('postRequest',{
                url: this.$path.uac_get_app_by_id,
                params: {appId:this.appId}
            }).then(resp => {
                if (resp.success) {
                    this.appList.push(resp.data)
                }
                /*else {
                    this.$Notice.error({
                        title:'错误信息',
                        desc:'app名称查询失败：'+resp.msg
                    })
                }*/
                let publicApp = {
                    id:"",
                    name:"通用角色",
                    code:"public"
                }
                this.appList.push(publicApp)
                this.appList.forEach(e=>{
                    this.appMap[e.code] = e
                })
                this.appName = this.appList[0].name
                this.paneName = this.appList[0].code
                this.currentApp = this.appList[0]
                this.dealAppListRoles();
            })
        },
        getAppList() {
            this.$store.dispatch('postRequest', {
                url: this.$path.uac_app_list_url, params: {}
            }).then(resp => {
                if (resp.success) {
                    let data = resp.data
                    // 非超级管理员只能查看系统应用
                    if (!this.isAdmin) {
                        data = data.filter(it => it.code == serverConfig.APP_CODE)
                    }
                    this.appList = data
                    let publicApp = {
                        id:"",
                        name:"通用角色",
                        code:"public"
                    }
                    this.appList.push(publicApp)
                    this.appList.forEach(e=>{
                        this.appMap[e.code] = e
                    })
                    if (this.appList && this.appList.length > 0) {
                        this.currentApp = this.appList[0]
                        this.appName = this.appList[0].name
                        this.paneName = this.currentApp.code
                        this.$nextTick(() => {
                            // 获取应用下的树数据
                            this.dealAppListRoles();
                        })
                    } else {
                        this.$Notice.warning({
                            title: "温馨提示",
                            desc: "暂无可查看应用"
                        })
                    }
                } else {
                    this.$Notice.error({
                        title: "错误提示",
                        desc: resp.msg
                    })
                }
            })
        },
        dealAppListRoles(){
            if (this.appList.length === 0) {
                return;
            }
            let idArr = []
            this.appList.forEach(e=>{
                if (e.code !== 'public') {
                    idArr.push(e.id)
                }
            })
            this.getRolesByAppIds(idArr.join(","))
        },
        getRolesByAppIds(ids){
            let _this = this
            this.$store.dispatch("postRequest", {
                url: this.$path.get_all_app_roles_by_appIds,
                params: {appIds:ids}
            }).then(resp => {
                this.loadding = false
                if (resp.success) {
                    _this.groupRolesByAppId(resp.rows);
                } else {
                    this.$Notice.error({
                        title:'错误提示',
                        desc:resp.msg,
                    })
                }
            });
        },
        groupRolesByAppId(arr){
            let map = new Map();
            arr.forEach(e=>{
                if (e.isPublic === '1') {
                    if (!map['public']) {
                        let tempArr = []
                        tempArr.push(e)
                        map['public'] = tempArr
                    } else {
                        map['public'].push(e)
                    }
                } else {
                    if (!map[e.appId]) {
                        let tempArr = []
                        tempArr.push(e)
                        map[e.appId] = tempArr
                    } else {
                        map[e.appId].push(e)
                    }
                }
            })
            let appId = ''
            let flag = false
            this.appList.forEach(app=>{
                if (app.code === 'public') {
                    appId = app.code
                    flag = true
                } else {
                    appId = app.id
                    flag = false
                }
                if (map[appId]) {
                    let tree = this.buildTree(map[appId],flag, app);
                    this.appRolesMap[app.code] = tree
                    this.originalAppRolesMap[app.code] = tree
                }
            })
            this.$forceUpdate()
        },
        buildTree(arr,isPublic,app){
            // 转换结果
            let result = [];
            // Map用于分组取值
            let devideMap = {};
            // 一个分类的角色数组
            let oneGroupRoleList = [];
            arr.forEach((item, index) => {
                item.catId = item.catId ? item.catId : "others";
                item.catName = item.catName ? item.catName : "其他";
                oneGroupRoleList = devideMap[item.catId];
                if (oneGroupRoleList == undefined || oneGroupRoleList == null) {
                    oneGroupRoleList = [];
                    devideMap[item.catId] = oneGroupRoleList;

                    var obj = {};
                    obj.catId = item.catId;
                    obj.catName = item.catName;
                    obj.roles = oneGroupRoleList;
                    result.push(obj);
                }
                oneGroupRoleList.push(item);
            });
            // 添加 多选字段
            result.map(res => {
                res.indeterminate = false
                let flag = true
                let hasChecked = false
                if (res.roles && res.roles.length > 0) {
                    res.fold = true
                    res.roles.forEach(e=>{
                        if (isPublic) {
                            if (this.selectedPublicIdArr.indexOf(e.id) > -1) {
                                e.checked = true
                                hasChecked = true
                                if (!this.checkedRolesMap[app.code]) {
                                    let arr = []
                                    arr.push(e)
                                    this.checkedRolesMap[app.code] = arr
                                } else {
                                    this.checkedRolesMap[app.code].push(e)
                                }
                            } else {
                                flag = false
                                e.checked = false
                            }
                        } else {
                            if (this.selectedRoleIdArr.indexOf(e.id) > -1) {
                                e.checked = true
                                hasChecked = true
                                if (!this.checkedRolesMap[app.code]) {
                                    let arr = []
                                    arr.push(e)
                                    this.checkedRolesMap[app.code] = arr
                                } else {
                                    this.checkedRolesMap[app.code].push(e)
                                }
                            } else {
                                flag = false
                                e.checked = false
                            }
                        }
                    })
                    if (!flag && hasChecked) {
                        res.indeterminate = true
                    }
                } else {
                    res.fold = false
                    flag = false
                }
                res.checkAll = flag
                this.openTabsKind.push(res.catName)
            })
            return result;
        },
        singleCheckNode(subItem,item,index){
            if (subItem.checked) {
                if (!this.checkedRolesMap[this.currentApp.code]) {
                    let arr = []
                    arr.push(subItem)
                    this.checkedRolesMap[this.currentApp.code] = arr
                } else {
                    this.checkedRolesMap[this.currentApp.code].push(subItem)
                }
            } else {
                let arr = this.checkedRolesMap[this.currentApp.code]
                for (let x=0; x<arr.length; x++) {
                    if (subItem.id === arr[x].id) {
                        arr.splice(x,1)
                        this.checkedRolesMap[this.currentApp.code] = arr
                    }
                }
            }
            let inflag = false
            let flag = true
            item.roles.forEach(e=>{
                if (e.checked) {
                    inflag = true
                } else {
                    flag = false
                }
            })
            item.checkAll = flag
            item.indeterminate = false
            if (!flag && inflag) {
                item.indeterminate = true
            }
            this.$forceUpdate()
        },
        comfired(){
            this.loadding = true
            let selectedRoleIds = []
            let selectedPublicIds = []
            let roles = []
            let publicRoles = []
            this.appList.forEach(app=>{
                if (this.checkedRolesMap[app.code] && this.checkedRolesMap[app.code].length > 0){
                    this.checkedRolesMap[app.code].forEach(role=>{
                        if (app.code === 'public') {
                            selectedPublicIds.push(role.id)
                            publicRoles.push(role)
                        } else {
                            selectedRoleIds.push(role.id)
                            roles.push(role)
                        }
                    })
                }
            })
            let data = {
                selectedRoleIds : selectedRoleIds.join(","),
                selectedPublicIds : selectedPublicIds.join(",")
            }
            if (this.returnVal === '1') {
                data.selectedRoleList = roles
                data.selectedPublicList = publicRoles
                this.loadding = false
            }
            if (this.returnVal === '2') {
                data.userId = this.userId
                data.orgId = this.orgId
                this.$store.dispatch('postRequest',{
                    url: this.$path.uac_save_roles_by_user_id,
                    params: data
                }).then(resp=>{
                    this.loadding = false
                    if (resp.success) {
                        this.$Notice.success({
                            title:'成功提示',
                            desc:resp.msg
                        })
                        this.cancel()
                    } else {
                        this.$Notice.warning({
                            title:'错误提示',
                            desc:resp.msg
                        })
                    }
                })
                // this.on_cancel()
                this.$emit('update:showRoleAuth', false);
            } else {
                this.$emit("choice_confirm", data)
            }
        },
        selectTreeNode(){
            let arr = this.originalAppRolesMap[this.currentApp.code]
            let searchArr = []
            if (arr && arr.length > 0) {
                arr.forEach(kind=>{
                    let searchRoles = []
                    if (kind.roles && kind.roles.length > 0) {
                        kind.roles.forEach(role=>{
                            if (this.searchTxt !== '' && role.name.indexOf(this.searchTxt) > -1) {
                                searchRoles.push(role)
                            }
                        })
                    }
                    if (this.searchTxt !== '' && searchRoles.length > 0) {
                        let newKind = Object.assign({},kind)
                        newKind.roles = searchRoles
                        searchArr.push(newKind)
                    }
                })
            }
            if (this.searchTxt !== '') {
                this.appRolesMap[this.currentApp.code] = searchArr
            } else {
                this.appRolesMap[this.currentApp.code] = arr
            }
        },
        switchTabs(name){
            this.currentApp = this.appMap[name]
            this.appName = this.currentApp.name
            this.appList.forEach(e=>{
                if (e.code === name) {
                    this.currentApp = e
                }
            })
            this.selectTreeNode()
        },
        clearAll(){
            this.checkedRolesMap = new Map();
            this.appList.forEach(e=>{
                if (this.appRolesMap[e.code] && this.appRolesMap[e.code].length > 0) {
                    this.appRolesMap[e.code].forEach(kind=>{
                        kind.checkAll = false
                        kind.indeterminate = false
                        if (kind.roles && kind.roles.length > 0) {
                            kind.roles.forEach(role=>{
                                role.checked = false
                            })
                        }
                    })
                    this.originalAppRolesMap[e.code].forEach(kind=>{
                        kind.checkAll = false
                        kind.indeterminate = false
                        if (kind.roles && kind.roles.length > 0) {
                            kind.roles.forEach(role=>{
                                role.checked = false
                            })
                        }
                    })
                }
            })
            this.$forceUpdate()
        },
    }
}
</script>
<style scoped lang="less">

.bsp-role-assign {
    padding: 10px;
    overflow:hidden
}
.bsp-role-assign .flex-box {
    display:flex;/*设为伸缩容器*/
    flex-flow:row;/*伸缩项目单行排列*/
}
.bsp-role-assign .flex-box .left-box {
    flex:1;/*这里设置为占比1，填充满剩余空间*/
    margin:0px;
    border:1px solid #CEE0F0;
    max-width: 70%;
}
.bsp-role-assign .search-div {
    padding: 9px;
    text-align: right;
    background: #F5FAFF;
    margin-top: -5px;
}
.bsp-role-assign .flex-box .right-box {
    width:285px;/*固定宽度*/
    border:1px solid #CEE0F0;
    margin:0 0 0 10px;
}
.bsp-role-assign .ul-box {
    margin-left: 22px;
    overflow: hidden;
}

.bsp-role-assign .ul-box .li-box {
    float: left;
    padding-right: 10px;
    list-style-type: none;
    width: 25%;
    line-height: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}


/deep/ .bsp-role-assign {
    font-size: 16px !important;
}


.bsp-role-assign /deep/ .ivu-icon-ios-arrow-forward {
    float: right;
    line-height: 38px;
    font-size: 18px;
    // transform: rotate(90deg);
}

.bsp-role-assign /deep/ .ivu-collapse ,.bsp-role-assign /deep/ .ivu-collapse > .ivu-collapse-item {
    border-width: 0px;
}
.bsp-role-assign /deep/ .ivu-collapse-content > .ivu-collapse-content-box {
    padding-bottom: 0px;
}
.bsp-role-assign /deep/ .ivu-collapse > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header > i {
    transform: rotate(-90deg);
}

.bsp-role-assign /deep/ .ivu-input {
    height: 30px;
    border-color: #cee0f0;
    font-size: 15px !important;
}
.bsp-role-assign /deep/ .ivu-input:focus {
    border-color: #337bf5;
    box-shadow: inset 0 0 0 1000px #FFFFFF!important;
}
.bsp-role-assign /deep/ .ivu-tabs-bar {margin-bottom: 5px; border-bottom-color: #CEE0F0;}
.bsp-role-assign /deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab{border: none;background: none;height: 32px;line-height: 32px;font-size: 16px; padding: 0; margin: 0 32px 0 0;color: #333333;}
.bsp-role-assign /deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{position: relative;padding: 0;line-height: 32px;color: #2B5FD9;font-weight: bold;}
.bsp-role-assign /deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active::after{
    content: " ";position: absolute;width: 76px;height: 4px;background: #2B5FD9;border-radius: 4px;top: 100%;left: 50%;margin-left: -38px; margin-top: 0px;
}
// .bsp-role-assign /deep/ .ivu-tabs-nav-scroll,.bsp-role-assign /deep/ .ivu-tabs-nav-wrap,.bsp-role-assign /deep/ .ivu-tabs-nav-container{overflow: initial;}
.bsp-role-assign /deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-nav-container{height: 38px;line-height: 38px;padding: 0 22px;position: unset;}

.bsp-role-assign /deep/ .ivu-checkbox-wrapper {
    display: unset;
}

.bsp-role-assign .flow-modal-title {
    height: 40px;
    background: #2b5fda;
    width: 100%;
    text-indent: 1em;
    color: #fff;
    line-height: 40px;
}

.bsp-role-assign .img-box {
    cursor: pointer;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAABEUlEQVQ4T2NkwAL6156RZPj1bwMDw38zVGnGUwxsTAGFwSbP0bUxIgs0NPxn4lc/9YWBgYETmwVIYt8/3jTjaWhg/AcTgxvUv/iMJAPL32cEDECV/sMsVRgLcR3YIKhL/pJkCFTxx5tmzCCXgQ3qW3byCyMjAzc5Bv3/z/C1KMqch7Fz+REpNgbWp8iGuJsrMajLCzNMWnUaxey8MFOGGw/fMuw6eQ/Di4z9y0+eYmBgMEWWAWlgYmJk+PfvP9wwbGJIek6DDPqPzUvIGkHy6AZjRD8ug0AKYYaB2Miuw2YxThdRzSByvEadwMYW/W7mSgwapEY/1RIkVbMIyDCqZFpYuqBKMYKcyCARwLaelIINAPXCscs343ggAAAAAElFTkSuQmCC')  no-repeat center center;
}

.bsp-role-assign .job-del {
    padding: 0 0 0 12px;
}
.bsp-role-assign .roles-box{
    height:450px;
    margin-top:-5px;
    padding: 5px 0;
}
.bsp-role-assign .content-box {
    height: 500px;
    padding: 5px 0px;
    width: 100%;
}

.bsp-role-assign .role-table {
    border: none;
    border-collapse: collapse;
    width:100%;
    table-layout: fixed;
}
.bsp-role-assign .role-table tr:hover {
    background:#F0F5FF;
}
.bsp-role-assign .role-table td{
    border: none;
}
.bsp-role-assign .role-table .role-left{
    width:88%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    height: 36px;
    font-size: 16px;
    padding-right: 10px;
    padding-left: 15px;
}
.bsp-role-assign .role-table .role-left > span{
    color:#2B5FD9;
}
.bsp-role-assign .role-del{
    width:12%;
    padding-right: 15px;
    text-align:right;
}


.bsp-role-assign .bsp-scroll {
    overflow: overlay;
}



/deep/ .bsp-role-assign .ivu-modal-content .ivu-modal-body , /deep/ .bsp-role-assign .ivu-modal-header {
    padding: 0px
}

/deep/ .bsp-role-assign .ivu-modal-footer {
    border-top: 1px solid #CEE0F0;
    padding: 9px 18px 10px 18px;
    background: #F7FAFF;
}
/**重置对话框 */



.bsp-role-assign .main-button {
    background: #2b5fd9;
    margin-left: 20px;
}


.bsp-role-assign .cancel-btn {
    opacity: 1;
    font-size: 16px;

    border-radius: 2px;
    padding: 0;
    height: 30px;
    width: 70px;
}

.bsp-role-assign .tit-box{
    background: #F2F6FC;
    line-height:37px;
    padding: 0 15px;
    font-weight: bold;
    border-bottom: 1px solid #CEE0F0;
}

.bsp-scroll::-webkit-scrollbar {/*滚动条整体样式*/
    width: 6px;     /*高宽分别对应横竖滚动条的尺寸*/
    height: 6px;

}
.bsp-scroll::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
    border-radius: 3px;
    background: #b7c7dd;

}
.bsp-scroll::-webkit-scrollbar-track {/*滚动条里面轨道*/
    border-radius: 3px;
    background: #EDEDED;
}




</style>
