<template>
  <div ref="galley" class="bsp-img-container">
    <div class="upload-container" v-if="Number(maxFiles) != uploadList.length">
      <Tooltip content="点击选择上传视频" v-show="!isDetail && showUpload" placement="right-start">
        <div :class="['input-container',{'showBtnContainer':showBtnTag}]" @click="uploadFile">
          <input type="file" style="display: none" name="file" :id="id" :multiple="multiple" @change="upload" :accept="accept" />
          <!-- 采用按钮形式显示 -->
          <Button type="primary" icon="md-add" v-if="showBtnTag">点击上传视频</Button>

          <div class="tip-container" v-else>
            <div class="icon-container">
              <Icon type="ios-add" color="#fff" size="40"></Icon>
            </div>
            <p>点击上传视频</p>
          </div>
        </div>
      </Tooltip>
    </div>
    <div class="img-upload-list" v-for="(item, index) in uploadList" :key="index">
      <template v-if="item.status === 'progress'">
        <div>
          <Circle :percent="item.percent" :size="100">
            <span class="demo-Circle-inner" style="font-size: 24px">{{ item.percent }}%</span>
          </Circle>
        </div>
      </template>
      <template v-if="item.status === 'finished'">
        <!-- <img :src="item.downUrl"> -->
        
        <view-img :bucketName="item.bucketName" :fileName="item.fileName" :objectName="item.objectName" :serviceMark="item.serviceMark"></view-img>
        <div class="img-upload-list-cover4" v-if="item.success">
          <span class="img-upload-tip successColor">上传成功</span>
        </div>
        <!-- <div class="img-upload-list-cover">
          <video width="520" controls  name="media">
                                        <source :src="item.downUrl">
                                            Your browser does not support the video tag.
          </video>
        </div> -->
        <!-- <div class="img-upload-list-cover">
          <Icon type="md-download" class="icon-hover" title="下载图片" size="22" @click.native="handleDown(item)"></Icon>
          <Icon type="ios-eye" class="icon-hover" title="查看图片" size="22" @click.native="handleView(index)"></Icon>
          <Icon type="ios-trash-outline" size="20" class="icon-error" v-if="!isDetail" title="删除图片" @click.native="handleRemove(item, index)"></Icon>
        </div> -->
        <Icon type="ios-trash-outline" size="20" color="red" class="icon-error" style="  cursor: pointer;position: absolute;right: 0;top: 0;background: #fff;border-radius: 50%;" v-if="!isDetail" title="删除" @click.native="handleRemove(item, index)"></Icon>

      </template>
      <template v-else-if="item.status === 'error'">
        <!-- <img :src="item.dataUrl" /> -->
        <div class="img-upload-list-cover3">
          <span class="img-upload-tip failColor">上传失败</span>
        </div>
        <div class="img-upload-error-cover">
          <Icon type="md-refresh" class="icon-error" title="重新上传" size="22" @click.native="handleReload(item, index)"></Icon>
          <Icon type="ios-eye" title="查看图片" class="icon-hover" size="22" @click.native="handleView(index)"></Icon>
          <Icon type="ios-trash-outline" class="icon-error" title="删除图片" size="22" @click.native="handleRemove(item, index)"></Icon>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import  viewImg  from './viewImg'
import axios from 'axios'
import _ from 'lodash'
import 'viewerjs/dist/viewer.css'
import Viewer from 'viewerjs'

export default {
  props: {
    showBtn: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: '.mp4',//'.jpg,.jpeg,.png,.bmp,.gif,.tif'
    },
    // 过期时间
    expireTime: {
      type: Number,
      default: 3600
    },
    // 文件路径：minio服务器中文件存储的具体位置
    filePath: {
      type: String,
      default: ''
    },
    // 服务标识
    serviceMark: {
      type: String,
      default: ''
    },
    // 桶名
    bucketName: {
      type: String,
      default: ''
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 最大size
    maxSize: {
      type: Number,
      default: 10240
    },
    // 最多文件
    maxFiles: {
      type: Number,
      default: 20
    },
    // 文件类型
    fileType: {
      type: String,
      default: 'image'
    },
    fileTag: {
      type: String,
      default: ''
    },
    // 已上传列表
    defaultList: {
      type: Array,
      default: () => []
    },
    // 删除回调
    onRemove: {
      type: Function
    },
    // 上传之前产生的回调
    beforeUpload: {
      type: Function
    },
    // 文件超出指定大小的钩子
    onExceededSize: {
      type: Function
    },
    // 超出最大文件数
    onExceededFile: {
      type: Function
    },
    // 是否详情,标识是否可以上传和删除图片
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  components: {
    viewImg
  },
  created () {
    console.log(this.maxFiles, 'this.maxFiles')
    this.id = _.uniqueId()
  },
  mounted () {
    this.uploadList = this.defaultList
    if (this.uploadList && this.uploadList.length > 0) {
      let sortArr = this.uploadList.map(item => item.sort)
      this.sort = Math.max(...sortArr)
      this.uploadList.forEach(item => {
        this.$set(item, 'percent', 100)
        this.$set(item, 'status', 'finished')
      })
    }
    this.initImgTool()
  },
  data () {
    return {
      showBtnTag: this.showBtn,
      uploadList: [],
      viewer: null,
      showUpload: true,
      sort: 0,
      id: ''
    }
  },
  methods: {
    // 初始化预览工具
    initImgTool () {
      const galley = this.$refs.galley
      this.viewer = new Viewer(galley, {
        title: function (image) {
          return image.alt + ' (' + (this.index + 1) + '/' + this.length + ')'
        }
      })
    },
    // 文件上传出发事件
    uploadFile () {
      if (!this.serviceMark) {
        this.$Modal.info({
          title: '温馨提示',
          content: '服务标识不能为空'
        })
        return
      }
      if (!this.bucketName) {
        this.$Modal.info({
          title: '温馨提示',
          content: '桶名不能为空'
        })
        return
      }
      let fileId = document.getElementById(this.id)
      fileId.value = ''
      fileId.click()
    },
    // 文件上传
    upload () {
      const fileId = document.getElementById(this.id)
      let file = fileId.files // 获取文件流
      // 获取文件总数
      let fileArr = file.length + this.defaultList.length
      // 文件超出最大文件个数
      if (fileArr && fileArr > this.maxFiles) {
        if (this.onExceededFile) {
          this.onExceededFile(this.maxFiles, res => {
            this.$Modal.error({
              title: '温馨提示',
              content: res
            })
          })
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: '最多可上传' + this.maxFiles + '张图片'
          })
        }
        return
      }

      // 文件添加回调
      if (this.beforeUpload) {
        this.beforeUpload(file)
      }
      for (let j = 0; j < file.length; j++) {
        // 文件超出最大size限制
        let fileSize = Math.ceil(file[j].size / 1024)
        if (fileSize > this.maxSize) {
          if (this.onExceededSize) {
            this.onExceededSize(file, this.maxSize)
          } else {
            this.$Notice.info({
              title: '温馨提示',
              desc: `${file[j].name}大小超过${this.maxSize}k，请重新选择`
            })
          }
          continue
        }
        let uploadFileData = { file: file[j], fileSize: file[j].size, fileType: file[j].type, percent: 0, status: 'progress', sort: ++this.sort }
        console.log(uploadFileData, '888')
        this.getPresignUrl(uploadFileData)
        // filesArr.push()
        this.uploadList.push(uploadFileData)
      }
    },
    // 获取预上传地址
    getPresignUrl (fileObj) {
      let params = {
        serviceMark: this.serviceMark,
        bucket: this.bucketName,
        path: this.filePath,
        fileName: fileObj.file.name,
        expireTime: this.expireTime
      }
      this.$store.dispatch('postRequest', { url: '/bsp-com/com/oss/presign/putObject', params: params }).then(resp => {
        if (resp.success) {
          this.handleFileUpload(fileObj, fileObj.file.name, resp.data.presignedUrl, resp.data.objectName)
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg
          })
        }
      })
    },
    // 获取文件后缀
    getFileSuffix (fileName) {
      // 文件后缀
      let suffix = ''
      try {
        let flieArr = fileName.split('.')
        suffix = flieArr[flieArr.length - 1]
      } catch (err) {
        suffix = ''
      }
      return suffix
    },
    // 获取地址后，处理文件上传
    handleFileUpload (fileObj, fileName, url, objectName) {
      let _this = this
      return new Promise((resolve, reject) => {
        axios({
          method: 'PUT',
          url: `${url}`,
          headers: { 'Content-Type': 'application/octet-stream' },
          onUploadProgress: progressEvent => {
            if (progressEvent.lengthComputable) {
              let loaded = ((progressEvent.loaded / progressEvent.total) * 100) | 0
              if (loaded < 100) {
                fileObj.percent = loaded
              }
            }
          },
          data: fileObj.file
        })
          .then(res => {
            fileObj.percent = 100
            fileObj.bucketName = this.bucketName
            fileObj.objectName = objectName
            fileObj.serviceMark = this.serviceMark
            fileObj.fileName = fileName
            fileObj.status = 'finished'
            fileObj.success = true
            // 单文件上传成功的回调
            this.handleSFileSuccess(fileObj)
            // 文件上传成功得回调
            this.handleSuccess()
          })
          .catch(error => {
            if (error) {
              console.log(error)
              fileObj.percent = 0
              fileObj.bucketName = this.bucketName
              fileObj.objectName = objectName
              fileObj.serviceMark = this.serviceMark
              fileObj.fileName = fileName
              fileObj.status = 'error'
              // 图片上传失败，转为base64
              const reader = new FileReader()
              reader.readAsDataURL(fileObj.file)
              reader.onload = function (e) {
                _this.$set(fileObj, 'dataUrl', e.target.result)
              }
              this.handleFileError(fileObj)
            }
          })
        resolve()
      })
    },
    // 重新上传
    handleReload (item, index) {
      this.uploadList[index].status = 'progress'
      let file = this.dataURLtoBlob(item.dataUrl)
      this.$nextTick(() => {
        this.getPresignUrl(file)
      })
    },
    // 转blob文件
    dataURLtoBlob (baseurl) {
      let arr = baseurl.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr[1])
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], {
        type: mime
      })
    },
    // 预览
    handleView (index) {
      this.viewer.update()
      this.viewer.view(index)
    },
    // 处理移除
    handleRemove (file, index) {
      if (file.status == 'error') {
        this.uploadList.splice(file, 1)
        return
      }
      if (this.onRemove) {
        this.onRemove(file, res => {
          if (res) {
            this.removeFile(file, index)
          }
        })
      } else {
        this.removeFile(file, index)
      }
    },
    // 移除文件
    removeFile (file, index) {
      this.uploadList.splice(index, 1)
      this.$emit('fileRemove', file)
      this.handleSuccess()
      this.$nextTick(() => {})
    },
    // 处理上传成功
    handleSuccess () {
      const success = this.uploadList.every(item => item.status === 'finished')
      if (success) {
        this.$emit('fileComplete', this.buildData())
      }
    },
    // 当个文件上传成功
    handleSFileSuccess (file) {
      if (file.status === 'finished') {
        this.$emit('fileSuccess', file)
      }
    },
    // 文件上传失败钩子
    handleFileError (file) {
      this.$emit('fileError', file)
    },
    // 构建数据
    buildData () {
      let returnArr = this.uploadList.map(item => {
        return {
          serviceMark: item.serviceMark,
          fileName: item.fileName,
          fileSize: item.fileSize,
          fileType: item.fileType,
          fileSuffix: this.getFileSuffix(item.fileName),
          objectName: item.objectName,
          sort: item.sort
        }
      })
      return returnArr
    },
    // 下载
    handleDown (item) {
      this.getPresignDown(item)
    },
    // 获取图片预下载地址
    getPresignDown (item) {
      let params = {
        serviceMark: item.serviceMark,
        bucket: item.bucketName,
        objectName: item.objectName,
        expireTime: this.expireTime
      }
      this.$store.dispatch('getRequest', { url: '/bsp-com/com/oss/presign/getObject', params: params }).then(resp => {
        if (resp.success) {
          this.downloadByBlob(resp.data.presignedUrl, resp.data.objectName)
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg
          })
        }
      })
    },
    // 转换图片并下载图片
    downloadByBlob (url, name) {
      let _this = this
      let image = new Image()
      image.setAttribute('crossOrigin', 'anonymous')
      image.src = url
      image.onload = () => {
        let canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        let ctx = canvas.getContext('2d')
        ctx.drawImage(image, 0, 0, image.width, image.height)
        canvas.toBlob(blob => {
          let url = URL.createObjectURL(blob)
          _this.download(url, name)
          // 用完释放URL对象
          URL.revokeObjectURL(url)
        })
      }
    },
    // 下载
    download (href, name) {
      let eleLink = document.createElement('a')
      eleLink.download = name
      eleLink.href = href
      eleLink.click()
      eleLink.remove()
    },
    // 清空所有已上传文件
    fileRemoveAll () {
      this.uploadList = []
      // this.$emit('fileRemoveAll', this.uploadList)
      this.handleSuccess()
      this.$nextTick(() => {})
    }
  }
}
</script>
<style scoped>
@import './imgload.css';
</style>
