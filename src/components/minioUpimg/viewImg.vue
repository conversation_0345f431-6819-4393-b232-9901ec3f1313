<template>
  <span class="view-container">
    <!-- <img ref="galleyImg" v-if="imgUrl" :src="imgUrl" @click.stop="handleView" :alt="alt"> -->
    <video width="400px" style="margin-top: 16px;" controls  name="media" v-if="imgUrl">
      <source :src="imgUrl">
          Your browser does not support the video tag.
    </video>
  </span>
</template>

<script>
import 'viewerjs/dist/viewer.css'
import Viewer from 'viewerjs'
export default {
  props: {
    // 是否支持预览
    isView: {
      type: Boolean,
      default: false
    },
    // 桶名
    bucketName: {
      type: String,
      require: true
    },
    // 服务标识
    serviceMark: {
      type: String,
      require: true
    },
    // 文件路径
    objectName: {
      type: String,
      default: ''
    },
    // 文件名
    fileName: {
      type: String,
      require: true
    },
    // 属性提示
    alt: {
      type: String,
      default: ''
    }
  },
  created () {
   // this.getPresignDown()
    if (this.isView) {
      this.initViewTool()
    }
  },
  data () {
    return {
      imgUrl: '',
      viewer: null
    }
  },
  watch: {
    isView: {
      handler (val, newVal) {
        if (newVal) {
          this.initViewTool()
        }
      },
      immediate: true
    },
    bucketName: {
      handler (val) {
        if (val && this.objectName && this.serviceMark) {
          this.getPresignDown()
        }
      },
      immediate: true
    }
  },
  methods: {
    initViewTool () {
      const galley = this.$refs.galleyImg
      this.viewer = new Viewer(galley, {
        title: function (image) {
          return image.alt + ' (' + (this.index + 1) + '/' + this.length + ')'
        }
      })
    },
    // 预览
    handleView () {
      if (this.isView) {
        this.viewer.update()
        this.viewer.view()
      }
    },
    /**
     * 获取图片地址
     * @params bucketName 桶名
     * @params fileName 文件名
     */
    // 获取图片预览地址
    getPresignDown () {
      let params = {
        serviceMark: this.serviceMark,
        bucket: this.bucketName,
        objectName: this.objectName,
        expireTime: 3600
      }
      this.$store.dispatch('postRequest', { url: '/bsp-com/com/oss/presign/getObject', params: params }).then(resp => {
        if (resp.success) {
          this.imgUrl = resp.data.presignedUrl
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg
          })
        }
      })
    }
  }
}
</script>
<style scoped>
</style>
