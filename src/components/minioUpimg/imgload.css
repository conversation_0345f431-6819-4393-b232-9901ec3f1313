.bsp-img-container {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.bsp-img-container .img-upload-list {
  display: inline-block;
  width: 160px;
  height: 120px;
  text-align: center;
  line-height: 120px;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
  margin-left: 4px;
  margin-bottom: 4px;
}

.bsp-img-container .img-upload-list img {
  width: 100%;
  height: 100%;
}

.bsp-img-container .img-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .4);
  transition: opacity .3s;
}

.bsp-img-container .img-upload-list-cover .icon-hover:hover,
.bsp-img-container .img-upload-list-cover .icon-hover:hover {
  color: #3179F5;
}

.bsp-img-container .img-upload-list-cover .icon-error:hover,
.bsp-img-container .img-upload-list-cover .icon-error:hover {
  color: #E62E3D;
}

.bsp-img-container .img-upload-list:hover .img-upload-list-cover {
  /* display: block; */
}

.bsp-img-container .img-upload-error-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .4);
  transition: opacity .3s;
}

.bsp-img-container .img-upload-list:hover .img-upload-error-cover {
  display: block;
}

.bsp-img-container .img-uploading-cover {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.bsp-img-container .img-upload-list-cover3,
.bsp-img-container .img-upload-list-cover4 {
  position: absolute;
  top: 0;
  bottom: 0;
  height: 35px;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .6);
}

.bsp-img-container .img-upload-list-cover4 {
  animation: move 2s forwards;
}


.bsp-img-container .img-upload-list-cover3 .img-upload-tip,
.bsp-img-container .img-upload-list-cover4 .img-upload-tip {
  font-size: 12px;
  height: 35px;
  line-height: 35px;
  display: block;
}
.bsp-img-container .img-upload-list-cover4 .successColor {
  color: #fff;
}

.bsp-img-container .img-upload-list-cover3 .failColor {
  color: #E62E3D;
}

@keyframes move {
  form {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@-webkit-keyframes move {
  form {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.bsp-img-container .img-upload-list-cover i {
  color: #fff;
  font-size: 26px;
  cursor: pointer;
  margin: 0 5px;
}

.bsp-img-container .img-upload-error-cover i {
  color: #fff;
  font-size: 26px;
  cursor: pointer;
  margin: 0 2px;
}

.bsp-img-container .take-photo-container {
  width: 100px;
  height: 100px;
  line-height: 100px;
  border: 1px dashed #dcdee2;
  float: right;
  margin-left: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bsp-img-container.take-photo-container:hover {
  border: 1px dashed #2d8cf0;
}
/* 上传样式 */
.bsp-img-container .upload-container .input-container {
  width: 160px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #F0F4FB;
  border-radius: 2px;
  background-color: #F0F4FB;
}

.bsp-img-container .upload-container .input-container.showBtnContainer {
  width: 160px;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 2px;
  background-color: transparent;
}


.bsp-img-container .upload-container .tip-container {
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.bsp-img-container .upload-container .tip-container .icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #64BBFA;
}

.bsp-img-container .upload-container .tip-container p {
  font-family: Microsoft YaHei Regular, Microsoft YaHei Regular-Regular;
  color: #abb8cc;
  font-size: 16px;
}