<!--
 * @Author: hanjinxiang && <EMAIL>
 * @Date: 2021-11-30 15:36:00
 * @LastEditors: hanjinxiang && <EMAIL>
 * @LastEditTime: 2022-10-26 15:59:50
 * @FilePath: \ims-admin-web\src\components\pdfViewer.vue
 * @Description: 
-->
<template>
    <div class="dossierCenterContent" style="height: 100vh;">
        <!-- :src="'/pdfjs/web/viewer.html?file='+pdfUrl" -->
        <embed width="100%" height="100%" name="plugin" id="plugin" :src="pdfUrl" type="application/pdf" javascript="allow" />

        <!-- <iframe v-if="isPdfFileFlag" id="pdfViewerFrame" :src="'/pdfjs/web/viewer.html?file='+pdfUrl" frameborder="0" class="dossierCenterContent"></iframe>
        <iframe v-else id="ofdViewerFrame" :src="'/ofdView/ofdview.html?file='+pdfUrl" frameborder="0" class="dossierCenterContent"></iframe> -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            isPdfFileFlag: true,
            pdfUrl:''
        }
    },
    created(){
        if(this.$route.query.pdfUrl){
            this.pdfUrl = this.$route.query.pdfUrl //.replace(serverConfig.uploadUrl,serverConfig.downloadUrl);
            if(this.pdfUrl.includes('.ofd')){
                this.isPdfFileFlag = false
            }
        }
    },
    mounted(){
        // this.initOFD()
    },
    methods:{
        
    }
}
</script>
<style lang="less" scoped>
    #ofdView{
        width: 100%;
        height: 100%;
    }
</style>