
<template>
  <div class="com-null-box" :style="styles">
      <img class="content" src="./images/icon_no_result.svg" alt />
      <p>暂无数据</p>
  </div>
</template>

<script>
export default {
  name: "comNullBox",
  props: {
    height: {
      type: String,
      default: "auto"
    },
    width: {
      type: String,
      default: "auto"
    }
  },
  computed: {
    styles() {
      return {
        width: this.width,
        height: this.height,
      };
    }
  }
};
</script>

<style lang="less" scoped>
.com-null-box{
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .content {
    width:100%;
    height:100%;
    min-height: 120px;
    max-height: 300px;
    font-size: 0;
    margin:0 auto;
    display:flex;
    justify-content:center;
    align-items: flex-end;
  }
  p {
    font-size: 16px;
    letter-spacing: 5px;
    color: gray;
    transform: translateY(-100%);
  }
}
</style>
