# rs-DataGrid 组件

基于 s-DataGrid 封装的 Vue 2 组件，提供自动前缀管理、权限控制和完全兼容的数据表格解决方案。

## 🌟 核心特性

- ✅ **自动前缀管理** - 智能为 funcMark 和插槽名称添加前缀，避免命名冲突
- 🎯 **100% 兼容** - 完全保持 s-DataGrid 的所有功能和 API
- 🚀 **透传支持** - 无缝透传所有 props、events 和 methods
- 🔧 **灵活控制** - 支持全局和插槽级别的前缀控制
- 🎨 **全局组件** - 注册为全局组件，无需 import 即可使用
- 🛡️ **类型兼容** - 支持字符串和数组类型的 func 参数
- 📝 **智能处理** - 自动识别系统插槽，防重复前缀
- 🔄 **向后兼容** - 支持禁用前缀功能，兼容现有代码

## 📦 快速开始

### 安装和注册

rs-DataGrid 已注册为全局组件，无需手动导入：

```javascript
// main.js 中已自动注册
import RSDataGrid from '@/components/rs-datagrid/install.js'
Vue.use(RSDataGrid)
```

### 基础使用

```vue
<template>
  <!-- 直接使用，无需 import -->
  <rs-DataGrid
    ref="grid"
    prefix="my-module"
    funcMark="ihc:demo"
    :customFunc="true"
  >
    <!-- 系统插槽 -->
    <template v-slot:customHeadFunc="{ func, hasPermission }">
      <Button v-if="hasPermission('ihc:demo:add')" @click="handleAdd">
        新增
      </Button>
    </template>

    <!-- 操作插槽 -->
    <template v-slot:ihc:demo:edit="{ oper, row, index }">
      <Button @click="handleEdit(row)">{{ oper.name }}</Button>
    </template>
  </rs-DataGrid>
</template>

<script>
export default {
  methods: {
    handleAdd() {
      console.log('新增操作')
    },
    handleEdit(row) {
      console.log('编辑操作', row)
    }
  }
}
</script>
```

## 🎛️ 组件名称支持

rs-DataGrid 支持多种命名方式：

```vue
<!-- 推荐写法 -->
<rs-DataGrid funcMark="ihc:demo" />

<!-- 驼峰式 -->
<rsDataGrid funcMark="ihc:demo" />

<!-- kebab-case -->
<rs-data-grid funcMark="ihc:demo" />
```

## 📋 API 文档

### Props

| 参数 | 说明 | 类型 | 必填 | 默认值 |
|------|------|------|------|--------|
| prefix | 前缀字符串，会自动添加到 funcMark 和插槽名称前面。如不定义，会自动从 `Vue.prototype.globalAppCode` 获取 | String | 否 | '' |
| funcMark | 功能标识，用于后端权限控制 | String | 是 | - |
| enablePrefix | 是否启用前缀功能 | Boolean | 否 | true |
| ...其他 | 继承 s-DataGrid 的所有 props | - | - | - |

### Events

继承 s-DataGrid 的所有事件，完全透传。

### Methods

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| query_grid_data | 查询表格数据 | pageNo: Number | - |
| refreshGrid | 刷新表格 | pageNo: Number | - |
| reload | 重新加载数据（回到第一页） | - | - |
| getBatchSelect | 获取批量选择的数据 | - | Array |
| getCurrentData | 获取当前页数据 | - | Array |
| getBatchSelectIds | 获取选中行的 ID 数组 | idField: String | Array |
| hasSelection | 检查是否有选中数据 | - | Boolean |
| clearSelection | 清空选择 | - | - |
| selectAll | 全选 | - | - |
| clearAll | 取消全选 | - | - |
| setSelection | 设置选中行 | rows: Array | - |
| getSelection | 获取选中行数据（别名） | - | Array |
| exportData | 导出数据 | options: Object | - |
| confirmDelete | 确认删除 | row: Object, callback: Function | - |
| confirmBatchDelete | 批量删除确认 | callback: Function | - |
| getGridInstance | 获取原始 s-DataGrid 实例 | - | Object |

### 计算属性

| 属性名 | 说明 | 类型 |
|--------|------|------|
| batch_select | 批量选择的数据 | Array |
| data | 表格数据 | Array |
| pagination | 分页信息 | Object |
| loading | 加载状态 | Boolean |
| headOper | 头部操作按钮配置 | Array |
| rowOper | 行操作按钮配置 | Array |
| columns | 列配置（别名） | Array |
| tableData | 表格数据（别名） | Array |
| total | 总记录数 | Number |
| currentPage | 当前页码 | Number |
| pageSize | 每页大小 | Number |
| selectedRows | 选中行数据（别名） | Array |
| actualPrefix | 实际使用的前缀 | String |
| computedFuncMark | 计算后的 funcMark | String |

## 🔧 前缀控制功能

### 前缀获取规则

rs-DataGrid 按以下优先级获取前缀：

1. **props.prefix** - 如果组件传入了 prefix 属性，优先使用
2. **Vue.prototype.globalAppCode** - 如果没有传入 prefix，自动从全局获取
3. **无前缀** - 如果以上都没有，不添加前缀（保持原有行为）

```javascript
// 在 main.js 中设置全局应用代码
Vue.prototype.globalAppCode = 'myapp'

// 使用时可以不传 prefix
<rs-DataGrid funcMark="ihc:ztbhdjlb" />
// 等同于
<rs-DataGrid prefix="myapp" funcMark="ihc:ztbhdjlb" />
```

### 前缀控制方式

#### 1. 全局控制 - enablePrefix 属性

```vue
<!-- 启用前缀功能（默认） -->
<rs-DataGrid
  prefix="my-module"
  funcMark="ihc:demo"
  :enablePrefix="true"
>
  <!-- 所有插槽都会添加前缀 -->
</rs-DataGrid>

<!-- 禁用前缀功能 -->
<rs-DataGrid
  prefix="my-module"
  funcMark="ihc:demo"
  :enablePrefix="false"
>
  <!-- 所有插槽都不会添加前缀，保持原样 -->
</rs-DataGrid>
```

#### 2. 插槽级别控制 - __no-prefix__ 标记

```vue
<rs-DataGrid
  prefix="my-module"
  funcMark="ihc:demo"
  :enablePrefix="true"
>
  <!-- 正常插槽 - 会添加前缀 -->
  <template v-slot:ihc:demo:action1="{ oper, row, index }">
    <Button>操作1</Button>
  </template>

  <!-- 特殊插槽 - 不添加前缀 -->
  <template v-slot:ihc:demo:action2__no-prefix__="{ oper, row, index }">
    <Button>操作2</Button>
  </template>
</rs-DataGrid>
```

### 使用场景

| 场景 | 配置 | 说明 |
|------|------|------|
| 新项目 | `enablePrefix="true"` | 推荐使用，统一前缀管理 |
| 兼容旧代码 | `enablePrefix="false"` | 保持原有行为，无需修改代码 |
| 混合使用 | `enablePrefix="true"` + `__no-prefix__` | 大部分插槽使用前缀，个别插槽不使用 |

## ⚠️ 重要：权限检查用法

### func 参数类型支持

rs-DataGrid 支持两种 func 参数类型：

#### 1. 字符串类型
```javascript
func = "prefix:ihc:demo:add,prefix:ihc:demo:edit,prefix:ihc:demo:delete"
```

#### 2. 数组类型（推荐）
```javascript
func = [
  "prefix:ihc:demo:add",
  "prefix:ihc:demo:edit",
  "prefix:ihc:demo:delete"
]
```

### 权限检查方法

#### 方法1: 原有写法（完全兼容）
```vue
<template v-slot:customHeadFunc="{ func }">
  <!-- 原有代码无需修改 -->
  <Button v-if="func.includes('ihc:demo:add')" @click="handleAdd">新增</Button>
</template>
```

#### 方法2: hasPermission 方法（推荐）
```vue
<template v-slot:customHeadFunc="{ func, hasPermission }">
  <!-- 推荐：自动处理前缀 -->
  <Button v-if="hasPermission('ihc:demo:add')" @click="handleAdd">新增</Button>
</template>
```

#### 方法3: 通用权限检查方法
```vue
<template v-slot:customHeadFunc="{ func }">
  <Button v-if="checkPermission(func, 'add')" @click="handleAdd">新增</Button>
</template>

<script>
methods: {
  checkPermission(func, permission) {
    if (!func) return false

    const targetPermission = `${this.actualPrefix}:ihc:demo:${permission}`

    if (Array.isArray(func)) {
      return func.includes(targetPermission)
    } else if (typeof func === 'string') {
      return func.includes(targetPermission)
    }

    return false
  }
},
computed: {
  actualPrefix() {
    return this.$refs.grid.actualPrefix
  }
}
</script>
```

## 🎯 插槽处理规则

### 1. 系统插槽（不添加前缀）
- `customHeadFunc`: 头部自定义功能区
- `customRowFunc`: 行操作自定义功能区
- `slot_*`: 表格列的自定义插槽

### 2. 操作按钮插槽（自动添加前缀）
- 格式: `prefix:原插槽名`
- 示例: `my-module:ihc:demo:edit`

### 3. 防重复机制
- 如果插槽名已包含指定前缀，不会重复添加
- 如果 funcMark 已包含指定前缀，不会重复添加
- 使用 `__no-prefix__` 标记的插槽不会添加前缀

## 📝 完整使用示例

```vue
<template>
  <div class="page-container">
    <rs-DataGrid
      ref="grid"
      prefix="sick-manage"
      funcMark="ihc:ztbhdjlb"
      :customFunc="true"
    >
      <!-- 头部操作区 -->
      <template v-slot:customHeadFunc="{ func, hasPermission }">
        <Button
          type="primary"
          v-if="hasPermission('ihc:ztbhdjlb:add')"
          @click="handleAdd"
        >
          新增病情
        </Button>

        <Button
          type="success"
          v-if="func.includes('ihc:ztbhdjlb:export')"
          @click="handleExport"
        >
          导出数据
        </Button>
      </template>

      <!-- 行操作区 -->
      <template v-slot:customRowFunc="{ func, hasPermission, row, index }">
        <Button
          type="primary"
          size="small"
          v-if="hasPermission('ihc:ztbhdjlb:edit')"
          @click="handleEdit(row)"
          style="margin-right: 5px"
        >
          编辑
        </Button>

        <Button
          type="error"
          size="small"
          v-if="func.includes('sick-manage:ihc:ztbhdjlb:delete')"
          @click="handleDelete(row)"
        >
          删除
        </Button>
      </template>

      <!-- 健康档案查看 -->
      <template v-slot:ihc:ztbhdjlb:jkda="{ oper, row, index }">
        <Button type="primary"  @click="viewHealthRecord(row)">
          <Icon v-if="oper.iconPath" :type="oper.iconPath"/>
          {{ oper.name }}
        </Button>
      </template>

      <!-- 病情登记 -->
      <template v-slot:ihc:ztbhdjlb:dj="{ oper, row, index }">
        <Button
          type="primary"
          size="small"
          @click="addSickCondition(row)"
          v-if="row.business_status==='1'"
        >
          <Icon v-if="oper.iconPath" :type="oper.iconPath"/>
          {{ oper.name }}
        </Button>
      </template>

      <!-- 特殊操作 - 不添加前缀 -->
      <template v-slot:ihc:ztbhdjlb:special__no-prefix__="{ oper, row, index }">
        <Button type="warning" size="small" @click="specialAction(row)">
          特殊操作
        </Button>
      </template>
    </rs-DataGrid>
  </div>
</template>

<script>
// 无需 import，rs-DataGrid 已经是全局组件

export default {
  name: 'SickManageExample',
  methods: {
    handleAdd() {
      console.log('新增操作')
    },

    handleEdit(row) {
      console.log('编辑操作', row)
    },

    handleDelete(row) {
      this.$refs.grid.confirmDelete(row, (data) => {
        console.log('删除数据', data)
        this.$refs.grid.reload()
      })
    },

    handleExport() {
      const selected = this.$refs.grid.getBatchSelect()
      console.log('导出数据', selected)
    },

    viewHealthRecord(row) {
      console.log('查看健康档案', row)
    },

    addSickCondition(row) {
      console.log('病情登记', row)
    },

    specialAction(row) {
      console.log('特殊操作', row)
    }
  }
}
</script>
```

## 🔄 迁移指南

### 从 s-DataGrid 迁移

#### 1. 替换组件标签
```vue
<!-- 原有 -->
<s-DataGrid funcMark="ihc:demo">

<!-- 替换为 -->
<rs-DataGrid funcMark="ihc:demo">
```

#### 2. 添加前缀配置（可选）
```vue
<!-- 方式1: 指定前缀 -->
<rs-DataGrid prefix="my-module" funcMark="ihc:demo">

<!-- 方式2: 使用全局前缀 -->
<rs-DataGrid funcMark="ihc:demo">

<!-- 方式3: 禁用前缀（完全兼容） -->
<rs-DataGrid funcMark="ihc:demo" :enablePrefix="false">
```

#### 3. 权限检查升级（可选）
```vue
<!-- 原有写法（仍然有效） -->
<template v-slot:customHeadFunc="{ func }">
  <Button v-if="func.includes('add')" @click="handleAdd">新增</Button>
</template>

<!-- 推荐写法 -->
<template v-slot:customHeadFunc="{ func, hasPermission }">
  <Button v-if="hasPermission('ihc:demo:add')" @click="handleAdd">新增</Button>
</template>
```

### 兼容性

| 功能 | 兼容性 | 说明 |
|------|--------|------|
| 基本属性 | ✅ 100% | 完全兼容 |
| 事件处理 | ✅ 100% | 完全兼容 |
| 方法调用 | ✅ 100% | 完全兼容 |
| 插槽功能 | ✅ 100% | 完全兼容，增强了前缀处理 |
| 权限检查 | ✅ 100% | 兼容原有写法，提供新方法 |

## ⚠️ 注意事项

1. **前缀命名**: 建议使用有意义的前缀，如模块名或页面名
2. **唯一性**: 确保在同一个应用中，不同模块使用不同的前缀
3. **一致性**: 同一个模块内的所有 rs-DataGrid 使用相同的前缀
4. **func 类型**: 支持字符串和数组两种类型的 func 参数
5. **权限检查**: 使用 hasPermission 方法可以自动处理前缀

## 🚀 最佳实践

1. **新项目**: 推荐使用 `enablePrefix="true"` 和 `hasPermission` 方法
2. **旧项目**: 可以设置 `enablePrefix="false"` 保持兼容
3. **混合项目**: 使用 `__no-prefix__` 标记灵活控制
4. **全局配置**: 在 main.js 中设置 `Vue.prototype.globalAppCode`
5. **错误处理**: 使用 `confirmDelete` 和 `confirmBatchDelete` 方法

## 📞 技术支持

如有问题，请查看：
- 测试文件：`src/components/rs-datagrid/`
- 示例代码：各种 `*-test.vue` 文件
- 迁移指南：`MIGRATION.md`
