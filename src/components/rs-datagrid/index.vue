<template>
  <div>
    <component
      :is="dataGridComponent"
      ref="dataGrid"
      :funcMark="computedFuncMark"
      v-bind="$attrs"
      @returnParamsSuccess="returnParamsSuccess"
      v-on="$listeners"
    >
      <!-- 透传所有作用域插槽，自动处理前缀 -->
      <template v-for="(_, name) in $scopedSlots" :slot="getPrefixedSlotName(name)" slot-scope="slotProps">
        <slot :name="name" v-bind="getProcessedSlotProps(name, slotProps)" v-if="slotProps"></slot>
      </template>

      <!-- 透传所有普通插槽，自动处理前缀 -->
      <template v-for="(_, name) in $slots" :slot="getPrefixedSlotName(name)">
        <slot :name="name"></slot>
      </template>
    </component>
  </div>
</template>

<script>
// 导入 s-DataGrid 组件
import {sDataGrid} from "sd-data-grid"

export default {
  name: 'rs-DataGrid',
  components: {
    sDataGrid
  },
  inheritAttrs: false,
  props: {
    // 前缀配置 - 可选，不定义时从 Vue.prototype.globalAppCode 获取
    prefix: {
      type: String,
      default: '',
      validator: function (value) {
        // 允许空字符串，会自动从 globalAppCode 获取
        return typeof value === 'string'
      }
    },
    // funcMark 属性 - 必需
    funcMark: {
      type: String,
      required: true
    },
    // 是否启用前缀功能 - 可选，默认启用
    enablePrefix: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    // 使用 s-DataGrid 组件
    dataGridComponent() {
      return 'sDataGrid'
    },

    // 计算实际使用的前缀
    actualPrefix() {

      // 如果 prefix 有值，直接使用
      if (this.prefix && this.prefix.trim()) {
        return this.prefix.trim()
      }

      // 如果 prefix 为空，从 Vue.prototype.globalAppCode 获取
      // 尝试多种方式获取全局应用代码
      const globalAppCode = this.$globalAppCode ||
        this.globalAppCode ||
        this.$options.globalAppCode ||
        (this.$root && this.$root.$options.globalAppCode) || serverConfig.APP_CODE ||
        (window.Vue && window.Vue.prototype && window.Vue.prototype.globalAppCode)

      if (globalAppCode) {
        return globalAppCode
      }

      // 如果都没有，返回空字符串（不添加前缀）
      console.warn('RS-DataGrid: 未找到 prefix 或 Vue.prototype.globalAppCode，将不添加前缀')
      return ''
    },

    // 计算带前缀的 funcMark
    computedFuncMark() {
      if (!this.funcMark) return ''

      // 如果禁用前缀功能，直接返回原 funcMark
      if (!this.enablePrefix) {
        return this.funcMark
      }

      // 如果没有前缀，直接返回原 funcMark
      if (!this.actualPrefix) {
        return this.funcMark
      }

      // 如果已经包含前缀，则不重复添加
      if (this.funcMark.startsWith(this.actualPrefix + ':')) {
        return this.funcMark
      }

      return `${this.actualPrefix}:${this.funcMark}`
    },

    // 透传批量选择数据
    batch_select() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.batch_select : []
    },

    // 透传表格数据
    data() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.data : []
    },

    // 透传分页信息
    pagination() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.pagination : {}
    },

    // 透传加载状态
    loading() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.loading : false
    },

    // 透传表格数据（别名）
    gridData() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.gridData || this.$refs.dataGrid.data : []
    },

    // 透传列配置
    dataGridColumns() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.dataGridColumns || this.$refs.dataGrid.columns : []
    },

    // 透传错误信息
    errorMsg() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.errorMsg || '' : ''
    },

    // 透传头部操作按钮配置
    headOper() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.headOper || [] : []
    },

    // 透传行操作按钮配置
    rowOper() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.rowOper || [] : []
    },

    // 透传列配置（别名）
    columns() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.columns || this.$refs.dataGrid.dataGridColumns || [] : []
    },

    // 透传表格数据（别名）
    tableData() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.tableData || this.$refs.dataGrid.data || [] : []
    },

    // 透传总记录数
    total() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.total || (this.$refs.dataGrid.pagination && this.$refs.dataGrid.pagination.total) || 0 : 0
    },

    // 透传当前页码
    currentPage() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.currentPage || (this.$refs.dataGrid.pagination && this.$refs.dataGrid.pagination.current) || 1 : 1
    },

    // 透传每页大小
    pageSize() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.pageSize || (this.$refs.dataGrid.pagination && this.$refs.dataGrid.pagination.pageSize) || 20 : 20
    },

    // 透传选中行数据
    selectedRows() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.selectedRows || this.$refs.dataGrid.batch_select || [] : []
    }
  },
  methods: {
    returnParamsSuccess(data,value,value2){
       console.log(data,value,value2,'data,value,value2')
    },
    getAppendFuncMark(rawFuncMark) {
      if (!rawFuncMark) return ''
      // 如果禁用前缀功能，直接返回原 funcMark
      if (!this.enablePrefix) {
        return rawFuncMark
      }

      // 如果没有前缀，直接返回原 funcMark
      if (!this.actualPrefix) {
        return rawFuncMark
      }

      // 如果已经包含前缀，则不重复添加
      if (rawFuncMark.startsWith(this.actualPrefix + ':')) {
        return rawFuncMark
      }

      return `${this.actualPrefix}:${rawFuncMark}`
    }
    ,

    /**
     * 获取带前缀的插槽名称
     * @param {string} slotName - 原始插槽名称
     * @returns {string} 处理后的插槽名称
     */
    getPrefixedSlotName(slotName) {

      // 如果禁用前缀功能，直接返回原插槽名称
      if (!this.enablePrefix) {
        return slotName
      }

      // 系统插槽不添加前缀
      const systemSlots = [
        'customHeadFunc',
        'customRowFunc'
      ]

      // 如果是系统插槽，直接返回原名称
      if (systemSlots.includes(slotName)) {
        return slotName
      }

      // 如果是以 slot_ 开头的列插槽，也不添加前缀
      if (slotName.startsWith('slot_')) {
        return slotName
      }

      // 检查插槽是否有 no-prefix 属性（通过插槽名称约定）
      if (slotName.includes('__no-prefix__')) {
        // 移除 __no-prefix__ 标记，返回原始插槽名
        return slotName.replace('__no-prefix__', '')
      }

      // 如果没有前缀，直接返回原插槽名称
      if (!this.actualPrefix) {
        return slotName
      }

      // 如果已经包含前缀，不重复添加
      if (slotName.startsWith(this.actualPrefix + ':')) {
        return slotName
      }

      // 为操作按钮插槽添加前缀
      return `${this.actualPrefix}:${slotName}`
    },

    /**
     * 处理插槽 props，为系统插槽添加权限检查方法
     * @param {string} slotName - 插槽名称
     * @param {Object} slotProps - 原始插槽 props
     * @returns {Object} 处理后的插槽 props
     */
    getProcessedSlotProps(slotName, slotProps) {
      // 检查 slotProps 是否为 null 或 undefined
      if (!slotProps) {
        return {}
      }

      // 只处理系统插槽
      const systemSlots = ['customHeadFunc', 'customRowFunc']

      if (!systemSlots.includes(slotName)) {
        return slotProps
      }

      // 创建增强的 func 对象
      const enhancedFunc = this.createEnhancedFunc(slotProps.func)

      // 添加权限检查方法到插槽 props
      const processedProps = {
        ...slotProps,
        func: enhancedFunc,
        // 添加权限检查方法，保持原始 func 数据不变
        hasPermission: (permission) => {
          return this.checkPermission(slotProps.func, permission)
        },
        appendEqualFuncMark: (rawFuncMark) => {
          return this.appendEqual(rawFuncMark)
        },
        resetMethod: this.reload,
        funcMark:''
      }
      return processedProps
    },

    /**
     * 创建增强的 func 对象，重写 includes 方法自动添加前缀
     * @param {Array|String} originalFunc - 原始 func 数据
     * @returns {Array|String} 增强后的 func 对象
     */
    createEnhancedFunc(originalFunc) {
      if (!originalFunc || !this.actualPrefix) {
        return originalFunc
      }

      if (Array.isArray(originalFunc)) {
        // 如果是数组，创建新数组并重写 includes 方法
        const enhancedArray = [...originalFunc]
        const originalIncludes = enhancedArray.includes.bind(enhancedArray)

        enhancedArray.includes = (searchElement) => {
          // 如果搜索元素不包含前缀，自动添加前缀
          if (typeof searchElement === 'string' && !searchElement.includes(':')) {
            const prefixedElement = `${this.actualPrefix}:${this.funcMark}:${searchElement}`
            return originalIncludes(prefixedElement)
          }
          // 如果已经包含前缀或不是字符串，直接使用原方法
          return originalIncludes(searchElement)
        }

        return enhancedArray
      } else if (typeof originalFunc === 'string') {
        // 如果是字符串，创建包装对象
        const funcWrapper = {
          toString: () => originalFunc,
          valueOf: () => originalFunc,
          includes: (searchElement) => {
            // 如果搜索元素不包含前缀，自动添加前缀
            if (typeof searchElement === 'string' && !searchElement.includes(':')) {
              const prefixedElement = `${this.actualPrefix}:${this.funcMark}:${searchElement}`
              return originalFunc.includes(prefixedElement)
            }
            // 如果已经包含前缀或不是字符串，直接使用原方法
            return originalFunc.includes(searchElement)
          }
        }

        return funcWrapper;
      }

      return originalFunc
    },

    /**
     * 检查权限方法
     * @param {Array|String} func - 权限数组或字符串
     * @param {String} permission - 要检查的权限
     * @returns {Boolean} 是否有权限
     */
    checkPermission(func, permission) {
      if (!func) return false

      // 如果禁用前缀功能，直接检查原始权限
      if (!this.enablePrefix) {
        if (Array.isArray(func)) {
          return func.includes(permission)
        } else if (typeof func === 'string') {
          return func.includes(permission)
        }
        return false
      }

      // 如果没有前缀，直接检查原始权限
      if (!this.actualPrefix) {
        if (Array.isArray(func)) {
          return func.includes(permission)
        } else if (typeof func === 'string') {
          return func.includes(permission)
        }
        return false
      }

      // 有前缀时，检查带前缀的权限
      const prefixedPermission = `${this.actualPrefix}:${permission}`

      if (Array.isArray(func)) {
        return func.includes(prefixedPermission)
      } else if (typeof func === 'string') {
        return func.includes(prefixedPermission)
      }

      return false
    },
    appendEqual(rawfuncMark) {
      if (this.getAppendFuncMark(rawfuncMark) === this.getAppendFuncMark(this.funcMark)) {
        return true;
      }
      return false;
    },

    // === 透传 s-DataGrid 的核心方法 ===

    /**
     * 查询表格数据
     * @param {number} pageNo - 页码，默认为 1
     */
    query_grid_data(pageNo = 1) {
      if (this.$refs.dataGrid && this.$refs.dataGrid.query_grid_data && this.$refs.dataGrid.comModel) {
        return this.$refs.dataGrid.query_grid_data(pageNo)
      } else {
        console.warn('DataGrid not fully initialized yet, skipping query')
        return Promise.resolve()
      }
    },

    /**
     * 刷新表格数据
     * @param {number} pageNo - 页码，默认为 1
     */
    refreshGrid(pageNo = 1) {
      if (this.$refs.dataGrid && this.$refs.dataGrid.refreshGrid) {
        return this.$refs.dataGrid.refreshGrid(pageNo)
      }
    },

    /**
     * 重新加载数据（回到第一页）
     */
    reload() {
      // 检查组件是否已经初始化完成
      if (this.$refs.dataGrid && this.$refs.dataGrid.comModel) {
        return this.query_grid_data(1)
      } else {
        console.warn('DataGrid not fully initialized yet, skipping reload')
        return Promise.resolve()
      }
    },

    // === 数据获取方法 ===

    /**
     * 获取批量选择的数据
     * @returns {Array} 选中的行数据数组
     */
    getBatchSelect() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.batch_select || [] : []
    },

    /**
     * 获取当前页数据
     * @returns {Array} 当前页的数据数组
     */
    getCurrentData() {
      return this.$refs.dataGrid ? this.$refs.dataGrid.data || [] : []
    },

    /**
     * 获取选中行的 ID 数组
     * @param {string} idField - ID 字段名，默认为 'id'
     * @returns {Array} ID 数组
     */
    getBatchSelectIds(idField = 'id') {
      const selected = this.getBatchSelect()
      return selected.map(item => item[idField]).filter(id => id !== undefined && id !== null)
    },

    // === 透传其他可能的方法 ===

    /**
     * 重置查询条件
     */
    reset_query_condis() {
      if (this.$refs.dataGrid && this.$refs.dataGrid.reset_query_condis) {
        return this.$refs.dataGrid.reset_query_condis()
      }
    },

    /**
     * 初始化表格
     */
    init_grid() {
      if (this.$refs.dataGrid && this.$refs.dataGrid.init_grid) {
        return this.$refs.dataGrid.init_grid()
      }
    },

    // === 增强兼容性方法 ===

    /**
     * 刷新表格（别名方法，兼容旧代码）
     */
    on_refresh_table() {
      return this.query_grid_data(1)
    },

    /**
     * 确认删除方法
     * @param {Object} row - 要删除的行数据
     * @param {Function} deleteCallback - 删除回调函数
     */
    confirmDelete(row, deleteCallback) {
      if (this.$refs.dataGrid && this.$refs.dataGrid.confirmDelete) {
        return this.$refs.dataGrid.confirmDelete(row, deleteCallback)
      } else {
        // 如果 s-DataGrid 没有 confirmDelete 方法，提供默认实现
        this.$Modal.confirm({
          title: '确认删除',
          content: '是否确认删除该记录？',
          onOk: () => {
            if (typeof deleteCallback === 'function') {
              deleteCallback(row)
            }
          }
        })
      }
    },

    /**
     * 批量删除确认
     * @param {Function} deleteCallback - 删除回调函数
     */
    confirmBatchDelete(deleteCallback) {
      const selected = this.getBatchSelect()
      if (selected.length === 0) {
        this.$Notice.warning({
          title: '提示',
          desc: '请选择要删除的数据'
        })
        return
      }

      this.$Modal.confirm({
        title: '批量删除确认',
        content: `是否确认删除选中的 ${selected.length} 条数据？`,
        onOk: () => {
          if (typeof deleteCallback === 'function') {
            deleteCallback(selected)
          }
        }
      })
    },

    /**
     * 获取表格实例（用于直接访问 s-DataGrid）
     */
    getGridInstance() {
      return this.$refs.dataGrid
    },

    /**
     * 检查是否有选中数据
     */
    hasSelection() {
      return this.getBatchSelect().length > 0
    },

    /**
     * 清空选择
     */
    clearSelection() {
      if (this.$refs.dataGrid && this.$refs.dataGrid.clearSelection) {
        return this.$refs.dataGrid.clearSelection()
      }
    },

    /**
     * 设置加载状态
     */
    setLoading(loading) {
      if (this.$refs.dataGrid && this.$refs.dataGrid.setLoading) {
        return this.$refs.dataGrid.setLoading(loading)
      }
    },

    /**
     * 全选
     */
    selectAll() {
      if (this.$refs.dataGrid && this.$refs.dataGrid.selectAll) {
        return this.$refs.dataGrid.selectAll()
      }
    },

    /**
     * 取消全选
     */
    clearAll() {
      if (this.$refs.dataGrid && this.$refs.dataGrid.clearAll) {
        return this.$refs.dataGrid.clearAll()
      }
    },

    /**
     * 设置选中行
     * @param {Array} rows - 要选中的行数据数组
     */
    setSelection(rows) {
      if (this.$refs.dataGrid && this.$refs.dataGrid.setSelection) {
        return this.$refs.dataGrid.setSelection(rows)
      }
    },

    /**
     * 获取选中行数据（别名方法）
     */
    getSelection() {
      return this.getBatchSelect()
    },

    /**
     * 导出数据
     * @param {Object} options - 导出选项
     */
    exportData(options = {}) {
      if (this.$refs.dataGrid && this.$refs.dataGrid.exportData) {
        return this.$refs.dataGrid.exportData(options)
      }
    }
  }
}
</script>

<style scoped>
/* 继承原有样式，不添加额外样式以保持兼容性 */
</style>
