<template>
  <div class="global-app-code-test-container">
    <h2>rs-DataGrid 全局应用代码获取测试</h2>
    <p>测试 Vue.prototype.globalAppCode 的获取方式</p>
    
    <div class="test-info">
      <h3>全局应用代码检测结果</h3>
      <div class="detection-results">
        <div class="result-item">
          <span class="label">this.$globalAppCode:</span>
          <span class="value" :class="globalAppCodeClass">{{ globalAppCodeValue }}</span>
        </div>
        
        <div class="result-item">
          <span class="label">this.$options.globalAppCode:</span>
          <span class="value" :class="optionsGlobalAppCodeClass">{{ optionsGlobalAppCodeValue }}</span>
        </div>
        
        <div class="result-item">
          <span class="label">this.$root.$options.globalAppCode:</span>
          <span class="value" :class="rootGlobalAppCodeClass">{{ rootGlobalAppCodeValue }}</span>
        </div>
        
        <div class="result-item">
          <span class="label">window.Vue.prototype.globalAppCode:</span>
          <span class="value" :class="windowGlobalAppCodeClass">{{ windowGlobalAppCodeValue }}</span>
        </div>
        
        <div class="result-item">
          <span class="label">Vue.prototype.globalAppCode (直接访问):</span>
          <span class="value" :class="directGlobalAppCodeClass">{{ directGlobalAppCodeValue }}</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>rs-DataGrid 前缀获取测试</h3>
      
      <rs-DataGrid 
        ref="testGrid"
        funcMark="ihc:test"
        :customFunc="true"
      >
        <template v-slot:customHeadFunc="{ func, hasPermission }">
          <div class="test-panel">
            <h4>前缀获取结果</h4>
            <p><strong>实际前缀:</strong> <code>{{ actualPrefix }}</code></p>
            <p><strong>计算后的 funcMark:</strong> <code>{{ computedFuncMark }}</code></p>
            <p><strong>func 参数:</strong> <code>{{ JSON.stringify(func) }}</code></p>
            
            <div class="button-group">
              <Button 
                type="primary" 
                v-if="hasPermission('ihc:test:add')" 
                @click="testWithPrefix"
              >
                新增 (有前缀)
              </Button>
              
              <Button 
                type="success" 
                v-if="func && func.includes && func.includes('ihc:test:info')" 
                @click="testWithoutPrefix"
              >
                详情 (无前缀检查)
              </Button>
            </div>
          </div>
        </template>
      </rs-DataGrid>
    </div>

    <div class="test-section">
      <h3>手动设置全局应用代码</h3>
      
      <div class="manual-setting">
        <div class="input-group">
          <Input 
            v-model="manualAppCode" 
            placeholder="输入应用代码"
            style="width: 200px; margin-right: 10px;"
          />
          <Button @click="setGlobalAppCode">设置全局应用代码</Button>
          <Button @click="clearGlobalAppCode">清除全局应用代码</Button>
        </div>
        
        <div class="setting-result" v-if="settingMessage">
          <p>{{ settingMessage }}</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>解决方案</h3>
      
      <div class="solutions">
        <div class="solution-item">
          <h4>方案1: 检查 main.js 设置</h4>
          <pre><code>// main.js
Vue.prototype.globalAppCode = serverConfig.APP_CODE;

// 确保在创建 Vue 实例之前设置</code></pre>
        </div>

        <div class="solution-item">
          <h4>方案2: 手动传入 prefix</h4>
          <pre><code>// 直接传入 prefix，不依赖全局配置
&lt;rs-DataGrid prefix="your-app-code" funcMark="ihc:demo" /&gt;</code></pre>
        </div>

        <div class="solution-item">
          <h4>方案3: 在组件中设置</h4>
          <pre><code>// 在使用 rs-DataGrid 的组件中
mounted() {
  if (!this.$globalAppCode) {
    this.$globalAppCode = 'your-app-code'
  }
}</code></pre>
        </div>

        <div class="solution-item">
          <h4>方案4: 使用 Vuex 或其他状态管理</h4>
          <pre><code>// 从 Vuex 获取应用代码
computed: {
  appCode() {
    return this.$store.state.appCode
  }
}

// 使用时传入
&lt;rs-DataGrid :prefix="appCode" funcMark="ihc:demo" /&gt;</code></pre>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>调试信息</h3>
      
      <div class="debug-info">
        <h4>Vue 实例信息</h4>
        <ul>
          <li><strong>Vue 版本:</strong> {{ vueVersion }}</li>
          <li><strong>组件名:</strong> {{ $options.name }}</li>
          <li><strong>是否有 $root:</strong> {{ !!$root }}</li>
          <li><strong>是否有 window.Vue:</strong> {{ !!windowVue }}</li>
        </ul>
        
        <h4>原型链检查</h4>
        <ul>
          <li><strong>Vue.prototype 存在:</strong> {{ !!vuePrototype }}</li>
          <li><strong>Vue.prototype.globalAppCode 存在:</strong> {{ !!vuePrototypeGlobalAppCode }}</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h3>测试日志</h3>
      <div class="test-logs">
        <div v-if="testLogs.length === 0" class="no-logs">
          点击上方按钮进行测试
        </div>
        <ul v-else>
          <li v-for="(log, index) in testLogs" :key="index" :class="log.type">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GlobalAppCodeTest',
  data() {
    return {
      testLogs: [],
      manualAppCode: 'test-app-code',
      settingMessage: ''
    }
  },
  computed: {
    // 检测各种获取方式
    globalAppCodeValue() {
      return this.$globalAppCode || '未设置'
    },
    globalAppCodeClass() {
      return this.$globalAppCode ? 'success' : 'error'
    },
    
    optionsGlobalAppCodeValue() {
      return this.$options.globalAppCode || '未设置'
    },
    optionsGlobalAppCodeClass() {
      return this.$options.globalAppCode ? 'success' : 'error'
    },
    
    rootGlobalAppCodeValue() {
      return (this.$root && this.$root.$options.globalAppCode) || '未设置'
    },
    rootGlobalAppCodeClass() {
      return (this.$root && this.$root.$options.globalAppCode) ? 'success' : 'error'
    },
    
    windowGlobalAppCodeValue() {
      return (window.Vue && window.Vue.prototype && window.Vue.prototype.globalAppCode) || '未设置'
    },
    windowGlobalAppCodeClass() {
      return (window.Vue && window.Vue.prototype && window.Vue.prototype.globalAppCode) ? 'success' : 'error'
    },
    
    directGlobalAppCodeValue() {
      try {
        return Vue.prototype.globalAppCode || '未设置'
      } catch (e) {
        return '访问失败'
      }
    },
    directGlobalAppCodeClass() {
      try {
        return Vue.prototype.globalAppCode ? 'success' : 'error'
      } catch (e) {
        return 'error'
      }
    },
    
    // rs-DataGrid 相关
    actualPrefix() {
      return this.$refs.testGrid ? this.$refs.testGrid.actualPrefix : '未初始化'
    },
    computedFuncMark() {
      return this.$refs.testGrid ? this.$refs.testGrid.computedFuncMark : '未初始化'
    },
    
    // 调试信息
    vueVersion() {
      return Vue.version || '未知'
    },
    windowVue() {
      return window.Vue
    },
    vuePrototype() {
      return Vue.prototype
    },
    vuePrototypeGlobalAppCode() {
      try {
        return Vue.prototype.globalAppCode
      } catch (e) {
        return null
      }
    }
  },
  mounted() {
    this.addLog('info', '全局应用代码测试组件已挂载')
    this.addLog('info', `检测到的前缀: ${this.actualPrefix}`)
    
    // 输出调试信息到控制台
    console.log('=== rs-DataGrid 全局应用代码调试信息 ===')
    console.log('this.$globalAppCode:', this.$globalAppCode)
    console.log('this.$options.globalAppCode:', this.$options.globalAppCode)
    console.log('this.$root.$options.globalAppCode:', this.$root && this.$root.$options.globalAppCode)
    console.log('window.Vue.prototype.globalAppCode:', window.Vue && window.Vue.prototype && window.Vue.prototype.globalAppCode)
    console.log('Vue.prototype.globalAppCode:', Vue.prototype.globalAppCode)
    console.log('=======================================')
  },
  methods: {
    addLog(type, message) {
      this.testLogs.unshift({
        type,
        time: new Date().toLocaleTimeString(),
        message
      })
      
      if (this.testLogs.length > 20) {
        this.testLogs = this.testLogs.slice(0, 20)
      }
    },

    testWithPrefix() {
      this.addLog('success', '✅ 带前缀权限检查通过')
      this.$Notice.success({
        title: '权限测试',
        desc: '带前缀的权限检查成功'
      })
    },

    testWithoutPrefix() {
      this.addLog('success', '✅ 无前缀权限检查通过')
      this.$Notice.info({
        title: '权限测试',
        desc: '无前缀的权限检查成功'
      })
    },

    setGlobalAppCode() {
      if (!this.manualAppCode.trim()) {
        this.settingMessage = '请输入有效的应用代码'
        return
      }
      
      // 设置全局应用代码
      Vue.prototype.globalAppCode = this.manualAppCode.trim()
      this.$globalAppCode = this.manualAppCode.trim()
      
      this.settingMessage = `已设置全局应用代码为: ${this.manualAppCode.trim()}`
      this.addLog('success', `✅ 设置全局应用代码: ${this.manualAppCode.trim()}`)
      
      // 强制更新组件
      this.$forceUpdate()
      this.$nextTick(() => {
        if (this.$refs.testGrid) {
          this.$refs.testGrid.$forceUpdate()
        }
      })
    },

    clearGlobalAppCode() {
      delete Vue.prototype.globalAppCode
      delete this.$globalAppCode
      
      this.settingMessage = '已清除全局应用代码'
      this.addLog('warning', '⚠️ 已清除全局应用代码')
      
      // 强制更新组件
      this.$forceUpdate()
      this.$nextTick(() => {
        if (this.$refs.testGrid) {
          this.$refs.testGrid.$forceUpdate()
        }
      })
    }
  }
}
</script>

<style scoped>
.global-app-code-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
}

.test-info h3 {
  margin-top: 0;
  color: #0369a1;
}

.detection-results {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
  padding: 8px;
  background: #f9fafb;
  border-radius: 4px;
}

.label {
  font-weight: bold;
  color: #374151;
}

.value {
  font-family: monospace;
  padding: 2px 6px;
  border-radius: 2px;
}

.value.success {
  background: #f0fdf4;
  color: #15803d;
  border: 1px solid #bbf7d0;
}

.value.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #fafafa;
}

.test-section h3 {
  margin-top: 0;
  color: #374151;
  border-bottom: 2px solid #3b82f6;
  padding-bottom: 8px;
}

.test-panel {
  padding: 15px;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  margin: 10px 0;
}

.test-panel h4 {
  margin-top: 0;
  color: #374151;
}

.test-panel p {
  margin: 5px 0;
  font-size: 13px;
}

.button-group {
  margin: 10px 0;
}

.button-group .ivu-btn {
  margin-right: 8px;
  margin-bottom: 5px;
}

.manual-setting {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.input-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.setting-result {
  padding: 10px;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 4px;
  color: #15803d;
}

.solutions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.solution-item {
  padding: 15px;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}

.solution-item h4 {
  margin-top: 0;
  color: #374151;
}

.solution-item pre {
  background: #f9fafb;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}

.debug-info {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
}

.debug-info h4 {
  margin-top: 0;
  color: #374151;
}

.debug-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.debug-info li {
  margin: 5px 0;
  font-size: 13px;
}

.test-logs {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  max-height: 300px;
  overflow-y: auto;
}

.no-logs {
  text-align: center;
  color: #9ca3af;
  padding: 20px;
  font-style: italic;
}

.test-logs ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.test-logs li {
  margin: 5px 0;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.test-logs li.success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.test-logs li.info {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
}

.test-logs li.warning {
  background: #fffbeb;
  border: 1px solid #fed7aa;
}

.log-time {
  color: #6b7280;
  margin-right: 10px;
  font-family: monospace;
}

.log-message {
  color: #374151;
}

code {
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: monospace;
  color: #dc2626;
}
</style>
