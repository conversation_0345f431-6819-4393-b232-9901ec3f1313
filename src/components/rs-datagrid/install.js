/**
 * rs-DataGrid 全局组件安装插件
 */
import RSDataGrid from './index.vue'

// 定义 install 方法，供 Vue.use() 调用
RSDataGrid.install = function (Vue) {
  // 注册全局组件
  Vue.component('rs-DataGrid', RSDataGrid)
  Vue.component('rsDataGrid', RSDataGrid)
  Vue.component('rs-data-grid', RSDataGrid)
}

// 自动安装（如果在浏览器环境中直接引入）
if (typeof window !== 'undefined' && window.Vue) {
  RSDataGrid.install(window.Vue)
}

export default RSDataGrid
