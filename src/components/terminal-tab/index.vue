<template>
  <div class="tab-box">
    <div
      v-for="item in tabList"
      :key="item.code"
      :class="['tab-item', value === item.code && 'selected']"
      @click="handleTab(item)"
    >
      {{ item.name }}
    </div>
  </div>
</template>
<script>
export default {
  props: {
    tabList: {
      type: Array,
      default: () => [],
    },
    value: {
      type: [String, Number],
      required: true,
    },
  },
  model: {
    event: "change",
    prop: "value",
  },
  methods: {
    handleTab(item) {
      this.$emit("change", item.code);
      this.$emit("on-change", item);
    },
  },
};
</script>
<style lang="less" scoped>
.tab-box {
  display: flex;
  align-items: center;
  padding: 0;
  height: 48px;
  color: #2b3346;
  font-size: 18px;
  line-height: 48px;
  border-bottom: 1px solid #cee0f0;

  .tab-item {
    padding: 0 16px;
    position: relative;
    cursor: pointer;

    & + .tab-item {
      margin-left: 4px;
    }

    &.selected {
      color: #2b5fda;

      &::before {
        content: "";
        position: absolute;
        height: 2px;
        background: #2b5fda;
        width: 100%;
        bottom: 0;
        left: 0;
      }
    }
  }
}
</style>
