<template>
  <div style="display: flex; flex-direction: column; flex: 1;">
    <div
      style="line-height: 2.8;background: #eff6ff;border:1px solid #efefef; padding: 0 10px; display: flex; flex-direction: row; justify-content: space-between;">
      <span style="font-size: 16px; font-weight: bold;">{{ title }}</span>
      <div>
        <slot name="action"></slot>
      </div>
    </div>
    <div v-loading="loading" style="border: 1px solid #eaeaea; padding:10px; background-color: #f5f7fa; flex:1">
      <embed width="100%" height="100%" name="plugin" id="plugin" :src="pdfUrl" type="application/pdf"
        javascript="allow" />
    </div>
  </div>
</template>

<script>
import { getToken } from '@/libs/util'
import printJS from 'print-js';
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    formId: {
      type: String,
      default: ''
    },
    businessId: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      pdfUrl: `${this.$path.pdf_getPdf}?plugIns=MultipleRowTableRenderPolicy&formId=${this.formId}&businessId=${this.businessId}&access_token=${getToken()}#toolbar=0`,
      loading: false
    }
  },
  methods: {
    print() {
      this.loading = true;
      const _this = this;
      printJS({
        printable: this.pdfUrl,
        type: 'pdf',
        header: '打印',
        onLoadingEnd: async () => {
          _this.loading = false;
        }
      })
    }
  }
}
</script>

<style lang="less" scoped></style>
