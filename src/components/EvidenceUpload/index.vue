<template>
    <div class="EvidenceUploadBox">
        <!-- 证据上传 -->
        <Modal
            width="950"
            class="progressModalWrap"
            v-model="EvidenceUploadModal"
            :footer-hide="true"
            :closable="true"
            :mask-closable="false"
            :title="showUploadBtn ? '证据上传' : '属性'">
                <Form ref="formData" :model="formData" :rules="ruleValidate" label-position="right">
                    <FormItem label="证据名称:" prop="evidenceName" v-if="showUploadBtn">
                        <Select v-model="evidenceName2" @on-select="zjmcAction"  :loading="loading">
                                <Option v-for="item in zjmcList" 
                                :value="JSON.stringify(item)" :key="item.evidenceCode" >{{ item.evidenceName }}</Option>
                                <Page v-show="total > 10" :total="total" size="small" show-total :page-size="pageSize" 
                                :current="currentPage"
                                @on-change="handlePage"/>
                        </Select>
                    </FormItem>
                    <FormItem v-if="!showUploadBtn" label="证据名称:" prop="evidenceName2">
                        <Input v-model="evidenceName2" disabled></Input>
                    </FormItem>
                    <FormItem label="证据类型:" prop="evidenceType">
                        <Input v-model="formData.evidenceType" disabled></Input>
                    </FormItem>
                    <FormItem label="证据编码:" prop="evidenceCode">
                        <Input v-model="formData.evidenceCode" disabled></Input>
                    </FormItem>
                    <FormItem label="证据提供对象:" prop="evidenceProvider">
                        <Select v-model="formData.evidenceProvider">
                            <Option v-for="(item,index) in zjtgdxList" 
                            :value="item" :key="item+'zjtgdx'+index" >{{ item }}</Option>
                        </Select>
                    </FormItem>
                     <FormItem label="材料名称:" prop="materialName">
                        <Input v-model="formData.materialName"></Input>
                    </FormItem>
                    <FormItem label="证据指向对象:" prop="evidencePointer">
                        <Select v-model="formData.evidencePointer">
                            <Option v-for="item in zjzxdxList" 
                            :value="item" :key="item+'zjzxdx'" >{{ item }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem label="证据证明目的:" prop="evidencePurpose" style="width:100%;">
                        <Input style="width:98%;" v-model="formData.evidencePurpose" type="textarea" :autosize="{minRows: 2,maxRows: 5}"/>
                    </FormItem>
                    <FormItem label="备注:" prop="remarks" style="width:100%;">
                        <Input style="width:98%;" v-model="formData.remarks" type="textarea" :autosize="{minRows: 2,maxRows: 5}"/>
                    </FormItem>
                    <div class="uploadWrap ivu-form-item-required" v-if="showUploadBtn">
                        <label class="ivu-form-item-label">上传材料:</label>
                        <div class="uploadBox">
                            <ul class="uploadType">
                                <li>
                                    <sd-img-upload ref="imageUpload"
                                        :url="uploadUrl+'&fileType=img'"
                                        :directory="false"
                                        :autoStart="true"
                                        :simultaneousUploads="3"
                                        :maxSize="uploadMaxSize"
                                        :maxFiles="Number(uploadMaxCount)"
                                        className="fies"
                                        :showUploaderList="false"
                                        @file-success="onFileSuccess"
                                        @file-added="onFileAdded"
                                        @files-submitted="filesSubmitted"
                                        @complete="complete">
                                        <span slot="func" class="uploadBtn" @click="fileType='img'">图片上传</span>
                                    </sd-img-upload>
                                </li>
                                <li>
                                    <sd-img-upload ref="imageUpload3"
                                        :url="pdfUploadUrl+'&fileType=pdf'"
                                        :autoStart="true"
                                        accept="application/pdf"
                                        :simultaneousUploads="3"
                                        :maxSize="uploadMaxSize"
                                        :maxFiles="Number(uploadMaxCount)"
                                        className="fies"
                                        :showUploaderList="false"
                                        @file-success="onFileSuccess"
                                        @file-added="onFileAdded"
                                        @files-submitted="filesSubmitted"
                                        @complete="complete">
                                        <span slot="func" class="uploadBtn" @click="fileType='pdf'">PDF上传</span>
                                    </sd-img-upload>
                                </li>
                                <li @click="scanUploadAction">扫描上传</li>
                            </ul>
                            <div class="uploadImgList">
                                <div class="uploadImgItem2" v-for="(item,index) in allPdfList" :key="index+'PL'">
                                    <span :title="item.name" @click="ViewOriginalFile(item)">{{item.name}}</span>
                                    <i class="deleteBtn2" @click="imgDeleteEvent(item.md5)"></i>
                                </div>
                                <div class="uploadImgItem" v-for="(item,index) in allImgList" :key="index+'UIL'">
                                    <img :src="item.url" :data-src="changeUrl(item.url)" data-magnify="gallery"/>
                                    <i class="deleteBtn" @click="imgDeleteEvent(item.md5)"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="isFinishBtn">
                        <div @click="closeAction">重置</div>
                        <div v-if="showUploadBtn" @click="uploadAction">上传</div>
                        <div v-else @click="saveFormAction">保存</div>
                    </div>
                </Form>
        </Modal>
        <!-- 上传进度 -->
        <Modal
            width="570"
            class="progressModalWrap"
            v-model="progressModal"
            :footer-hide="true"
            :closable="false"
            :mask-closable="false"
            title="上传进度">
            <div class="progressModal">
                <p>正在<span class="title">上传材料</span>，目前进度<i class="count">{{uploadFinishCount}}</i>/{{uoloadFileTotal-uploadErrorCount}}份,共{{uoloadFileTotal}}份</p>
                <Progress  :percent="percentVal" :stroke-width="32" status="success" text-inside />
                <p class="inTime">上传耗时：<span >{{inTime}}</span></p>
                <div class="uploadErrorBox" v-show="uploadErrorCount > 0">
                    <h3>上传失败：<i>{{uploadErrorCount}}</i>张，请重新选择上传</h3>
                    <ul class="uploadErrorName">
                        <li v-for="(item,index) in uploadErrorNameList" :key="index">{{item}}</li>
                        <li v-show="uploadErrorNameList.length == 2">...</li>
                    </ul>
                </div>
            </div>
            <div class="isFinishBtn" v-if="isFinishBtn">
                <Button size="large" type="primary" @click="isFinishAction">完成</Button>
            </div>
        </Modal>
    </div>
    
</template>
<script>
import { sdImgUpload } from 'sd-img-upload'
import '@/assets/js/magnify'
import { getUploadUrl, goFastUrlDeal } from "@/libs/util";
export default {
    components:{
        sdImgUpload,
    },
    data() {
        return {
            loading: false,
            EvidenceUploadModal: false,
            fileType: 'img',
            evidenceName2: '',
            formData: {
                evidenceName: '',
                evidenceType: '',
                evidenceCode: '',
                evidenceProvider: '',
                evidencePointer: '',
                evidencePurpose: '',
                materialName: '',
                remarks: ''
            },
            ruleValidate: {
                evidenceName: [
                    { required: true, message: '请选择证据名称', trigger: 'change' }
                ],
                materialName: [
                    { required: true, message: '请输入材料名称', trigger: 'change' }
                ]
            },
            zjmcList: [],
            zjtgdxList: [],
            zjzxdxList: [],
            uploadUrl: getUploadUrl() + '&uploadType=1',
            pdfUploadUrl: getUploadUrl() + '&uploadType=1',
            uploadMaxSize: serverConfig.uploadMaxSize,
            uploadMaxCount: serverConfig.uploadMaxCount,
            uploadFinishCount: 0,
            uoloadFileTotal: 0,
            progressModal: false,
            uploadTimer: null,
            inTime: '',
            uploadErrorCount: 0,
            percentVal: 0,
            uploadImgList: [],
            uploadErrorNameList: [],
            isFinishBtn: false,
            allImgList: [],
            allPdfList: [],
            rightClickNode: {},
            total: 0,
            pageSize: 10,
            currentPage: 1,
            templateId: '',
            uploadType: '', //证据上传 type: 1 右上角，2 树结构
            ajlx: '',
            showUploadBtn: true
        }
    },
    created(){
        
    },
    methods:{
        initAction(type,templateId){
            this.showUploadBtn = true;
            this.closeAction();
            this.EvidenceUploadModal = true;
            this.uploadType = type;
            this.templateId = templateId;
            this.getZjmcList(1)
        },
        getTemplateId(){
            let templateId = ''
            if(this.$parent.newDocDataAll_ybm.length > 0){
                templateId = this.$parent.newDocDataAll_ybm[0].templateId || ''
            }else if(this.$parent.newDocDataAll_wbm.length > 0){
                templateId = this.$parent.newDocDataAll_wbm[0].templateId || ''
            }else{
                templateId = this.$parent.templateId
            }
            return templateId
        },
        searchEvent(val){
            this.getZjmcList(1,val)
        },
        handlePage(page){ // 分页
            this.getZjmcList(page)
        },
        getEvidenceDetail(catalogId){ // 获取证据材料属性（可编辑）
            this.showUploadBtn = false;
            let params = {
                id: catalogId,
            }
            this.$Post_RP(this.API.DmsApi.GET_EVIDENCE_DETAIL,params).then(res=>{
                if(res.success){
                    this.formData = res.data;
                    this.evidenceName2 = res.data.evidenceName;
                    this.formData.dossierMaterialInfoId = res.data.dossierMaterialInfoId;
                }else{
                    this.$Message.warning(res.msg)
                }
            })
        },
        changeUrl(originalUrl){ // 图片查看路径转换
            if(originalUrl){
                return originalUrl.replace(serverConfig.uploadUrl,serverConfig.downloadUrl);
            }
        },
        ViewOriginalFile(item){ // pdf 文件预览
            let routeData = this.$router.resolve({
                path:'/dms/pdfViewer',
                query:{
                    pdfUrl:item.url
                }
            })
            window.open(routeData.href, '_blank');
        },
        getPeopleList(ajxxDetailObj){ // 获取证据提供对象、证据指向对象
            this.zjtgdxList = [];
            this.zjzxdxList = [];
            if(ajxxDetailObj.ajblList.length > 0){
                ajxxDetailObj.ajblList.forEach(item=>{
                    this.zjtgdxList.push(item.cbrXm)
                })
            }
            if(ajxxDetailObj.peopleList.length > 0){
                ajxxDetailObj.peopleList.forEach(item=>{
                    this.zjtgdxList.push(item.xm)
                    this.zjzxdxList.push(item.xm)
                })
            }
        },
        getZjmcList(currentPage, searchText){ // 获取证据名称
            let params = {
                name: searchText,
                ajlx: this.ajlx,
                pageSize: 10,
                currentPage: currentPage,
                catalogId: this.uploadType == '2' ? this.rightClickNode.catalogId : '', //材料id（目录上传需要传）
                templateId:  this.getTemplateId() //模板id 目录上传跟右上角上传都需要模板id
            }
            this.$Post_RP(this.API.DmsApi.GET_EVIDENCE_NAME_INFO,params).then(res=>{
                if(res.success){
                    this.zjmcList = res.data.records;
                    let obj = {
                        value: JSON.stringify(this.zjmcList[0])
                    }
                    this.evidenceName2 = obj.value;
                    this.zjmcAction(obj)
                    this.total = res.data.total;
                }
            })
        },
        zjmcAction(val){ // 证据名称选择事件
            var obj = JSON.parse(val.value)
            this.formData.evidenceName = obj.evidenceName;
            this.formData.evidenceType = obj.typeName;
            this.formData.evidenceCode = obj.evidenceCode;
        },
        scanUploadAction(){ // 扫描上传
            this.$parent.ScanUploadEvent();
        },
        filesSubmitted(files,fileList){ //开始上传
            if(files.length > 0){
                this.uploadFinishCount = 0;
                this.uoloadFileTotal = files.length;
                this.progressModal = true;
                console.log('开始上传')
                var startTime = new Date().getTime();
                this.uploadTimer = setInterval(()=>{
                    let nowTime = new Date().getTime();
                    let time = nowTime - startTime;
                    var h = parseInt(time/3600000),
                        m = parseInt((time - (h*3600000))/60000),
                        s = parseInt((time - (h*3600000) - (m*60000))/1000);
                        h = h < 10 ? '0'+h : h;
                        m = m < 10 ? '0'+m : m;
                        s = s < 10 ? '0'+s : s;
                        this.inTime = h+':'+m+':'+s;
                },1000)
            }else{
                this.$alert({
                    type: 'warning',
                    title: '温馨提示',
                    width: 400,
                    content:  "请选择文件上传！"
                });
            }
        },
        onFileAdded(files,fileList, event){
            files.name = this.utils.getUploadFileNmae(files.name);
            files.relativePath = this.utils.getUploadFileNmae(files.relativePath);
            files.file = new File([files.file],files.name)
        },
        onFileSuccess(rootFile,message,file,chunk){ // 树结构本地上传 1：为未编目上传，2：为树结构处上传
            if((file && file.url) || (file.data && file.data.length > 0)){
                this.uploadFinishCount ++;
                this.percentVal = parseInt((this.uploadFinishCount/(this.uoloadFileTotal-this.uploadErrorCount))*100);
                //文件序号赋值
                file.xh = rootFile.xh
                file.name = this.utils.getUploadFileNmae(file.url);
                this.uploadImgList.push(file);
                if(this.fileType !== 'pdf'){
                    this.allImgList.push(file)
                }else{
                    this.allPdfList.push(file)
                }
                if(this.uploadImgList.length > 0 && this.percentVal == 100){
                    if(this.fileType !== 'pdf'){
                        
                    }
                    this.uploadImgList = []
                }
            }else{
                this.uploadErrorCount ++;
                if(this.uploadErrorNameList.length < 2){
                    if(file.url){
                        this.uploadErrorNameList.push(this.utils.getUploadFileNmae(file.url));
                    }else if(file.data && file.data.lenth > 0){
                        file.data.forEach(item=>{
                            this.uploadErrorNameList.push(this.utils.getUploadFileNmae(item.url));
                        })
                    }
                }
                
            }
        },
        complete(){ // 上传结束
            this.$refs['imageUpload'].uploader.uploader.cancel();
            // this.$refs['imageUpload2'].uploader.uploader.cancel();

            this.$refs['imageUpload3'].uploader.uploader.cancel();
            // this.$refs['imageUpload4'].uploader.uploader.cancel();
            
            clearInterval(this.uploadTimer);
            this.isFinishBtn = true;
        },
        imgUploadEvent(){ // 上传图片保存
            var uploadImg = [];
            this.allImgList.sort(this.compare)
            this.allImgList.forEach((item,index) => {
                if(item.url){
                    var obj = {
                        url: goFastUrlDeal(item),
                        fileName: this.utils.getUploadFileNmae(item.url),
                        addTime: (new Date()).getTime()+index
                    }
                    uploadImg.push(obj)
                }else{
                    item.data.forEach((item2,index2)=>{
                        var obj = {
                            url: goFastUrlDeal(item2),
                            fileName: this.utils.getUploadFileNmae(item2.url),
                            addTime: ((new Date()).getTime()+(index+index2))
                        }
                        uploadImg.push(obj)
                    })
                }
            });
            this.$parent.fileSuccessAfter(uploadImg,this.formData);
        },
        pdfUploadEvent(){ // 上传pdf保存
            let params = {
                ajbh: this.$route.query.ajbh,
                pdfMsg: JSON.stringify(this.allPdfList),
                dmsEvidenceInfo: JSON.stringify(this.formData),
                type: 1,
                addUser: JSON.parse(localStorage.getItem('usreInfo')).id,
                name: this.rightClickNode.name,
                catalogId: this.rightClickNode.catalogId,
                templateId: this.getTemplateId(),
                partCatalogId: '',
            }
            this.$Post(this.API.DmsConvertApi.PDF_UPLOAD,params).then(data=>{
                if(data.success){
                    if(data.repeatPdf && data.repeatPdf.length > 0){
                        const title = '警告';
                        var content = '<p style="margin-top:0.2rem;">请勿重复上传以下PDF文件：</p>';
                        data.repeatPdf.forEach((item,index)=>{
                            content += '<p>'+(index+1)+'、' +item + '</p>';
                        })
                        this.$alert({
                            type: 'warning',
                            title: title,
                            content: content
                        });
                    }
                    this.closeAction(true);
                    // localStorage.setItem('pdfUploadBatchId',data.batchId);
                    this.$parent.ConversionProgressAction() // 保存之后触发PDF转换
                }else{
                    this.$Message.warning(data.msg)
                }
            })
        },
        uploadAction(){ // 上传所有保存触发事件
            this.$refs['formData'].validate((valid) => {
                if (valid) {
                    if(this.allImgList.length > 0){
                        this.imgUploadEvent()
                    }
                    if(this.allPdfList.length > 0){
                        this.pdfUploadEvent()
                    }
                    if(this.allImgList.length < 1 && this.allPdfList.length < 1 && this.showUploadBtn){
                        this.$Message.error('请上传附件！')
                    }
                } else {
                    this.$Message.error('表单验证不通过!')
                }
            })
        },
        saveFormAction(){ // 属性保存
            this.$refs['formData'].validate((valid) => {
                if (valid) {
                    this.$Post_RP(this.API.DmsApi.UPDATE_EVIDENCE_DETAIL,this.formData).then(res=>{
                        if(res.success){
                            this.closeAction(true)
                            this.$Message.success('保存成功!');
                        }else{
                            this.$Message.warning(res.msg);
                        }
                    })
                } else {
                    this.$Message.error('表单验证不通过!')
                }
            })
        },
        closeAction(isClosed){ // 关闭取消弹窗
            if(isClosed){
                this.EvidenceUploadModal = false;
            }
            if(this.showUploadBtn){
                this.$refs.formData.resetFields();
                this.evidenceName2 = '';
                this.allImgList = [];
                this.allPdfList = [];
            }else{
                this.allImgList = [];
                this.allPdfList = [];
                this.formData.evidenceProvider = '';
                this.formData.evidencePointer = '';
                this.formData.evidencePurpose = '';
                this.formData.materialName = '';
                this.formData.remarks = '';
            }
        },
        isFinishAction(){ // 上传进度弹窗
            this.isFinishBtn = false;
            this.progressModal = false;
            this.percentVal = 0;
            this.uploadErrorCount = 0;
            this.uploadErrorNameList = [];
        },
        imgDeleteEvent(md5){ // 附件删除功能
            let params = {
                md5: md5
            }
            this.$Post(this.API.UploadApi.SUNDUN_FILE_DELETE,params).then(res=>{
                if(res.status == 'ok'){
                    if(this.allImgList.length > 0){
                        this.allImgList.forEach((item,index)=>{
                            if(item.md5 == md5){
                                this.allImgList.splice(index,1)
                            }
                        })
                    }
                    if(this.allPdfList.length > 0){
                        this.allPdfList.forEach((item,index)=>{
                            if(item.md5 == md5){
                                this.allPdfList.splice(index,1)
                            }
                        })
                    }
                }
            })
        }
    },
}
</script>
<style lang="less" scoped>
@assets: '../../assets';
    .ivu-form-item{
        width: 49%;
    }
    .uploadWrap{
        width: 100%;
        display: flex;
        margin-bottom: 0.24rem;
        label{
            text-align: right;
            display: inline-block;
            font-size: 0.16rem;
            min-width: 1.5rem;
            line-height: 0.35rem;
            padding-right: 0.1rem;
        }
        .uploadBox{
            width: calc(~'100% - 1.6rem');
            border-radius: 4px;
        }
        .uploadType{
            display: flex;
            align-items: center;
            li{
                width: calc(~'33% - 0.18rem');
                line-height: 0.35rem;
                text-align: center;
                border: solid 1px #CCCCCC;
                border-radius: 2px;
                font-size: 0.14rem;
                cursor: pointer;
            }
            li:hover{
                opacity: 0.7;
            }
            li:nth-child(2){
                margin: 0 0.27rem;
            }
        }
        .uploadImgList{
            min-height: 1rem;
            max-height: 2.5rem;
            overflow: auto;
            margin-top: 0.1rem;
            display: flex;
            flex-wrap: wrap;
            .uploadImgItem{
                width: 1rem;
                position: relative;
                margin-right: 0.1rem;
                margin-bottom: 0.1rem;
                border: solid 2px #a9b6c9;
                img{
                    width: 100%;
                    cursor: pointer;
                }
                .deleteBtn{
                    display: inline-block;
                    width: 0.25rem;
                    height: 0.25rem;
                    background: url('@{assets}/images/dzjz/icon_scbtn2.png') no-repeat;
                    position: absolute;
                    top: 0;
                    right: 0;
                    cursor: pointer;
                }
                .deleteBtn:hover{
                    opacity: 0.7;
                }
            }
            .uploadImgItem2{
                max-width: 2.3rem;
                line-height: 0.35rem;
                margin-bottom: 0.1rem;
                margin-right: 0.15rem;
                display: flex;
                align-items: center;
                span{
                    display: inline-block;
                    width: 94%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    cursor: pointer;
                }
                .deleteBtn2{
                    display: inline-block;
                    width: 0.25rem;
                    height: 0.25rem;
                    background: url('@{assets}/images/dzjz/icon_dlt2.png') no-repeat;
                    cursor: pointer;
                }
                .deleteBtn2:hover{
                    opacity: 0.7;
                }
            }
        }
    }
    .isFinishBtn{
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        div{
            margin: 0 0.1rem;
            width: 0.8rem;
            height: 0.32rem;
            line-height: 0.32rem;
            background: #FFFFFF;
            border: 1px solid #087EFF;
            border-radius: 2px;
            color: #087EFF;
            font-size: 0.14rem;
            cursor: pointer;
        }
        div:hover{
            opacity: 0.7;
        }
        div:last-child{
            background: #087EFF;
            color: #fff;
        }
    }
    .uploadBtn{
        display: inline-block;
        width: 100%;
        height: 100%;
    }
</style>
<style lang="less" scoped>
    /deep/.ivu-select-dropdown{
        max-height: 4.2rem !important;
    }
    /deep/.ivu-page{
        width: 5rem;
        justify-content: center;
    }
    /deep/.ivu-select-single .ivu-select-input{
        font-size: 0.16rem
    }
</style>
