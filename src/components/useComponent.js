import MyBtn from '_c/myButton' // 按钮
// import MyMessage from '_c/myMessage' // 静态提示

import PageFooter from '_c/pageFooter'   //分页器

import ReplaceIcon from '_c/replaceIcon'   //空白替换icon

// import MyTag from '_c/myTag'    //自定义tag组件

export default {
    install(Vue) {
        Vue.component('MyBtn', MyBtn)
        // Vue.component('MyMessage', MyMessage)
        Vue.component('PageFooter', PageFooter)
        Vue.component('ReplaceIcon', ReplaceIcon)
        // Vue.component('MyTag', MyTag)
    }
}
