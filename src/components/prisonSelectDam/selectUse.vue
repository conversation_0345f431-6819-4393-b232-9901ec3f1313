<!-- 监所人员选择组件 -->
 <template>
    <div class="prison-select">
       <div class="prison-select-center" v-if="userList && userList.length>0"  >
           <div class="use-list" style="height: auto;" >
              <div v-for="(item,index) in userList" :key="index" :class="['use-list-box',item.checked?'checked':'']"  :style="{width:showRybh?'19%':'16%'}" style="height:160px;">
                 <div class="user-flex"><p class="rybh" v-show="showRybh">人员编号：<span class="rybhZs" :title="item.rybh">{{ item.rybh }}</span></p><span class="userXb" v-show="showRybh"><img :src="item.xb=='男'?men:women" /></span></div>
                 <div class="user-flex-sm">
                    <img :src="item.frontPhoto?http+item.frontPhoto:defaultImg" style="width: 88px;height:110px;margin-right: 10px;" />
                    <div style="width: 100%;">
                       <p class="user-flex-sm" ><span class="userName">{{ item.xm }}</span><span class="" v-show="!showRybh">&nbsp;&nbsp;<img :src="item.xb=='男'?men:women" /></span><span class="ZS" v-show="showRybh">在所</span></p>
                       <p><span><i class="fontType ">{{ item.roomName }}</i></span></p>
                     <div class="user-flex">
                       <div>
                        <p><span><i class="fontTip ">{{ item.sxzm }}</i><span v-if="item.sshj">&nbsp;&nbsp;|&nbsp;&nbsp;</span><i class="fontTip ">{{ item.sshj }}</i></span></p>
                        <p v-if="item.fxdj"><span class="fxdj">{{ item.fxdj }}</span></p>
                       </div>
                     </div>
                    </div>
                    <div></div>
                 </div>
                 <Icon v-if="showRemove" type="ios-close-circle-outline" :size="26" color="gray"  class="checkedIcon" style="right: -90%;top: 1px;" @click="remove(item,index)" />
                 <!-- <Spin size="large" fix v-if="spinShow"></Spin> -->
               </div>
             
           </div>
           <!-- class="use-list" -->


       </div>
       <div  v-else style="">  
            <div style="margin:auto">
               <!-- <img src="@/assets/images/noData.png" style=""  /> -->
               <p class="">暂无人员数据</p>
            </div>
      </div>

    </div>
 </template>
 
 <script>


 export default {
   props: {
      showRybh:{
         type:Boolean,
         default:false
      },
      checkedArr:{
         type:Array,
         default:[]
      },
      selectUseIds:{
         type: String,  //已选择人员
         default: '' //'7aaf5d9403bc11f08fec0242ac110002,7aaf581a03bc11f08fec0242ac110002'
      },
      showRemove:{
         type:Boolean,
         default:true
      }
   },
   watch:{
      checkedArr:{
         handler(n,o){
           if(n && n.length>0){
            this.userList=n
           }
         },deep:true,immediate:true
      },
   },
    data(){
        return{
         // jgrybm:this.$route.query.jgrybm?this.$route.query.jgrybm:'',
         // spinShow:false,
         defaultImg:require('@/assets/images/main.png'),
         http:serverConfig.severHttp,
         women:'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAABjklEQVQ4EcVSsUoDQRB9c3eJQbAwn6BFKlNYqAFtLQ0mGLERsRQLjZXaWgqxSilil4gJaa2FoEUKray1TgRFwuVux9nlclxiTCU4sLezM2/evZld4L+NxgnIV3iLoVIDGMvyYOGqlqc3HXcGkkMHRXwMxtxAWCmQMIid6c9YgnrBSmtQ1NYrvohSYV3oaNBug6feu+qIiZaltyTALdhWWeS2oiRR32jRgY0qpzpdfpLiUzlOyHoFaI19fshXeV9jRpkhkGLbA18TIUE2LYr0lVrBytqgGWI0FLiUu+X5XwkkkQHzgvzxICr3pkCfNtGOtNOBr/YMAdGzDPGlT2Zm4DEMe2ISd/1Ef9ckuapqCrnBDA/WtCCfti7we3pwP41loLIMZjhrCJw4mgRSbi+QGUEFvS8R830kHLrhS8xV/HO5gaJcXSkes8p2DO3uF1blfMGM7nSC0pdZ+ggrAyd8BzZZJz4rCdOh66oi3ABB9OgQbY8q1ohQQQDHZp1nPRcZoUo6BP2AmjJIv5//8/0bMieGFMlZxEQAAAAASUVORK5CYII=',
         men:'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAABuElEQVQ4Eb2SPUsDQRCGZ/aSiIFYKwgWWhgQxMZSrexslCSoBLWQgCDEYDqNJirit2CjYCuSC/kB2hksAhZidTYidtoaUEzudpw1boxyYCNecbs78z5zs+8cwH88tJpvo7V8i9u30C2oY3bGHAMJOwTUrGKIcEdCzHgXQ+daI/Tm52qns9Mk6QSQLBQ4JQTGCPAZHHlWWckNar1rB7RrNjoleGJRwUiFhhCRFECm6bMtKCJQk2cp0qFi7h28GN1EFCAQBxpWYgyHywLhkAjatSfuBQB8CmCkXF2/3txKNUZVTa2AvZIbrqTNTW7TAK/nGhlGcGJfKF+BCNnUCc494sLIg8p5agIpA0CQdCzRagTfomTRFZ8jdtrkxThi3OdkcrM8kQE2dVxz30x0Mtl5KWFLoLiVJDv5/pdM9zL0cSU+l3iWCU8qdOxaQAV5fBdsUp8qIoLUBfcNfqjYPZwqg9+5wUT4VcNq/dZBZTm7x7E4/zAFVQRRnBpBirL7Tj1Uv6+ZqGFO7vOM+4WAJJEc5bmv1wM/9x8FaCPfyom4gr3LkTklMlKRbe5kkg0rqvOvz2eRX3V/LngHpeWkFKJkiGAAAAAASUVORK5CYII=',
         // openCurRoom:1,
         //   jsData:[],
         //   total:0,
         //   page:{
         //    pageNo:1,
         //    pageSize:this.pageSize,
         //    xm:'',rssj:[]
         //   },
           userList:[],
         //   checkedUse:[],
         //   curRoom:''
        }
    },
    mounted(){
      // this.getJqData()
      // // this.getAllUserData()
      // if(this.selectUseIds){
      //    this.getAllUserData(true)
      // }
    },
    methods:{
      calculateAgeFromID(id) {
         if(id){
            if (id.length && id.length !== 18) {
            return ''
         }
         // Extract the birth date from the ID (yyyyMMdd format)
         const birthDateString = id.substring(6, 14); // Extracts yyyyMMdd
         const [year, month, day] = [birthDateString.substring(0, 4), birthDateString.substring(4, 6), birthDateString.substring(6, 8)];
         // Create a Date object for the birth date
         const birthDate = new Date(year, month - 1, day); // Month is 0-based in JavaScript
         // Calculate the age
         const today = new Date();
         let age = today.getFullYear() - birthDate.getFullYear();
         const m = today.getMonth() - birthDate.getMonth();
         if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
            age--; // Adjust if the current date is before the birthday this year
         }
            return age;
         }else{
            return null
         }
        
      },
      remove(item,index){
         this.userList.splice(index,1)
         this.$emit('remove', this.userList)
      }
    }
 }
 </script>
 
 <style lang="less">
 @import url('./index.less');
 </style>