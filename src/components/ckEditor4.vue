<template>
    <div>
        <textarea v-show="false" name="editor" id="editor" :value="editValue" rows="10" cols="80" placeholder="请输入内容"></textarea>
    </div>
</template>
<script>
export default {
    name: 'Editor',
    data () {
        return {
            editValue:'',
            ckEditorObj:[]
        }
    },
    created(){
        this.Bus.$on('editValue',(val)=>{
            if(val !== undefined){
                this.editValue = val.content;
                CKEDITOR.instances.editor.setData(this.editValue);
            }
        })
    },
    mounted(){
        this.setting();
    },
    methods: {
        setting(){
            this.ckEditorObj = CKEDITOR.replace( 'editor', {
                toolbar: [
                    { name: 'document', items: ['Source', 'Save', 'Print', 'NewPage', 'Preview' ] },
                    { name: 'clipboard', items: [ 'Cut', 'Copy', 'Paste', 'Undo', 'Redo' ] },
                    { name: 'styles', items: [ 'Format', 'Font', 'FontSize' ] },
                    { name: 'basicstyles', items: [ 'Bold', 'Italic', 'Underline', 'CopyFormatting' ] },
                    { name: 'colors', items: [ 'TextColor', 'BGColor' ] },
                    { name: 'align', items: [ 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock' ] },
                    { name: 'links', items: [ 'Link', 'Unlink' ] },
                    { name: 'paragraph', items: [ 'NumberedList', 'BulletedList'] },
                    { name: 'insert', items: [ 'Table'] },
                    { name: 'tools', items: [ 'Maximize' ] },
                ],
                //不允许改变大小
                resize_enabled: false,
                //使用tab键时走过的空格数
                tabSpaces: 4,
                language: 'zh-cn',
                //去掉左下角的一系列提示标签，如body, p
                removePlugins: 'elementspath',
                enableContextMenu: true,
                contextMenu: [],
                //图片上传预览文字
                image_previewText: ' ',
                filebrowserUploadMethod: 'form',
                //工具栏上传接口
                // filebrowserUploadUrl: layui.web.appUrl + "/api/editor/uploadImg?uploadUrl=" + layui.info.uploadUrl,
                customConfig: '',
                allowedContent: true,
                extraPlugins: 'tableresize,uploadimage,uploadfile',
                //通过复制或拖拽的文件上传的api
                // uploadUrl: layui.web.appUrl + "/api/editor/uploadPastedOrDraggedImg?uploadUrl=" + layui.info.uploadUrl,
                height: 660,
                contentsCss: ['/ckeditor/contents.css','/ckeditor/mystyles.css' ],
                bodyClass: 'document-editor',
                format_tags: 'p;h1;h2;h3;pre',
                removeDialogTabs: 'image:advanced;image:Link',
                stylesSet: [
                    { name: 'Marker', element: 'span', attributes: { 'class': 'marker' } },
                    { name: 'Cited Work', element: 'cite' },
                    { name: 'Inline Quotation', element: 'q' },
                    {
                        name: 'Special Container',
                        element: 'div',
                        styles: {
                            padding: '5px 10px',
                            background: '#eee',
                            border: '1px solid #ccc'
                        }
                    },
                    {
                        name: 'Compact table',
                        element: 'table',
                        attributes: {
                            cellpadding: '5',
                            cellspacing: '0',
                            border: '1',
                            bordercolor: '#ccc'
                        },
                        styles: {
                            'border-collapse': 'collapse'
                        }
                    },
                    { name: 'Borderless Table', element: 'table', styles: { 'border-style': 'hidden', 'background-color': '#E6E6FA' } },
                    { name: 'Square Bulleted List', element: 'ul', styles: { 'list-style-type': 'square' } }
                ]
            });
            //重写富文本编辑器的保存事件
            var _this = this;
            this.ckEditorObj.on("instanceReady", function(event){
                this.addCommand("save", {modes: {wysiwyg:1, source:1}, exec: (editor)=>{
                    _this.$parent.saveDoc();
                }});
            });
           
        },
    }
}
</script>
<style>
    .cke_combo_text{
        width: 0.58rem !important;
    }
</style>