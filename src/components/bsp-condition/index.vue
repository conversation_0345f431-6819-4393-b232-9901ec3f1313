<template>
  <div :class="['bsp-condition', 'condition-' + layout]">
    <Form class="bsp-form" ref="gxxForm" :label-width="labelWidth" :model="value">
      <slot></slot>
      <div class="bsp-search">
        <slot name="operate">
          <Button type="primary" class="btn-search" @click="handleSearch">查询</Button>
          <Button type="primary" class="btn-clear" @click="handleClear">重置</Button>
        </slot>
      </div>
    </Form>
  </div>
</template>
<script>
export default {
  name: 'bspCondition',
  props: {
    labelWidth: {
      type: Number,
      default: 120
    },
    value: {
      type: Object,
      default: () => { }
    },
    instance: {
      type: Object,
      required: false
    },
    layout: {
      type: String,
      default: 'right'
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  mounted() {
    let param = Object.assign({}, this.value)
    this.$emit('change', param)
  },
  methods: {
    handleSearch() {
      this.instance && this.getTableList()
      this.$emit('on-submit')
    },
    handleClear() {
      this.$refs.gxxForm.resetFields()
      let param = Object.assign({}, this.value)
      this.$nextTick(() => {
        this.instance && this.getTableList()
        this.$emit('change', param)
        this.$emit('on-reset')
      })
    },
    getTableList() {
      if (!this.instance) return
      this.$nextTick(() => {
        this.instance.query_grid_data(1)
      })
    }
  }
}
</script>
<style lang="less">
.bsp-condition {
  width: 100%;
  .bsp-form {
    background: #f7f9fc;
    padding: 20px 20px 10px 20px;
    margin-bottom: 10px;
    border: 1px solid #cee0f0;
    display: flex;

    .ivu-form-item {
      margin-bottom: 10px;
    }

    .ivu-form-item-label {
      font-size: 16px;
      padding: 8px 2px 10px 0;
      color: #1a2133;
    }

    .ivu-form-item-content {
      flex: 1;
    }
    .ivu-input-wrapper,.ivu-select {
      width: 200px;
    }

  }

  .bsp-search {
    display: flex;
    font-size: 16px;

    button {
      padding: 0;
      margin-left: 20px;
      width: 56px;
      line-height: 30px;

      &.btn-search {
        background: #2b5fd9;
        color: #fff;
      }

      &.btn-clear {
        color: #2b5fd9;
        border: 1px solid #2b5fd9;
        background: #fff;
      }
    }
  }


  &.condition-center {
    .bsp-search {
      flex: 1;
      justify-content: center;
    }
  }
}
</style>
