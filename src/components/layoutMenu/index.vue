<template>
    <div class="layoutMenu">
        <mainNew v-if="menuMode == 'side'" />
        <mainTop v-else />
    </div>
</template>
<script>
import mainTop from '@/components/main/main.vue'
import mainNew from '@/components/main-menu-new/main.vue'
export default {
    components: { mainTop, mainNew },
    data() {
        return {
            menuMode: localStorage.getItem('menuMode') ? localStorage.getItem('menuMode') : 'side'
        }
    }
}
</script>
<style scoped lang="less">
.layoutMenu {
    width: 100vw;
    height: 100vh;
}
</style>