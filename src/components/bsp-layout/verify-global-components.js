/**
 * BSP 动态布局组件全局注册验证
 * 用于验证组件是否正确注册为全局组件
 */

export function verifyGlobalComponents(Vue) {
  const requiredComponents = [
    'DynamicLayout',
    'DynamicComponent',
    'LayoutRenderer',
    'DetailCardLayout',
    'DynamicForm',
    'PersonnelSelector'
  ]

  const registrationStatus = {}
  const errors = []

  // 检查每个组件是否已注册
  requiredComponents.forEach(componentName => {
    try {
      const component = Vue.component(componentName)
      if (component) {
        registrationStatus[componentName] = {
          registered: true,
          component: component
        }
        console.log(`✅ ${componentName} 已成功注册为全局组件`)
      } else {
        registrationStatus[componentName] = {
          registered: false,
          component: null
        }
        errors.push(`❌ ${componentName} 未注册为全局组件`)
      }
    } catch (error) {
      registrationStatus[componentName] = {
        registered: false,
        component: null,
        error: error.message
      }
      errors.push(`❌ ${componentName} 注册检查时出错: ${error.message}`)
    }
  })

  // 检查布局配置是否可用
  try {
    if (Vue.prototype.$layoutConfigs) {
      console.log('✅ 布局配置已注册到 Vue 原型')
      console.log('可用布局类型:', Object.keys(Vue.prototype.$layoutConfigs))
    } else {
      errors.push('❌ 布局配置未注册到 Vue 原型')
    }
  } catch (error) {
    errors.push(`❌ 布局配置检查时出错: ${error.message}`)
  }

  // 检查组件注册中心是否可用
  try {
    if (Vue.prototype.$componentRegistry) {
      console.log('✅ 组件注册中心已注册到 Vue 原型')
      const stats = Vue.prototype.$componentRegistry.getCacheStats()
      console.log('组件注册统计:', stats)
    } else {
      errors.push('❌ 组件注册中心未注册到 Vue 原型')
    }
  } catch (error) {
    errors.push(`❌ 组件注册中心检查时出错: ${error.message}`)
  }

  // 输出验证结果
  console.log('\n=== BSP 动态布局组件注册验证结果 ===')

  if (errors.length === 0) {
    console.log('🎉 所有组件都已成功注册为全局组件！')
    console.log('您现在可以在任何 Vue 组件中直接使用以下组件：')
    requiredComponents.forEach(name => {
      console.log(`  - <${name}>`)
    })
  } else {
    console.error('⚠️ 发现以下问题：')
    errors.forEach(error => console.error(error))
  }

  return {
    success: errors.length === 0,
    registrationStatus,
    errors,
    availableComponents: requiredComponents.filter(name =>
      registrationStatus[name] && registrationStatus[name].registered
    )
  }
}

// 在开发环境下自动验证
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，确保 Vue 实例已创建
  setTimeout(() => {
    if (typeof window !== 'undefined' && window.Vue) {
      verifyGlobalComponents(window.Vue)
    }
  }, 1000)
}

export default verifyGlobalComponents
