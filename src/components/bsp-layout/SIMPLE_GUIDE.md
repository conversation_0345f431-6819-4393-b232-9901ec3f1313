# BSP 布局组件 - 简化使用指南

让页面开发变得超级简单！只需要几行代码就能搭建专业的详情页面。

## 🚀 三种使用方式，无缝升级

### 阶段1: 超简单起步 - EasyLayout

**适合**: 快速原型、概念验证、5分钟搭建

```vue
<template>
  <!-- 第一步：最简单的开始 -->
  <EasyLayout left-width="300px">
    <template #left>
      <div style="padding: 20px;">
        <h3>张三</h3>
        <p>编号：P001</p>
        <p>监区：一监区</p>
      </div>
    </template>

    <template #right>
      <div style="padding: 16px;">
        <EasyCard title="基本信息">
          <p>谈话时间：2024-01-15</p>
          <p>谈话民警：李警官</p>
        </EasyCard>

        <EasyCard title="操作功能">
          <Button @click="viewVideo">查看录像</Button>
          <Button @click="viewRecord">查看笔录</Button>
        </EasyCard>
      </div>
    </template>
  </EasyLayout>
</template>

<script>
export default {
  data() {
    return {
      // 数据先写在这里
      person: { name: '张三', code: 'P001', area: '一监区' }
    }
  },
  methods: {
    viewVideo() { this.$Message.info('查看录像') },
    viewRecord() { this.$Message.info('查看笔录') }
  }
}
</script>
```

**升级提示**: 当需要收缩功能、数据绑定时 → 升级到阶段2

### 阶段2: 功能增强 - SimpleDetailCardLayout

**适合**: 需要收缩、数据绑定、标准化的业务场景

```vue
<template>
  <!-- 第二步：无缝升级，保持相同的插槽内容 -->
  <SimpleDetailCardLayout
    left-title="人员信息"
    left-icon="ios-person"
    :data="person"
    :cards="cards"
    :actions="actions"
    @action="handleAction"
    @update:data="person = $event"
  >
    <!-- 🔄 插槽内容完全一样，只是加了数据绑定 -->
    <template #left="{ data, update }">
      <div style="padding: 20px;">
        <h3>{{ data.name }}</h3>
        <p>编号：{{ data.code }}</p>
        <p>监区：{{ data.area }}</p>

        <!-- ✨ 新增：数据更新功能 -->
        <Button @click="() => update({ lastViewed: new Date().toLocaleString() })"
                size="small" long style="margin-top: 16px;">
          更新查看时间
        </Button>

        <div v-if="data.lastViewed" style="margin-top: 12px;">
          <Alert><span slot="desc">{{ data.lastViewed }}</span></Alert>
        </div>
      </div>
    </template>

    <!-- 🔄 卡片内容也完全一样，只是用了插槽 -->
    <template #basic-info>
      <div style="padding: 16px;">
        <p>谈话时间：2024-01-15</p>
        <p>谈话民警：李警官</p>
        <!-- ✨ 新增：可以加更多功能 -->
        <Button @click="refreshInfo" size="small">刷新</Button>
      </div>
    </template>

    <template #actions-card>
      <div style="padding: 16px;">
        <Button @click="viewVideo" type="primary">查看录像</Button>
        <Button @click="viewRecord" style="margin-left: 8px;">查看笔录</Button>
      </div>
    </template>
  </SimpleDetailCardLayout>
</template>

<script>
export default {
  data() {
    return {
      // 🔄 数据结构保持一样，只是提取到配置中
      person: { name: '张三', code: 'P001', area: '一监区' },

      // ✨ 新增：卡片配置（对应原来的EasyCard）
      cards: [
        { title: '基本信息', slot: 'basic-info' },
        { title: '操作功能', slot: 'actions-card' }
      ],

      // ✨ 新增：底部操作
      actions: [
        { name: 'save', label: '保存', type: 'primary' }
      ]
    }
  },
  methods: {
    // 🔄 方法保持一样
    viewVideo() { this.$Message.info('查看录像') },
    viewRecord() { this.$Message.info('查看笔录') },

    // ✨ 新增：更多功能
    refreshInfo() { this.$Message.info('信息已刷新') },
    handleAction(action) {
      if (action.name === 'save') {
        this.$Message.success('保存成功')
      }
    }
  }
}
</script>
```

**升级收益**:
- ✅ 获得收缩展开功能
- ✅ 自动数据绑定和更新
- ✅ 标准化的卡片结构
- ✅ 底部操作栏
- ✅ 插槽内容几乎不变

**升级提示**: 当需要复杂配置、动态组件时 → 升级到阶段3

### 阶段3: 完全定制 - DetailCardLayout

**适合**: 复杂业务、动态组件、企业级应用

```vue
<template>
  <!-- 第三步：最终升级，插槽内容完全保持 -->
  <DetailCardLayout
    :left-config="leftConfig"
    :right-cards="rightCards"
    :bottom-actions="bottomActions"
    @card-action="handleCardAction"
    @bottom-action="handleBottomAction"
    @collapse-change="handleCollapseChange"
  >
    <!-- 🔄 插槽内容完全一样！ -->
    <template #left="{ data, updateData }">
      <div style="padding: 20px;">
        <h3>{{ data.name }}</h3>
        <p>编号：{{ data.code }}</p>
        <p>监区：{{ data.area }}</p>

        <Button @click="() => updateData({ lastViewed: new Date().toLocaleString() })"
                size="small" long style="margin-top: 16px;">
          更新查看时间
        </Button>

        <div v-if="data.lastViewed" style="margin-top: 12px;">
          <Alert><span slot="desc">{{ data.lastViewed }}</span></Alert>
        </div>
      </div>
    </template>

    <!-- 🔄 卡片插槽名字一样，内容一样 -->
    <template #basic-info>
      <div style="padding: 16px;">
        <p>谈话时间：2024-01-15</p>
        <p>谈话民警：李警官</p>
        <Button @click="refreshInfo" size="small">刷新</Button>
      </div>
    </template>

    <template #actions-card>
      <div style="padding: 16px;">
        <Button @click="viewVideo" type="primary">查看录像</Button>
        <Button @click="viewRecord" style="margin-left: 8px;">查看笔录</Button>
      </div>
    </template>
  </DetailCardLayout>
</template>

<script>
export default {
  data() {
    return {
      // 🔄 数据转换为DetailCardLayout格式，但内容一样
      leftConfig: {
        width: '300px',
        title: '人员信息',
        icon: 'ios-person',
        collapsible: true,
        data: { name: '张三', code: 'P001', area: '一监区' }
      },

      // 🔄 卡片配置转换格式，但对应关系清晰
      rightCards: [
        {
          name: 'basic-info',
          title: '基本信息',
          icon: 'ios-information-circle',
          slot: 'basic-info',
          actions: [
            { name: 'edit', label: '编辑', icon: 'ios-create' }
          ]
        },
        {
          name: 'actions-card',
          title: '操作功能',
          icon: 'ios-options',
          slot: 'actions-card'
        }
      ],

      // 🔄 底部操作格式转换
      bottomActions: [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'save', label: '保存', type: 'primary', icon: 'ios-checkmark' }
      ]
    }
  },
  methods: {
    // 🔄 原有方法保持不变
    viewVideo() { this.$Message.info('查看录像') },
    viewRecord() { this.$Message.info('查看笔录') },
    refreshInfo() { this.$Message.info('信息已刷新') },

    // ✨ 新增：更强大的事件处理
    handleCardAction({ action, card }) {
      if (action.name === 'edit') {
        this.$Message.info(`编辑${card.title}`)
      }
    },

    handleBottomAction({ action }) {
      if (action.name === 'save') {
        this.$Message.success('保存成功')
      } else if (action.name === 'back') {
        this.$Message.info('返回上一页')
      }
    },

    handleCollapseChange({ isCollapsed }) {
      this.$Message.info(`面板${isCollapsed ? '收缩' : '展开'}`)
    }
  }
}
</script>
```

**升级收益**:
- ✅ 完整的配置选项和事件系统
- ✅ 支持动态组件和异步加载
- ✅ 高度可定制的样式和行为
- ✅ 企业级的功能完整性
- ✅ 插槽内容完全不变！

## 🔄 无缝升级指南

### 升级路径图
```
EasyLayout (5分钟)
    ↓ 需要收缩功能
SimpleDetailCardLayout (10分钟升级)
    ↓ 需要高级定制
DetailCardLayout (15分钟升级)
```

### 阶段1 → 阶段2 升级步骤

**第1步**: 替换组件标签
```vue
<!-- 从 -->
<EasyLayout left-width="300px">

<!-- 改为 -->
<SimpleDetailCardLayout
  left-title="人员信息"
  left-icon="ios-person"
  :data="person"
  :cards="cards"
>
```

**第2步**: 插槽内容保持不变，只加数据绑定
```vue
<!-- 插槽内容完全一样 -->
<template #left="{ data, update }">  <!-- 只是加了参数 -->
  <div style="padding: 20px;">
    <h3>{{ data.name }}</h3>  <!-- data.name 代替 person.name -->
    <!-- 其他内容完全一样 -->
  </div>
</template>
```

**第3步**: 提取卡片配置
```javascript
// 新增配置，对应原来的 EasyCard
cards: [
  { title: '基本信息', slot: 'basic-info' },
  { title: '操作功能', slot: 'actions-card' }
]
```

### 阶段2 → 阶段3 升级步骤

**第1步**: 替换组件标签
```vue
<!-- 从 -->
<SimpleDetailCardLayout
  left-title="人员信息"
  :data="person"
  :cards="cards"
>

<!-- 改为 -->
<DetailCardLayout
  :left-config="leftConfig"
  :right-cards="rightCards"
>
```

**第2步**: 转换配置格式（插槽内容不变）
```javascript
// 从简单配置
data: { name: '张三' },
cards: [{ title: '基本信息', slot: 'basic-info' }]

// 转为详细配置
leftConfig: {
  title: '人员信息',
  data: { name: '张三' }
},
rightCards: [{
  name: 'basic-info',
  title: '基本信息',
  slot: 'basic-info'
}]
```

**第3步**: 插槽内容完全不变！
```vue
<!-- 插槽名字和内容完全一样 -->
<template #left="{ data, updateData }">
  <!-- 内容完全不变 -->
</template>
```

## 📋 升级决策指南

| 需求 | 当前阶段 | 建议升级到 | 升级时间 |
|------|----------|------------|----------|
| 快速验证想法 | - | 阶段1 | 5分钟 |
| 需要收缩功能 | 阶段1 | 阶段2 | +10分钟 |
| 需要数据绑定 | 阶段1 | 阶段2 | +10分钟 |
| 需要底部操作栏 | 阶段1/2 | 阶段2 | +5分钟 |
| 需要动态组件 | 阶段1/2 | 阶段3 | +15分钟 |
| 需要复杂配置 | 阶段1/2 | 阶段3 | +15分钟 |
| 需要事件系统 | 阶段1/2 | 阶段3 | +10分钟 |

## 🎯 实际使用建议

### 开发流程
1. **原型阶段**: 使用 EasyLayout 快速搭建
2. **开发阶段**: 升级到 SimpleDetailCardLayout
3. **优化阶段**: 根据需要使用 DetailCardLayout

### 最佳实践
```vue
<!-- 推荐：从简单开始 -->
<template>
  <EasyLayout>
    <template #left>
      <!-- 先把内容放进来 -->
    </template>
    <template #right>
      <EasyCard title="信息">
        <!-- 内容 -->
      </EasyCard>
    </template>
  </EasyLayout>
</template>

<!-- 需要更多功能时再升级 -->
<template>
  <SimpleDetailCardLayout
    left-title="信息"
    :data="data"
    :cards="cards"
  >
    <!-- 同样的内容，更多功能 -->
  </SimpleDetailCardLayout>
</template>
```

## 🔧 常用代码片段

### 人员信息左侧面板
```vue
<template #left="{ data }">
  <div style="padding: 20px; text-align: center;">
    <Avatar size="large" icon="ios-person" />
    <h3 style="margin: 12px 0;">{{ data.name }}</h3>
    <Tag color="success">{{ data.status }}</Tag>

    <div style="margin-top: 20px; text-align: left;">
      <p><strong>编号：</strong>{{ data.code }}</p>
      <p><strong>年龄：</strong>{{ data.age }}岁</p>
      <p><strong>监区：</strong>{{ data.area }}</p>
    </div>
  </div>
</template>
```

### 操作功能卡片
```vue
<EasyCard title="操作功能" icon="ios-options">
  <Row :gutter="16">
    <Col span="12">
      <div class="action-item" @click="viewVideo">
        <Icon type="ios-videocam" size="32" color="#5b8ff9" />
        <p>查看录像</p>
      </div>
    </Col>
    <Col span="12">
      <div class="action-item" @click="viewRecord">
        <Icon type="ios-document" size="32" color="#52c41a" />
        <p>查看笔录</p>
      </div>
    </Col>
  </Row>
</EasyCard>

<style>
.action-item {
  text-align: center;
  padding: 20px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.action-item:hover {
  border-color: #5b8ff9;
  box-shadow: 0 4px 12px rgba(91, 143, 249, 0.15);
}
</style>
```

### 表格信息卡片
```vue
<EasyCard title="基本信息" icon="ios-information-circle">
  <template #actions>
    <Button size="small">编辑</Button>
  </template>

  <Table
    :columns="[
      { title: '项目', key: 'label' },
      { title: '内容', key: 'value' }
    ]"
    :data="[
      { label: '谈话时间', value: '2024-01-15 09:00' },
      { label: '谈话民警', value: '李警官' },
      { label: '谈话类型', value: '日常谈话' }
    ]"
    size="small"
  />
</EasyCard>
```

## 💡 小贴士

1. **从简单开始**: 先用 EasyLayout，需要时再升级
2. **复用代码片段**: 保存常用的插槽模板
3. **渐进增强**: 功能不够时再添加，不要一开始就用最复杂的
4. **保持一致**: 同一个项目中尽量使用同一种组件

## 🎉 5分钟搭建完整页面

```vue
<template>
  <EasyLayout>
    <template #left>
      <div style="padding: 20px; text-align: center;">
        <Avatar size="large" icon="ios-person" />
        <h3>张三</h3>
        <Tag color="success">在押</Tag>
        <div style="margin-top: 20px; text-align: left;">
          <p><strong>编号：</strong>P001</p>
          <p><strong>监区：</strong>一监区</p>
        </div>
      </div>
    </template>

    <template #right>
      <div style="padding: 16px;">
        <EasyCard title="基本信息">
          <p>谈话时间：2024-01-15 09:00</p>
          <p>谈话民警：李警官</p>
          <p>谈话状态：已完成</p>
        </EasyCard>

        <EasyCard title="操作功能">
          <Button type="primary">查看录像</Button>
          <Button style="margin-left: 8px;">查看笔录</Button>
        </EasyCard>
      </div>
    </template>
  </EasyLayout>
</template>
```

就这么简单！🎉
