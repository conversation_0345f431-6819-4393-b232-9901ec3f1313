<template>
  <div class="bsp-dynamic-component" :class="componentClass">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="component-loading">
      <Spin size="large">
        <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
        <div>组件加载中...</div>
      </Spin>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="component-error">
      <Icon type="ios-alert" size="48" color="#ed4014"></Icon>
      <p>组件加载失败</p>
      <p class="error-message">{{ errorMessage }}</p>
      <Button @click="retryLoad" type="primary" size="small">重试</Button>
    </div>

    <!-- 动态组件 -->
    <component
      v-else-if="currentComponent"
      :is="currentComponent"
      v-bind="componentProps"
      v-on="componentEvents"
      @component-ready="handleComponentReady"
      @component-error="handleComponentError"
      ref="dynamicComponentInstance"
    >
      <!-- 传递插槽内容 -->
      <template v-for="(slot, name) in componentSlots" :slot="name">
        <component
          v-if="slot.component"
          :is="slot.component"
          v-bind="slot.props"
          v-on="slot.events || {}"
          :key="name"
        />
        <div v-else-if="slot.content" v-html="slot.content" :key="name"></div>
      </template>

      <!-- 传递父组件的插槽 -->
      <template v-for="(slot, name) in $slots" :slot="name">
        <slot :name="name"></slot>
      </template>
    </component>

    <!-- 空状态 -->
    <div v-else class="component-empty">
      <Icon type="ios-cube-outline" size="48" color="#c5c8ce"></Icon>
      <p>暂无组件</p>
    </div>
  </div>
</template>

<script>
import { componentRegistry } from './registry/componentRegistry.js'

export default {
  name: 'DynamicComponent',
  props: {
    // 组件配置
    componentConfig: {
      type: [String, Object, Function],
      required: true
    },
    // 组件数据
    data: {
      type: Object,
      default: () => ({})
    },
    // 组件属性
    props: {
      type: Object,
      default: () => ({})
    },
    // 组件事件
    events: {
      type: Object,
      default: () => ({})
    },
    // 是否延迟加载
    lazy: {
      type: Boolean,
      default: false
    },
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentComponent: null,
      isLoading: false,
      hasError: false,
      errorMessage: '',
      componentInstance: null,
      retryCount: 0,
      maxRetries: 3
    }
  },
  computed: {
    // 组件样式类
    componentClass() {
      const classes = ['bsp-dynamic-component']

      if (this.customClass) {
        classes.push(this.customClass)
      }

      if (this.isLoading) {
        classes.push('is-loading')
      }

      if (this.hasError) {
        classes.push('has-error')
      }

      return classes.join(' ')
    },

    // 组件属性
    componentProps() {
      const config = this.normalizedConfig
      return {
        ...config.props,
        ...this.props,
        data: this.data,
        // 添加父子通信的属性
        $parent: this.$parent,
        $emit: this.handleChildEmit
      }
    },

    // 组件事件
    componentEvents() {
      const config = this.normalizedConfig
      const events = {
        ...config.events,
        ...this.events
      }

      // 添加数据更新事件监听
      if (!events['update:data']) {
        events['update:data'] = this.handleDataUpdate
      }

      // 添加属性更新事件监听
      if (!events['update:props']) {
        events['update:props'] = this.handlePropsUpdate
      }

      // 添加通用事件监听
      Object.keys(events).forEach(eventName => {
        if (typeof events[eventName] === 'string') {
          // 如果是字符串，转换为方法调用
          const methodName = events[eventName]
          events[eventName] = (...args) => {
            this.callParentMethod(methodName, ...args)
          }
        }
      })

      return events
    },

    // 组件插槽
    componentSlots() {
      const config = this.normalizedConfig
      return config.slots || {}
    },

    // 标准化的配置
    normalizedConfig() {
      if (typeof this.componentConfig === 'string') {
        return {
          name: this.componentConfig,
          props: {},
          events: {},
          slots: {}
        }
      }

      if (typeof this.componentConfig === 'object') {
        return {
          name: '',
          props: {},
          events: {},
          slots: {},
          ...this.componentConfig
        }
      }

      return {
        name: '',
        props: {},
        events: {},
        slots: {}
      }
    }
  },
  watch: {
    componentConfig: {
      handler: 'loadComponent',
      immediate: true
    }
  },
  methods: {
    // 加载组件
    async loadComponent() {
      if (this.lazy && !this.shouldLoad()) {
        return
      }

      this.isLoading = true
      this.hasError = false
      this.errorMessage = ''

      try {
        const config = this.normalizedConfig

        // 如果是函数配置，先执行获取实际配置
        if (typeof this.componentConfig === 'function') {
          const dynamicConfig = await this.componentConfig(this.data)
          config.name = dynamicConfig.name || config.name
          config.props = { ...config.props, ...dynamicConfig.props }
          config.events = { ...config.events, ...dynamicConfig.events }
          config.slots = { ...config.slots, ...dynamicConfig.slots }
        }

        // 从注册中心获取组件
        const component = await componentRegistry.getComponent(config.name)

        if (!component) {
          throw new Error(`组件 "${config.name}" 未找到`)
        }

        this.currentComponent = component
        this.retryCount = 0

        // 触发组件加载成功事件
        this.$emit('component-loaded', {
          name: config.name,
          component: this.currentComponent
        })

      } catch (error) {
        console.error('组件加载失败:', error)
        this.hasError = true
        this.errorMessage = error.message

        // 触发组件加载失败事件
        this.$emit('component-error', {
          name: this.normalizedConfig.name,
          error
        })
      } finally {
        this.isLoading = false
      }
    },

    // 判断是否应该加载组件
    shouldLoad() {
      // 可以在这里添加懒加载逻辑，比如检查组件是否在视口内
      return true
    },

    // 重试加载
    async retryLoad() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        await this.loadComponent()
      } else {
        this.$Message.error('组件加载重试次数已达上限')
      }
    },

    // 处理组件就绪事件
    handleComponentReady(instance) {
      this.componentInstance = instance
      this.$emit('component-ready', {
        name: this.normalizedConfig.name,
        instance
      })
    },

    // 处理组件错误事件
    handleComponentError(error) {
      this.hasError = true
      this.errorMessage = error.message || '组件运行时错误'
      this.$emit('component-error', {
        name: this.normalizedConfig.name,
        error
      })
    },

    // 获取组件实例
    getComponentInstance() {
      return this.componentInstance
    },

    // 调用组件方法
    callComponentMethod(methodName, ...args) {
      if (this.componentInstance && typeof this.componentInstance[methodName] === 'function') {
        return this.componentInstance[methodName](...args)
      }
      console.warn(`组件方法 "${methodName}" 不存在`)
    },

    // 更新组件数据
    updateComponentData(newData) {
      this.$emit('update:data', { ...this.data, ...newData })
    },

    // 处理子组件的事件发射
    handleChildEmit(eventName, ...args) {
      // 向父组件传递子组件的事件
      this.$emit('component-event', {
        componentName: this.normalizedConfig.name,
        eventName,
        args,
        timestamp: Date.now()
      })

      // 如果是特殊的更新事件，进行特殊处理
      if (eventName === 'update:data') {
        this.handleDataUpdate(args[0])
      } else if (eventName === 'update:props') {
        this.handlePropsUpdate(args[0])
      }
    },

    // 处理数据更新
    handleDataUpdate(newData) {
      const updatedData = { ...this.data, ...newData }
      this.$emit('update:data', updatedData)
    },

    // 处理属性更新
    handlePropsUpdate(newProps) {
      const updatedProps = { ...this.props, ...newProps }
      this.$emit('update:props', updatedProps)
    },

    // 调用父组件方法
    callParentMethod(methodName, ...args) {
      if (this.$parent && typeof this.$parent[methodName] === 'function') {
        return this.$parent[methodName](...args)
      } else {
        console.warn(`父组件方法 "${methodName}" 不存在`)
      }
    },

    // 获取子组件实例
    getChildComponentInstance() {
      return this.$refs.dynamicComponentInstance
    },

    // 调用子组件方法
    callChildMethod(methodName, ...args) {
      const childInstance = this.getChildComponentInstance()
      if (childInstance && typeof childInstance[methodName] === 'function') {
        return childInstance[methodName](...args)
      } else {
        console.warn(`子组件方法 "${methodName}" 不存在`)
      }
    },

    // 向子组件传递数据
    passDataToChild(data) {
      const childInstance = this.getChildComponentInstance()
      if (childInstance) {
        // 如果子组件有 updateData 方法，调用它
        if (typeof childInstance.updateData === 'function') {
          childInstance.updateData(data)
        }
        // 如果子组件有 data 属性，直接更新
        else if (childInstance.$data) {
          Object.assign(childInstance.$data, data)
        }
      }
    },

    // 从子组件获取数据
    getDataFromChild() {
      const childInstance = this.getChildComponentInstance()
      if (childInstance) {
        // 如果子组件有 getData 方法，调用它
        if (typeof childInstance.getData === 'function') {
          return childInstance.getData()
        }
        // 否则返回子组件的 data
        return childInstance.$data || {}
      }
      return {}
    }
  }
}
</script>

<style lang="less" scoped>
.bsp-dynamic-component {
  width: 100%;
  height: 100%;
  position: relative;

  .component-loading,
  .component-error,
  .component-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999;

    .ivu-icon {
      margin-bottom: 16px;
    }

    p {
      margin: 8px 0;
      font-size: 14px;
    }

    .error-message {
      color: #ed4014;
      font-size: 12px;
      max-width: 300px;
      text-align: center;
      word-break: break-word;
    }
  }

  .spin-icon-load {
    animation: ani-spin 1s linear infinite;
  }

  &.is-loading {
    pointer-events: none;
  }

  &.has-error {
    .component-error {
      border: 1px dashed #ed4014;
      border-radius: 4px;
      background: #fff2f0;
    }
  }
}

@keyframes ani-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
