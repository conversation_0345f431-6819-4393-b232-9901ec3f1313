<template>
  <div class="bsp-layout-renderer" :class="layoutClass">
    <!-- 管理页面布局 -->
    <template v-if="layoutType === 'management'">
      <div class="bsp-management-layout">
        <div class="bsp-management-layout-left">
          <slot name="left"></slot>
        </div>
        <div class="bsp-management-layout-right">
          <slot name="right"></slot>
        </div>
      </div>
    </template>

    <!-- 选择页面布局 -->
    <template v-else-if="layoutType === 'selection'">
      <div class="bsp-selection-layout">
        <div class="bsp-selection-layout-left">
          <slot name="left"></slot>
        </div>
        <div class="bsp-selection-layout-right">
          <slot name="right"></slot>
        </div>
      </div>
    </template>

    <!-- 详情页面布局 -->
    <template v-else-if="layoutType === 'detail'">
      <div class="bsp-detail-layout">
        <div class="bsp-detail-layout-header">
          <slot name="header"></slot>
        </div>
        <div class="bsp-detail-layout-content">
          <slot name="content"></slot>
        </div>
        <div class="bsp-detail-layout-footer">
          <slot name="footer"></slot>
        </div>
      </div>
    </template>

    <!-- 搜索页面布局 -->
    <template v-else-if="layoutType === 'search'">
      <div class="bsp-search-layout">
        <div class="bsp-search-layout-header">
          <slot name="header"></slot>
        </div>
        <div class="bsp-search-layout-content">
          <div class="bsp-search-layout-content-left">
            <slot name="left"></slot>
          </div>
          <div class="bsp-search-layout-content-right">
            <slot name="right"></slot>
          </div>
        </div>
      </div>
    </template>

    <!-- 工作台布局 -->
    <template v-else-if="layoutType === 'dashboard'">
      <div class="bsp-dashboard-layout">
        <div class="bsp-dashboard-layout-sidebar">
          <slot name="sidebar"></slot>
        </div>
        <div class="bsp-dashboard-layout-header">
          <slot name="header"></slot>
        </div>
        <div class="bsp-dashboard-layout-content">
          <slot name="content"></slot>
        </div>
      </div>
    </template>

    <!-- 卡片网格布局 -->
    <template v-else-if="layoutType === 'card-grid'">
      <div class="bsp-card-grid-layout">
        <div class="bsp-card-grid-layout-header">
          <div class="bsp-card-grid-layout-header-title">
            <slot name="title"></slot>
          </div>
          <div class="bsp-card-grid-layout-header-filters">
            <slot name="filters"></slot>
          </div>
        </div>
        <div class="bsp-card-grid-layout-content">
          <div class="bsp-card-grid-layout-content-grid">
            <slot name="grid"></slot>
          </div>
        </div>
      </div>
    </template>

    <!-- 分步表单布局 -->
    <template v-else-if="layoutType === 'wizard'">
      <div class="bsp-wizard-layout">
        <div class="bsp-wizard-layout-steps">
          <slot name="steps"></slot>
        </div>
        <div class="bsp-wizard-layout-content">
          <slot name="content"></slot>
        </div>
        <div class="bsp-wizard-layout-actions">
          <slot name="actions"></slot>
        </div>
      </div>
    </template>

    <!-- 主从表布局 -->
    <template v-else-if="layoutType === 'master-detail'">
      <div class="bsp-master-detail-layout">
        <div class="bsp-master-detail-layout-master">
          <slot name="master"></slot>
        </div>
        <div class="bsp-master-detail-layout-detail">
          <slot name="detail"></slot>
        </div>
      </div>
    </template>

    <!-- 聊天界面布局 -->
    <template v-else-if="layoutType === 'chat'">
      <div class="bsp-chat-layout">
        <div class="bsp-chat-layout-sidebar">
          <div class="bsp-chat-layout-sidebar-header">
            <slot name="sidebar-header"></slot>
          </div>
          <div class="bsp-chat-layout-sidebar-list">
            <slot name="sidebar-list"></slot>
          </div>
        </div>
        <div class="bsp-chat-layout-main">
          <div class="bsp-chat-layout-main-header">
            <slot name="main-header"></slot>
          </div>
          <div class="bsp-chat-layout-main-content">
            <slot name="main-content"></slot>
          </div>
          <div class="bsp-chat-layout-main-input">
            <slot name="main-input"></slot>
          </div>
        </div>
      </div>
    </template>

    <!-- 嵌套布局 -->
    <template v-else-if="layoutType === 'nested'">
      <div class="bsp-nested-layout">
        <div class="bsp-nested-layout-left">
          <div class="bsp-nested-layout-left-top">
            <slot name="left-top"></slot>
          </div>
          <div class="bsp-nested-layout-left-bottom">
            <slot name="left-bottom"></slot>
          </div>
        </div>
        <div class="bsp-nested-layout-right">
          <div class="bsp-nested-layout-right-header">
            <slot name="right-header"></slot>
          </div>
          <div class="bsp-nested-layout-right-content">
            <slot name="right-content"></slot>
          </div>
        </div>
      </div>
    </template>

    <!-- 复杂嵌套布局 -->
    <template v-else-if="layoutType === 'complex-nested'">
      <div class="bsp-complex-nested-layout">
        <div class="bsp-complex-nested-layout-left">
          <div class="bsp-complex-nested-layout-left-search">
            <slot name="left-search"></slot>
          </div>
          <div class="bsp-complex-nested-layout-left-tree">
            <slot name="left-tree"></slot>
          </div>
        </div>
        <div class="bsp-complex-nested-layout-right">
          <div class="bsp-complex-nested-layout-right-toolbar">
            <slot name="right-toolbar"></slot>
          </div>
          <div class="bsp-complex-nested-layout-right-main">
            <slot name="right-main"></slot>
          </div>
          <div class="bsp-complex-nested-layout-right-detail">
            <slot name="right-detail"></slot>
          </div>
        </div>
      </div>
    </template>

    <!-- 详情卡片布局 -->
    <template v-else-if="layoutType === 'detail-card'">
      <DetailCardLayout
        :left-config="config.leftConfig || {}"
        :right-cards="config.rightCards || []"
        :bottom-actions="config.bottomActions || []"
        :show-bottom-actions="config.showBottomActions !== false"
        :responsive="config.responsive !== false"
        :custom-class="config.customClass"
        :layout-data="layoutData"
        @component-event="handleComponentEvent"
        @card-action="handleCardAction"
        @bottom-action="handleBottomAction"
      >
        <!-- 传递所有插槽 -->
        <template v-for="(slot, name) in $slots" :slot="name">
          <slot :name="name"></slot>
        </template>
      </DetailCardLayout>
    </template>

    <!-- 自定义布局 -->
    <template v-else-if="layoutType === 'custom'">
      <div class="bsp-custom-layout" :style="customLayoutStyle">
        <slot></slot>
      </div>
    </template>

    <!-- 默认布局 -->
    <template v-else>
      <div class="bsp-default-layout">
        <slot></slot>
      </div>
    </template>
  </div>
</template>

<script>
import DetailCardLayout from './layouts/DetailCardLayout.vue'

export default {
  name: 'LayoutRenderer',
  components: {
    DetailCardLayout
  },
  props: {
    // 布局类型
    layoutType: {
      type: String,
      default: 'default'
    },
    // 布局数据
    layoutData: {
      type: Object,
      default: () => ({})
    },
    // 自定义样式
    customStyle: {
      type: Object,
      default: () => ({})
    },
    // 布局配置
    config: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    // 布局样式类
    layoutClass() {
      const classes = [`layout-type-${this.layoutType}`]

      if (this.config.className) {
        classes.push(this.config.className)
      }

      return classes.join(' ')
    },

    // 自定义布局样式
    customLayoutStyle() {
      return {
        ...this.customStyle,
        ...this.config.style
      }
    }
  },
  methods: {
    // 处理组件事件
    handleComponentEvent(event) {
      this.$emit('component-event', event)
    },

    // 处理卡片操作事件
    handleCardAction(event) {
      this.$emit('card-action', event)
    },

    // 处理底部操作事件
    handleBottomAction(event) {
      this.$emit('bottom-action', event)
    }
  },
  mounted() {
    // 触发布局渲染完成事件
    this.$emit('layout-rendered', {
      layoutType: this.layoutType,
      element: this.$el
    })
  }
}
</script>

<style lang="less" scoped>
.bsp-layout-renderer {
  width: 100%;
  height: 100%;

  // 布局样式已经全局引入，这里只需要组件特定的样式
  &.layout-type-management {
    // 管理布局特定样式
  }

  &.layout-type-detail {
    // 详情布局特定样式
  }

  &.layout-type-card-grid {
    // 卡片网格布局特定样式
  }

  &.layout-type-search {
    // 搜索布局特定样式
  }

  &.layout-type-dashboard {
    // 工作台布局特定样式
  }

  &.layout-type-wizard {
    // 分步表单布局特定样式
  }

  &.layout-type-master-detail {
    // 主从表布局特定样式
  }

  &.layout-type-chat {
    // 聊天布局特定样式
  }

  &.layout-type-nested {
    // 嵌套布局特定样式
  }

  &.layout-type-complex-nested {
    // 复杂嵌套布局特定样式
  }

  &.layout-type-custom {
    // 自定义布局特定样式
  }
}
</style>
