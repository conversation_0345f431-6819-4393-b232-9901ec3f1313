/**
 * BSP 动态布局组件库入口文件
 * 提供动态布局和动态组件的完整解决方案
 *
 * @version 1.0.0
 * <AUTHOR> Team
 * @description 基于 Vue.js 的动态布局和动态组件解决方案
 */

import DynamicLayout from './DynamicLayout.vue'
import DynamicComponent from './DynamicComponent.vue'
import LayoutRenderer from './LayoutRenderer.vue'
import DetailCardLayout from './layouts/DetailCardLayout.vue'

// 简化组件
import SimpleDetailCardLayout from './layouts/SimpleDetailCardLayout.vue'
import EasyLayout from './layouts/EasyLayout.vue'
import FormLayout from './layouts/FormLayout.vue'
import EasyCard from './components/EasyCard.vue'
import { layoutConfigs, getLayoutConfig, getLayoutTypes, isValidLayoutType } from './configs/layoutConfigs.js'
import { componentRegistry } from './registry/componentRegistry.js'

// 导出组件
export {
  DynamicLayout,
  DynamicComponent,
  LayoutRenderer,
  DetailCardLayout,
  // 简化组件
  SimpleDetailCardLayout,
  EasyLayout,
  FormLayout,
  EasyCard
}

// 导出配置和工具
export {
  layoutConfigs,
  getLayoutConfig,
  getLayoutTypes,
  isValidLayoutType,
  componentRegistry
}

// Vue 插件安装函数
export function install(Vue, options = {}) {
  // 检查是否已经安装
  if (install.installed) {
    return
  }
  install.installed = true

  // 注册全局组件
  Vue.component('DynamicLayout', DynamicLayout)
  Vue.component('DynamicComponent', DynamicComponent)
  Vue.component('LayoutRenderer', LayoutRenderer)
  Vue.component('DetailCardLayout', DetailCardLayout)

  // 注册简化组件
  Vue.component('SimpleDetailCardLayout', SimpleDetailCardLayout)
  Vue.component('EasyLayout', EasyLayout)
  Vue.component('FormLayout', FormLayout)
  Vue.component('EasyCard', EasyCard)

  // 注册全局属性
  Vue.prototype.$layoutConfigs = layoutConfigs
  Vue.prototype.$componentRegistry = componentRegistry

  // 全局混入布局工具方法
  Vue.mixin({
    methods: {
      // 获取布局配置
      $getLayoutConfig(layoutType) {
        return getLayoutConfig(layoutType)
      },

      // 验证布局类型
      $isValidLayoutType(layoutType) {
        return isValidLayoutType(layoutType)
      },

      // 获取所有布局类型
      $getLayoutTypes() {
        return getLayoutTypes()
      },

      // 注册组件到注册中心
      $registerComponent(name, component, config) {
        componentRegistry.register(name, component, config)
      },

      // 获取组件
      async $getComponent(name) {
        return await componentRegistry.getComponent(name)
      },

      // 预加载组件
      async $preloadComponents(names) {
        return await componentRegistry.preloadComponents(names)
      }
    }
  })

  // 处理插件选项
  if (options.components) {
    // 批量注册组件
    componentRegistry.registerBatch(options.components)
  }

  if (options.preload) {
    // 预加载指定组件
    Vue.nextTick(() => {
      componentRegistry.preloadComponents(options.preload)
    })
  }
}

// 默认导出插件对象
export default {
  install,
  DynamicLayout,
  DynamicComponent,
  LayoutRenderer,
  DetailCardLayout,
  SimpleDetailCardLayout,
  EasyLayout,
  FormLayout,
  EasyCard,
  layoutConfigs,
  componentRegistry
}

// 布局工具类
export class LayoutUtils {
  /**
   * 创建布局配置
   * @param {string} layoutType - 布局类型
   * @param {Object} customConfig - 自定义配置
   * @returns {Object} 完整的布局配置
   */
  static createLayoutConfig(layoutType, customConfig = {}) {
    const baseConfig = getLayoutConfig(layoutType)
    return {
      ...baseConfig,
      ...customConfig,
      slots: [
        ...(baseConfig.slots || []),
        ...(customConfig.slots || [])
      ]
    }
  }

  /**
   * 验证布局配置
   * @param {Object} config - 布局配置
   * @returns {Object} 验证结果
   */
  static validateLayoutConfig(config) {
    const errors = []
    const warnings = []

    if (!config.layoutType) {
      errors.push('缺少 layoutType 配置')
    } else if (!isValidLayoutType(config.layoutType)) {
      errors.push(`无效的布局类型: ${config.layoutType}`)
    }

    if (config.slots && !Array.isArray(config.slots)) {
      errors.push('slots 配置必须是数组')
    }

    if (config.responsive !== undefined && typeof config.responsive !== 'boolean') {
      warnings.push('responsive 配置应该是布尔值')
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 生成布局样式类
   * @param {string} layoutType - 布局类型
   * @param {Object} options - 选项
   * @returns {string} 样式类字符串
   */
  static generateLayoutClass(layoutType, options = {}) {
    const classes = [`bsp-${layoutType}-layout`]

    if (options.responsive) {
      classes.push('bsp-layout-responsive')
    }

    if (options.customClass) {
      classes.push(options.customClass)
    }

    if (options.size) {
      classes.push(`bsp-layout-${options.size}`)
    }

    return classes.join(' ')
  }

  /**
   * 解析组件配置
   * @param {string|Object|Function} componentConfig - 组件配置
   * @returns {Object} 标准化的组件配置
   */
  static parseComponentConfig(componentConfig) {
    if (typeof componentConfig === 'string') {
      return {
        name: componentConfig,
        props: {},
        events: {},
        slots: {}
      }
    }

    if (typeof componentConfig === 'function') {
      return {
        name: 'dynamic',
        loader: componentConfig,
        props: {},
        events: {},
        slots: {}
      }
    }

    if (typeof componentConfig === 'object') {
      return {
        name: '',
        props: {},
        events: {},
        slots: {},
        ...componentConfig
      }
    }

    throw new Error('无效的组件配置格式')
  }
}

// 布局事件总线
export class LayoutEventBus {
  constructor() {
    this.events = new Map()
  }

  // 监听事件
  on(event, callback) {
    if (!this.events.has(event)) {
      this.events.set(event, [])
    }
    this.events.get(event).push(callback)
  }

  // 移除事件监听
  off(event, callback) {
    if (this.events.has(event)) {
      const callbacks = this.events.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  // 触发事件
  emit(event, ...args) {
    if (this.events.has(event)) {
      this.events.get(event).forEach(callback => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`布局事件处理错误 [${event}]:`, error)
        }
      })
    }
  }

  // 清空所有事件
  clear() {
    this.events.clear()
  }
}

// 创建全局事件总线实例
export const layoutEventBus = new LayoutEventBus()
