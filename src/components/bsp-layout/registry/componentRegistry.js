/**
 * 组件注册中心
 * 管理动态组件的注册、加载和缓存
 */

class ComponentRegistry {
  constructor() {
    // 组件缓存
    this.componentCache = new Map()
    // 组件配置
    this.componentConfigs = new Map()
    // 加载中的组件
    this.loadingComponents = new Map()
    // 组件别名
    this.componentAliases = new Map()
  }

  /**
   * 注册组件
   * @param {string} name - 组件名称
   * @param {Object|Function} component - 组件定义或加载函数
   * @param {Object} config - 组件配置
   */
  register(name, component, config = {}) {
    // 如果是函数，说明是异步加载组件
    if (typeof component === 'function') {
      this.componentConfigs.set(name, {
        loader: component,
        ...config
      })
    } else {
      // 直接缓存组件
      this.componentCache.set(name, component)
      this.componentConfigs.set(name, config)
    }

    // 注册别名
    if (config.alias) {
      if (Array.isArray(config.alias)) {
        config.alias.forEach(alias => {
          this.componentAliases.set(alias, name)
        })
      } else {
        this.componentAliases.set(config.alias, name)
      }
    }
  }

  /**
   * 批量注册组件
   * @param {Object} components - 组件映射对象
   */
  registerBatch(components) {
    Object.entries(components).forEach(([name, component]) => {
      if (typeof component === 'object' && component.component) {
        this.register(name, component.component, component.config || {})
      } else {
        this.register(name, component)
      }
    })
  }

  /**
   * 获取组件
   * @param {string} name - 组件名称或别名
   * @returns {Promise<Object>} 组件定义
   */
  async getComponent(name) {
    // 解析别名
    const realName = this.componentAliases.get(name) || name

    // 检查缓存
    if (this.componentCache.has(realName)) {
      return this.componentCache.get(realName)
    }

    // 检查是否正在加载
    if (this.loadingComponents.has(realName)) {
      return this.loadingComponents.get(realName)
    }

    // 获取组件配置
    const config = this.componentConfigs.get(realName)
    if (!config) {
      throw new Error(`组件 "${name}" 未注册`)
    }

    // 如果有加载器，异步加载组件
    if (config.loader) {
      const loadPromise = this.loadComponent(realName, config)
      this.loadingComponents.set(realName, loadPromise)

      try {
        const component = await loadPromise
        this.loadingComponents.delete(realName)
        return component
      } catch (error) {
        this.loadingComponents.delete(realName)
        throw error
      }
    }

    throw new Error(`组件 "${name}" 配置错误`)
  }

  /**
   * 加载组件
   * @param {string} name - 组件名称
   * @param {Object} config - 组件配置
   * @returns {Promise<Object>} 组件定义
   */
  async loadComponent(name, config) {
    try {
      let component

      if (typeof config.loader === 'function') {
        // 执行加载函数
        const result = await config.loader()

        // 处理不同的返回格式
        if (result && result.default) {
          component = result.default
        } else if (result && typeof result === 'object') {
          component = result
        } else {
          throw new Error(`组件 "${name}" 加载结果格式错误`)
        }
      } else {
        throw new Error(`组件 "${name}" 加载器配置错误`)
      }

      // 缓存组件
      this.componentCache.set(name, component)

      return component
    } catch (error) {
      console.error(`组件 "${name}" 加载失败:`, error)
      throw new Error(`组件 "${name}" 加载失败: ${error.message}`)
    }
  }

  /**
   * 检查组件是否已注册
   * @param {string} name - 组件名称或别名
   * @returns {boolean}
   */
  hasComponent(name) {
    const realName = this.componentAliases.get(name) || name
    return this.componentConfigs.has(realName)
  }

  /**
   * 获取组件配置
   * @param {string} name - 组件名称或别名
   * @returns {Object|null}
   */
  getComponentConfig(name) {
    const realName = this.componentAliases.get(name) || name
    return this.componentConfigs.get(realName) || null
  }

  /**
   * 预加载组件
   * @param {string|Array} names - 组件名称或名称数组
   */
  async preloadComponents(names) {
    const componentNames = Array.isArray(names) ? names : [names]

    const loadPromises = componentNames.map(async (name) => {
      try {
        await this.getComponent(name)
        console.log(`组件 "${name}" 预加载成功`)
      } catch (error) {
        console.warn(`组件 "${name}" 预加载失败:`, error)
      }
    })

    await Promise.all(loadPromises)
  }

  /**
   * 卸载组件
   * @param {string} name - 组件名称
   */
  unregister(name) {
    const realName = this.componentAliases.get(name) || name

    // 清除缓存
    this.componentCache.delete(realName)
    this.componentConfigs.delete(realName)
    this.loadingComponents.delete(realName)

    // 清除别名
    for (const [alias, componentName] of this.componentAliases.entries()) {
      if (componentName === realName) {
        this.componentAliases.delete(alias)
      }
    }
  }

  /**
   * 清空所有组件
   */
  clear() {
    this.componentCache.clear()
    this.componentConfigs.clear()
    this.loadingComponents.clear()
    this.componentAliases.clear()
  }

  /**
   * 获取所有已注册的组件名称
   * @returns {Array<string>}
   */
  getRegisteredComponents() {
    return Array.from(this.componentConfigs.keys())
  }

  /**
   * 获取缓存统计信息
   * @returns {Object}
   */
  getCacheStats() {
    return {
      registered: this.componentConfigs.size,
      cached: this.componentCache.size,
      loading: this.loadingComponents.size,
      aliases: this.componentAliases.size
    }
  }
}

// 创建全局实例
export const componentRegistry = new ComponentRegistry()

// 注册中心已创建，可以在需要时动态注册组件
// 示例：componentRegistry.register('component-name', () => import('./Component.vue'))

export default componentRegistry
