<template>
  <div class="parent-child-communication-example">
    <h2>父子组件通信示例</h2>
    <p>展示BSP动态布局组件中的父传子、子传父数据和事件传递</p>
    
    <!-- 控制面板 -->
    <Card style="margin-bottom: 20px;">
      <p slot="title">
        <Icon type="ios-settings" />
        父组件控制面板
      </p>
      
      <Row :gutter="16">
        <Col span="8">
          <h4>左侧数据控制</h4>
          <Form :label-width="80">
            <FormItem label="姓名">
              <Input v-model="leftData.name" @on-change="updateLeftData" />
            </FormItem>
            <FormItem label="年龄">
              <InputNumber v-model="leftData.age" @on-change="updateLeftData" />
            </FormItem>
            <FormItem label="状态">
              <Select v-model="leftData.status" @on-change="updateLeftData">
                <Option value="active">活跃</Option>
                <Option value="inactive">非活跃</Option>
              </Select>
            </FormItem>
          </Form>
        </Col>
        
        <Col span="8">
          <h4>卡片数据控制</h4>
          <Form :label-width="80">
            <FormItem label="卡片索引">
              <InputNumber v-model="selectedCardIndex" :min="0" :max="rightCards.length - 1" />
            </FormItem>
            <FormItem label="标题">
              <Input v-model="cardUpdateData.title" />
            </FormItem>
            <FormItem label="内容">
              <Input v-model="cardUpdateData.content" type="textarea" />
            </FormItem>
            <FormItem>
              <Button @click="updateSelectedCard" type="primary">更新卡片</Button>
            </FormItem>
          </Form>
        </Col>
        
        <Col span="8">
          <h4>事件日志</h4>
          <div class="event-log" style="height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 8px;">
            <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-type" :class="log.type">{{ log.type }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
          <Button @click="clearLogs" size="small" style="margin-top: 8px;">清空日志</Button>
        </Col>
      </Row>
    </Card>
    
    <!-- 动态布局展示 -->
    <DetailCardLayout
      ref="detailLayout"
      :left-config="leftConfig"
      :right-cards="rightCards"
      :bottom-actions="bottomActions"
      :layout-data="layoutData"
      @component-event="handleComponentEvent"
      @card-action="handleCardAction"
      @bottom-action="handleBottomAction"
      @data-updated="handleDataUpdated"
      @update:left-config="handleLeftConfigUpdate"
      @update:right-cards="handleRightCardsUpdate"
    >
      <!-- 左侧插槽 - 展示数据绑定 -->
      <template #left="{ data, updateData }">
        <div class="left-content">
          <div class="data-display">
            <h4>当前数据：</h4>
            <p><strong>姓名：</strong>{{ data.name || '未设置' }}</p>
            <p><strong>年龄：</strong>{{ data.age || '未设置' }}</p>
            <p><strong>状态：</strong>
              <Tag :color="data.status === 'active' ? 'success' : 'default'">
                {{ data.status === 'active' ? '活跃' : '非活跃' }}
              </Tag>
            </p>
          </div>
          
          <Divider />
          
          <div class="child-controls">
            <h4>子组件操作：</h4>
            <Button @click="() => updateData({ lastUpdated: new Date().toLocaleTimeString() })" type="primary" size="small">
              更新时间戳
            </Button>
            <Button @click="() => sendEventToParent('left-button-clicked')" size="small" style="margin-left: 8px;">
              发送事件到父组件
            </Button>
          </div>
          
          <div v-if="data.lastUpdated" style="margin-top: 16px;">
            <Alert>
              <span slot="desc">最后更新时间：{{ data.lastUpdated }}</span>
            </Alert>
          </div>
        </div>
      </template>
      
      <!-- 基本信息卡片 -->
      <template #basic-info="{ card, data, updateData }">
        <div class="card-content">
          <h4>{{ card.title }}</h4>
          <p>{{ data.content || '暂无内容' }}</p>
          
          <div class="card-actions" style="margin-top: 16px;">
            <Button @click="() => updateData({ content: '内容已从子组件更新 - ' + new Date().toLocaleTimeString() })" type="primary" size="small">
              子组件更新内容
            </Button>
            <Button @click="() => sendCardEvent('basic-info-action', { action: 'refresh' })" size="small" style="margin-left: 8px;">
              发送卡片事件
            </Button>
          </div>
          
          <div style="margin-top: 16px;">
            <h5>接收到的属性：</h5>
            <pre style="background: #f5f5f5; padding: 8px; font-size: 12px;">{{ JSON.stringify(card, null, 2) }}</pre>
          </div>
        </div>
      </template>
      
      <!-- 交互测试卡片 -->
      <template #interaction-test="{ card, index, data, updateData, updateCard }">
        <div class="interaction-card">
          <h4>交互测试卡片</h4>
          
          <Form :label-width="80">
            <FormItem label="计数器">
              <InputNumber 
                :value="data.counter || 0" 
                @on-change="(val) => updateData({ counter: val })"
              />
            </FormItem>
            
            <FormItem label="文本">
              <Input 
                :value="data.text || ''" 
                @on-change="(e) => updateData({ text: e.target.value })"
                placeholder="输入文本"
              />
            </FormItem>
            
            <FormItem>
              <ButtonGroup>
                <Button @click="() => updateData({ counter: (data.counter || 0) + 1 })">
                  增加计数
                </Button>
                <Button @click="() => updateCard({ title: '标题已更新 - ' + new Date().toLocaleTimeString() })">
                  更新卡片标题
                </Button>
                <Button @click="() => sendCardEvent('interaction-test', { type: 'custom', data })">
                  发送自定义事件
                </Button>
              </ButtonGroup>
            </FormItem>
          </Form>
          
          <Alert v-if="data.counter > 5" type="warning">
            <span slot="desc">计数器已超过5，这是一个条件性显示的提示</span>
          </Alert>
        </div>
      </template>
    </DetailCardLayout>
    
    <!-- 数据状态展示 -->
    <Card style="margin-top: 20px;">
      <p slot="title">
        <Icon type="ios-analytics" />
        当前数据状态
      </p>
      
      <Row :gutter="16">
        <Col span="12">
          <h4>左侧配置数据：</h4>
          <pre class="data-display">{{ JSON.stringify(leftConfig.data, null, 2) }}</pre>
        </Col>
        <Col span="12">
          <h4>卡片数据：</h4>
          <pre class="data-display">{{ JSON.stringify(rightCards.map(card => ({ name: card.name, data: card.data })), null, 2) }}</pre>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<script>
export default {
  name: 'ParentChildCommunicationExample',
  data() {
    return {
      // 左侧数据
      leftData: {
        name: '张三',
        age: 35,
        status: 'active'
      },
      
      // 卡片更新数据
      cardUpdateData: {
        title: '',
        content: ''
      },
      
      // 选中的卡片索引
      selectedCardIndex: 0,
      
      // 左侧配置
      leftConfig: {
        width: '400px',
        title: '人员信息',
        icon: 'ios-contact',
        showHeader: true,
        data: {
          name: '张三',
          age: 35,
          status: 'active'
        }
      },
      
      // 右侧卡片
      rightCards: [
        {
          name: 'basic-info',
          title: '基本信息卡片',
          icon: 'ios-information-circle',
          slot: 'basic-info',
          data: {
            content: '这是基本信息内容'
          }
        },
        {
          name: 'interaction-test',
          title: '交互测试卡片',
          icon: 'ios-flask',
          slot: 'interaction-test',
          data: {
            counter: 0,
            text: ''
          }
        }
      ],
      
      // 底部操作
      bottomActions: [
        { name: 'reset', label: '重置数据', icon: 'ios-refresh' },
        { name: 'save', label: '保存', type: 'primary', icon: 'ios-checkmark' }
      ],
      
      // 布局数据
      layoutData: {
        title: '父子通信示例'
      },
      
      // 事件日志
      eventLogs: []
    }
  },
  methods: {
    // 更新左侧数据
    updateLeftData() {
      this.leftConfig = {
        ...this.leftConfig,
        data: { ...this.leftData }
      }
      this.addLog('info', `左侧数据已更新: ${JSON.stringify(this.leftData)}`)
    },
    
    // 更新选中的卡片
    updateSelectedCard() {
      if (this.selectedCardIndex >= 0 && this.selectedCardIndex < this.rightCards.length) {
        this.$refs.detailLayout.updateCard(this.selectedCardIndex, {
          title: this.cardUpdateData.title || this.rightCards[this.selectedCardIndex].title,
          data: {
            ...this.rightCards[this.selectedCardIndex].data,
            content: this.cardUpdateData.content
          }
        })
        this.addLog('info', `卡片 ${this.selectedCardIndex} 已更新`)
      }
    },
    
    // 处理组件事件
    handleComponentEvent(event) {
      this.addLog('component', `组件事件: ${JSON.stringify(event)}`)
    },
    
    // 处理卡片操作
    handleCardAction(event) {
      this.addLog('card', `卡片操作: ${event.action.name} - ${event.card.name}`)
    },
    
    // 处理底部操作
    handleBottomAction(event) {
      if (event.action.name === 'reset') {
        this.resetAllData()
      } else if (event.action.name === 'save') {
        this.saveAllData()
      }
      this.addLog('bottom', `底部操作: ${event.action.name}`)
    },
    
    // 处理数据更新
    handleDataUpdated(event) {
      this.addLog('data', `数据更新: ${event.section} - ${JSON.stringify(event.data)}`)
    },
    
    // 处理左侧配置更新
    handleLeftConfigUpdate(config) {
      this.leftConfig = config
      this.addLog('config', '左侧配置已更新')
    },
    
    // 处理右侧卡片更新
    handleRightCardsUpdate(cards) {
      this.rightCards = cards
      this.addLog('config', '右侧卡片配置已更新')
    },
    
    // 发送事件到父组件
    sendEventToParent(eventType) {
      this.addLog('child', `子组件发送事件: ${eventType}`)
      this.$Message.info(`收到子组件事件: ${eventType}`)
    },
    
    // 发送卡片事件
    sendCardEvent(cardName, eventData) {
      this.addLog('child', `卡片 ${cardName} 发送事件: ${JSON.stringify(eventData)}`)
      this.$Message.info(`收到卡片事件: ${cardName}`)
    },
    
    // 重置所有数据
    resetAllData() {
      this.$refs.detailLayout.resetAllData()
      this.leftData = { name: '', age: 0, status: 'inactive' }
      this.updateLeftData()
      this.addLog('action', '所有数据已重置')
    },
    
    // 保存所有数据
    saveAllData() {
      const allData = this.$refs.detailLayout.getAllData()
      console.log('保存的数据:', allData)
      this.addLog('action', `数据已保存: ${JSON.stringify(allData)}`)
      this.$Message.success('数据保存成功')
    },
    
    // 添加日志
    addLog(type, message) {
      this.eventLogs.unshift({
        type,
        message,
        time: new Date().toLocaleTimeString()
      })
      
      if (this.eventLogs.length > 50) {
        this.eventLogs = this.eventLogs.slice(0, 50)
      }
    },
    
    // 清空日志
    clearLogs() {
      this.eventLogs = []
    }
  },
  mounted() {
    this.addLog('info', '父子通信示例页面加载完成')
  }
}
</script>

<style lang="less" scoped>
.parent-child-communication-example {
  padding: 20px;
  
  h2, h4 {
    color: #333;
    margin-bottom: 16px;
  }
  
  .left-content {
    .data-display {
      background: #f8fafc;
      padding: 16px;
      border-radius: 8px;
      margin-bottom: 16px;
      
      h4 {
        margin: 0 0 12px 0;
        color: #2c3e50;
      }
      
      p {
        margin: 8px 0;
        font-size: 14px;
      }
    }
    
    .child-controls {
      h4 {
        margin: 0 0 12px 0;
        color: #2c3e50;
      }
    }
  }
  
  .card-content, .interaction-card {
    h4, h5 {
      margin: 0 0 12px 0;
      color: #2c3e50;
    }
    
    pre {
      max-height: 150px;
      overflow-y: auto;
    }
  }
  
  .event-log {
    .log-item {
      display: flex;
      gap: 8px;
      padding: 4px 0;
      border-bottom: 1px solid #f0f0f0;
      font-size: 12px;
      
      .log-time {
        color: #999;
        min-width: 80px;
      }
      
      .log-type {
        min-width: 80px;
        font-weight: 500;
        
        &.info { color: #1890ff; }
        &.component { color: #52c41a; }
        &.card { color: #faad14; }
        &.bottom { color: #722ed1; }
        &.data { color: #eb2f96; }
        &.config { color: #13c2c2; }
        &.child { color: #f5222d; }
        &.action { color: #fa541c; }
      }
      
      .log-message {
        flex: 1;
        word-break: break-all;
      }
    }
  }
  
  .data-display {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
  }
}
</style>
