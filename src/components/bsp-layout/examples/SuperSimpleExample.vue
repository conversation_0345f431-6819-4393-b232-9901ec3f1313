<template>
  <div class="super-simple-example">
    <!-- 超简单用法 - 只需要两个插槽 + 底部按钮 -->
    <EasyLayout
      left-width="350px"
      :actions="bottomActions"
      @action="handleAction"
    >
      <!-- 左侧：人员信息 -->
      <template #left>
        <div style="padding: 20px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <Avatar size="large" icon="ios-person" />
            <h3 style="margin: 12px 0;">{{ person.name }}</h3>
            <Tag color="success">{{ person.status }}</Tag>
          </div>

          <div>
            <p><strong>编号：</strong>{{ person.code }}</p>
            <p><strong>年龄：</strong>{{ person.age }}岁</p>
            <p><strong>监区：</strong>{{ person.area }}</p>
            <p><strong>入所时间：</strong>{{ person.entryDate }}</p>
          </div>
        </div>
      </template>

      <!-- 右侧：卡片内容 -->
      <template #right>
        <div style="padding: 16px;">
          <!-- 基本信息卡片 -->
          <EasyCard title="基本信息" icon="ios-information-circle">
            <template #actions>
              <Button size="small">编辑</Button>
            </template>

            <Table :columns="columns" :data="tableData" size="small" />
          </EasyCard>

          <!-- 操作功能卡片 -->
          <EasyCard title="操作功能" icon="ios-options">
            <Row :gutter="16">
              <Col span="12">
                <div class="action-btn" @click="viewVideo">
                  <Icon type="ios-videocam" size="32" color="#5b8ff9" />
                  <p>查看录像</p>
                </div>
              </Col>
              <Col span="12">
                <div class="action-btn" @click="viewRecord">
                  <Icon type="ios-document" size="32" color="#52c41a" />
                  <p>查看笔录</p>
                </div>
              </Col>
            </Row>
          </EasyCard>

          <!-- 评估结论卡片 -->
          <EasyCard title="评估结论" icon="ios-analytics">
            <Form :label-width="80">
              <FormItem label="心理评估">
                <Input v-model="assessment.psychology" type="textarea" :rows="3" />
              </FormItem>
              <FormItem label="协作建议">
                <Input v-model="assessment.collaboration" type="textarea" :rows="3" />
              </FormItem>
              <FormItem>
                <Button type="primary" @click="save">保存</Button>
                <Button @click="reset" style="margin-left: 8px;">重置</Button>
              </FormItem>
            </Form>
          </EasyCard>
        </div>
      </template>
    </EasyLayout>
  </div>
</template>

<script>
export default {
  name: 'SuperSimpleExample',
  data() {
    return {
      // 人员信息
      person: {
        name: '张三',
        code: 'P001',
        age: 35,
        area: '一监区',
        entryDate: '2023-01-15',
        status: '在押'
      },

      // 表格配置
      columns: [
        { title: '项目', key: 'label' },
        { title: '内容', key: 'value' }
      ],
      tableData: [
        { label: '谈话时间', value: '2024-01-15 09:00' },
        { label: '谈话民警', value: '李警官' },
        { label: '谈话类型', value: '日常谈话' },
        { label: '谈话状态', value: '已完成' }
      ],

      // 评估数据
      assessment: {
        psychology: '',
        collaboration: ''
      },

      // 底部按钮配置
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          icon: 'ios-arrow-back',
          type: 'default'
        },
        {
          name: 'cancel',
          label: '取消',
          icon: 'ios-close',
          type: 'default'
        },
        {
          name: 'approve',
          label: '审批',
          icon: 'ios-checkmark',
          type: 'primary'
        }
      ]
    }
  },

  methods: {
    viewVideo() {
      this.$Message.info('查看录像')
    },

    viewRecord() {
      this.$Message.info('查看笔录')
    },

    save() {
      this.$Message.success('保存成功')
    },

    reset() {
      this.assessment = { psychology: '', collaboration: '' }
      this.$Message.info('已重置')
    },

    // 处理底部按钮点击
    handleAction(action) {
      switch (action.name) {
        case 'back':
          this.$Message.info('返回上一页')
          break
        case 'cancel':
          this.$Message.info('取消操作')
          break
        case 'approve':
          if (!this.assessment.psychology && !this.assessment.collaboration) {
            this.$Message.warning('请先填写评估内容')
            return
          }
          this.$Message.success('审批完成')
          break
        default:
          this.$Message.info(`执行操作: ${action.name}`)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.action-btn {
  text-align: center;
  padding: 20px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #5b8ff9;
    box-shadow: 0 4px 12px rgba(91, 143, 249, 0.15);
  }

  p {
    margin: 8px 0 0;
    font-size: 14px;
    color: #2c3e50;
  }
}
</style>
