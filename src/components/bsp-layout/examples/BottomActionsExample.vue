<template>
  <div class="bottom-actions-example">
    <!-- 控制面板 -->
    <div class="control-panel">
      <h2>底部按钮统一演示</h2>
      <p>三种布局组件都支持底部操作按钮</p>
      
      <div class="layout-selector">
        <Button 
          :type="currentLayout === 'easy' ? 'primary' : 'default'"
          @click="switchLayout('easy')"
        >
          EasyLayout
        </Button>
        <Button 
          :type="currentLayout === 'simple' ? 'primary' : 'default'"
          @click="switchLayout('simple')"
        >
          SimpleDetailCardLayout
        </Button>
        <Button 
          :type="currentLayout === 'detail' ? 'primary' : 'default'"
          @click="switchLayout('detail')"
        >
          DetailCardLayout
        </Button>
      </div>
      
      <div class="actions-config">
        <h4>当前按钮配置：</h4>
        <div class="config-display">
          <pre>{{ JSON.stringify(currentActions, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- EasyLayout 演示 -->
    <div v-if="currentLayout === 'easy'" class="layout-demo">
      <EasyLayout 
        left-width="300px"
        :actions="currentActions"
        @action="handleAction"
      >
        <template #left>
          <div class="demo-content">
            <h3>EasyLayout 左侧</h3>
            <p>最简单的布局组件</p>
            <p>支持底部按钮配置</p>
          </div>
        </template>
        
        <template #right>
          <div class="demo-content">
            <EasyCard title="演示卡片">
              <p>这是右侧内容区域</p>
              <p>底部有统一的操作按钮</p>
            </EasyCard>
          </div>
        </template>
        
        <!-- 自定义按钮插槽演示 -->
        <template #actions>
          <Button @click="handleCustomAction('custom1')" type="dashed">
            自定义按钮1
          </Button>
          <Button @click="handleCustomAction('custom2')" type="dashed">
            自定义按钮2
          </Button>
          <Button @click="handleCustomAction('back')" icon="ios-arrow-back">
            返回
          </Button>
          <Button @click="handleCustomAction('cancel')">
            取消
          </Button>
          <Button @click="handleCustomAction('approve')" type="primary" icon="ios-checkmark">
            审批
          </Button>
        </template>
      </EasyLayout>
    </div>

    <!-- SimpleDetailCardLayout 演示 -->
    <div v-if="currentLayout === 'simple'" class="layout-demo">
      <SimpleDetailCardLayout
        left-title="审批信息"
        left-icon="ios-document"
        :data="demoData"
        :cards="demoCards"
        :actions="currentActions"
        @action="handleAction"
      >
        <template #left="{ data }">
          <div class="demo-content">
            <h3>{{ data.title }}</h3>
            <p>申请人：{{ data.applicant }}</p>
            <p>申请时间：{{ data.applyTime }}</p>
            <p>申请类型：{{ data.type }}</p>
            <p>当前状态：<Tag :color="getStatusColor(data.status)">{{ data.status }}</Tag></p>
          </div>
        </template>
        
        <template #approval-info>
          <div class="approval-content">
            <h4>审批流程</h4>
            <Steps :current="2" size="small">
              <Step title="提交申请" content="申请人提交"></Step>
              <Step title="初审" content="部门审核"></Step>
              <Step title="终审" content="领导审批"></Step>
              <Step title="完成" content="流程结束"></Step>
            </Steps>
          </div>
        </template>
        
        <template #approval-form>
          <div class="approval-content">
            <h4>审批意见</h4>
            <Form :label-width="80">
              <FormItem label="审批结果">
                <RadioGroup v-model="approvalResult">
                  <Radio label="approve">同意</Radio>
                  <Radio label="reject">拒绝</Radio>
                  <Radio label="return">退回</Radio>
                </RadioGroup>
              </FormItem>
              <FormItem label="审批意见">
                <Input 
                  v-model="approvalComment" 
                  type="textarea" 
                  :rows="4"
                  placeholder="请输入审批意见"
                />
              </FormItem>
            </Form>
          </div>
        </template>
      </SimpleDetailCardLayout>
    </div>

    <!-- DetailCardLayout 演示 -->
    <div v-if="currentLayout === 'detail'" class="layout-demo">
      <DetailCardLayout
        :left-config="detailLeftConfig"
        :right-cards="detailRightCards"
        :bottom-actions="currentActions"
        @bottom-action="handleAction"
      >
        <template #left="{ data }">
          <div class="demo-content">
            <h3>{{ data.title }}</h3>
            <p>申请人：{{ data.applicant }}</p>
            <p>申请时间：{{ data.applyTime }}</p>
            <p>申请类型：{{ data.type }}</p>
            <p>当前状态：<Tag :color="getStatusColor(data.status)">{{ data.status }}</Tag></p>
          </div>
        </template>
        
        <template #approval-info>
          <div class="approval-content">
            <h4>审批流程</h4>
            <Steps :current="2" size="small">
              <Step title="提交申请" content="申请人提交"></Step>
              <Step title="初审" content="部门审核"></Step>
              <Step title="终审" content="领导审批"></Step>
              <Step title="完成" content="流程结束"></Step>
            </Steps>
          </div>
        </template>
        
        <template #approval-form>
          <div class="approval-content">
            <h4>审批意见</h4>
            <Form :label-width="80">
              <FormItem label="审批结果">
                <RadioGroup v-model="approvalResult">
                  <Radio label="approve">同意</Radio>
                  <Radio label="reject">拒绝</Radio>
                  <Radio label="return">退回</Radio>
                </RadioGroup>
              </FormItem>
              <FormItem label="审批意见">
                <Input 
                  v-model="approvalComment" 
                  type="textarea" 
                  :rows="4"
                  placeholder="请输入审批意见"
                />
              </FormItem>
            </Form>
          </div>
        </template>
      </DetailCardLayout>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BottomActionsExample',
  data() {
    return {
      currentLayout: 'easy',
      
      // 审批表单数据
      approvalResult: 'approve',
      approvalComment: '',
      
      // 演示数据
      demoData: {
        title: '请假申请审批',
        applicant: '张三',
        applyTime: '2024-01-15 09:00',
        type: '事假',
        status: '待审批'
      },
      
      // SimpleDetailCardLayout 卡片配置
      demoCards: [
        {
          title: '审批流程',
          icon: 'ios-git-network',
          slot: 'approval-info'
        },
        {
          title: '审批表单',
          icon: 'ios-create',
          slot: 'approval-form'
        }
      ],
      
      // DetailCardLayout 配置
      detailLeftConfig: {
        title: '审批信息',
        icon: 'ios-document',
        data: {
          title: '请假申请审批',
          applicant: '张三',
          applyTime: '2024-01-15 09:00',
          type: '事假',
          status: '待审批'
        }
      },
      
      detailRightCards: [
        {
          name: 'approval-info',
          title: '审批流程',
          icon: 'ios-git-network',
          slot: 'approval-info'
        },
        {
          name: 'approval-form',
          title: '审批表单',
          icon: 'ios-create',
          slot: 'approval-form'
        }
      ]
    }
  },
  
  computed: {
    // 当前按钮配置
    currentActions() {
      return [
        {
          name: 'back',
          label: '返回',
          icon: 'ios-arrow-back',
          type: 'default'
        },
        {
          name: 'cancel',
          label: '取消',
          icon: 'ios-close',
          type: 'default'
        },
        {
          name: 'approve',
          label: '审批',
          icon: 'ios-checkmark',
          type: 'primary'
        }
      ]
    }
  },
  
  methods: {
    // 切换布局
    switchLayout(layout) {
      this.currentLayout = layout
      this.$Message.info(`切换到 ${this.getLayoutName(layout)}`)
    },
    
    // 获取布局名称
    getLayoutName(layout) {
      const names = {
        'easy': 'EasyLayout',
        'simple': 'SimpleDetailCardLayout', 
        'detail': 'DetailCardLayout'
      }
      return names[layout]
    },
    
    // 处理按钮点击
    handleAction(action) {
      const actionName = action.name || action
      
      switch (actionName) {
        case 'back':
          this.$Message.info('返回上一页')
          break
        case 'cancel':
          this.$Message.info('取消操作')
          break
        case 'approve':
          this.handleApproval()
          break
        default:
          this.$Message.info(`执行操作: ${actionName}`)
      }
    },
    
    // 处理自定义按钮
    handleCustomAction(action) {
      this.$Message.info(`自定义操作: ${action}`)
    },
    
    // 处理审批
    handleApproval() {
      if (!this.approvalComment.trim()) {
        this.$Message.warning('请填写审批意见')
        return
      }
      
      const resultText = {
        'approve': '同意',
        'reject': '拒绝', 
        'return': '退回'
      }[this.approvalResult]
      
      this.$Message.success(`审批完成: ${resultText}`)
      
      // 模拟审批后的状态更新
      this.demoData.status = this.approvalResult === 'approve' ? '已通过' : '已拒绝'
      this.detailLeftConfig.data.status = this.demoData.status
    },
    
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        '待审批': 'warning',
        '已通过': 'success',
        '已拒绝': 'error'
      }
      return colorMap[status] || 'default'
    }
  }
}
</script>

<style lang="less" scoped>
.bottom-actions-example {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .control-panel {
    background: white;
    padding: 16px;
    border-bottom: 1px solid #e8eaec;
    flex-shrink: 0;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 18px;
    }
    
    p {
      margin: 0 0 16px 0;
      color: #666;
    }
    
    .layout-selector {
      margin-bottom: 16px;
      
      .ivu-btn {
        margin-right: 8px;
      }
    }
    
    .actions-config {
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
      }
      
      .config-display {
        background: #f8f8f9;
        border: 1px solid #e8eaec;
        border-radius: 4px;
        padding: 12px;
        
        pre {
          margin: 0;
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
  
  .layout-demo {
    flex: 1;
    overflow: hidden;
  }
}

.demo-content {
  padding: 20px;
  
  h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
  }
  
  p {
    margin: 8px 0;
    color: #666;
  }
}

.approval-content {
  padding: 16px;
  
  h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
  }
}
</style>
