<template>
  <div class="simple-usage-example">
    <h2>简单易用的详情卡片布局</h2>
    <p>只需要几个属性就能快速搭建详情页面</p>
    
    <!-- 最简单的用法 -->
    <SimpleDetailCardLayout
      left-title="人员信息"
      left-icon="ios-person"
      :data="personData"
      :cards="cardConfigs"
      :actions="bottomActions"
      @toggle="handleToggle"
      @action="handleAction"
      @card-action="handleCardAction"
      @update:data="personData = $event"
    >
      <!-- 左侧内容 - 只需要关注业务逻辑 -->
      <template #left="{ data, update }">
        <div class="person-info">
          <div class="avatar-section">
            <Avatar :src="data.avatar" size="large" icon="ios-person" />
            <h3>{{ data.name || '未知' }}</h3>
            <Tag :color="data.status === 'active' ? 'success' : 'default'">
              {{ data.status === 'active' ? '在押' : '其他' }}
            </Tag>
          </div>
          
          <div class="info-list">
            <div class="info-item">
              <span>编号：</span>
              <span>{{ data.code || '-' }}</span>
            </div>
            <div class="info-item">
              <span>年龄：</span>
              <span>{{ data.age || '-' }}岁</span>
            </div>
            <div class="info-item">
              <span>监区：</span>
              <span>{{ data.area || '-' }}</span>
            </div>
            <div class="info-item">
              <span>入所时间：</span>
              <span>{{ data.entryDate || '-' }}</span>
            </div>
          </div>
          
          <Button @click="() => update({ lastViewed: new Date().toLocaleString() })" 
                  type="primary" size="small" long>
            更新查看时间
          </Button>
          
          <div v-if="data.lastViewed" style="margin-top: 12px;">
            <Alert>
              <span slot="desc">最后查看：{{ data.lastViewed }}</span>
            </Alert>
          </div>
        </div>
      </template>
      
      <!-- 收缩状态内容 -->
      <template #collapsed="{ data, toggle }">
        <div class="collapsed-info" @click="toggle">
          <Avatar :src="data.avatar" icon="ios-person" />
          <p style="margin: 8px 0 0; font-size: 12px;">{{ data.name || '未知' }}</p>
        </div>
      </template>
      
      <!-- 基本信息卡片 -->
      <template #basic-info="{ data, update }">
        <Table :columns="basicColumns" :data="basicData" size="small" />
        <div style="margin-top: 16px; text-align: right;">
          <Button @click="refreshBasicInfo" size="small">刷新</Button>
        </div>
      </template>
      
      <!-- 操作功能卡片 -->
      <template #actions-card>
        <Row :gutter="16">
          <Col span="12">
            <div class="action-item" @click="viewVideo">
              <Icon type="ios-videocam" size="32" color="#5b8ff9" />
              <h4>查看录像</h4>
              <p>观看谈话录音录像</p>
            </div>
          </Col>
          <Col span="12">
            <div class="action-item" @click="viewRecord">
              <Icon type="ios-document" size="32" color="#52c41a" />
              <h4>查看笔录</h4>
              <p>查看谈话记录内容</p>
            </div>
          </Col>
        </Row>
      </template>
      
      <!-- 评估结论卡片 -->
      <template #assessment="{ data, update }">
        <Form :label-width="80">
          <FormItem label="心理评估">
            <Input 
              v-model="assessmentData.psychology" 
              type="textarea" 
              :rows="3"
              placeholder="请输入心理评估结论"
            />
          </FormItem>
          <FormItem label="协作建议">
            <Input 
              v-model="assessmentData.collaboration" 
              type="textarea" 
              :rows="3"
              placeholder="请输入协作建议"
            />
          </FormItem>
          <FormItem>
            <Button @click="saveAssessment" type="primary" size="small">保存评估</Button>
            <Button @click="resetAssessment" size="small" style="margin-left: 8px;">重置</Button>
          </FormItem>
        </Form>
      </template>
    </SimpleDetailCardLayout>
  </div>
</template>

<script>
export default {
  name: 'SimpleUsageExample',
  data() {
    return {
      // 人员数据 - 简单的对象
      personData: {
        name: '张三',
        code: 'P001',
        age: 35,
        area: '一监区',
        entryDate: '2023-01-15',
        status: 'active',
        avatar: ''
      },
      
      // 卡片配置 - 简化的数组
      cardConfigs: [
        {
          name: 'basic-info',
          title: '基本信息',
          icon: 'ios-information-circle',
          slot: 'basic-info',
          actions: [
            { name: 'edit', label: '编辑', icon: 'ios-create', type: 'primary' }
          ]
        },
        {
          name: 'actions-card',
          title: '操作功能',
          icon: 'ios-options',
          slot: 'actions-card'
        },
        {
          name: 'assessment',
          title: '评估结论',
          icon: 'ios-analytics',
          slot: 'assessment'
        }
      ],
      
      // 底部操作 - 简单的数组
      bottomActions: [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'save', label: '保存', icon: 'ios-checkmark', type: 'primary' }
      ],
      
      // 基本信息表格
      basicColumns: [
        { title: '项目', key: 'label', width: 120 },
        { title: '内容', key: 'value' }
      ],
      basicData: [
        { label: '谈话时间', value: '2024-01-15 09:00' },
        { label: '谈话民警', value: '李警官' },
        { label: '谈话类型', value: '日常谈话' },
        { label: '谈话状态', value: '已完成' }
      ],
      
      // 评估数据
      assessmentData: {
        psychology: '',
        collaboration: ''
      }
    }
  },
  
  methods: {
    // 处理收缩切换
    handleToggle(isCollapsed) {
      console.log('面板收缩状态:', isCollapsed)
      this.$Message.info(`面板已${isCollapsed ? '收缩' : '展开'}`)
    },
    
    // 处理底部操作
    handleAction(action) {
      console.log('底部操作:', action.name)
      
      if (action.name === 'back') {
        this.$Message.info('返回上一页')
      } else if (action.name === 'save') {
        this.$Message.success('数据保存成功')
      }
    },
    
    // 处理卡片操作
    handleCardAction({ action, card, index }) {
      console.log('卡片操作:', action.name, card.name)
      
      if (action.name === 'edit') {
        this.$Message.info(`编辑${card.title}`)
      }
    },
    
    // 刷新基本信息
    refreshBasicInfo() {
      this.$Message.info('基本信息已刷新')
    },
    
    // 查看录像
    viewVideo() {
      this.$Message.info('打开录像播放器')
    },
    
    // 查看笔录
    viewRecord() {
      this.$Message.info('打开笔录查看器')
    },
    
    // 保存评估
    saveAssessment() {
      if (!this.assessmentData.psychology && !this.assessmentData.collaboration) {
        this.$Message.warning('请填写评估内容')
        return
      }
      this.$Message.success('评估结论已保存')
    },
    
    // 重置评估
    resetAssessment() {
      this.assessmentData = {
        psychology: '',
        collaboration: ''
      }
      this.$Message.info('评估内容已重置')
    }
  }
}
</script>

<style lang="less" scoped>
.simple-usage-example {
  height: 100vh;
  
  h2 {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 16px;
    border-radius: 4px;
    margin: 0;
    font-size: 18px;
    color: #2c3e50;
  }
  
  p {
    position: absolute;
    top: 55px;
    left: 20px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 16px;
    border-radius: 4px;
    margin: 0;
    font-size: 14px;
    color: #666;
  }
}

// 人员信息样式
.person-info {
  .avatar-section {
    text-align: center;
    margin-bottom: 24px;
    
    h3 {
      margin: 12px 0 8px;
      font-size: 18px;
      color: #2c3e50;
    }
  }
  
  .info-list {
    margin-bottom: 20px;
    
    .info-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      span:first-child {
        color: #666;
        font-size: 14px;
      }
      
      span:last-child {
        color: #2c3e50;
        font-weight: 500;
        font-size: 14px;
      }
    }
  }
}

// 收缩状态样式
.collapsed-info {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
  
  p {
    position: static !important;
    background: none !important;
    padding: 0 !important;
    color: #666;
  }
}

// 操作项样式
.action-item {
  text-align: center;
  padding: 20px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #5b8ff9;
    box-shadow: 0 4px 12px rgba(91, 143, 249, 0.15);
  }
  
  h4 {
    margin: 8px 0 4px;
    font-size: 14px;
    color: #2c3e50;
  }
  
  p {
    position: static !important;
    background: none !important;
    padding: 0 !important;
    margin: 0;
    font-size: 12px;
    color: #999;
  }
}
</style>
