<template>
  <div class="seamless-upgrade-demo">
    <!-- 控制面板 -->
    <div class="control-panel">
      <h2>无缝升级演示</h2>
      <p>同样的内容，不同的功能层次</p>
      
      <div class="stage-selector">
        <Button 
          :type="currentStage === 1 ? 'primary' : 'default'"
          @click="switchStage(1)"
        >
          阶段1: EasyLayout
        </Button>
        <Button 
          :type="currentStage === 2 ? 'primary' : 'default'"
          @click="switchStage(2)"
        >
          阶段2: SimpleDetailCardLayout
        </Button>
        <Button 
          :type="currentStage === 3 ? 'primary' : 'default'"
          @click="switchStage(3)"
        >
          阶段3: DetailCardLayout
        </Button>
      </div>
      
      <div class="stage-info">
        <Alert :type="stageInfo.type">
          <span slot="desc">{{ stageInfo.description }}</span>
        </Alert>
      </div>
    </div>

    <!-- 阶段1: EasyLayout -->
    <div v-if="currentStage === 1" class="stage-content">
      <EasyLayout left-width="350px">
        <template #left>
          <div class="person-content">
            <div class="avatar-section">
              <Avatar size="large" icon="ios-person" />
              <h3>{{ person.name }}</h3>
              <Tag color="success">{{ person.status }}</Tag>
            </div>
            
            <div class="info-list">
              <p><strong>编号：</strong>{{ person.code }}</p>
              <p><strong>年龄：</strong>{{ person.age }}岁</p>
              <p><strong>监区：</strong>{{ person.area }}</p>
              <p><strong>入所时间：</strong>{{ person.entryDate }}</p>
            </div>
          </div>
        </template>
        
        <template #right>
          <div class="cards-content">
            <EasyCard title="基本信息" icon="ios-information-circle">
              <div class="basic-info">
                <p>谈话时间：{{ talkInfo.time }}</p>
                <p>谈话民警：{{ talkInfo.officer }}</p>
                <p>谈话类型：{{ talkInfo.type }}</p>
                <p>谈话状态：{{ talkInfo.status }}</p>
              </div>
            </EasyCard>
            
            <EasyCard title="操作功能" icon="ios-options">
              <div class="actions-content">
                <Button @click="viewVideo" type="primary">查看录像</Button>
                <Button @click="viewRecord" style="margin-left: 8px;">查看笔录</Button>
              </div>
            </EasyCard>
          </div>
        </template>
      </EasyLayout>
    </div>

    <!-- 阶段2: SimpleDetailCardLayout -->
    <div v-if="currentStage === 2" class="stage-content">
      <SimpleDetailCardLayout
        left-title="人员信息"
        left-icon="ios-person"
        :data="person"
        :cards="simpleCards"
        :actions="simpleActions"
        @action="handleSimpleAction"
        @update:data="person = $event"
      >
        <!-- 完全相同的左侧内容 -->
        <template #left="{ data, update }">
          <div class="person-content">
            <div class="avatar-section">
              <Avatar size="large" icon="ios-person" />
              <h3>{{ data.name }}</h3>
              <Tag color="success">{{ data.status }}</Tag>
            </div>
            
            <div class="info-list">
              <p><strong>编号：</strong>{{ data.code }}</p>
              <p><strong>年龄：</strong>{{ data.age }}岁</p>
              <p><strong>监区：</strong>{{ data.area }}</p>
              <p><strong>入所时间：</strong>{{ data.entryDate }}</p>
            </div>
            
            <!-- 新增功能：数据更新 -->
            <Button @click="() => update({ lastViewed: new Date().toLocaleString() })" 
                    size="small" long style="margin-top: 16px;">
              更新查看时间
            </Button>
            
            <div v-if="data.lastViewed" style="margin-top: 12px;">
              <Alert>
                <span slot="desc">最后查看：{{ data.lastViewed }}</span>
              </Alert>
            </div>
          </div>
        </template>
        
        <!-- 完全相同的卡片内容 -->
        <template #basic-info>
          <div class="basic-info">
            <p>谈话时间：{{ talkInfo.time }}</p>
            <p>谈话民警：{{ talkInfo.officer }}</p>
            <p>谈话类型：{{ talkInfo.type }}</p>
            <p>谈话状态：{{ talkInfo.status }}</p>
          </div>
        </template>
        
        <template #actions-card>
          <div class="actions-content">
            <Button @click="viewVideo" type="primary">查看录像</Button>
            <Button @click="viewRecord" style="margin-left: 8px;">查看笔录</Button>
          </div>
        </template>
      </SimpleDetailCardLayout>
    </div>

    <!-- 阶段3: DetailCardLayout -->
    <div v-if="currentStage === 3" class="stage-content">
      <DetailCardLayout
        :left-config="detailLeftConfig"
        :right-cards="detailRightCards"
        :bottom-actions="detailBottomActions"
        @card-action="handleDetailCardAction"
        @bottom-action="handleDetailBottomAction"
        @collapse-change="handleCollapseChange"
      >
        <!-- 完全相同的左侧内容 -->
        <template #left="{ data, updateData }">
          <div class="person-content">
            <div class="avatar-section">
              <Avatar size="large" icon="ios-person" />
              <h3>{{ data.name }}</h3>
              <Tag color="success">{{ data.status }}</Tag>
            </div>
            
            <div class="info-list">
              <p><strong>编号：</strong>{{ data.code }}</p>
              <p><strong>年龄：</strong>{{ data.age }}岁</p>
              <p><strong>监区：</strong>{{ data.area }}</p>
              <p><strong>入所时间：</strong>{{ data.entryDate }}</p>
            </div>
            
            <Button @click="() => updateData({ lastViewed: new Date().toLocaleString() })" 
                    size="small" long style="margin-top: 16px;">
              更新查看时间
            </Button>
            
            <div v-if="data.lastViewed" style="margin-top: 12px;">
              <Alert>
                <span slot="desc">最后查看：{{ data.lastViewed }}</span>
              </Alert>
            </div>
          </div>
        </template>
        
        <!-- 完全相同的卡片内容 -->
        <template #basic-info>
          <div class="basic-info">
            <p>谈话时间：{{ talkInfo.time }}</p>
            <p>谈话民警：{{ talkInfo.officer }}</p>
            <p>谈话类型：{{ talkInfo.type }}</p>
            <p>谈话状态：{{ talkInfo.status }}</p>
          </div>
        </template>
        
        <template #actions-card>
          <div class="actions-content">
            <Button @click="viewVideo" type="primary">查看录像</Button>
            <Button @click="viewRecord" style="margin-left: 8px;">查看笔录</Button>
          </div>
        </template>
      </DetailCardLayout>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SeamlessUpgradeDemo',
  data() {
    return {
      currentStage: 1,
      
      // 共享数据 - 在所有阶段中保持一致
      person: {
        name: '张三',
        code: 'P001',
        age: 35,
        area: '一监区',
        entryDate: '2023-01-15',
        status: '在押'
      },
      
      talkInfo: {
        time: '2024-01-15 09:00',
        officer: '李警官',
        type: '日常谈话',
        status: '已完成'
      },
      
      // 阶段2配置
      simpleCards: [
        { title: '基本信息', icon: 'ios-information-circle', slot: 'basic-info' },
        { title: '操作功能', icon: 'ios-options', slot: 'actions-card' }
      ],
      
      simpleActions: [
        { name: 'save', label: '保存', type: 'primary' }
      ],
      
      // 阶段3配置
      detailLeftConfig: {
        width: '350px',
        title: '人员信息',
        icon: 'ios-person',
        collapsible: true,
        data: this.person
      },
      
      detailRightCards: [
        {
          name: 'basic-info',
          title: '基本信息',
          icon: 'ios-information-circle',
          slot: 'basic-info',
          actions: [
            { name: 'edit', label: '编辑', icon: 'ios-create' }
          ]
        },
        {
          name: 'actions-card',
          title: '操作功能',
          icon: 'ios-options',
          slot: 'actions-card'
        }
      ],
      
      detailBottomActions: [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'save', label: '保存', type: 'primary', icon: 'ios-checkmark' }
      ]
    }
  },
  
  computed: {
    stageInfo() {
      const infos = {
        1: {
          type: 'info',
          description: '阶段1: 最简单的布局，5分钟搭建，零配置'
        },
        2: {
          type: 'success',
          description: '阶段2: 增加收缩功能和数据绑定，插槽内容完全不变'
        },
        3: {
          type: 'warning',
          description: '阶段3: 完整功能，支持复杂配置，插槽内容依然不变'
        }
      }
      return infos[this.currentStage]
    }
  },
  
  watch: {
    // 同步数据到DetailCardLayout配置
    person: {
      handler(newVal) {
        this.detailLeftConfig.data = newVal
      },
      deep: true
    }
  },
  
  methods: {
    switchStage(stage) {
      this.currentStage = stage
      this.$Message.info(`切换到阶段${stage}`)
    },
    
    // 共享方法 - 在所有阶段中保持一致
    viewVideo() {
      this.$Message.info('查看录像功能')
    },
    
    viewRecord() {
      this.$Message.info('查看笔录功能')
    },
    
    // 阶段2事件处理
    handleSimpleAction(action) {
      if (action.name === 'save') {
        this.$Message.success('阶段2: 保存成功')
      }
    },
    
    // 阶段3事件处理
    handleDetailCardAction({ action, card }) {
      if (action.name === 'edit') {
        this.$Message.info(`阶段3: 编辑${card.title}`)
      }
    },
    
    handleDetailBottomAction({ action }) {
      if (action.name === 'save') {
        this.$Message.success('阶段3: 保存成功')
      } else if (action.name === 'back') {
        this.$Message.info('阶段3: 返回上一页')
      }
    },
    
    handleCollapseChange({ isCollapsed }) {
      this.$Message.info(`阶段3: 面板${isCollapsed ? '收缩' : '展开'}`)
    }
  }
}
</script>

<style lang="less" scoped>
.seamless-upgrade-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .control-panel {
    background: white;
    padding: 16px;
    border-bottom: 1px solid #e8eaec;
    flex-shrink: 0;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 18px;
    }
    
    p {
      margin: 0 0 16px 0;
      color: #666;
    }
    
    .stage-selector {
      margin-bottom: 16px;
      
      .ivu-btn {
        margin-right: 8px;
      }
    }
    
    .stage-info {
      margin: 0;
    }
  }
  
  .stage-content {
    flex: 1;
    overflow: hidden;
  }
}

// 共享样式 - 在所有阶段中保持一致
.person-content {
  padding: 20px;
  
  .avatar-section {
    text-align: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 12px 0 8px;
      font-size: 18px;
      color: #2c3e50;
    }
  }
  
  .info-list {
    p {
      margin: 8px 0;
      font-size: 14px;
      
      strong {
        color: #666;
      }
    }
  }
}

.cards-content {
  padding: 16px;
}

.basic-info {
  p {
    margin: 8px 0;
    font-size: 14px;
  }
}

.actions-content {
  text-align: center;
}
</style>
