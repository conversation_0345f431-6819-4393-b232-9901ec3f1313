<template>
  <div class="collapsible-detail-card-example">
    <h2>可收缩详情卡片布局示例</h2>
    <p>展示左侧面板收缩和展开功能</p>

    <!-- 控制面板 -->
    <Card style="margin-bottom: 20px;">
      <p slot="title">
        <Icon type="ios-settings" />
        控制面板
      </p>

      <Row :gutter="16">
        <Col span="8">
          <h4>收缩控制</h4>
          <div style="margin-bottom: 16px;">
            <Button @click="toggleCollapse" :type="isCollapsed ? 'primary' : 'default'">
              {{ isCollapsed ? '展开左侧面板' : '收缩左侧面板' }}
            </Button>
          </div>

          <div style="margin-bottom: 16px;">
            <Checkbox v-model="leftConfig.collapsible">启用收缩功能</Checkbox>
          </div>

          <div>
            <span>收缩宽度：</span>
            <InputNumber
              v-model="collapsedWidth"
              :min="50"
              :max="100"
              :step="10"
              @on-change="updateCollapsedWidth"
            />
            <span>px</span>
          </div>
        </Col>

        <Col span="8">
          <h4>状态信息</h4>
          <div class="status-info">
            <p><strong>当前状态：</strong>{{ isCollapsed ? '已收缩' : '已展开' }}</p>
            <p><strong>左侧宽度：</strong>{{ currentWidth }}</p>
            <p><strong>收缩次数：</strong>{{ collapseCount }}</p>
          </div>
        </Col>

        <Col span="8">
          <h4>事件日志</h4>
          <div class="event-log" style="height: 120px; overflow-y: auto; border: 1px solid #ddd; padding: 8px;">
            <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
          <Button @click="clearLogs" size="small" style="margin-top: 8px;">清空日志</Button>
        </Col>
      </Row>
    </Card>

    <!-- 详情卡片布局 -->
    <DetailCardLayout
      ref="detailLayout"
      :left-config="leftConfig"
      :right-cards="rightCards"
      :bottom-actions="bottomActions"
      @collapse-change="handleCollapseChange"
      @card-action="handleCardAction"
      @bottom-action="handleBottomAction"
    >
      <!-- 左侧展开状态内容 -->
      <template #left="{ data, updateData, isCollapsed }">
        <div class="person-info-panel">
          <div class="avatar-section">
            <img :src="data.avatar || defaultAvatar" :alt="data.name" class="person-avatar" />
            <div class="person-name">
              <h3>{{ data.name || '张三' }}</h3>
              <div class="person-tags">
                <Tag color="success">{{ data.status || '健康' }}</Tag>
                <Tag color="blue">{{ data.area || '一监区' }}</Tag>
              </div>
            </div>
          </div>

          <Divider />

          <div class="info-sections">
            <div class="info-section">
              <h4>基本信息</h4>
              <div class="info-list">
                <div class="info-row">
                  <span class="label">编号：</span>
                  <span class="value">{{ data.code || 'P001' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">年龄：</span>
                  <span class="value">{{ data.age || '35' }}岁</span>
                </div>
                <div class="info-row">
                  <span class="label">监区：</span>
                  <span class="value">{{ data.area || '一监区' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">入所时间：</span>
                  <span class="value">{{ data.entryDate || '2023-01-15' }}</span>
                </div>
              </div>
            </div>

            <div class="info-section">
              <h4>联系信息</h4>
              <div class="info-list">
                <div class="info-row">
                  <span class="label">主管：</span>
                  <span class="value">{{ data.manager || '王警官' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">副管：</span>
                  <span class="value">{{ data.assistant || '李警官' }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="action-buttons" style="margin-top: 16px;">
            <Button @click="() => updateData({ lastViewed: new Date().toLocaleTimeString() })" size="small" long>
              更新查看时间
            </Button>
          </div>

          <div v-if="data.lastViewed" class="last-viewed" style="margin-top: 12px;">
            <Alert>
              <span slot="desc">最后查看：{{ data.lastViewed }}</span>
            </Alert>
          </div>
        </div>
      </template>

      <!-- 左侧收缩状态内容 -->
      <template #left-collapsed="{ data, toggleCollapse }">
        <div class="collapsed-custom-content">
          <div class="collapsed-avatar" @click="toggleCollapse" title="点击展开">
            <img :src="data.avatar || defaultAvatar" :alt="data.name" />
          </div>

          <div class="collapsed-name" @click="toggleCollapse" title="点击展开">
            <p>{{ (data.name || '张三').charAt(0) }}</p>
          </div>

          <div class="collapsed-status">
            <div class="status-dot" :class="getStatusClass(data.status)"></div>
          </div>

          <Tooltip content="点击展开详细信息" placement="right">
            <Button
              type="text"
              size="small"
              @click="toggleCollapse"
              style="margin-top: 8px;"
            >
              <Icon type="ios-information-circle" size="16" />
            </Button>
          </Tooltip>
        </div>
      </template>

      <!-- 基本信息卡片 -->
      <template #basic-info>
        <Table :columns="basicColumns" :data="basicData" size="small" />
      </template>

      <!-- 操作功能卡片 -->
      <template #actions-card>
        <div class="actions-grid">
          <div class="action-item" @click="handleAction('video')">
            <Icon type="ios-videocam" size="32" color="#5b8ff9" />
            <h4>查看录像</h4>
            <p>观看谈话录音录像</p>
          </div>

          <div class="action-item" @click="handleAction('record')">
            <Icon type="ios-document" size="32" color="#52c41a" />
            <h4>查看笔录</h4>
            <p>查看谈话记录内容</p>
          </div>
        </div>
      </template>
    </DetailCardLayout>
  </div>
</template>

<script>
export default {
  name: 'CollapsibleDetailCardExample',
  data() {
    return {
      // 收缩状态
      isCollapsed: false,
      collapseCount: 0,
      collapsedWidth: 60,

      // 左侧配置
      leftConfig: {
        width: '400px',
        collapsedWidth: '60px',
        title: '被监管人员信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true,
        collapsible: true, // 启用收缩功能
        data: {
          name: '张三',
          code: 'P001',
          age: 35,
          area: '一监区',
          entryDate: '2023-01-15',
          status: 'healthy',
          manager: '王警官',
          assistant: '李警官',
          avatar: ''
        }
      },

      // 右侧卡片
      rightCards: [
        {
          name: 'basic-info',
          title: '基本信息',
          icon: 'ios-information-circle',
          slot: 'basic-info'
        },
        {
          name: 'actions-card',
          title: '操作功能',
          icon: 'ios-options',
          slot: 'actions-card'
        }
      ],

      // 底部操作
      bottomActions: [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'save', label: '保存', type: 'primary', icon: 'ios-checkmark' }
      ],

      // 默认头像
      defaultAvatar: require('@/assets/images/main.png'),

      // 基本信息表格
      basicColumns: [
        { title: '项目', key: 'label', width: 120 },
        { title: '内容', key: 'value' }
      ],
      basicData: [
        { label: '谈话时间', value: '2024-01-15 09:00' },
        { label: '谈话民警', value: '李警官' },
        { label: '谈话类型', value: '日常谈话' },
        { label: '谈话状态', value: '已完成' }
      ],

      // 事件日志
      eventLogs: []
    }
  },

  computed: {
    currentWidth() {
      return this.isCollapsed
        ? this.leftConfig.collapsedWidth
        : this.leftConfig.width
    }
  },

  methods: {
    // 切换收缩状态
    toggleCollapse() {
      if (this.$refs.detailLayout) {
        this.$refs.detailLayout.toggleCollapse()
      }
    },

    // 处理收缩状态变化
    handleCollapseChange(event) {
      this.isCollapsed = event.isCollapsed
      this.collapseCount++

      this.addLog(`面板${event.isCollapsed ? '收缩' : '展开'}，宽度: ${event.width}`)
    },

    // 更新收缩宽度
    updateCollapsedWidth() {
      this.leftConfig.collapsedWidth = `${this.collapsedWidth}px`
      this.addLog(`收缩宽度更新为: ${this.collapsedWidth}px`)
    },

    // 处理卡片操作
    handleCardAction(event) {
      this.addLog(`卡片操作: ${event.action.name}`)
    },

    // 处理底部操作
    handleBottomAction(event) {
      this.addLog(`底部操作: ${event.action.name}`)
    },

    // 处理操作
    handleAction(type) {
      this.addLog(`执行操作: ${type}`)
      this.$Message.info(`执行${type === 'video' ? '查看录像' : '查看笔录'}操作`)
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        'healthy': 'status-healthy',
        'sick': 'status-sick',
        'serious': 'status-serious'
      }
      return classMap[status] || 'status-default'
    },

    // 添加日志
    addLog(message) {
      this.eventLogs.unshift({
        time: new Date().toLocaleTimeString(),
        message
      })

      if (this.eventLogs.length > 10) {
        this.eventLogs = this.eventLogs.slice(0, 10)
      }
    },

    // 清空日志
    clearLogs() {
      this.eventLogs = []
    }
  },

  mounted() {
    this.addLog('可收缩详情卡片布局示例加载完成')
  }
}
</script>

<style lang="less" scoped>
.collapsible-detail-card-example {
  padding: 20px;

  h2 {
    color: #333;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    margin-bottom: 20px;
  }

  .status-info {
    p {
      margin: 8px 0;
      font-size: 14px;
    }
  }

  .event-log {
    .log-item {
      display: flex;
      gap: 8px;
      padding: 4px 0;
      border-bottom: 1px solid #f0f0f0;
      font-size: 12px;

      .log-time {
        color: #999;
        min-width: 80px;
      }

      .log-message {
        flex: 1;
      }
    }
  }
}

// 人员信息面板样式
.person-info-panel {
  .avatar-section {
    text-align: center;
    margin-bottom: 20px;

    .person-avatar {
      width: 120px;
      height: 150px;
      border-radius: 8px;
      object-fit: cover;
      border: 3px solid #e8f4fd;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .person-name {
      margin-top: 12px;

      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }

      .person-tags {
        display: flex;
        justify-content: center;
        gap: 8px;
      }
    }
  }

  .info-sections {
    .info-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #34495e;
        padding-bottom: 8px;
        border-bottom: 2px solid #ecf0f1;
      }

      .info-list {
        .info-row {
          display: flex;
          margin-bottom: 8px;

          .label {
            color: #7f8c8d;
            font-size: 13px;
            width: 80px;
            flex-shrink: 0;
          }

          .value {
            color: #2c3e50;
            font-size: 13px;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 收缩状态自定义内容
.collapsed-custom-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px 0;

  .collapsed-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e8f4fd;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #5b8ff9;
      transform: scale(1.1);
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .collapsed-name {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }

    p {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #2c3e50;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #f8fafc;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #e8f4fd;
    }
  }

  .collapsed-status {
    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;

      &.status-healthy {
        background: #52c41a;
      }

      &.status-sick {
        background: #faad14;
      }

      &.status-serious {
        background: #ff4d4f;
      }

      &.status-default {
        background: #d9d9d9;
      }
    }
  }
}

// 操作网格
.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 2px solid #e8f4fd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;

    &:hover {
      border-color: #5b8ff9;
      box-shadow: 0 4px 12px rgba(91, 143, 249, 0.15);
      transform: translateY(-2px);
    }

    h4 {
      margin: 8px 0 4px 0;
      font-size: 14px;
      color: #2c3e50;
    }

    p {
      margin: 0;
      font-size: 12px;
      color: #7f8c8d;
    }
  }
}
</style>
