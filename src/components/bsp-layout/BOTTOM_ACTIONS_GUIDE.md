# 底部按钮统一使用指南

所有BSP布局组件都支持底部操作按钮，提供统一的配置方式和使用体验。

## 🎯 支持的组件

| 组件 | 配置属性 | 事件名 | 插槽名 |
|------|----------|--------|--------|
| EasyLayout | `actions` | `@action` | `#actions` |
| SimpleDetailCardLayout | `actions` | `@action` | - |
| DetailCardLayout | `bottom-actions` | `@bottom-action` | `#bottom-actions` |

## 📋 按钮配置格式

### 统一配置结构
```javascript
actions: [
  {
    name: 'back',           // 按钮唯一标识
    label: '返回',          // 按钮文本
    type: 'default',        // 按钮类型: default, primary, success, warning, error
    icon: 'ios-arrow-back', // 按钮图标
    size: 'large',          // 按钮大小: small, default, large
    loading: false,         // 加载状态
    disabled: false         // 禁用状态
  }
]
```

### 常用按钮配置
```javascript
// 返回按钮
{
  name: 'back',
  label: '返回',
  icon: 'ios-arrow-back',
  type: 'default'
}

// 取消按钮
{
  name: 'cancel',
  label: '取消',
  icon: 'ios-close',
  type: 'default'
}

// 保存按钮
{
  name: 'save',
  label: '保存',
  icon: 'ios-checkmark',
  type: 'primary'
}

// 审批按钮
{
  name: 'approve',
  label: '审批',
  icon: 'ios-checkmark-circle',
  type: 'primary'
}

// 拒绝按钮
{
  name: 'reject',
  label: '拒绝',
  icon: 'ios-close-circle',
  type: 'error'
}

// 提交按钮
{
  name: 'submit',
  label: '提交',
  icon: 'ios-send',
  type: 'primary'
}
```

## 🚀 使用示例

### 1. EasyLayout 底部按钮

```vue
<template>
  <EasyLayout 
    :actions="actions"
    @action="handleAction"
  >
    <template #left>左侧内容</template>
    <template #right>右侧内容</template>
    
    <!-- 方式1: 使用配置 -->
    <!-- actions 属性会自动渲染按钮 -->
    
    <!-- 方式2: 使用插槽自定义 -->
    <template #actions>
      <Button @click="handleBack">返回</Button>
      <Button @click="handleCancel">取消</Button>
      <Button @click="handleApprove" type="primary">审批</Button>
    </template>
  </EasyLayout>
</template>

<script>
export default {
  data() {
    return {
      actions: [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'cancel', label: '取消', icon: 'ios-close' },
        { name: 'approve', label: '审批', icon: 'ios-checkmark', type: 'primary' }
      ]
    }
  },
  methods: {
    handleAction(action) {
      console.log('按钮点击:', action.name)
      
      switch (action.name) {
        case 'back':
          this.$router.go(-1)
          break
        case 'cancel':
          this.resetForm()
          break
        case 'approve':
          this.submitApproval()
          break
      }
    }
  }
}
</script>
```

### 2. SimpleDetailCardLayout 底部按钮

```vue
<template>
  <SimpleDetailCardLayout
    left-title="审批信息"
    :data="formData"
    :cards="cards"
    :actions="actions"
    @action="handleAction"
  >
    <template #left="{ data, update }">
      <!-- 左侧内容 -->
    </template>
    
    <template #form-card>
      <!-- 表单内容 -->
    </template>
  </SimpleDetailCardLayout>
</template>

<script>
export default {
  data() {
    return {
      formData: { /* 表单数据 */ },
      cards: [
        { title: '审批表单', slot: 'form-card' }
      ],
      actions: [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'save', label: '保存', icon: 'ios-checkmark', type: 'primary' }
      ]
    }
  },
  methods: {
    handleAction(action) {
      if (action.name === 'save') {
        this.saveForm()
      } else if (action.name === 'back') {
        this.$router.go(-1)
      }
    }
  }
}
</script>
```

### 3. DetailCardLayout 底部按钮

```vue
<template>
  <DetailCardLayout
    :left-config="leftConfig"
    :right-cards="rightCards"
    :bottom-actions="bottomActions"
    @bottom-action="handleBottomAction"
  >
    <!-- 插槽内容 -->
  </DetailCardLayout>
</template>

<script>
export default {
  data() {
    return {
      leftConfig: { /* 左侧配置 */ },
      rightCards: [ /* 右侧卡片 */ ],
      bottomActions: [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { name: 'cancel', label: '取消', icon: 'ios-close' },
        { name: 'approve', label: '审批', icon: 'ios-checkmark', type: 'primary' }
      ]
    }
  },
  methods: {
    handleBottomAction({ action }) {
      console.log('底部按钮点击:', action.name)
    }
  }
}
</script>
```

## 🎨 样式定制

### 按钮间距和对齐
```less
// 所有组件的底部按钮都支持统一的样式定制
.bottom-actions {
  .actions-container {
    display: flex;
    justify-content: flex-end;  // 右对齐
    gap: 12px;                  // 按钮间距
    
    .ivu-btn {
      min-width: 80px;          // 最小宽度
    }
  }
}

// 居中对齐
.bottom-actions .actions-container {
  justify-content: center;
}

// 左对齐
.bottom-actions .actions-container {
  justify-content: flex-start;
}

// 两端对齐
.bottom-actions .actions-container {
  justify-content: space-between;
}
```

### 响应式适配
```less
@media (max-width: 768px) {
  .bottom-actions {
    .actions-container {
      justify-content: center;
      flex-wrap: wrap;
      gap: 8px;
      
      .ivu-btn {
        flex: 1;
        min-width: 120px;
      }
    }
  }
}
```

## 🔧 高级用法

### 1. 动态按钮状态
```javascript
export default {
  data() {
    return {
      formValid: false,
      submitting: false
    }
  },
  
  computed: {
    actions() {
      return [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' },
        { 
          name: 'submit', 
          label: '提交', 
          icon: 'ios-send',
          type: 'primary',
          disabled: !this.formValid,    // 动态禁用
          loading: this.submitting      // 动态加载状态
        }
      ]
    }
  }
}
```

### 2. 条件显示按钮
```javascript
export default {
  computed: {
    actions() {
      const baseActions = [
        { name: 'back', label: '返回', icon: 'ios-arrow-back' }
      ]
      
      // 根据权限显示不同按钮
      if (this.hasEditPermission) {
        baseActions.push({
          name: 'edit',
          label: '编辑',
          icon: 'ios-create',
          type: 'primary'
        })
      }
      
      if (this.hasApprovalPermission) {
        baseActions.push({
          name: 'approve',
          label: '审批',
          icon: 'ios-checkmark',
          type: 'primary'
        })
      }
      
      return baseActions
    }
  }
}
```

### 3. 按钮分组
```vue
<template>
  <EasyLayout>
    <!-- 使用插槽实现按钮分组 -->
    <template #actions>
      <!-- 左侧按钮组 -->
      <div class="left-actions">
        <Button @click="handleHelp" icon="ios-help-circle">帮助</Button>
      </div>
      
      <!-- 右侧按钮组 -->
      <div class="right-actions">
        <Button @click="handleBack">返回</Button>
        <Button @click="handleCancel">取消</Button>
        <Button @click="handleSubmit" type="primary">提交</Button>
      </div>
    </template>
  </EasyLayout>
</template>

<style>
.actions-container {
  display: flex;
  justify-content: space-between;
  
  .left-actions,
  .right-actions {
    display: flex;
    gap: 12px;
  }
}
</style>
```

## 📱 移动端适配

所有组件的底部按钮都自动适配移动端：

- **自动换行**: 按钮过多时自动换行
- **居中对齐**: 移动端自动居中显示
- **合适尺寸**: 按钮大小适合触摸操作

## 🎯 最佳实践

### 1. 按钮顺序
推荐的按钮排列顺序（从左到右）：
1. 次要操作（返回、取消、帮助）
2. 主要操作（保存、提交、审批）

### 2. 按钮数量
- **建议**: 3-5个按钮
- **最多**: 不超过7个按钮
- **过多时**: 考虑使用下拉菜单或分组

### 3. 按钮文本
- **简洁明确**: 2-4个字符
- **动词优先**: 保存、提交、审批
- **避免歧义**: 确定 → 保存、取消 → 返回

### 4. 视觉层次
- **主要操作**: 使用 `type="primary"`
- **危险操作**: 使用 `type="error"`
- **次要操作**: 使用 `type="default"`

## 🔄 无缝升级

底部按钮配置在三个组件间可以无缝迁移：

```javascript
// 阶段1: EasyLayout
const actions = [
  { name: 'back', label: '返回' },
  { name: 'save', label: '保存', type: 'primary' }
]

// 阶段2: SimpleDetailCardLayout (配置完全一样)
const actions = [
  { name: 'back', label: '返回' },
  { name: 'save', label: '保存', type: 'primary' }
]

// 阶段3: DetailCardLayout (只需要改个属性名)
const bottomActions = [
  { name: 'back', label: '返回' },
  { name: 'save', label: '保存', type: 'primary' }
]
```

这样确保了在组件升级过程中，底部按钮的配置和行为保持一致！
