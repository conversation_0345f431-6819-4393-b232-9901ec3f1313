# BSP 动态布局组件库

一个基于 Vue.js 的动态布局和动态组件解决方案，提供灵活、可配置的页面布局系统。专为监狱管理系统设计，支持各种复杂的业务场景。

## 🚀 核心特性

- **12种预定义布局**: 覆盖管理、详情、卡片、工作台等常见页面布局
- **动态组件系统**: 支持组件的异步加载、缓存和热替换
- **完整的父子通信**: 双向数据绑定、事件传递、方法调用
- **响应式设计**: 自动适配桌面端、平板和移动端
- **组件注册中心**: 统一管理组件生命周期和依赖关系
- **事件系统**: 完整的事件冒泡和广播机制
- **插槽作用域**: 支持数据传递和方法注入
- **全局组件**: 开箱即用，无需重复导入
- **易于扩展**: 支持自定义布局和组件扩展

## 📦 安装与配置

### 自动安装
组件已在项目中全局注册，可直接使用：

```javascript
// main.js 中已配置
import BspLayout from '@/components/bsp-layout'
Vue.use(BspLayout)
```

### 验证安装
访问 `/test-bsp-layout` 路由查看所有布局效果和功能演示。

## 🔧 使用方法

### 1. 快速开始

由于组件已全局注册，可在任何Vue组件中直接使用：

```vue
<template>
  <!-- 无需导入，直接使用 -->
  <DynamicLayout layout-type="management">
    <template #left>
      <div>左侧内容</div>
    </template>
    <template #right>
      <div>右侧内容</div>
    </template>
  </DynamicLayout>
</template>

<script>
export default {
  // 无需在 components 中声明
}
</script>
```

### 2. 管理页面布局 (management)

**适用场景**: 数据管理、列表查询、CRUD操作页面

```vue
<template>
  <DynamicLayout
    layout-type="management"
    :layout-config="{ responsive: true }"
    :layout-data="{ title: '人员管理' }"
    @component-event="handleComponentEvent"
  >
    <!-- 左侧查询条件 -->
    <template #left>
      <Card>
        <p slot="title">
          <Icon type="ios-search" />
          查询条件
        </p>
        <Form :model="searchForm" :label-width="80">
          <FormItem label="姓名">
            <Input
              v-model="searchForm.name"
              placeholder="请输入姓名"
              @on-enter="handleSearch"
            />
          </FormItem>
          <FormItem label="监区">
            <Select v-model="searchForm.area" placeholder="请选择监区">
              <Option value="1">一监区</Option>
              <Option value="2">二监区</Option>
              <Option value="3">三监区</Option>
            </Select>
          </FormItem>
          <FormItem label="状态">
            <Select v-model="searchForm.status" placeholder="请选择状态">
              <Option value="healthy">健康</Option>
              <Option value="sick">患病</Option>
              <Option value="serious">重病号</Option>
            </Select>
          </FormItem>
          <FormItem label="入所时间">
            <DatePicker
              v-model="searchForm.dateRange"
              type="daterange"
              placeholder="选择日期范围"
              style="width: 100%"
            />
          </FormItem>
          <FormItem>
            <Button type="primary" long @click="handleSearch" :loading="searching">
              <Icon type="ios-search" />
              查询
            </Button>
          </FormItem>
          <FormItem>
            <Button long @click="handleReset">
              <Icon type="ios-refresh" />
              重置
            </Button>
          </FormItem>
        </Form>
      </Card>
    </template>

    <!-- 右侧数据表格 -->
    <template #right>
      <Card>
        <div slot="title" style="display: flex; justify-content: space-between; align-items: center;">
          <span>
            <Icon type="ios-people" />
            人员列表
            <Badge :count="tableData.length" style="margin-left: 16px;" />
          </span>
          <ButtonGroup>
            <Button type="primary" icon="ios-add" @click="handleAdd">新增</Button>
            <Button icon="ios-download-outline" @click="handleExport">导出</Button>
            <Button icon="ios-refresh" @click="handleRefresh">刷新</Button>
          </ButtonGroup>
        </div>

        <Table
          :columns="tableColumns"
          :data="tableData"
          :loading="tableLoading"
          stripe
          border
          @on-row-click="handleRowClick"
          @on-selection-change="handleSelectionChange"
        />

        <div style="margin-top: 16px; display: flex; justify-content: space-between; align-items: center;">
          <span style="color: #999;">
            已选择 {{ selectedRows.length }} 项，共 {{ pagination.total }} 条记录
          </span>
          <Page
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            show-sizer
            show-elevator
            show-total
            @on-change="handlePageChange"
            @on-page-size-change="handlePageSizeChange"
          />
        </div>
      </Card>
    </template>
  </DynamicLayout>
</template>

<script>
export default {
  data() {
    return {
      // 搜索表单
      searchForm: {
        name: '',
        area: '',
        status: '',
        dateRange: []
      },

      // 表格相关
      tableData: [],
      tableLoading: false,
      selectedRows: [],
      searching: false,

      // 分页配置
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      },

      // 表格列配置
      tableColumns: [
        { type: 'selection', width: 60, align: 'center' },
        { title: '姓名', key: 'name', width: 100, sortable: true },
        { title: '编号', key: 'code', width: 120 },
        { title: '年龄', key: 'age', width: 80, align: 'center', sortable: true },
        { title: '监区', key: 'area', width: 100, align: 'center' },
        {
          title: '状态',
          key: 'status',
          width: 100,
          align: 'center',
          render: (h, params) => {
            const colorMap = {
              'healthy': 'success',
              'sick': 'warning',
              'serious': 'error'
            }
            const textMap = {
              'healthy': '健康',
              'sick': '患病',
              'serious': '重病号'
            }
            return h('Tag', {
              props: { color: colorMap[params.row.status] }
            }, textMap[params.row.status])
          }
        },
        { title: '入所时间', key: 'entryDate', width: 120, align: 'center' },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'primary', size: 'small' },
                style: { marginRight: '5px' },
                on: { click: () => this.handleView(params.row) }
              }, '查看'),
              h('Button', {
                props: { size: 'small' },
                style: { marginRight: '5px' },
                on: { click: () => this.handleEdit(params.row) }
              }, '编辑'),
              h('Button', {
                props: { type: 'error', size: 'small' },
                on: { click: () => this.handleDelete(params.row) }
              }, '删除')
            ])
          }
        }
      ]
    }
  },

  mounted() {
    this.loadTableData()
  },

  methods: {
    // 搜索
    async handleSearch() {
      this.searching = true
      try {
        // 调用API搜索
        await this.loadTableData()
        this.$Message.success('查询完成')
      } catch (error) {
        this.$Message.error('查询失败')
      } finally {
        this.searching = false
      }
    },

    // 重置
    handleReset() {
      this.searchForm = {
        name: '',
        area: '',
        status: '',
        dateRange: []
      }
      this.pagination.current = 1
      this.loadTableData()
    },

    // 加载表格数据
    async loadTableData() {
      this.tableLoading = true
      try {
        // 模拟API调用
        const response = await this.fetchPersonList({
          ...this.searchForm,
          page: this.pagination.current,
          pageSize: this.pagination.pageSize
        })
        this.tableData = response.data
        this.pagination.total = response.total
      } catch (error) {
        this.$Message.error('数据加载失败')
      } finally {
        this.tableLoading = false
      }
    },

    // 新增
    handleAdd() {
      this.$router.push('/person/add')
    },

    // 查看
    handleView(row) {
      this.$router.push(`/person/detail/${row.id}`)
    },

    // 编辑
    handleEdit(row) {
      this.$router.push(`/person/edit/${row.id}`)
    },

    // 删除
    handleDelete(row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除 ${row.name} 的记录吗？`,
        onOk: async () => {
          try {
            await this.deletePersonById(row.id)
            this.$Message.success('删除成功')
            this.loadTableData()
          } catch (error) {
            this.$Message.error('删除失败')
          }
        }
      })
    },

    // 导出
    handleExport() {
      // 导出逻辑
      this.$Message.info('正在导出数据...')
    },

    // 刷新
    handleRefresh() {
      this.loadTableData()
    },

    // 行点击
    handleRowClick(row) {
      console.log('点击行:', row)
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 页码变化
    handlePageChange(page) {
      this.pagination.current = page
      this.loadTableData()
    },

    // 页大小变化
    handlePageSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.pagination.current = 1
      this.loadTableData()
    },

    // 处理组件事件
    handleComponentEvent(event) {
      console.log('组件事件:', event)
    },

    // 模拟API方法
    async fetchPersonList(params) {
      // 实际项目中替换为真实API调用
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            data: [
              { id: 1, name: '张三', code: 'P001', age: 35, area: '一监区', status: 'healthy', entryDate: '2023-01-15' },
              { id: 2, name: '李四', code: 'P002', age: 42, area: '二监区', status: 'sick', entryDate: '2023-02-20' },
              { id: 3, name: '王五', code: 'P003', age: 28, area: '一监区', status: 'healthy', entryDate: '2023-03-10' }
            ],
            total: 156
          })
        }, 1000)
      })
    },

    async deletePersonById(id) {
      // 实际项目中替换为真实API调用
      return new Promise(resolve => {
        setTimeout(resolve, 500)
      })
    }
  }
}
</script>
```

### 3. 详情页面布局 (detail)

**适用场景**: 记录详情查看、信息展示、编辑表单页面

```vue
<template>
  <DynamicLayout
    layout-type="detail"
    :layout-config="{ responsive: false }"
    :layout-data="{ personId: $route.params.id }"
  >
    <!-- 页面头部 -->
    <template #header>
      <div class="detail-header">
        <div class="header-left">
          <Avatar :src="personInfo.avatar" size="large" />
          <div class="person-basic">
            <h2>{{ personInfo.name || '未知' }}</h2>
            <div class="person-tags">
              <Tag :color="getRiskLevelColor(personInfo.riskLevel)">
                {{ personInfo.riskLevel || '未评估' }}
              </Tag>
              <Tag color="blue">{{ personInfo.area || '未分配' }}</Tag>
              <Tag v-if="personInfo.isVip" color="gold">重点关注</Tag>
            </div>
          </div>
        </div>

        <div class="header-right">
          <ButtonGroup>
            <Button type="primary" icon="ios-create" @click="handleEdit">
              编辑信息
            </Button>
            <Button icon="ios-print" @click="handlePrint">
              打印档案
            </Button>
            <Button icon="ios-share" @click="handleShare">
              分享
            </Button>
            <Dropdown @on-click="handleMoreAction">
              <Button>
                更多操作
                <Icon type="ios-arrow-down"></Icon>
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem name="export">导出档案</DropdownItem>
                <DropdownItem name="history">查看历史</DropdownItem>
                <DropdownItem name="transfer" divided>转移监区</DropdownItem>
                <DropdownItem name="archive" style="color: red;">归档</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </ButtonGroup>
        </div>
      </div>
    </template>

    <!-- 详情内容 -->
    <template #content>
      <div class="detail-content">
        <Tabs v-model="activeTab" @on-click="handleTabChange">
          <!-- 基本信息 -->
          <TabPane label="基本信息" name="basic" icon="ios-person">
            <Row :gutter="24">
              <Col span="12">
                <Card title="个人信息" icon="ios-contact">
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="label">姓名：</span>
                      <span class="value">{{ personInfo.name || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">编号：</span>
                      <span class="value">{{ personInfo.code || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">性别：</span>
                      <span class="value">{{ personInfo.gender || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">年龄：</span>
                      <span class="value">{{ personInfo.age || '-' }}岁</span>
                    </div>
                    <div class="info-item">
                      <span class="label">身份证号：</span>
                      <span class="value">{{ personInfo.idCard || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">籍贯：</span>
                      <span class="value">{{ personInfo.hometown || '-' }}</span>
                    </div>
                  </div>
                </Card>
              </Col>

              <Col span="12">
                <Card title="监管信息" icon="ios-home">
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="label">监区：</span>
                      <span class="value">{{ personInfo.area || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">监室：</span>
                      <span class="value">{{ personInfo.room || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">入所时间：</span>
                      <span class="value">{{ personInfo.entryDate || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">刑期：</span>
                      <span class="value">{{ personInfo.sentence || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">剩余刑期：</span>
                      <span class="value">{{ personInfo.remainingSentence || '-' }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">风险等级：</span>
                      <Tag :color="getRiskLevelColor(personInfo.riskLevel)">
                        {{ personInfo.riskLevel || '未评估' }}
                      </Tag>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>

            <Card title="管教信息" icon="ios-people" style="margin-top: 16px;">
              <Table :columns="policeColumns" :data="personInfo.policeList || []" size="small" />
            </Card>
          </TabPane>

          <!-- 健康档案 -->
          <TabPane label="健康档案" name="health" icon="ios-medical">
            <Row :gutter="24">
              <Col span="16">
                <Card title="健康记录" icon="ios-pulse">
                  <Timeline>
                    <TimelineItem v-for="record in healthRecords" :key="record.id" :color="getHealthColor(record.type)">
                      <p class="time">{{ record.date }}</p>
                      <p class="content">{{ record.description }}</p>
                      <p class="doctor" v-if="record.doctor">医生：{{ record.doctor }}</p>
                    </TimelineItem>
                  </Timeline>
                </Card>
              </Col>

              <Col span="8">
                <Card title="健康状态" icon="ios-heart">
                  <div class="health-status">
                    <div class="status-item">
                      <Icon type="ios-fitness" size="24" color="#52c41a" />
                      <div class="status-info">
                        <h4>当前状态</h4>
                        <p>{{ personInfo.healthStatus || '健康' }}</p>
                      </div>
                    </div>

                    <div class="status-item">
                      <Icon type="ios-calendar" size="24" color="#1890ff" />
                      <div class="status-info">
                        <h4>最后检查</h4>
                        <p>{{ personInfo.lastCheckDate || '-' }}</p>
                      </div>
                    </div>

                    <div class="status-item">
                      <Icon type="ios-alarm" size="24" color="#faad14" />
                      <div class="status-info">
                        <h4>下次检查</h4>
                        <p>{{ personInfo.nextCheckDate || '-' }}</p>
                      </div>
                    </div>
                  </div>

                  <Button type="primary" long style="margin-top: 16px;" @click="handleHealthCheck">
                    安排健康检查
                  </Button>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <!-- 家属信息 -->
          <TabPane label="家属信息" name="family" icon="ios-people">
            <div class="family-section">
              <div class="section-header">
                <h3>家属联系人</h3>
                <Button type="primary" icon="ios-add" @click="handleAddFamily">
                  添加家属
                </Button>
              </div>

              <Row :gutter="16">
                <Col span="8" v-for="family in familyMembers" :key="family.id">
                  <Card class="family-card">
                    <div class="family-info">
                      <Avatar :src="family.avatar" size="large" />
                      <div class="family-details">
                        <h4>{{ family.name }}</h4>
                        <p>{{ family.relationship }}</p>
                        <p>{{ family.phone }}</p>
                      </div>
                    </div>
                    <div class="family-actions">
                      <Button size="small" @click="handleCallFamily(family)">
                        <Icon type="ios-call" />
                        联系
                      </Button>
                      <Button size="small" @click="handleEditFamily(family)">
                        编辑
                      </Button>
                    </div>
                  </Card>
                </Col>
              </Row>
            </div>
          </TabPane>

          <!-- 操作历史 -->
          <TabPane label="操作历史" name="history" icon="ios-time">
            <Card>
              <div slot="title" style="display: flex; justify-content: space-between; align-items: center;">
                <span>操作记录</span>
                <Select v-model="historyFilter" style="width: 150px;" @on-change="loadHistory">
                  <Option value="">全部操作</Option>
                  <Option value="edit">信息修改</Option>
                  <Option value="transfer">转移</Option>
                  <Option value="health">健康检查</Option>
                  <Option value="visit">会见</Option>
                </Select>
              </div>

              <Table
                :columns="historyColumns"
                :data="operationHistory"
                :loading="historyLoading"
                size="small"
              />
            </Card>
          </TabPane>
        </Tabs>
      </div>
    </template>

    <!-- 页面底部 -->
    <template #footer>
      <div class="detail-footer">
        <div class="footer-left">
          <Button @click="handleBack">
            <Icon type="ios-arrow-back" />
            返回列表
          </Button>
        </div>

        <div class="footer-right">
          <Button @click="handleCancel" style="margin-right: 8px;">
            取消
          </Button>
          <Button type="primary" @click="handleSave" :loading="saving">
            <Icon type="ios-checkmark" />
            保存修改
          </Button>
        </div>
      </div>
    </template>
  </DynamicLayout>
</template>

<script>
export default {
  data() {
    return {
      // 人员信息
      personInfo: {},

      // 当前标签页
      activeTab: 'basic',

      // 健康记录
      healthRecords: [],

      // 家属成员
      familyMembers: [],

      // 操作历史
      operationHistory: [],
      historyFilter: '',
      historyLoading: false,

      // 状态
      saving: false,

      // 表格列配置
      policeColumns: [
        { title: '姓名', key: 'name', width: 100 },
        { title: '职务', key: 'position', width: 100 },
        { title: '联系电话', key: 'phone', width: 120 },
        { title: '负责类型', key: 'type', width: 100 }
      ],

      historyColumns: [
        { title: '操作时间', key: 'time', width: 150 },
        { title: '操作人', key: 'operator', width: 100 },
        { title: '操作类型', key: 'type', width: 100 },
        { title: '操作内容', key: 'content' },
        { title: '备注', key: 'remark', width: 150 }
      ]
    }
  },

  async created() {
    await this.loadPersonInfo()
    await this.loadHealthRecords()
    await this.loadFamilyMembers()
    await this.loadHistory()
  },

  methods: {
    // 加载人员信息
    async loadPersonInfo() {
      try {
        const personId = this.$route.params.id
        this.personInfo = await this.fetchPersonById(personId)
      } catch (error) {
        this.$Message.error('加载人员信息失败')
      }
    },

    // 加载健康记录
    async loadHealthRecords() {
      try {
        this.healthRecords = await this.fetchHealthRecords(this.personInfo.id)
      } catch (error) {
        this.$Message.error('加载健康记录失败')
      }
    },

    // 加载家属信息
    async loadFamilyMembers() {
      try {
        this.familyMembers = await this.fetchFamilyMembers(this.personInfo.id)
      } catch (error) {
        this.$Message.error('加载家属信息失败')
      }
    },

    // 加载操作历史
    async loadHistory() {
      this.historyLoading = true
      try {
        this.operationHistory = await this.fetchOperationHistory(this.personInfo.id, this.historyFilter)
      } catch (error) {
        this.$Message.error('加载操作历史失败')
      } finally {
        this.historyLoading = false
      }
    },

    // 获取风险等级颜色
    getRiskLevelColor(level) {
      const colorMap = {
        '高风险': 'error',
        '中风险': 'warning',
        '低风险': 'success',
        '未评估': 'default'
      }
      return colorMap[level] || 'default'
    },

    // 获取健康记录颜色
    getHealthColor(type) {
      const colorMap = {
        'normal': 'green',
        'warning': 'yellow',
        'danger': 'red'
      }
      return colorMap[type] || 'blue'
    },

    // 标签页切换
    handleTabChange(name) {
      this.activeTab = name
      if (name === 'history' && this.operationHistory.length === 0) {
        this.loadHistory()
      }
    },

    // 编辑
    handleEdit() {
      this.$router.push(`/person/edit/${this.personInfo.id}`)
    },

    // 打印
    handlePrint() {
      window.print()
    },

    // 分享
    handleShare() {
      this.$Message.info('分享功能开发中')
    },

    // 更多操作
    handleMoreAction(name) {
      switch (name) {
        case 'export':
          this.handleExport()
          break
        case 'history':
          this.activeTab = 'history'
          break
        case 'transfer':
          this.handleTransfer()
          break
        case 'archive':
          this.handleArchive()
          break
      }
    },

    // 健康检查
    handleHealthCheck() {
      this.$Message.info('安排健康检查')
    },

    // 添加家属
    handleAddFamily() {
      this.$Message.info('添加家属功能')
    },

    // 联系家属
    handleCallFamily(family) {
      this.$Message.info(`联系 ${family.name}: ${family.phone}`)
    },

    // 编辑家属
    handleEditFamily(family) {
      this.$Message.info(`编辑家属: ${family.name}`)
    },

    // 返回
    handleBack() {
      this.$router.go(-1)
    },

    // 取消
    handleCancel() {
      this.handleBack()
    },

    // 保存
    async handleSave() {
      this.saving = true
      try {
        await this.savePersonInfo(this.personInfo)
        this.$Message.success('保存成功')
      } catch (error) {
        this.$Message.error('保存失败')
      } finally {
        this.saving = false
      }
    },

    // 模拟API方法
    async fetchPersonById(id) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            id,
            name: '张三',
            code: 'P001',
            gender: '男',
            age: 35,
            idCard: '110101199001011234',
            hometown: '北京市',
            area: '一监区',
            room: '101室',
            entryDate: '2023-01-15',
            sentence: '5年',
            remainingSentence: '2年3个月',
            riskLevel: '低风险',
            healthStatus: '健康',
            lastCheckDate: '2024-01-10',
            nextCheckDate: '2024-04-10',
            isVip: false,
            policeList: [
              { name: '王警官', position: '主管', phone: '13800138001', type: '主要负责' },
              { name: '李警官', position: '副管', phone: '13800138002', type: '协助管理' }
            ]
          })
        }, 500)
      })
    },

    async fetchHealthRecords(personId) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve([
            { id: 1, date: '2024-01-10', type: 'normal', description: '定期健康检查，各项指标正常', doctor: '张医生' },
            { id: 2, date: '2023-10-15', type: 'warning', description: '血压偏高，建议注意饮食', doctor: '李医生' },
            { id: 3, date: '2023-07-20', type: 'normal', description: '入所体检，身体健康', doctor: '王医生' }
          ])
        }, 300)
      })
    },

    async fetchFamilyMembers(personId) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve([
            { id: 1, name: '张妻', relationship: '配偶', phone: '13900139001', avatar: '' },
            { id: 2, name: '张父', relationship: '父亲', phone: '13900139002', avatar: '' },
            { id: 3, name: '张子', relationship: '儿子', phone: '13900139003', avatar: '' }
          ])
        }, 300)
      })
    },

    async fetchOperationHistory(personId, filter) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve([
            { time: '2024-01-15 10:30', operator: '王警官', type: '信息修改', content: '更新联系方式', remark: '' },
            { time: '2024-01-10 14:20', operator: '张医生', type: '健康检查', content: '定期体检', remark: '正常' },
            { time: '2023-12-20 09:15', operator: '李警官', type: '会见', content: '家属会见', remark: '正常结束' }
          ])
        }, 500)
      })
    },

    async savePersonInfo(personInfo) {
      return new Promise(resolve => {
        setTimeout(resolve, 1000)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;

  .header-left {
    display: flex;
    align-items: center;

    .person-basic {
      margin-left: 16px;

      h2 {
        margin: 0 0 8px 0;
        color: #333;
      }

      .person-tags {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.detail-content {
  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    .info-item {
      display: flex;

      .label {
        color: #666;
        min-width: 80px;
      }

      .value {
        color: #333;
        font-weight: 500;
      }
    }
  }

  .health-status {
    .status-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .status-info {
        margin-left: 12px;

        h4 {
          margin: 0 0 4px 0;
          font-size: 14px;
          color: #333;
        }

        p {
          margin: 0;
          color: #666;
          font-size: 13px;
        }
      }
    }
  }

  .family-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        color: #333;
      }
    }

    .family-card {
      .family-info {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .family-details {
          margin-left: 12px;

          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
          }

          p {
            margin: 2px 0;
            font-size: 12px;
            color: #666;
          }
        }
      }

      .family-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}

.detail-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e8eef0;
}
</style>
```

### 4. 详情卡片布局 (detail-card)

**适用场景**: 谈话详情、档案查看、复杂信息展示页面

DetailCardLayout 是专为详情页面设计的布局组件，提供左侧固定信息面板 + 右侧多卡片展示 + 底部操作栏的布局结构。

#### 基础用法

```vue
<template>
  <DetailCardLayout
    :left-config="leftConfig"
    :right-cards="rightCards"
    :bottom-actions="bottomActions"
    :show-bottom-actions="true"
    :responsive="true"
    @card-action="handleCardAction"
    @bottom-action="handleBottomAction"
    @data-updated="handleDataUpdated"
  >
    <!-- 左侧人员信息面板 -->
    <template #left="{ data, updateData }">
      <div class="person-info-panel">
        <div class="avatar-section">
          <img :src="data.avatar || defaultAvatar" :alt="data.name" class="person-avatar" />
          <div class="person-name">
            <h3>{{ data.name || '未知' }}</h3>
            <div class="person-tags">
              <Tag :color="getRiskLevelColor(data.riskLevel)">
                {{ data.riskLevel || '未评估' }}
              </Tag>
              <Tag color="blue">{{ data.area || '未分配' }}</Tag>
            </div>
          </div>
        </div>

        <Divider />

        <div class="info-sections">
          <div class="info-section">
            <h4>基本信息</h4>
            <div class="info-list">
              <div class="info-row">
                <span class="label">编号：</span>
                <span class="value">{{ data.code || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="label">年龄：</span>
                <span class="value">{{ data.age || '-' }}岁</span>
              </div>
              <div class="info-row">
                <span class="label">监区：</span>
                <span class="value">{{ data.area || '-' }}</span>
              </div>
              <div class="info-row">
                <span class="label">入所时间：</span>
                <span class="value">{{ data.entryDate || '-' }}</span>
              </div>
            </div>
          </div>

          <div class="info-section">
            <h4>管教信息</h4>
            <div class="info-list">
              <div class="info-row">
                <span class="label">主管：</span>
                <span class="value">{{ getPoliceInfo(data.policeList, 0) }}</span>
              </div>
              <div class="info-row">
                <span class="label">副管：</span>
                <span class="value">{{ getPoliceInfo(data.policeList, 1) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 子组件可以更新数据 -->
        <div class="update-actions" style="margin-top: 16px;">
          <Button size="small" @click="() => updateData({ lastViewed: new Date().toLocaleTimeString() })">
            更新查看时间
          </Button>
        </div>

        <div v-if="data.lastViewed" class="last-viewed">
          <Alert>
            <span slot="desc">最后查看：{{ data.lastViewed }}</span>
          </Alert>
        </div>
      </div>
    </template>

    <!-- 谈话基本信息卡片 -->
    <template #talk-basic="{ card, data, updateData, updateCard }">
      <div class="talk-basic-card">
        <Table :columns="talkBasicColumns" :data="talkBasicData" size="small" />

        <div class="card-actions" style="margin-top: 16px;">
          <Button @click="() => updateData({ refreshTime: new Date().toLocaleTimeString() })" size="small">
            刷新数据
          </Button>
          <Button @click="() => updateCard({ title: '基本信息 (已更新)' })" size="small" style="margin-left: 8px;">
            更新标题
          </Button>
        </div>

        <div v-if="data.refreshTime" style="margin-top: 8px;">
          <Tag>最后刷新：{{ data.refreshTime }}</Tag>
        </div>
      </div>
    </template>

    <!-- 操作功能卡片 -->
    <template #talk-actions="{ card, data, updateData }">
      <div class="talk-actions-card">
        <Row :gutter="16">
          <Col span="12">
            <div class="action-item" @click="handleViewVideo">
              <div class="action-icon">
                <Icon type="ios-videocam" size="32" color="#5b8ff9" />
              </div>
              <div class="action-content">
                <h4>查看录像</h4>
                <p>观看谈话过程录音录像</p>
                <Tag v-if="data.hasVideo" color="success">有录像</Tag>
                <Tag v-else color="default">无录像</Tag>
              </div>
            </div>
          </Col>

          <Col span="12">
            <div class="action-item" @click="handleViewRecord">
              <div class="action-icon">
                <Icon type="ios-document-outline" size="32" color="#52c41a" />
              </div>
              <div class="action-content">
                <h4>查看笔录</h4>
                <p>查看谈话详细记录内容</p>
                <Tag v-if="data.hasRecord" color="success">有笔录</Tag>
                <Tag v-else color="default">无笔录</Tag>
              </div>
            </div>
          </Col>
        </Row>

        <Divider />

        <div class="action-stats">
          <Row :gutter="16">
            <Col span="8">
              <Statistic title="录像时长" :value="data.videoDuration || 0" suffix="分钟" />
            </Col>
            <Col span="8">
              <Statistic title="笔录字数" :value="data.recordWords || 0" suffix="字" />
            </Col>
            <Col span="8">
              <Statistic title="文件大小" :value="data.fileSize || 0" suffix="MB" />
            </Col>
          </Row>
        </div>
      </div>
    </template>

    <!-- 评估结论卡片 -->
    <template #assessment="{ card, data, updateData }">
      <div class="assessment-card">
        <div class="assessment-item">
          <h4 class="assessment-title">
            <Icon type="ios-analytics" color="#5b8ff9" />
            心理评估结论
          </h4>
          <div class="assessment-content">
            <Input
              v-model="assessmentData.psychology"
              type="textarea"
              :rows="3"
              placeholder="请输入心理评估结论"
              @on-change="() => updateData({ psychology: assessmentData.psychology })"
            />
          </div>
        </div>

        <div class="assessment-item">
          <h4 class="assessment-title">
            <Icon type="ios-people" color="#52c41a" />
            协作建议
          </h4>
          <div class="assessment-content">
            <Input
              v-model="assessmentData.collaboration"
              type="textarea"
              :rows="3"
              placeholder="请输入协作建议"
              @on-change="() => updateData({ collaboration: assessmentData.collaboration })"
            />
          </div>
        </div>

        <div class="assessment-actions" style="margin-top: 16px;">
          <Button type="primary" @click="saveAssessment">保存评估</Button>
          <Button @click="resetAssessment" style="margin-left: 8px;">重置</Button>
        </div>
      </div>
    </template>
  </DetailCardLayout>
</template>

<script>
export default {
  props: {
    // 接收路由参数或父组件传递的数据
    talkId: {
      type: [String, Number],
      default: null
    }
  },

  data() {
    return {
      // 左侧面板配置
      leftConfig: {
        width: '400px',
        title: '被监管人员信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true,
        data: {
          name: '张三',
          code: 'P001',
          age: 35,
          area: '一监区',
          entryDate: '2023-01-15',
          riskLevel: '低风险',
          avatar: '',
          policeList: [
            { name: '王警官', position: '主管' },
            { name: '李警官', position: '副管' }
          ]
        }
      },

      // 右侧卡片配置
      rightCards: [
        {
          name: 'talk-basic',
          title: '谈话基本信息',
          icon: 'ios-chatbubbles',
          iconColor: '#5b8ff9',
          slot: 'talk-basic',
          data: {
            refreshTime: null
          },
          actions: [
            { name: 'refresh', label: '刷新', type: 'default', icon: 'ios-refresh' }
          ]
        },
        {
          name: 'talk-actions',
          title: '谈话内容操作',
          icon: 'ios-options',
          iconColor: '#52c41a',
          slot: 'talk-actions',
          data: {
            hasVideo: true,
            hasRecord: true,
            videoDuration: 90,
            recordWords: 1500,
            fileSize: 256
          }
        },
        {
          name: 'assessment',
          title: '评估结论',
          icon: 'ios-analytics',
          iconColor: '#faad14',
          slot: 'assessment',
          data: {
            psychology: '',
            collaboration: ''
          }
        }
      ],

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        },
        {
          name: 'save',
          label: '保存修改',
          type: 'primary',
          icon: 'ios-checkmark'
        }
      ],

      // 默认头像
      defaultAvatar: require('@/assets/images/default-avatar.png'),

      // 谈话基本信息表格配置
      talkBasicColumns: [
        { title: '项目', key: 'label', width: 120 },
        { title: '内容', key: 'value' }
      ],

      // 谈话基本信息数据
      talkBasicData: [
        { label: '谈话时间', value: '2024-01-15 09:00:00' },
        { label: '结束时间', value: '2024-01-15 10:30:00' },
        { label: '谈话民警', value: '李警官' },
        { label: '谈话类型', value: '日常谈话' },
        { label: '谈话原因', value: '定期谈话' },
        { label: '谈话地点', value: '谈话室1' },
        { label: '谈话状态', value: '已完成' },
        { label: '持续时长', value: '1小时30分钟' }
      ],

      // 评估数据
      assessmentData: {
        psychology: '被谈话人情绪稳定，配合度较高，能够如实反映自己的思想状况。',
        collaboration: '建议继续保持定期谈话，关注其思想动态变化。'
      }
    }
  },

  created() {
    // 如果有传入的谈话ID，加载对应数据
    if (this.talkId) {
      this.loadTalkData(this.talkId)
    }
  },

  methods: {
    // 获取风险等级颜色
    getRiskLevelColor(level) {
      const colorMap = {
        '高风险': 'error',
        '中风险': 'warning',
        '低风险': 'success',
        '未评估': 'default'
      }
      return colorMap[level] || 'default'
    },

    // 获取管教信息
    getPoliceInfo(policeList, index) {
      return policeList && policeList[index]
        ? policeList[index].name
        : '-'
    },

    // 处理卡片操作
    handleCardAction(event) {
      const { action, card, index } = event
      console.log(`卡片操作: ${action.name} - ${card.name}`)

      if (action.name === 'refresh') {
        this.refreshCardData(card.name)
      }
    },

    // 处理底部操作
    handleBottomAction(event) {
      const { action } = event

      if (action.name === 'back') {
        this.$emit('on_show_table') // 返回列表
      } else if (action.name === 'save') {
        this.saveAllData()
      }
    },

    // 处理数据更新
    handleDataUpdated(event) {
      console.log('数据更新:', event)
      // 可以在这里处理数据变化的副作用
    },

    // 查看录像
    handleViewVideo() {
      this.$Message.info('打开录像播放器')
      // 实际项目中打开录像播放模态框
    },

    // 查看笔录
    handleViewRecord() {
      this.$Message.info('打开笔录查看器')
      // 实际项目中打开笔录查看模态框
    },

    // 保存评估
    saveAssessment() {
      this.$Message.success('评估结论已保存')
    },

    // 重置评估
    resetAssessment() {
      this.assessmentData = {
        psychology: '',
        collaboration: ''
      }
    },

    // 刷新卡片数据
    refreshCardData(cardName) {
      this.$Message.info(`刷新 ${cardName} 数据`)
      // 实际项目中重新加载对应卡片的数据
    },

    // 保存所有数据
    async saveAllData() {
      try {
        // 获取所有数据
        const allData = this.$refs.detailLayout?.getAllData()

        // 调用保存API
        await this.saveTalkData(allData)

        this.$Message.success('数据保存成功')
      } catch (error) {
        this.$Message.error('数据保存失败')
      }
    },

    // 加载谈话数据
    async loadTalkData(talkId) {
      try {
        // 实际项目中调用API加载数据
        const talkData = await this.fetchTalkById(talkId)

        // 更新左侧配置数据
        this.leftConfig.data = { ...this.leftConfig.data, ...talkData.person }

        // 更新卡片数据
        this.rightCards.forEach(card => {
          if (talkData[card.name]) {
            card.data = { ...card.data, ...talkData[card.name] }
          }
        })

      } catch (error) {
        this.$Message.error('加载谈话数据失败')
      }
    },

    // 模拟API方法
    async fetchTalkById(id) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            person: {
              name: '张三',
              code: 'P001',
              // ... 其他人员信息
            },
            'talk-basic': {
              // 谈话基本信息
            },
            'talk-actions': {
              hasVideo: true,
              hasRecord: true,
              // ... 其他操作数据
            },
            assessment: {
              psychology: '评估内容...',
              collaboration: '建议内容...'
            }
          })
        }, 500)
      })
    },

    async saveTalkData(data) {
      return new Promise(resolve => {
        setTimeout(resolve, 1000)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.person-info-panel {
  .avatar-section {
    text-align: center;
    margin-bottom: 20px;

    .person-avatar {
      width: 120px;
      height: 150px;
      border-radius: 8px;
      object-fit: cover;
      border: 3px solid #e8f4fd;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .person-name {
      margin-top: 12px;

      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }

      .person-tags {
        display: flex;
        justify-content: center;
        gap: 8px;
      }
    }
  }

  .info-sections {
    .info-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #34495e;
        padding-bottom: 8px;
        border-bottom: 2px solid #ecf0f1;
      }

      .info-list {
        .info-row {
          display: flex;
          margin-bottom: 8px;

          .label {
            color: #7f8c8d;
            font-size: 13px;
            width: 80px;
            flex-shrink: 0;
          }

          .value {
            color: #2c3e50;
            font-size: 13px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .last-viewed {
    margin-top: 12px;
  }
}

.talk-actions-card {
  .action-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 2px solid #e8f4fd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 16px;

    &:hover {
      border-color: #5b8ff9;
      box-shadow: 0 4px 12px rgba(91, 143, 249, 0.15);
      transform: translateY(-2px);
    }

    .action-icon {
      margin-right: 12px;
      flex-shrink: 0;
    }

    .action-content {
      flex: 1;

      h4 {
        margin: 0 0 4px 0;
        font-size: 14px;
        font-weight: 600;
        color: #2c3e50;
      }

      p {
        margin: 0 0 8px 0;
        font-size: 12px;
        color: #7f8c8d;
        line-height: 1.4;
      }
    }
  }

  .action-stats {
    margin-top: 16px;
  }
}

.assessment-card {
  .assessment-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .assessment-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #34495e;
    }

    .assessment-content {
      .ivu-input {
        border-radius: 6px;
      }
    }
  }

  .assessment-actions {
    text-align: right;
  }
}
</style>
```

#### leftConfig 详细配置

leftConfig 用于配置左侧固定信息面板的显示和行为：

```javascript
leftConfig: {
  // 面板宽度
  width: '400px',                    // 展开状态宽度，支持 px、%、vw 等单位
  collapsedWidth: '60px',            // 收缩状态宽度

  // 头部配置
  title: '被监管人员信息',            // 面板标题
  icon: 'ios-contact',               // 标题图标
  iconColor: '#5b8ff9',              // 图标颜色
  iconSize: 20,                      // 图标大小
  showHeader: true,                  // 是否显示头部

  // 收缩功能配置
  collapsible: true,                 // 是否启用收缩功能，默认 true
  defaultCollapsed: false,           // 默认是否收缩，默认 false

  // 数据配置
  data: {
    // 传递给左侧插槽的数据
    name: '张三',
    code: 'P001',
    age: 35,
    area: '一监区',
    avatar: 'path/to/avatar.jpg',    // 头像URL，收缩时会显示
    // ... 更多数据
  },

  // 动态组件配置（可选）
  component: {
    name: 'PersonInfoComponent',     // 组件名称
    props: {                         // 组件属性
      showAvatar: true,
      editable: false
    },
    events: {                        // 组件事件
      'on-edit': 'handleEdit',
      'on-update': 'handleUpdate'
    }
  },

  // 样式配置（可选）
  className: 'custom-left-panel',    // 自定义样式类
  style: {                           // 内联样式
    background: '#f8fafc'
  }
}
```

#### 收缩展开功能

DetailCardLayout 支持左侧面板的收缩和展开功能：

**1. 基础收缩功能**

```vue
<template>
  <DetailCardLayout
    :left-config="{
      width: '400px',
      collapsedWidth: '60px',
      collapsible: true,
      title: '人员信息'
    }"
    @collapse-change="handleCollapseChange"
  >
    <!-- 展开状态内容 -->
    <template #left="{ data, updateData, isCollapsed }">
      <div v-if="!isCollapsed">
        <!-- 完整的人员信息展示 -->
        <PersonDetailInfo :person="data" />
      </div>
    </template>

    <!-- 收缩状态内容（可选，如果不提供会使用默认的简化显示） -->
    <template #left-collapsed="{ data, toggleCollapse }">
      <div class="collapsed-content">
        <Avatar :src="data.avatar" @click="toggleCollapse" />
        <p>{{ data.name.charAt(0) }}</p>
      </div>
    </template>
  </DetailCardLayout>
</template>

<script>
export default {
  methods: {
    handleCollapseChange(event) {
      console.log('收缩状态变化:', event.isCollapsed)
      console.log('当前宽度:', event.width)
    }
  }
}
</script>
```

**2. 程序控制收缩状态**

```vue
<template>
  <div>
    <Button @click="togglePanel">
      {{ isCollapsed ? '展开' : '收缩' }}面板
    </Button>

    <DetailCardLayout
      ref="detailLayout"
      :left-config="leftConfig"
      @collapse-change="handleCollapseChange"
    >
      <!-- 内容 -->
    </DetailCardLayout>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isCollapsed: false,
      leftConfig: {
        width: '400px',
        collapsedWidth: '60px',
        collapsible: true
      }
    }
  },

  methods: {
    // 切换收缩状态
    togglePanel() {
      this.$refs.detailLayout.toggleCollapse()
    },

    // 设置收缩状态
    setCollapsed(collapsed) {
      this.$refs.detailLayout.setCollapsed(collapsed)
    },

    // 获取当前收缩状态
    getCurrentState() {
      return this.$refs.detailLayout.getCollapsed()
    },

    // 处理状态变化
    handleCollapseChange(event) {
      this.isCollapsed = event.isCollapsed

      // 可以在这里处理布局变化的副作用
      // 比如通知其他组件重新计算尺寸
      this.$nextTick(() => {
        this.$emit('layout-changed', event)
      })
    }
  }
}
</script>
```

**3. 自定义收缩内容**

```vue
<template>
  <DetailCardLayout>
    <!-- 展开状态 - 完整信息 -->
    <template #left="{ data, updateData }">
      <div class="full-person-info">
        <div class="avatar-section">
          <img :src="data.avatar" :alt="data.name" />
          <h3>{{ data.name }}</h3>
          <div class="tags">
            <Tag color="success">{{ data.status }}</Tag>
            <Tag color="blue">{{ data.area }}</Tag>
          </div>
        </div>

        <div class="info-details">
          <div class="info-group">
            <h4>基本信息</h4>
            <p>编号：{{ data.code }}</p>
            <p>年龄：{{ data.age }}岁</p>
            <p>监区：{{ data.area }}</p>
          </div>

          <div class="info-group">
            <h4>管教信息</h4>
            <p>主管：{{ data.manager }}</p>
            <p>副管：{{ data.assistant }}</p>
          </div>
        </div>
      </div>
    </template>

    <!-- 收缩状态 - 简化信息 -->
    <template #left-collapsed="{ data, toggleCollapse }">
      <div class="collapsed-person-info">
        <!-- 头像 -->
        <div class="collapsed-avatar" @click="toggleCollapse">
          <img :src="data.avatar" :alt="data.name" />
        </div>

        <!-- 姓名首字母 -->
        <div class="collapsed-name" @click="toggleCollapse">
          {{ data.name.charAt(0) }}
        </div>

        <!-- 状态指示器 -->
        <div class="status-indicator" :class="getStatusClass(data.status)"></div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
          <Tooltip content="查看详情" placement="right">
            <Button type="text" size="small" @click="toggleCollapse">
              <Icon type="ios-information-circle" />
            </Button>
          </Tooltip>

          <Tooltip content="快速编辑" placement="right">
            <Button type="text" size="small" @click="quickEdit">
              <Icon type="ios-create" />
            </Button>
          </Tooltip>
        </div>
      </div>
    </template>
  </DetailCardLayout>
</template>

<style lang="less" scoped>
.collapsed-person-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px 8px;

  .collapsed-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .collapsed-name {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #e6f7ff;
      color: #1890ff;
    }
  }

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &.healthy { background: #52c41a; }
    &.sick { background: #faad14; }
    &.serious { background: #ff4d4f; }
  }

  .quick-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
}
</style>
```

#### 收缩功能事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `collapse-change` | 收缩状态变化 | `{ isCollapsed: Boolean, width: String }` |

#### 收缩功能方法

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| `toggleCollapse()` | 切换收缩状态 | - | - |
| `setCollapsed(collapsed)` | 设置收缩状态 | `collapsed: Boolean` | - |
| `getCollapsed()` | 获取当前收缩状态 | - | `Boolean` |
```

#### rightCards 详细配置

rightCards 是一个数组，每个元素配置一个右侧卡片：

```javascript
rightCards: [
  {
    // 基础配置
    name: 'basic-info',                    // 卡片唯一标识
    title: '基本信息',                     // 卡片标题
    icon: 'ios-information-circle',        // 标题图标
    iconColor: '#5b8ff9',                  // 图标颜色
    iconSize: 20,                          // 图标大小

    // 插槽配置
    slot: 'basic-info',                    // 对应的插槽名称

    // 显示配置
    showHeader: true,                      // 是否显示卡片头部
    className: 'custom-card',              // 自定义样式类

    // 数据配置
    data: {
      // 传递给卡片插槽的数据
      content: '卡片内容',
      lastUpdate: '2024-01-15'
    },

    // 头部操作按钮
    actions: [
      {
        name: 'refresh',                   // 按钮标识
        label: '刷新',                     // 按钮文本
        type: 'default',                   // 按钮类型
        icon: 'ios-refresh',               // 按钮图标
        size: 'small'                      // 按钮大小
      },
      {
        name: 'edit',
        label: '编辑',
        type: 'primary',
        icon: 'ios-create'
      }
    ],

    // 动态组件配置（可选，与插槽二选一）
    component: {
      name: 'BasicInfoTable',             // 组件名称

      // 内联组件定义
      template: `
        <div class="basic-info">
          <Table :columns="columns" :data="tableData" />
        </div>
      `,

      // 异步组件加载
      loader: () => import('@/components/BasicInfoTable.vue'),

      // 组件属性
      props: {
        columns: [
          { title: '项目', key: 'label' },
          { title: '内容', key: 'value' }
        ],
        bordered: true,
        size: 'small'
      },

      // 组件事件
      events: {
        'on-row-click': 'handleRowClick',
        'on-edit': 'handleEdit'
      },

      // 组件数据（可选）
      data() {
        return {
          localData: {}
        }
      },

      // 组件方法（可选）
      methods: {
        handleLocalAction() {
          // 组件内部方法
        }
      }
    },

    // 空状态配置
    placeholder: '请配置卡片内容',         // 空状态提示文本

    // 条件显示
    visible: true,                         // 是否显示该卡片
    condition: (data) => data.showCard     // 动态显示条件
  }
]
```

#### card.component 使用示例

**1. 表格组件示例**

```javascript
{
  name: 'data-table',
  title: '数据表格',
  component: {
    name: 'DataTable',
    template: `
      <div class="data-table-card">
        <div class="table-toolbar" v-if="showToolbar">
          <div class="toolbar-left">
            <h4>{{ tableTitle }}</h4>
            <Badge :count="tableData.length" style="margin-left: 8px;" />
          </div>
          <div class="toolbar-right">
            <Button @click="handleAdd" type="primary" size="small">新增</Button>
            <Button @click="handleRefresh" size="small">刷新</Button>
          </div>
        </div>

        <Table
          :columns="columns"
          :data="tableData"
          :loading="loading"
          size="small"
          stripe
          @on-row-click="handleRowClick"
        />

        <div class="table-footer" v-if="showPagination">
          <Page
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            size="small"
            @on-change="handlePageChange"
          />
        </div>
      </div>
    `,
    props: {
      tableTitle: { type: String, default: '数据列表' },
      columns: { type: Array, required: true },
      tableData: { type: Array, default: () => [] },
      loading: { type: Boolean, default: false },
      showToolbar: { type: Boolean, default: true },
      showPagination: { type: Boolean, default: true },
      pagination: {
        type: Object,
        default: () => ({ current: 1, pageSize: 10, total: 0 })
      }
    },
    methods: {
      handleAdd() {
        this.$emit('table-add')
      },
      handleRefresh() {
        this.$emit('table-refresh')
      },
      handleRowClick(row) {
        this.$emit('table-row-click', row)
      },
      handlePageChange(page) {
        this.$emit('table-page-change', page)
      }
    }
  },
  data: {
    tableTitle: '人员列表',
    columns: [
      { title: '姓名', key: 'name' },
      { title: '年龄', key: 'age' },
      { title: '监区', key: 'area' }
    ],
    tableData: [
      { name: '张三', age: 35, area: '一监区' },
      { name: '李四', age: 42, area: '二监区' }
    ],
    loading: false,
    pagination: { current: 1, pageSize: 10, total: 2 }
  }
}
```

**2. 表单组件示例**

```javascript
{
  name: 'edit-form',
  title: '编辑表单',
  component: {
    name: 'EditForm',
    template: `
      <div class="edit-form-card">
        <Form :model="formData" :rules="formRules" ref="form" :label-width="80">
          <FormItem label="姓名" prop="name">
            <Input v-model="formData.name" placeholder="请输入姓名" />
          </FormItem>

          <FormItem label="年龄" prop="age">
            <InputNumber v-model="formData.age" :min="1" :max="100" />
          </FormItem>

          <FormItem label="监区" prop="area">
            <Select v-model="formData.area" placeholder="请选择监区">
              <Option value="1">一监区</Option>
              <Option value="2">二监区</Option>
              <Option value="3">三监区</Option>
            </Select>
          </FormItem>

          <FormItem label="备注" prop="remark">
            <Input v-model="formData.remark" type="textarea" :rows="3" />
          </FormItem>

          <FormItem>
            <Button @click="handleSubmit" type="primary" :loading="submitting">
              保存
            </Button>
            <Button @click="handleReset" style="margin-left: 8px;">
              重置
            </Button>
          </FormItem>
        </Form>
      </div>
    `,
    props: {
      initialData: { type: Object, default: () => ({}) },
      readonly: { type: Boolean, default: false }
    },
    data() {
      return {
        formData: { ...this.initialData },
        submitting: false,
        formRules: {
          name: [
            { required: true, message: '请输入姓名', trigger: 'blur' }
          ],
          age: [
            { required: true, type: 'number', message: '请输入年龄', trigger: 'blur' }
          ],
          area: [
            { required: true, message: '请选择监区', trigger: 'change' }
          ]
        }
      }
    },
    methods: {
      handleSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.submitting = true
            this.$emit('form-submit', this.formData)

            // 模拟提交
            setTimeout(() => {
              this.submitting = false
              this.$Message.success('保存成功')
            }, 1000)
          }
        })
      },
      handleReset() {
        this.$refs.form.resetFields()
        this.formData = { ...this.initialData }
        this.$emit('form-reset')
      }
    },
    watch: {
      initialData: {
        handler(newVal) {
          this.formData = { ...newVal }
        },
        deep: true
      }
    }
  },
  data: {
    initialData: {
      name: '张三',
      age: 35,
      area: '1',
      remark: ''
    }
  }
}
```

**3. 图表组件示例**

```javascript
{
  name: 'chart-display',
  title: '数据图表',
  component: {
    name: 'ChartDisplay',
    template: `
      <div class="chart-display-card">
        <div class="chart-header">
          <h4>{{ chartTitle }}</h4>
          <Select v-model="chartType" @on-change="handleChartTypeChange" style="width: 120px;">
            <Option value="line">折线图</Option>
            <Option value="bar">柱状图</Option>
            <Option value="pie">饼图</Option>
          </Select>
        </div>

        <div class="chart-container" :style="{ height: chartHeight + 'px' }">
          <!-- 这里可以集成 ECharts、Chart.js 等图表库 -->
          <div class="chart-placeholder">
            <Icon type="ios-analytics" size="48" color="#ddd" />
            <p>{{ chartType }} 图表</p>
            <p style="font-size: 12px; color: #999;">
              数据点: {{ chartData.length }}
            </p>
          </div>
        </div>

        <div class="chart-footer">
          <Button @click="handleExport" size="small">导出图表</Button>
          <Button @click="handleRefresh" size="small">刷新数据</Button>
        </div>
      </div>
    `,
    props: {
      chartTitle: { type: String, default: '数据图表' },
      chartData: { type: Array, default: () => [] },
      chartHeight: { type: Number, default: 300 },
      defaultType: { type: String, default: 'line' }
    },
    data() {
      return {
        chartType: this.defaultType
      }
    },
    methods: {
      handleChartTypeChange(type) {
        this.$emit('chart-type-change', type)
      },
      handleExport() {
        this.$emit('chart-export', { type: this.chartType, data: this.chartData })
      },
      handleRefresh() {
        this.$emit('chart-refresh')
      }
    }
  },
  data: {
    chartTitle: '月度统计',
    chartData: [
      { month: '1月', value: 100 },
      { month: '2月', value: 120 },
      { month: '3月', value: 90 }
    ],
    chartHeight: 250
  }
}
```

#### bottomActions 详细配置

bottomActions 配置底部固定操作栏的按钮：

```javascript
bottomActions: [
  {
    name: 'back',                         // 按钮唯一标识
    label: '返回',                        // 按钮文本
    type: 'default',                      // 按钮类型: default, primary, success, warning, error
    size: 'large',                        // 按钮大小: small, default, large
    icon: 'ios-arrow-back',               // 按钮图标
    loading: false,                       // 加载状态
    disabled: false,                      // 禁用状态

    // 样式配置
    style: {                              // 内联样式
      marginRight: '16px'
    },
    className: 'custom-button',           // 自定义样式类

    // 条件显示
    visible: true,                        // 是否显示
    condition: (data) => data.canGoBack,  // 动态显示条件

    // 确认配置
    confirm: {                            // 点击确认配置
      title: '确认返回',
      content: '确定要返回吗？未保存的数据将丢失。',
      okText: '确定',
      cancelText: '取消'
    }
  },
  {
    name: 'save',
    label: '保存修改',
    type: 'primary',
    icon: 'ios-checkmark',
    loading: false,

    // 快捷键配置
    shortcut: 'Ctrl+S',                   // 快捷键

    // 权限配置
    permission: 'edit',                   // 所需权限

    // 验证配置
    validate: true                        // 是否需要验证
  }
]
```

### 5. 卡片网格布局 (card-grid)

**适用场景**: 人员卡片展示、设备列表、统计面板

```vue
<template>
  <DynamicLayout
    layout-type="card-grid"
    :layout-config="{ responsive: true, columns: 'auto-fill' }"
  >
    <!-- 页面标题 -->
    <template #title>
      <div class="page-title">
        <h2>人员卡片展示</h2>
        <p>以卡片形式浏览人员信息</p>
      </div>
    </template>

    <!-- 筛选条件 -->
    <template #filters>
      <div class="filter-bar">
        <Select v-model="filters.area" placeholder="选择监区" style="width: 120px;">
          <Option value="">全部监区</Option>
          <Option value="1">一监区</Option>
          <Option value="2">二监区</Option>
          <Option value="3">三监区</Option>
        </Select>

        <Select v-model="filters.status" placeholder="选择状态" style="width: 120px; margin-left: 8px;">
          <Option value="">全部状态</Option>
          <Option value="healthy">健康</Option>
          <Option value="sick">患病</Option>
          <Option value="serious">重病号</Option>
        </Select>

        <Input
          v-model="filters.search"
          placeholder="搜索姓名或编号"
          style="width: 200px; margin-left: 8px;"
          @on-enter="handleSearch"
        />

        <Button @click="handleSearch" type="primary" style="margin-left: 8px;">
          搜索
        </Button>
      </div>
    </template>

    <!-- 卡片网格 -->
    <template #grid>
      <div class="card-grid-container">
        <Row :gutter="16">
          <Col
            :xs="24" :sm="12" :md="8" :lg="6" :xl="4"
            v-for="person in filteredPersons"
            :key="person.id"
          >
            <Card class="person-card" @click.native="handleCardClick(person)">
              <div class="card-content">
                <div class="avatar-section">
                  <Avatar :src="person.avatar" size="large" />
                  <div class="status-indicator" :class="person.status"></div>
                </div>

                <div class="person-info">
                  <h4>{{ person.name }}</h4>
                  <p class="person-code">{{ person.code }}</p>
                  <p class="person-area">{{ person.area }}</p>
                </div>

                <div class="person-status">
                  <Tag :color="getStatusColor(person.status)">
                    {{ getStatusText(person.status) }}
                  </Tag>
                </div>

                <div class="card-actions">
                  <Button size="small" @click.stop="handleView(person)">查看</Button>
                  <Button size="small" @click.stop="handleEdit(person)">编辑</Button>
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        <!-- 加载更多 -->
        <div class="load-more" v-if="hasMore">
          <Button @click="loadMore" :loading="loading" long>
            加载更多
          </Button>
        </div>
      </div>
    </template>
  </DynamicLayout>
</template>

<script>
export default {
  data() {
    return {
      // 筛选条件
      filters: {
        area: '',
        status: '',
        search: ''
      },

      // 人员数据
      persons: [],
      loading: false,
      hasMore: true,

      // 分页
      pagination: {
        current: 1,
        pageSize: 20
      }
    }
  },

  computed: {
    // 过滤后的人员列表
    filteredPersons() {
      return this.persons.filter(person => {
        const matchArea = !this.filters.area || person.area === this.filters.area
        const matchStatus = !this.filters.status || person.status === this.filters.status
        const matchSearch = !this.filters.search ||
          person.name.includes(this.filters.search) ||
          person.code.includes(this.filters.search)

        return matchArea && matchStatus && matchSearch
      })
    }
  },

  mounted() {
    this.loadPersons()
  },

  methods: {
    // 加载人员数据
    async loadPersons() {
      this.loading = true
      try {
        const response = await this.fetchPersons({
          page: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.filters
        })

        if (this.pagination.current === 1) {
          this.persons = response.data
        } else {
          this.persons.push(...response.data)
        }

        this.hasMore = response.data.length === this.pagination.pageSize
      } catch (error) {
        this.$Message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadPersons()
    },

    // 加载更多
    loadMore() {
      this.pagination.current++
      this.loadPersons()
    },

    // 卡片点击
    handleCardClick(person) {
      this.$router.push(`/person/detail/${person.id}`)
    },

    // 查看
    handleView(person) {
      this.$router.push(`/person/view/${person.id}`)
    },

    // 编辑
    handleEdit(person) {
      this.$router.push(`/person/edit/${person.id}`)
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        'healthy': 'success',
        'sick': 'warning',
        'serious': 'error'
      }
      return colorMap[status] || 'default'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'healthy': '健康',
        'sick': '患病',
        'serious': '重病号'
      }
      return textMap[status] || '未知'
    },

    // 模拟API
    async fetchPersons(params) {
      return new Promise(resolve => {
        setTimeout(() => {
          const mockData = Array.from({ length: params.pageSize }, (_, index) => ({
            id: (params.page - 1) * params.pageSize + index + 1,
            name: `人员${(params.page - 1) * params.pageSize + index + 1}`,
            code: `P${String((params.page - 1) * params.pageSize + index + 1).padStart(3, '0')}`,
            area: ['一监区', '二监区', '三监区'][index % 3],
            status: ['healthy', 'sick', 'serious'][index % 3],
            avatar: ''
          }))

          resolve({ data: mockData })
        }, 500)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.page-title {
  text-align: center;
  margin-bottom: 24px;

  h2 {
    margin: 0 0 8px 0;
    color: #333;
  }

  p {
    margin: 0;
    color: #666;
  }
}

.filter-bar {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 8px;
}

.card-grid-container {
  .person-card {
    cursor: pointer;
    transition: all 0.3s;
    height: 280px;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    }

    .card-content {
      text-align: center;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .avatar-section {
        position: relative;
        margin-bottom: 16px;

        .status-indicator {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          border: 2px solid white;

          &.healthy { background: #52c41a; }
          &.sick { background: #faad14; }
          &.serious { background: #ff4d4f; }
        }
      }

      .person-info {
        margin-bottom: 12px;

        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          color: #333;
        }

        p {
          margin: 2px 0;
          font-size: 12px;
          color: #666;

          &.person-code {
            font-weight: 500;
          }
        }
      }

      .person-status {
        margin-bottom: 12px;
      }

      .card-actions {
        display: flex;
        justify-content: center;
        gap: 8px;
      }
    }
  }

  .load-more {
    margin-top: 24px;
    text-align: center;
  }
}
</style>
```

### 6. 工作台布局 (dashboard)

**适用场景**: 数据看板、统计面板、监控中心

```vue
<template>
  <DynamicLayout
    layout-type="dashboard"
    :layout-config="{ responsive: true, sidebarCollapsible: true }"
  >
    <!-- 侧边栏导航 -->
    <template #sidebar>
      <div class="dashboard-sidebar">
        <div class="sidebar-header">
          <h3>数据看板</h3>
        </div>

        <Menu
          :active-name="activeMenu"
          theme="light"
          width="auto"
          @on-select="handleMenuSelect"
        >
          <MenuItem name="overview">
            <Icon type="ios-analytics" />
            总览
          </MenuItem>
          <MenuItem name="persons">
            <Icon type="ios-people" />
            人员统计
          </MenuItem>
          <MenuItem name="health">
            <Icon type="ios-medical" />
            健康统计
          </MenuItem>
          <MenuItem name="areas">
            <Icon type="ios-home" />
            监区分析
          </MenuItem>
          <MenuItem name="trends">
            <Icon type="ios-trending-up" />
            趋势分析
          </MenuItem>
          <MenuItem name="alerts">
            <Icon type="ios-notifications" />
            预警信息
            <Badge :count="alertCount" style="margin-left: 8px;" />
          </MenuItem>
        </Menu>
      </div>
    </template>

    <!-- 顶部工具栏 -->
    <template #header>
      <div class="dashboard-header">
        <div class="header-left">
          <h2>{{ getCurrentMenuTitle() }}</h2>
          <div class="breadcrumb">
            <Breadcrumb>
              <BreadcrumbItem>数据看板</BreadcrumbItem>
              <BreadcrumbItem>{{ getCurrentMenuTitle() }}</BreadcrumbItem>
            </Breadcrumb>
          </div>
        </div>

        <div class="header-right">
          <div class="date-range">
            <DatePicker
              v-model="dateRange"
              type="daterange"
              placeholder="选择日期范围"
              @on-change="handleDateChange"
            />
          </div>

          <div class="header-actions">
            <Button @click="handleExport" icon="ios-download-outline">
              导出报告
            </Button>
            <Button @click="handleRefresh" icon="ios-refresh" :loading="refreshing">
              刷新数据
            </Button>
            <Button @click="handleFullscreen" icon="ios-expand">
              全屏
            </Button>
          </div>
        </div>
      </div>
    </template>

    <!-- 主要内容区域 -->
    <template #content>
      <div class="dashboard-content">
        <!-- 总览页面 -->
        <div v-if="activeMenu === 'overview'" class="overview-section">
          <!-- 统计卡片 -->
          <Row :gutter="20" class="stats-row">
            <Col span="6" v-for="stat in overviewStats" :key="stat.key">
              <Card class="stat-card">
                <div class="stat-content">
                  <div class="stat-icon" :style="{ background: stat.color }">
                    <Icon :type="stat.icon" size="24" color="white" />
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ stat.value }}</div>
                    <div class="stat-label">{{ stat.label }}</div>
                    <div class="stat-change" :class="stat.trend">
                      <Icon :type="stat.trend === 'up' ? 'ios-arrow-up' : 'ios-arrow-down'" />
                      {{ stat.change }}%
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>

          <!-- 图表区域 -->
          <Row :gutter="20" class="charts-row">
            <Col span="16">
              <Card title="人员趋势分析">
                <div class="chart-container" style="height: 300px;">
                  <!-- 这里集成图表组件 -->
                  <div class="chart-placeholder">
                    <Icon type="ios-analytics" size="48" color="#ddd" />
                    <p>趋势图表</p>
                  </div>
                </div>
              </Card>
            </Col>

            <Col span="8">
              <Card title="状态分布">
                <div class="chart-container" style="height: 300px;">
                  <div class="chart-placeholder">
                    <Icon type="ios-pie" size="48" color="#ddd" />
                    <p>饼图</p>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </div>

        <!-- 人员统计页面 -->
        <div v-if="activeMenu === 'persons'" class="persons-section">
          <Row :gutter="20">
            <Col span="12">
              <Card title="人员分布">
                <Table :columns="personColumns" :data="personStats" size="small" />
              </Card>
            </Col>

            <Col span="12">
              <Card title="年龄分布">
                <div class="age-distribution">
                  <div v-for="age in ageDistribution" :key="age.range" class="age-item">
                    <div class="age-label">{{ age.range }}</div>
                    <div class="age-bar">
                      <div class="age-progress" :style="{ width: age.percentage + '%' }"></div>
                    </div>
                    <div class="age-value">{{ age.count }}人</div>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </div>

        <!-- 其他页面内容... -->
      </div>
    </template>
  </DynamicLayout>
</template>

<script>
export default {
  data() {
    return {
      // 当前菜单
      activeMenu: 'overview',

      // 日期范围
      dateRange: [],

      // 刷新状态
      refreshing: false,

      // 预警数量
      alertCount: 3,

      // 总览统计
      overviewStats: [
        {
          key: 'total',
          label: '总人数',
          value: 1250,
          icon: 'ios-people',
          color: '#5b8ff9',
          trend: 'up',
          change: 2.5
        },
        {
          key: 'healthy',
          label: '健康人数',
          value: 1180,
          icon: 'ios-checkmark-circle',
          color: '#52c41a',
          trend: 'up',
          change: 1.2
        },
        {
          key: 'sick',
          label: '患病人数',
          value: 65,
          icon: 'ios-warning',
          color: '#faad14',
          trend: 'down',
          change: 0.8
        },
        {
          key: 'serious',
          label: '重病号',
          value: 5,
          icon: 'ios-alert',
          color: '#ff4d4f',
          trend: 'down',
          change: 1.5
        }
      ],

      // 人员统计表格
      personColumns: [
        { title: '监区', key: 'area' },
        { title: '总人数', key: 'total' },
        { title: '健康', key: 'healthy' },
        { title: '患病', key: 'sick' },
        { title: '重病', key: 'serious' }
      ],

      personStats: [
        { area: '一监区', total: 320, healthy: 300, sick: 18, serious: 2 },
        { area: '二监区', total: 280, healthy: 265, sick: 13, serious: 2 },
        { area: '三监区', total: 350, healthy: 330, sick: 19, serious: 1 },
        { area: '四监区', total: 300, healthy: 285, sick: 15, serious: 0 }
      ],

      // 年龄分布
      ageDistribution: [
        { range: '18-25岁', count: 150, percentage: 12 },
        { range: '26-35岁', count: 380, percentage: 30.4 },
        { range: '36-45岁', count: 420, percentage: 33.6 },
        { range: '46-55岁', count: 250, percentage: 20 },
        { range: '56岁以上', count: 50, percentage: 4 }
      ]
    }
  },

  methods: {
    // 菜单选择
    handleMenuSelect(name) {
      this.activeMenu = name
      this.loadMenuData(name)
    },

    // 获取当前菜单标题
    getCurrentMenuTitle() {
      const titleMap = {
        'overview': '总览',
        'persons': '人员统计',
        'health': '健康统计',
        'areas': '监区分析',
        'trends': '趋势分析',
        'alerts': '预警信息'
      }
      return titleMap[this.activeMenu] || '总览'
    },

    // 日期变化
    handleDateChange(date) {
      this.loadCurrentData()
    },

    // 导出报告
    handleExport() {
      this.$Message.info('正在生成报告...')
    },

    // 刷新数据
    async handleRefresh() {
      this.refreshing = true
      try {
        await this.loadCurrentData()
        this.$Message.success('数据刷新成功')
      } catch (error) {
        this.$Message.error('数据刷新失败')
      } finally {
        this.refreshing = false
      }
    },

    // 全屏
    handleFullscreen() {
      if (document.fullscreenElement) {
        document.exitFullscreen()
      } else {
        document.documentElement.requestFullscreen()
      }
    },

    // 加载菜单数据
    async loadMenuData(menuName) {
      // 根据菜单加载对应数据
      console.log('加载菜单数据:', menuName)
    },

    // 加载当前数据
    async loadCurrentData() {
      // 根据当前菜单和日期范围加载数据
      return new Promise(resolve => {
        setTimeout(resolve, 1000)
      })
    }
  },

  mounted() {
    this.loadCurrentData()
  }
}
</script>

<style lang="less" scoped>
.dashboard-sidebar {
  .sidebar-header {
    padding: 20px 16px;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      margin: 0;
      color: #333;
    }
  }
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;

  .header-left {
    h2 {
      margin: 0 0 4px 0;
      color: #333;
    }

    .breadcrumb {
      font-size: 12px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.dashboard-content {
  .stats-row {
    margin-bottom: 20px;

    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
          }

          .stat-change {
            font-size: 12px;

            &.up { color: #52c41a; }
            &.down { color: #ff4d4f; }
          }
        }
      }
    }
  }

  .charts-row {
    .chart-container {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: #999;

      .chart-placeholder {
        text-align: center;

        p {
          margin: 8px 0 0 0;
        }
      }
    }
  }

  .age-distribution {
    .age-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .age-label {
        width: 80px;
        font-size: 12px;
        color: #666;
      }

      .age-bar {
        flex: 1;
        height: 8px;
        background: #f0f0f0;
        border-radius: 4px;
        margin: 0 12px;
        overflow: hidden;

        .age-progress {
          height: 100%;
          background: #5b8ff9;
          transition: width 0.3s;
        }
      }

      .age-value {
        width: 60px;
        text-align: right;
        font-size: 12px;
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
```

### 7. 动态组件使用

DynamicComponent 是核心的动态组件容器，支持异步加载、缓存管理和事件通信。

#### 基础用法

```vue
<template>
  <div>
    <!-- 基础动态组件 -->
    <DynamicComponent
      component-config="data-table"
      :data="tableData"
      @component-loaded="handleComponentLoaded"
      @component-error="handleComponentError"
    />

    <!-- 配置对象方式 -->
    <DynamicComponent
      :component-config="{
        name: 'form-builder',
        props: { fields: formFields },
        events: { 'on-submit': handleFormSubmit }
      }"
      :data="formData"
    />

    <!-- 异步组件 -->
    <DynamicComponent
      :component-config="{
        name: 'async-chart',
        loader: () => import('@/components/charts/LineChart.vue'),
        props: { height: 300 }
      }"
      :data="chartData"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      formData: {},
      chartData: {},
      formFields: [
        { name: 'name', label: '姓名', type: 'input', required: true },
        { name: 'age', label: '年龄', type: 'number', required: true }
      ]
    }
  },

  methods: {
    handleComponentLoaded(event) {
      console.log('组件加载完成:', event.name)
    },

    handleComponentError(error) {
      console.error('组件加载失败:', error)
      this.$Message.error(`组件加载失败: ${error.message}`)
    },

    handleFormSubmit(formData) {
      console.log('表单提交:', formData)
    }
  }
}
</script>
```

#### 组件注册

```javascript
// 在组件中注册
import { componentRegistry } from '@/components/bsp-layout'

// 注册单个组件
componentRegistry.register('my-component', () => import('./MyComponent.vue'), {
  alias: ['custom-comp'],
  description: '我的自定义组件',
  category: 'custom'
})

// 批量注册
componentRegistry.registerBatch({
  'data-table': () => import('@/components/DataTable.vue'),
  'form-builder': () => import('@/components/FormBuilder.vue'),
  'chart-display': () => import('@/components/ChartDisplay.vue')
})

// 注册内联组件
componentRegistry.register('inline-component', {
  template: '<div>{{ message }}</div>',
  props: ['message'],
  data() {
    return {
      localData: 'Hello'
    }
  }
})
```

### 8. 父子通信详解

BSP动态布局组件提供了完整的父子通信机制，支持数据传递、事件通信和方法调用。

#### 父传子 - 数据传递

**1. 通过 props 传递数据**

```vue
<template>
  <!-- 父组件 -->
  <DetailCardLayout
    :left-config="leftConfig"
    :right-cards="rightCards"
  >
    <template #left="{ data, updateData }">
      <!-- 子组件接收父组件数据 -->
      <ChildComponent
        :person-data="data"
        :parent-config="leftConfig"
        @update="updateData"
      />
    </template>
  </DetailCardLayout>
</template>

<script>
export default {
  data() {
    return {
      leftConfig: {
        data: {
          name: '张三',
          age: 35,
          area: '一监区'
        }
      },
      rightCards: [
        {
          name: 'info-card',
          data: {
            content: '卡片内容'
          }
        }
      ]
    }
  }
}
</script>
```

**2. 通过插槽作用域传递**

```vue
<template>
  <DetailCardLayout>
    <!-- 插槽作用域提供数据和方法 -->
    <template #card-slot="{ card, data, updateData, updateCard }">
      <div>
        <!-- 使用插槽提供的数据 -->
        <h4>{{ card.title }}</h4>
        <p>{{ data.content }}</p>

        <!-- 使用插槽提供的方法 -->
        <Button @click="() => updateData({ content: '新内容' })">
          更新数据
        </Button>
        <Button @click="() => updateCard({ title: '新标题' })">
          更新卡片
        </Button>
      </div>
    </template>
  </DetailCardLayout>
</template>
```

**3. 通过 DynamicComponent 传递**

```vue
<template>
  <DynamicComponent
    :component-config="{
      name: 'child-component',
      props: {
        parentData: parentData,
        config: componentConfig
      }
    }"
    :data="sharedData"
  />
</template>

<script>
export default {
  data() {
    return {
      parentData: { /* 父组件数据 */ },
      componentConfig: { /* 组件配置 */ },
      sharedData: { /* 共享数据 */ }
    }
  }
}
</script>
```

#### 子传父 - 事件通信

**1. 通过事件发射**

```vue
<!-- 子组件 -->
<template>
  <div>
    <Button @click="sendToParent">发送给父组件</Button>
  </div>
</template>

<script>
export default {
  methods: {
    sendToParent() {
      // 发射事件给父组件
      this.$emit('child-event', {
        action: 'update',
        data: { name: '新数据' },
        timestamp: Date.now()
      })
    }
  }
}
</script>

<!-- 父组件 -->
<template>
  <ChildComponent @child-event="handleChildEvent" />
</template>

<script>
export default {
  methods: {
    handleChildEvent(event) {
      console.log('收到子组件事件:', event)
      // 处理子组件传递的数据
    }
  }
}
</script>
```

**2. 通过插槽方法**

```vue
<template>
  <DetailCardLayout>
    <template #card-slot="{ updateData }">
      <ChildComponent
        :update-parent-data="updateData"
        @local-change="handleLocalChange"
      />
    </template>
  </DetailCardLayout>
</template>

<script>
// 子组件
export default {
  props: ['updateParentData'],
  methods: {
    handleButtonClick() {
      // 直接调用父组件提供的方法
      this.updateParentData({ newValue: 'updated' })

      // 或发射本地事件
      this.$emit('local-change', { data: 'changed' })
    }
  }
}
</script>
```

**3. 通过 DynamicComponent 事件**

```vue
<template>
  <DynamicComponent
    :component-config="componentConfig"
    @component-event="handleComponentEvent"
    @update:data="handleDataUpdate"
  />
</template>

<script>
export default {
  data() {
    return {
      componentConfig: {
        name: 'dynamic-child',
        events: {
          'custom-event': 'handleCustomEvent',
          'data-change': 'handleDataChange'
        }
      }
    }
  },

  methods: {
    handleComponentEvent(event) {
      // 处理组件事件
      console.log('组件事件:', event)
    },

    handleDataUpdate(newData) {
      // 处理数据更新
      console.log('数据更新:', newData)
    },

    handleCustomEvent(eventData) {
      // 处理自定义事件
      console.log('自定义事件:', eventData)
    }
  }
}
</script>
```

#### 双向数据绑定

**1. 使用 v-model 模式**

```vue
<!-- 父组件 -->
<template>
  <DynamicComponent
    :component-config="{
      name: 'input-component',
      props: { value: inputValue },
      events: { 'input': (val) => inputValue = val }
    }"
  />
</template>

<!-- 子组件 -->
<template>
  <Input
    :value="value"
    @input="$emit('input', $event.target.value)"
  />
</template>

<script>
export default {
  props: ['value']
}
</script>
```

**2. 使用 sync 修饰符模式**

```vue
<!-- 父组件 -->
<template>
  <ChildComponent
    :data.sync="sharedData"
    :config.sync="componentConfig"
  />
</template>

<!-- 子组件 -->
<template>
  <div>
    <Button @click="updateData">更新数据</Button>
    <Button @click="updateConfig">更新配置</Button>
  </div>
</template>

<script>
export default {
  props: ['data', 'config'],
  methods: {
    updateData() {
      this.$emit('update:data', { ...this.data, updated: true })
    },
    updateConfig() {
      this.$emit('update:config', { ...this.config, modified: true })
    }
  }
}
</script>
```

#### 方法调用

**1. 父组件调用子组件方法**

```vue
<template>
  <div>
    <Button @click="callChildMethod">调用子组件方法</Button>
    <DynamicComponent
      ref="dynamicChild"
      :component-config="componentConfig"
    />
  </div>
</template>

<script>
export default {
  methods: {
    callChildMethod() {
      // 获取子组件实例
      const childInstance = this.$refs.dynamicChild.getChildComponentInstance()

      // 调用子组件方法
      if (childInstance && childInstance.childMethod) {
        childInstance.childMethod('参数')
      }

      // 或使用 DynamicComponent 提供的方法
      this.$refs.dynamicChild.callChildMethod('childMethod', '参数')
    }
  }
}
</script>
```

**2. 子组件调用父组件方法**

```vue
<!-- 子组件 -->
<template>
  <Button @click="callParentMethod">调用父组件方法</Button>
</template>

<script>
export default {
  methods: {
    callParentMethod() {
      // 通过 $parent 调用
      if (this.$parent && this.$parent.parentMethod) {
        this.$parent.parentMethod('参数')
      }

      // 通过事件调用
      this.$emit('call-parent-method', {
        method: 'parentMethod',
        args: ['参数']
      })
    }
  }
}
</script>

<!-- 父组件 -->
<template>
  <ChildComponent @call-parent-method="handleMethodCall" />
</template>

<script>
export default {
  methods: {
    parentMethod(param) {
      console.log('父组件方法被调用:', param)
    },

    handleMethodCall(event) {
      const { method, args } = event
      if (this[method]) {
        this[method](...args)
      }
    }
  }
}
</script>
```

#### 完整的通信示例

```vue
<template>
  <div class="communication-example">
    <!-- 父组件控制面板 -->
    <Card title="父组件控制面板">
      <Form :label-width="80">
        <FormItem label="共享数据">
          <Input v-model="sharedData.message" />
        </FormItem>
        <FormItem label="计数器">
          <InputNumber v-model="sharedData.counter" />
        </FormItem>
        <FormItem>
          <Button @click="updateSharedData" type="primary">更新数据</Button>
          <Button @click="callChildMethod">调用子组件方法</Button>
        </FormItem>
      </Form>
    </Card>

    <!-- 动态布局 -->
    <DetailCardLayout
      :left-config="leftConfig"
      :right-cards="rightCards"
      @data-updated="handleDataUpdated"
      ref="detailLayout"
    >
      <!-- 左侧插槽 -->
      <template #left="{ data, updateData }">
        <CommunicationChild
          :shared-data="sharedData"
          :parent-data="data"
          :update-parent="updateData"
          @child-event="handleChildEvent"
          @method-call="handleChildMethodCall"
          ref="leftChild"
        />
      </template>

      <!-- 右侧卡片 -->
      <template #info-card="{ card, data, updateData, updateCard }">
        <CommunicationChild
          :shared-data="sharedData"
          :card-data="data"
          :update-card-data="updateData"
          :update-card-config="updateCard"
          @child-event="handleChildEvent"
          ref="rightChild"
        />
      </template>
    </DetailCardLayout>

    <!-- 事件日志 -->
    <Card title="通信日志" style="margin-top: 16px;">
      <div class="event-log">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-type">{{ log.type }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import CommunicationChild from './CommunicationChild.vue'

export default {
  components: {
    CommunicationChild
  },

  data() {
    return {
      // 共享数据
      sharedData: {
        message: 'Hello World',
        counter: 0
      },

      // 左侧配置
      leftConfig: {
        data: {
          title: '左侧数据',
          content: '这是左侧内容'
        }
      },

      // 右侧卡片
      rightCards: [
        {
          name: 'info-card',
          title: '信息卡片',
          data: {
            info: '卡片信息',
            value: 100
          }
        }
      ],

      // 事件日志
      eventLogs: []
    }
  },

  methods: {
    // 更新共享数据
    updateSharedData() {
      this.sharedData = {
        ...this.sharedData,
        message: this.sharedData.message + ' (updated)',
        counter: this.sharedData.counter + 1
      }
      this.addLog('parent', '父组件更新了共享数据')
    },

    // 调用子组件方法
    callChildMethod() {
      if (this.$refs.leftChild) {
        this.$refs.leftChild.childMethod('来自父组件的调用')
      }
      this.addLog('parent', '父组件调用了子组件方法')
    },

    // 处理子组件事件
    handleChildEvent(event) {
      this.addLog('child', `子组件事件: ${event.action}`)

      // 根据事件类型处理
      switch (event.action) {
        case 'update-shared':
          this.sharedData = { ...this.sharedData, ...event.data }
          break
        case 'request-data':
          this.sendDataToChild(event.data)
          break
      }
    },

    // 处理子组件方法调用请求
    handleChildMethodCall(event) {
      const { method, args } = event
      if (this[method]) {
        const result = this[method](...args)
        this.addLog('child', `子组件调用了父组件方法: ${method}`)
        return result
      }
    },

    // 处理数据更新
    handleDataUpdated(event) {
      this.addLog('data', `数据更新: ${event.section}`)
    },

    // 发送数据给子组件
    sendDataToChild(requestData) {
      // 可以通过 ref 直接调用子组件方法
      // 或者更新共享数据
      this.addLog('parent', '父组件响应子组件数据请求')
    },

    // 添加日志
    addLog(type, message) {
      this.eventLogs.unshift({
        type,
        message,
        time: new Date().toLocaleTimeString()
      })

      if (this.eventLogs.length > 20) {
        this.eventLogs = this.eventLogs.slice(0, 20)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.communication-example {
  .event-log {
    max-height: 200px;
    overflow-y: auto;

    .log-item {
      display: flex;
      gap: 12px;
      padding: 4px 0;
      border-bottom: 1px solid #f0f0f0;
      font-size: 12px;

      .log-time {
        color: #999;
        min-width: 80px;
      }

      .log-type {
        min-width: 60px;
        font-weight: 500;

        &[data-type="parent"] { color: #1890ff; }
        &[data-type="child"] { color: #52c41a; }
        &[data-type="data"] { color: #faad14; }
      }

      .log-message {
        flex: 1;
      }
    }
  }
}
</style>
```

这个完整的通信示例展示了：
- 父组件向子组件传递数据
- 子组件向父组件发送事件
- 双向数据绑定
- 方法调用
- 实时的通信日志

通过这些机制，可以实现复杂的组件间通信需求。

### 9. 高级用法

#### 自定义布局类型

```javascript
// 在 layoutConfigs.js 中添加自定义布局
export const customLayouts = {
  'my-custom-layout': {
    name: '我的自定义布局',
    description: '专为特定业务设计的布局',
    className: 'my-custom-layout',
    component: 'CustomLayoutRenderer',
    slots: [
      { name: 'header', title: '头部区域' },
      { name: 'sidebar', title: '侧边栏' },
      { name: 'main', title: '主要内容' },
      { name: 'footer', title: '底部区域' }
    ],
    responsive: true,
    breakpoint: 768
  }
}

// 注册自定义布局
import { layoutConfigs } from '@/components/bsp-layout'
Object.assign(layoutConfigs, customLayouts)
```

#### 条件渲染和动态配置

```vue
<template>
  <DynamicLayout
    :layout-type="currentLayoutType"
    :layout-config="dynamicLayoutConfig"
    :key="layoutKey"
  >
    <!-- 动态插槽 -->
    <template v-for="slot in dynamicSlots" :slot="slot.name" :key="slot.name">
      <DynamicComponent
        v-if="slot.condition"
        :component-config="slot.component"
        :data="slot.data"
      />
    </template>
  </DynamicLayout>
</template>

<script>
export default {
  data() {
    return {
      userRole: 'admin', // 用户角色
      screenSize: 'desktop', // 屏幕尺寸
      businessType: 'health' // 业务类型
    }
  },

  computed: {
    // 根据条件动态选择布局类型
    currentLayoutType() {
      if (this.screenSize === 'mobile') {
        return 'detail' // 移动端使用简单布局
      }

      if (this.businessType === 'health') {
        return 'detail-card' // 健康业务使用卡片布局
      }

      return 'management' // 默认管理布局
    },

    // 动态布局配置
    dynamicLayoutConfig() {
      const baseConfig = { responsive: true }

      // 根据用户角色调整配置
      if (this.userRole === 'admin') {
        baseConfig.showAdvanced = true
      }

      // 根据屏幕尺寸调整
      if (this.screenSize === 'mobile') {
        baseConfig.leftConfig = { width: '100%' }
      }

      return baseConfig
    },

    // 动态插槽配置
    dynamicSlots() {
      const slots = []

      // 管理员才显示管理功能
      if (this.userRole === 'admin') {
        slots.push({
          name: 'admin-panel',
          component: 'admin-tools',
          condition: true,
          data: { permissions: this.userPermissions }
        })
      }

      // 健康业务显示健康相关组件
      if (this.businessType === 'health') {
        slots.push({
          name: 'health-monitor',
          component: 'health-dashboard',
          condition: true,
          data: { patientId: this.currentPatientId }
        })
      }

      return slots
    },

    // 布局键，用于强制重新渲染
    layoutKey() {
      return `${this.currentLayoutType}-${this.userRole}-${this.screenSize}`
    }
  }
}
</script>
```

#### 组件缓存和预加载

```javascript
// 组件缓存策略
import { componentRegistry } from '@/components/bsp-layout'

// 设置缓存策略
componentRegistry.setCacheStrategy({
  maxSize: 50, // 最大缓存数量
  ttl: 300000, // 缓存时间 5分钟
  strategy: 'lru' // LRU 策略
})

// 预加载关键组件
const criticalComponents = [
  'data-table',
  'form-builder',
  'person-info'
]

export async function preloadComponents() {
  const promises = criticalComponents.map(name =>
    componentRegistry.preload(name)
  )

  try {
    await Promise.all(promises)
    console.log('关键组件预加载完成')
  } catch (error) {
    console.error('组件预加载失败:', error)
  }
}

// 在应用启动时预加载
// main.js
import { preloadComponents } from './preload'
preloadComponents()
```

#### 主题和样式定制

```less
// 自定义主题变量
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #ff4d4f;

// 布局主题定制
.bsp-layout-theme-dark {
  background: #1f1f1f;
  color: #fff;

  .card-container {
    background: #2f2f2f;
    border-color: #404040;
  }

  .card-header {
    border-bottom-color: #404040;

    h3 {
      color: #fff;
    }
  }
}

// 响应式断点定制
@media (max-width: 1200px) {
  .bsp-detail-card-layout {
    .main-content {
      flex-direction: column;
    }

    .left-panel {
      flex: none !important;
      width: 100% !important;
    }
  }
}

// 动画定制
.bsp-layout-enter-active,
.bsp-layout-leave-active {
  transition: all 0.3s ease;
}

.bsp-layout-enter,
.bsp-layout-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
```

#### 国际化支持

```javascript
// i18n 配置
export const layoutI18n = {
  'zh-CN': {
    layouts: {
      management: '管理布局',
      detail: '详情布局',
      'detail-card': '详情卡片布局'
    },
    actions: {
      save: '保存',
      cancel: '取消',
      back: '返回',
      edit: '编辑'
    }
  },
  'en-US': {
    layouts: {
      management: 'Management Layout',
      detail: 'Detail Layout',
      'detail-card': 'Detail Card Layout'
    },
    actions: {
      save: 'Save',
      cancel: 'Cancel',
      back: 'Back',
      edit: 'Edit'
    }
  }
}

// 在组件中使用
export default {
  computed: {
    t() {
      const locale = this.$i18n.locale || 'zh-CN'
      return (key) => {
        const keys = key.split('.')
        let value = layoutI18n[locale]

        for (const k of keys) {
          value = value?.[k]
        }

        return value || key
      }
    }
  },

  data() {
    return {
      bottomActions: [
        {
          name: 'back',
          label: this.t('actions.back'),
          icon: 'ios-arrow-back'
        },
        {
          name: 'save',
          label: this.t('actions.save'),
          type: 'primary'
        }
      ]
    }
  }
}
```

#### 权限控制

```javascript
// 权限控制混入
export const permissionMixin = {
  methods: {
    // 检查权限
    hasPermission(permission) {
      const userPermissions = this.$store.state.user.permissions || []
      return userPermissions.includes(permission)
    },

    // 过滤有权限的操作
    filterActionsByPermission(actions) {
      return actions.filter(action => {
        if (!action.permission) return true
        return this.hasPermission(action.permission)
      })
    }
  }
}

// 在组件中使用
export default {
  mixins: [permissionMixin],

  computed: {
    // 根据权限过滤底部操作
    filteredBottomActions() {
      return this.filterActionsByPermission(this.bottomActions)
    },

    // 根据权限过滤卡片
    filteredRightCards() {
      return this.rightCards.filter(card => {
        if (!card.permission) return true
        return this.hasPermission(card.permission)
      })
    }
  }
}
```

### 10. API 文档

#### DynamicLayout Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `layout-type` | `String` | `'management'` | 布局类型 |
| `layout-config` | `Object` | `{}` | 布局配置 |
| `layout-data` | `Object` | `{}` | 布局数据 |
| `responsive` | `Boolean` | `true` | 是否响应式 |
| `custom-class` | `String` | `''` | 自定义样式类 |

#### DynamicLayout Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `layout-initialized` | 布局初始化完成 | `{ layoutType, element }` |
| `layout-rendered` | 布局渲染完成 | `{ layoutType, element }` |
| `component-event` | 组件事件 | `{ layoutType, event }` |
| `card-action` | 卡片操作事件 | `{ layoutType, action, card, index }` |
| `bottom-action` | 底部操作事件 | `{ layoutType, action }` |
| `update:layout-data` | 布局数据更新 | `newLayoutData` |
| `update:layout-config` | 布局配置更新 | `newLayoutConfig` |

#### DynamicComponent Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `component-config` | `String|Object|Function` | - | 组件配置 |
| `data` | `Object` | `{}` | 传递给组件的数据 |
| `props` | `Object` | `{}` | 传递给组件的属性 |
| `events` | `Object` | `{}` | 组件事件监听 |
| `loading-text` | `String` | `'加载中...'` | 加载提示文本 |
| `error-text` | `String` | `'加载失败'` | 错误提示文本 |

#### DynamicComponent Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `component-loaded` | 组件加载完成 | `{ name, instance }` |
| `component-error` | 组件加载失败 | `{ name, error }` |
| `component-ready` | 组件就绪 | `{ name, instance }` |
| `component-event` | 组件事件 | `{ componentName, eventName, args }` |
| `update:data` | 数据更新 | `newData` |
| `update:props` | 属性更新 | `newProps` |

#### DetailCardLayout Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `left-config` | `Object` | `{}` | 左侧面板配置 |
| `right-cards` | `Array` | `[]` | 右侧卡片配置 |
| `bottom-actions` | `Array` | `[]` | 底部操作按钮 |
| `show-bottom-actions` | `Boolean` | `true` | 是否显示底部操作栏 |
| `responsive` | `Boolean` | `true` | 是否响应式 |
| `custom-class` | `String` | `''` | 自定义样式类 |
| `layout-data` | `Object` | `{}` | 布局数据 |

#### DetailCardLayout Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `component-event` | 组件事件 | `event` |
| `card-action` | 卡片操作事件 | `{ action, card, index, layoutData }` |
| `bottom-action` | 底部操作事件 | `{ action, layoutData }` |
| `data-updated` | 数据更新事件 | `{ section, index?, data }` |
| `update:left-config` | 左侧配置更新 | `newLeftConfig` |
| `update:right-cards` | 右侧卡片更新 | `newRightCards` |
| `data-reset` | 数据重置事件 | - |

#### DetailCardLayout Methods

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| `addCard(cardConfig)` | 添加卡片 | `cardConfig: Object` | - |
| `removeCard(index)` | 移除卡片 | `index: Number` | - |
| `updateCard(index, cardConfig)` | 更新卡片 | `index: Number, cardConfig: Object` | - |
| `getLayoutInfo()` | 获取布局信息 | - | `Object` |
| `getAllData()` | 获取所有数据 | - | `Object` |
| `setLeftData(data)` | 设置左侧数据 | `data: Object` | - |
| `setCardData(index, data)` | 设置卡片数据 | `index: Number, data: Object` | - |
| `resetAllData()` | 重置所有数据 | - | - |

### 11. 常见问题

#### Q: 组件无法正常显示怎么办？

**A: 检查以下几个方面：**

1. **组件是否正确注册**
```javascript
// 检查控制台是否有组件注册验证信息
// 访问 /test-bsp-layout 查看测试页面
```

2. **样式是否正确引入**
```less
// 确保在全局样式中引入了 layout-style.less
@import '@/components/bsp-layout/styles/layout-style.less';
```

3. **布局类型是否正确**
```vue
<!-- 检查 layout-type 是否为支持的类型 -->
<DynamicLayout layout-type="management"> <!-- 正确 -->
<DynamicLayout layout-type="invalid-type"> <!-- 错误 -->
```

#### Q: 动态组件加载失败怎么处理？

**A: 常见解决方案：**

1. **检查组件路径**
```javascript
// 确保组件路径正确
componentRegistry.register('my-component', () => import('@/components/MyComponent.vue'))
```

2. **处理加载错误**
```vue
<template>
  <DynamicComponent
    :component-config="componentConfig"
    @component-error="handleComponentError"
  />
</template>

<script>
export default {
  methods: {
    handleComponentError(error) {
      console.error('组件加载失败:', error)
      this.$Message.error(`组件加载失败: ${error.message}`)

      // 可以设置降级组件
      this.componentConfig = 'fallback-component'
    }
  }
}
</script>
```

3. **设置超时和重试**
```javascript
componentRegistry.register('my-component', () => {
  return Promise.race([
    import('@/components/MyComponent.vue'),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('加载超时')), 5000)
    )
  ])
})
```

#### Q: 如何实现组件间的复杂通信？

**A: 使用事件总线或状态管理：**

1. **事件总线方式**
```javascript
// eventBus.js
import Vue from 'vue'
export const EventBus = new Vue()

// 发送事件
EventBus.$emit('data-updated', { type: 'person', data: newData })

// 监听事件
EventBus.$on('data-updated', (event) => {
  console.log('数据更新:', event)
})
```

2. **Vuex 状态管理**
```javascript
// store/modules/layout.js
export default {
  namespaced: true,
  state: {
    layoutData: {},
    sharedData: {}
  },
  mutations: {
    UPDATE_LAYOUT_DATA(state, data) {
      state.layoutData = { ...state.layoutData, ...data }
    }
  },
  actions: {
    updateLayoutData({ commit }, data) {
      commit('UPDATE_LAYOUT_DATA', data)
    }
  }
}

// 在组件中使用
this.$store.dispatch('layout/updateLayoutData', newData)
```

#### Q: 如何优化大量数据的渲染性能？

**A: 性能优化策略：**

1. **虚拟滚动**
```vue
<template>
  <DynamicComponent
    :component-config="{
      name: 'virtual-table',
      props: {
        data: largeDataSet,
        itemHeight: 50,
        visibleCount: 20
      }
    }"
  />
</template>
```

2. **分页加载**
```javascript
export default {
  data() {
    return {
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      },
      tableData: []
    }
  },

  methods: {
    async loadData() {
      const response = await this.fetchData({
        page: this.pagination.current,
        pageSize: this.pagination.pageSize
      })

      this.tableData = response.data
      this.pagination.total = response.total
    }
  }
}
```

3. **组件懒加载**
```javascript
// 只在需要时加载组件
const LazyComponent = () => ({
  component: import('@/components/HeavyComponent.vue'),
  loading: LoadingComponent,
  error: ErrorComponent,
  delay: 200,
  timeout: 3000
})
```

#### Q: 如何处理移动端适配？

**A: 响应式设计方案：**

1. **使用响应式布局**
```vue
<template>
  <DynamicLayout
    layout-type="detail-card"
    :layout-config="{ responsive: true }"
  />
</template>
```

2. **自定义断点**
```less
// 移动端优先
.bsp-detail-card-layout {
  // 默认移动端样式
  .main-content {
    flex-direction: column;
  }

  // 平板端
  @media (min-width: 768px) {
    .main-content {
      flex-direction: row;
    }
  }

  // 桌面端
  @media (min-width: 1200px) {
    .left-panel {
      flex: 0 0 400px;
    }
  }
}
```

3. **动态调整配置**
```javascript
export default {
  computed: {
    layoutConfig() {
      const isMobile = window.innerWidth < 768

      return {
        responsive: true,
        leftConfig: {
          width: isMobile ? '100%' : '400px'
        },
        rightCards: isMobile ? this.mobileCards : this.desktopCards
      }
    }
  }
}
```

#### Q: 如何自定义主题样式？

**A: 主题定制方法：**

1. **CSS 变量方式**
```css
:root {
  --bsp-primary-color: #1890ff;
  --bsp-success-color: #52c41a;
  --bsp-warning-color: #faad14;
  --bsp-error-color: #ff4d4f;
  --bsp-border-radius: 6px;
  --bsp-box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.bsp-layout-dark {
  --bsp-primary-color: #177ddc;
  --bsp-bg-color: #1f1f1f;
  --bsp-text-color: #fff;
}
```

2. **Less 变量覆盖**
```less
// theme.less
@primary-color: #722ed1;
@border-radius-base: 8px;
@box-shadow-base: 0 4px 12px rgba(0,0,0,0.15);

// 导入布局样式
@import '@/components/bsp-layout/styles/layout-style.less';
```

3. **动态主题切换**
```javascript
export default {
  methods: {
    switchTheme(theme) {
      document.documentElement.className = `bsp-layout-${theme}`
      localStorage.setItem('theme', theme)
    }
  },

  mounted() {
    const savedTheme = localStorage.getItem('theme') || 'light'
    this.switchTheme(savedTheme)
  }
}
```

### 12. 最佳实践

#### 组件设计原则

1. **单一职责原则**
```javascript
// 好的做法：每个组件只负责一个功能
const PersonInfoComponent = {
  // 只负责显示人员信息
}

const PersonEditComponent = {
  // 只负责编辑人员信息
}

// 避免：一个组件承担过多职责
const PersonMegaComponent = {
  // 显示、编辑、删除、统计等多个功能混在一起
}
```

2. **数据流向清晰**
```vue
<!-- 好的做法：明确的数据流向 -->
<template>
  <DetailCardLayout
    :left-config="leftConfig"
    @data-updated="handleDataUpdate"
  >
    <template #left="{ data, updateData }">
      <PersonInfo
        :person="data"
        @update="updateData"
      />
    </template>
  </DetailCardLayout>
</template>

<!-- 避免：复杂的数据传递 -->
<template>
  <DetailCardLayout ref="layout">
    <template #left>
      <PersonInfo ref="personInfo" />
    </template>
  </DetailCardLayout>
</template>
```

3. **组件可复用性**
```javascript
// 好的做法：通过配置实现复用
const TableComponent = {
  props: {
    columns: Array,
    data: Array,
    actions: Array
  }
}

// 避免：硬编码特定业务逻辑
const PersonTableComponent = {
  // 只能用于人员表格，无法复用
}
```

#### 性能优化建议

1. **合理使用组件缓存**
```javascript
// 缓存经常使用的组件
componentRegistry.register('frequently-used', component, {
  cache: true,
  ttl: 600000 // 10分钟
})

// 不缓存大型组件
componentRegistry.register('heavy-component', component, {
  cache: false
})
```

2. **懒加载非关键组件**
```javascript
// 关键组件立即加载
import CriticalComponent from './CriticalComponent.vue'

// 非关键组件懒加载
const NonCriticalComponent = () => import('./NonCriticalComponent.vue')
```

3. **避免不必要的重新渲染**
```vue
<template>
  <!-- 使用 key 控制重新渲染 -->
  <DynamicLayout
    :layout-type="layoutType"
    :key="layoutKey"
  />
</template>

<script>
export default {
  computed: {
    // 只在必要时改变 key
    layoutKey() {
      return `${this.layoutType}-${this.userRole}`
    }
  }
}
</script>
```

#### 错误处理策略

1. **组件级错误边界**
```vue
<template>
  <div class="error-boundary">
    <DynamicComponent
      v-if="!hasError"
      :component-config="componentConfig"
      @component-error="handleError"
    />

    <div v-else class="error-fallback">
      <h3>组件加载失败</h3>
      <p>{{ errorMessage }}</p>
      <Button @click="retry">重试</Button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      hasError: false,
      errorMessage: ''
    }
  },

  methods: {
    handleError(error) {
      this.hasError = true
      this.errorMessage = error.message
    },

    retry() {
      this.hasError = false
      this.errorMessage = ''
    }
  }
}
</script>
```

2. **全局错误处理**
```javascript
// main.js
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue 错误:', err, info)

  // 发送错误报告
  if (process.env.NODE_ENV === 'production') {
    sendErrorReport(err, info)
  }
}

// 组件加载错误处理
componentRegistry.setErrorHandler((error, componentName) => {
  console.error(`组件 ${componentName} 加载失败:`, error)

  // 可以设置降级组件
  return 'error-fallback-component'
})
```

#### 测试建议

1. **单元测试**
```javascript
// DynamicLayout.test.js
import { mount } from '@vue/test-utils'
import DynamicLayout from '@/components/bsp-layout/DynamicLayout.vue'

describe('DynamicLayout', () => {
  test('应该正确渲染管理布局', () => {
    const wrapper = mount(DynamicLayout, {
      propsData: {
        layoutType: 'management'
      }
    })

    expect(wrapper.find('.bsp-management-layout')).toBeTruthy()
  })

  test('应该正确处理布局切换', async () => {
    const wrapper = mount(DynamicLayout, {
      propsData: {
        layoutType: 'management'
      }
    })

    await wrapper.setProps({ layoutType: 'detail' })
    expect(wrapper.find('.bsp-detail-layout')).toBeTruthy()
  })
})
```

2. **集成测试**
```javascript
// integration.test.js
describe('布局组件集成测试', () => {
  test('完整的用户交互流程', async () => {
    const wrapper = mount(App)

    // 模拟用户操作
    await wrapper.find('.search-button').trigger('click')
    await wrapper.vm.$nextTick()

    // 验证结果
    expect(wrapper.find('.table-data')).toBeTruthy()
  })
})
```

### 13. 更新日志

#### v1.0.0 (2024-01-15)
- ✨ 初始版本发布
- ✨ 支持 12 种预定义布局类型
- ✨ 完整的动态组件系统
- ✨ 父子通信机制
- ✨ 响应式设计支持

#### 未来规划

- 🔄 支持更多布局类型
- 🎨 可视化布局编辑器
- 📱 更好的移动端支持
- 🌐 完整的国际化支持
- 📊 性能监控和分析
- 🔧 开发者工具集成

### 14. 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 15. 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

### 16. 联系方式

- 项目地址: [BSP Layout Components](https://github.com/your-org/bsp-layout)
- 问题反馈: [Issues](https://github.com/your-org/bsp-layout/issues)
- 文档网站: [Documentation](https://your-org.github.io/bsp-layout)

---

## 🎉 总结

BSP 动态布局组件库为监狱管理系统提供了完整的页面布局解决方案。通过预定义的布局类型、动态组件系统和完善的通信机制，可以快速构建复杂的业务页面。

### 核心优势

- **🚀 开箱即用** - 全局注册，无需重复导入
- **🎨 丰富布局** - 12种预定义布局，覆盖常见场景
- **⚡ 动态加载** - 支持组件异步加载和缓存管理
- **📱 响应式** - 自动适配不同屏幕尺寸
- **🔄 双向通信** - 完整的父子组件通信机制
- **🎯 高度可配置** - 支持深度定制和扩展
- **📚 完善文档** - 详细的使用说明和示例代码

立即开始使用 BSP 动态布局组件，让页面开发更加高效！

```vue
<template>
  <DynamicComponent
    :component-config="componentConfig"
    :data="componentData"
    @component-loaded="handleComponentLoaded"
  />
</template>

<script>
import { DynamicComponent } from '@/components/bsp-layout'

export default {
  components: {
    DynamicComponent
  },
  data() {
    return {
      componentConfig: {
        name: 'data-grid',
        props: {
          columns: this.columns,
          data: this.tableData
        }
      },
      componentData: {
        // 传递给组件的数据
      }
    }
  }
}
</script>
```

### 3. 动态插槽组件

在布局插槽中使用动态组件，实现完全配置化的布局：

```vue
<template>
  <DynamicLayout layout-type="management" :layout-config="layoutConfig">
    <!-- 使用动态组件替代静态内容 -->
    <template #left>
      <DynamicComponent
        :component-config="leftComponentConfig"
        :data="searchData"
        @component-event="handleSearchEvent"
      />
    </template>

    <template #right>
      <DynamicComponent
        :component-config="rightComponentConfig"
        :data="tableData"
        @component-event="handleTableEvent"
      />
    </template>
  </DynamicLayout>
</template>

<script>
export default {
  data() {
    return {
      leftComponentConfig: {
        name: 'search-form',
        props: {
          fields: [
            { name: 'name', label: '姓名', type: 'input' },
            { name: 'area', label: '监区', type: 'select' }
          ]
        }
      },
      rightComponentConfig: {
        name: 'data-table',
        props: {
          columns: this.tableColumns,
          actions: ['add', 'edit', 'delete']
        }
      }
    }
  }
}
</script>
```

### 4. 自定义布局配置

创建自定义的布局配置：

```javascript
export default {
  data() {
    return {
      layoutType: 'management',
      layoutConfig: {
        responsive: true,
        leftConfig: {
          width: '350px',
          title: '查询条件'
        },
        rightConfig: {
          title: '数据列表'
        }
      },
      layoutData: {
        searchForm: {},
        tableData: []
      }
    }
  }
}
```

### 5. 全局组件注册

BSP动态布局组件已注册为全局组件，可直接使用：

```javascript
// main.js 中已配置
import BspLayout from '@/components/bsp-layout'
Vue.use(BspLayout)
```

如需注册自定义组件到动态组件系统：

```javascript
import { componentRegistry } from '@/components/bsp-layout'

// 注册单个组件
componentRegistry.register('my-component', () => import('./MyComponent.vue'), {
  alias: ['custom-comp'],
  description: '我的自定义组件'
})
```

## 📋 支持的布局类型

| 布局类型 | 说明 | 使用场景 |
|---------|------|----------|
| `management` | 管理页面布局 | 数据管理列表页面 |
| `selection` | 选择页面布局 | 人员/设备选择页面 |
| `detail` | 详情页面布局 | 记录详情查看页面 |
| `search` | 搜索页面布局 | 搜索功能页面 |
| `dashboard` | 工作台布局 | 数据统计看板 |
| `card-grid` | 卡片网格布局 | 卡片展示页面 |
| `wizard` | 分步表单布局 | 多步骤操作流程 |
| `master-detail` | 主从表布局 | 主从数据关系展示 |
| `chat` | 聊天界面布局 | 在线沟通页面 |
| `nested` | 嵌套布局 | 复杂的嵌套结构 |
| `complex-nested` | 复杂嵌套布局 | 三层嵌套的复杂页面 |
| `detail-card` | 详情卡片布局 | 左侧固定信息，右侧多卡片，底部操作栏 |

## 🎯 API 文档

### DynamicLayout 组件

#### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `layout-type` | `String` | - | 布局类型，必填 |
| `layout-config` | `Object` | `{}` | 布局配置 |
| `layout-data` | `Object` | `{}` | 布局数据 |
| `responsive` | `Boolean` | `true` | 是否启用响应式 |
| `custom-class` | `String` | `''` | 自定义样式类 |

#### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `layout-initialized` | 布局初始化完成 | `{ layoutType, config }` |
| `layout-event` | 布局事件 | `{ layoutType, event }` |
| `component-event` | 组件事件 | `{ layoutType, event }` |

### DynamicComponent 组件

#### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `component-config` | `String|Object|Function` | - | 组件配置，必填 |
| `data` | `Object` | `{}` | 组件数据 |
| `props` | `Object` | `{}` | 组件属性 |
| `events` | `Object` | `{}` | 组件事件 |
| `lazy` | `Boolean` | `false` | 是否延迟加载 |
| `custom-class` | `String` | `''` | 自定义样式类 |

#### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `component-loaded` | 组件加载完成 | `{ name, component }` |
| `component-ready` | 组件就绪 | `{ name, instance }` |
| `component-error` | 组件错误 | `{ name, error }` |

### DetailCardLayout 组件

#### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `left-config` | `Object` | `{}` | 左侧面板配置 |
| `right-cards` | `Array` | `[]` | 右侧卡片配置数组 |
| `bottom-actions` | `Array` | `[]` | 底部操作按钮配置 |
| `show-bottom-actions` | `Boolean` | `true` | 是否显示底部操作栏 |
| `responsive` | `Boolean` | `true` | 是否启用响应式 |
| `custom-class` | `String` | `''` | 自定义样式类 |
| `layout-data` | `Object` | `{}` | 布局数据 |

#### leftConfig 配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `width` | `String` | `'400px'` | 左侧面板宽度 |
| `title` | `String` | `'详细信息'` | 面板标题 |
| `icon` | `String` | `'ios-contact'` | 标题图标 |
| `iconColor` | `String` | `'#5b8ff9'` | 图标颜色 |
| `iconSize` | `Number` | `20` | 图标大小 |
| `showHeader` | `Boolean` | `true` | 是否显示头部 |
| `component` | `Object` | `null` | 动态组件配置 |
| `data` | `Object` | `{}` | 传递给组件的数据 |

#### rightCards 配置

每个卡片对象支持以下配置：

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `name` | `String` | - | 卡片唯一标识 |
| `title` | `String` | - | 卡片标题 |
| `icon` | `String` | `'ios-information-circle'` | 标题图标 |
| `iconColor` | `String` | `'#5b8ff9'` | 图标颜色 |
| `iconSize` | `Number` | `20` | 图标大小 |
| `slot` | `String` | - | 对应的插槽名称 |
| `showHeader` | `Boolean` | `true` | 是否显示卡片头部 |
| `className` | `String` | `''` | 自定义样式类 |
| `component` | `Object` | `null` | 动态组件配置 |
| `data` | `Object` | `{}` | 传递给组件的数据 |
| `actions` | `Array` | `[]` | 卡片头部操作按钮 |
| `placeholder` | `String` | `'请配置卡片内容'` | 空状态提示文本 |

#### bottomActions 配置

每个操作按钮支持以下配置：

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `name` | `String` | - | 按钮唯一标识 |
| `label` | `String` | - | 按钮文本 |
| `type` | `String` | `'default'` | 按钮类型 |
| `size` | `String` | `'large'` | 按钮大小 |
| `icon` | `String` | - | 按钮图标 |
| `loading` | `Boolean` | `false` | 加载状态 |

#### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `component-event` | 组件事件 | `event` |
| `card-action` | 卡片操作事件 | `{ action, card, index, layoutData }` |
| `bottom-action` | 底部操作事件 | `{ action, layoutData }` |

#### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| `addCard(cardConfig)` | 添加卡片 | `cardConfig: Object` |
| `removeCard(index)` | 移除卡片 | `index: Number` |
| `updateCard(index, cardConfig)` | 更新卡片 | `index: Number, cardConfig: Object` |
| `getLayoutInfo()` | 获取布局信息 | - |

## 🛠️ 高级用法

### 1. DetailCardLayout 详情卡片布局

DetailCardLayout 是基于 talkDetailOptimized 设计的专用布局组件，提供左侧固定信息面板 + 右侧多卡片展示 + 底部操作栏的布局结构。

#### 基础用法

```vue
<template>
  <!-- 方式一：通过 DynamicLayout 使用 -->
  <DynamicLayout
    layout-type="detail-card"
    :layout-config="detailCardConfig"
    @card-action="handleCardAction"
    @bottom-action="handleBottomAction"
  >
    <template #left>
      <PersonInfoCard :person="personData" />
    </template>
    <template #basic-info>
      <BasicInfoTable :data="basicInfo" />
    </template>
  </DynamicLayout>

  <!-- 方式二：直接使用 DetailCardLayout 组件 -->
  <DetailCardLayout
    :left-config="leftConfig"
    :right-cards="rightCards"
    :bottom-actions="bottomActions"
    :show-bottom-actions="true"
    :responsive="true"
    @card-action="handleCardAction"
    @bottom-action="handleBottomAction"
  >
    <!-- 左侧信息面板 -->
    <template #left>
      <div class="person-info">
        <div class="avatar-section">
          <img :src="person.avatar" :alt="person.name" />
          <h3>{{ person.name }}</h3>
          <Tag :color="getRiskColor(person.riskLevel)">{{ person.riskLevel }}</Tag>
        </div>
        <div class="info-section">
          <p><strong>编号：</strong>{{ person.code }}</p>
          <p><strong>年龄：</strong>{{ person.age }}岁</p>
          <p><strong>监区：</strong>{{ person.area }}</p>
        </div>
      </div>
    </template>

    <!-- 基本信息卡片 -->
    <template #basic-info>
      <Table :columns="basicColumns" :data="basicData" size="small" />
    </template>

    <!-- 操作功能卡片 -->
    <template #actions-card>
      <div class="action-buttons">
        <div class="action-item" @click="viewVideo">
          <Icon type="ios-videocam" size="32" color="#5b8ff9" />
          <h4>查看录像</h4>
          <p>观看谈话录音录像</p>
        </div>
        <div class="action-item" @click="viewRecord">
          <Icon type="ios-document" size="32" color="#52c41a" />
          <h4>查看笔录</h4>
          <p>查看谈话记录内容</p>
        </div>
      </div>
    </template>

    <!-- 评估结论卡片 -->
    <template #assessment>
      <div class="assessment-content">
        <h4>心理评估结论</h4>
        <p>{{ assessmentData.psychology }}</p>
        <h4>协作建议</h4>
        <p>{{ assessmentData.collaboration }}</p>
      </div>
    </template>
  </DetailCardLayout>
</template>

<script>
export default {
  data() {
    return {
      // 左侧面板配置
      leftConfig: {
        width: '400px',
        title: '被监管人员信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true
      },

      // 右侧卡片配置
      rightCards: [
        {
          name: 'basic-info',
          title: '谈话基本信息',
          icon: 'ios-chatbubbles',
          iconColor: '#5b8ff9',
          slot: 'basic-info'
        },
        {
          name: 'actions-card',
          title: '谈话内容操作',
          icon: 'ios-options',
          iconColor: '#52c41a',
          slot: 'actions-card',
          actions: [
            { name: 'refresh', label: '刷新', type: 'default', icon: 'ios-refresh' }
          ]
        },
        {
          name: 'assessment',
          title: '评估结论',
          icon: 'ios-analytics',
          iconColor: '#faad14',
          slot: 'assessment'
        }
      ],

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        },
        {
          name: 'save',
          label: '保存修改',
          type: 'primary',
          icon: 'ios-checkmark'
        }
      ]
    }
  },
  methods: {
    // 处理卡片操作
    handleCardAction(event) {
      const { action, card, index } = event
      console.log(`卡片操作: ${action.name} - ${card.name}`)

      if (action.name === 'refresh') {
        this.refreshCardData(card.name)
      }
    },

    // 处理底部操作
    handleBottomAction(event) {
      const { action } = event

      if (action.name === 'back') {
        this.$router.go(-1)
      } else if (action.name === 'save') {
        this.saveChanges()
      }
    },

    // 查看录像
    viewVideo() {
      // 录像查看逻辑
    },

    // 查看笔录
    viewRecord() {
      // 笔录查看逻辑
    }
  }
}
</script>
```

### 2. DetailCardLayout 动态操作

```javascript
export default {
  methods: {
    // 动态添加卡片
    addNewCard() {
      const newCard = {
        name: 'new-card',
        title: '新增卡片',
        icon: 'ios-add-circle',
        slot: 'new-card',
        component: {
          name: 'custom-component',
          props: { data: this.newCardData }
        }
      }
      this.$refs.detailCardLayout.addCard(newCard)
    },

    // 动态移除卡片
    removeCard(index) {
      this.$refs.detailCardLayout.removeCard(index)
    },

    // 动态更新卡片
    updateCard(index) {
      const updatedCard = {
        title: '更新后的标题',
        icon: 'ios-checkmark-circle',
        iconColor: '#52c41a'
      }
      this.$refs.detailCardLayout.updateCard(index, updatedCard)
    },

    // 获取布局信息
    getLayoutInfo() {
      const info = this.$refs.detailCardLayout.getLayoutInfo()
      console.log('布局信息:', info)
    }
  }
}
```

### 3. 响应式断点自定义

```vue
<template>
  <DetailCardLayout
    :responsive="true"
    custom-class="my-custom-layout"
  >
    <!-- 内容 -->
  </DetailCardLayout>
</template>

<style>
.my-custom-layout {
  /* 自定义响应式断点 */
  @media (max-width: 1024px) {
    .main-content {
      flex-direction: column;
    }
  }

  /* 自定义卡片样式 */
  .card-container {
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.08);
  }
}
</style>
```

### 4. 自定义布局配置

```javascript
import { layoutConfigs } from '@/components/bsp-layout'

// 添加自定义布局
layoutConfigs['my-layout'] = {
  name: '我的布局',
  description: '自定义布局描述',
  className: 'my-custom-layout',
  component: 'LayoutRenderer',
  props: {
    layoutType: 'my-layout'
  },
  slots: [
    {
      name: 'header',
      title: '头部区域',
      description: '页面头部'
    },
    {
      name: 'content',
      title: '内容区域',
      description: '主要内容'
    }
  ]
}
```

### 2. 组件预加载

```javascript
import { componentRegistry } from '@/components/bsp-layout'

// 预加载组件
await componentRegistry.preloadComponents(['data-grid', 'form-builder'])
```

### 3. 事件总线

```javascript
import { layoutEventBus } from '@/components/bsp-layout'

// 监听事件
layoutEventBus.on('layout-changed', (data) => {
  console.log('布局变化:', data)
})

// 触发事件
layoutEventBus.emit('layout-changed', { layoutType: 'management' })
```

## 🎨 样式定制

### 1. CSS 变量

```less
// 自定义布局变量
@layout-left-width-default: 340px;
@layout-right-min-width: 400px;
@detail-header-height: 60px;
@detail-footer-height: 60px;
```

### 2. DetailCardLayout 样式定制

```less
// DetailCardLayout 样式定制
.bsp-detail-card-layout {
  // 自定义左侧面板样式
  .left-panel {
    .card-container {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      .card-header h3 {
        color: white;
      }
    }
  }

  // 自定义右侧卡片样式
  .right-panel {
    .card-container {
      border-radius: 16px;
      box-shadow: 0 8px 24px rgba(0,0,0,0.08);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(0,0,0,0.12);
      }

      .card-header {
        background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 16px 16px 0 0;
      }
    }
  }

  // 自定义底部操作栏样式
  .bottom-actions {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);

    .actions-container {
      .ivu-btn {
        border-radius: 8px;
        font-weight: 600;

        &.ivu-btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
        }
      }
    }
  }
}
```

### 3. 自定义样式

```less
// 覆盖默认样式
.bsp-management-layout {
  &-left {
    background: #f5f5f5;
  }

  &-right {
    padding: 20px;
  }
}
```

## 📝 示例

查看以下示例文件获取完整的使用示例：

- `examples/DynamicLayoutExample.vue` - 基础动态布局使用示例
- `examples/DynamicSlotExample.vue` - 动态插槽配置示例
- `examples/TalkDetailExample.vue` - DetailCardLayout 完整使用示例
- `view/test-bsp-layout/index.vue` - 所有布局类型的测试页面

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个组件库。

## 📄 许可证

MIT License
