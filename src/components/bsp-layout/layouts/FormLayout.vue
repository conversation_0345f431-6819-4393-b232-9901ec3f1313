<template>
  <div class="form-layout" :class="layoutClass">
    <!-- 表单内容区域 -->
    <div class="form-content" :class="contentClass">
      <div class="form-container" :class="containerClass">
        <!-- 表单标题区域 -->
        <div class="form-header" v-if="showHeader">
          <div class="header-left">
            <Icon
              v-if="headerConfig.icon"
              :type="headerConfig.icon"
              :size="headerConfig.iconSize || 20"
              :color="headerConfig.iconColor || '#5b8ff9'"
            />
            <h2 v-if="headerConfig.title" class="form-title">
              {{ headerConfig.title }}
            </h2>
          </div>
          <div class="header-right" v-if="headerConfig.actions && headerConfig.actions.length">
            <Button
              v-for="action in headerConfig.actions"
              :key="action.name"
              :type="action.type || 'default'"
              :size="action.size || 'default'"
              :icon="action.icon"
              :loading="action.loading"
              :disabled="action.disabled"
              @click="handleHeaderAction(action)"
            >
              {{ action.label }}
            </Button>
          </div>
        </div>

        <!-- 表单主体内容 -->
        <div class="form-body">
          <slot name="form" :form-data="formData" :update-form-data="updateFormData">
            <!-- 默认插槽内容 -->
            <div class="default-form-content">
              <Alert show-icon>
                <span slot="desc">请使用 #form 插槽添加表单内容</span>
              </Alert>
            </div>
          </slot>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮区域 -->
    <div class="bottom-actions" v-if="showBottomActions" :class="actionsClass">
      <div class="actions-container">
        <slot name="actions" :form-data="formData" :handle-action="handleBottomAction">
          <Button
            v-for="action in bottomActions"
            :key="action.name"
            :type="action.type || 'default'"
            :size="action.size || 'large'"
            :icon="action.icon"
            :loading="action.loading"
            :disabled="action.disabled"
            @click="handleBottomAction(action)"
          >
            {{ action.label }}
          </Button>
        </slot>
      </div>
    </div>

    <!-- 模态框插槽 -->
    <slot name="modals"></slot>
  </div>
</template>

<script>
export default {
  name: 'FormLayout',
  props: {
    // 表单头部配置
    headerConfig: {
      type: Object,
      default: () => ({
        title: '',
        icon: '',
        iconSize: 20,
        iconColor: '#5b8ff9',
        actions: []
      })
    },

    // 是否显示头部
    showHeader: {
      type: Boolean,
      default: false
    },

    // 底部操作按钮配置
    bottomActions: {
      type: Array,
      default: () => ([
        {
          name: 'cancel',
          label: '取消',
          type: 'default',
          icon: 'ios-close'
        },
        {
          name: 'submit',
          label: '提交',
          type: 'primary',
          icon: 'ios-checkmark'
        }
      ])
    },

    // 是否显示底部操作栏
    showBottomActions: {
      type: Boolean,
      default: true
    },

    // 表单数据
    formData: {
      type: Object,
      default: () => ({})
    },

    // 布局模式
    layoutMode: {
      type: String,
      default: 'default', // default, compact, wide
      validator: value => ['default', 'compact', 'wide'].includes(value)
    },

    // 是否启用响应式
    responsive: {
      type: Boolean,
      default: true
    },

    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    },

    // 表单容器样式
    containerStyle: {
      type: Object,
      default: () => ({})
    },

    // 是否加载中
    loading: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    // 布局样式类
    layoutClass() {
      const classes = []

      if (this.responsive) {
        classes.push('responsive')
      }

      if (this.loading) {
        classes.push('loading')
      }

      if (this.layoutMode) {
        classes.push(`layout-${this.layoutMode}`)
      }

      if (this.customClass) {
        classes.push(this.customClass)
      }

      return classes.join(' ')
    },

    // 内容区域样式类
    contentClass() {
      const classes = []

      if (this.showHeader) {
        classes.push('has-header')
      }

      if (this.showBottomActions) {
        classes.push('has-bottom-actions')
      }

      return classes.join(' ')
    },

    // 容器样式类
    containerClass() {
      const classes = []

      if (this.layoutMode === 'compact') {
        classes.push('compact')
      } else if (this.layoutMode === 'wide') {
        classes.push('wide')
      }

      return classes.join(' ')
    },

    // 操作按钮区域样式类
    actionsClass() {
      const classes = []

      if (this.layoutMode === 'compact') {
        classes.push('compact')
      }

      return classes.join(' ')
    }
  },

  methods: {
    // 处理头部操作
    handleHeaderAction(action) {
      this.$emit('header-action', {
        action,
        formData: this.formData
      })
    },

    // 处理底部操作
    handleBottomAction(action) {
      this.$emit('bottom-action', {
        action,
        formData: this.formData
      })
    },

    // 更新表单数据
    updateFormData(data) {
      this.$emit('update:form-data', {
        ...this.formData,
        ...data
      })
    },

    // 获取表单数据
    getFormData() {
      return this.formData
    },

    // 设置表单数据
    setFormData(data) {
      this.updateFormData(data)
    },

    // 重置表单数据
    resetFormData() {
      this.$emit('update:form-data', {})
      this.$emit('form-reset')
    },

    // 获取布局信息
    getLayoutInfo() {
      return {
        headerConfig: this.headerConfig,
        bottomActions: this.bottomActions,
        formData: this.formData,
        layoutMode: this.layoutMode
      }
    }
  }
}
</script>

<style lang="less" scoped>
.form-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f5f7fa;

  // 表单内容区域
  .form-content {
    flex: 1;
    padding: 20px;
    padding-bottom: 100px; // 为底部按钮留出空间

    &.has-bottom-actions {
      padding-bottom: 100px;
    }

    .form-container {
      width: 100%;
      margin: 0 auto;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.05);
      border: 1px solid #e8f4fd;
      overflow: visible;

      &.compact {
        width: 100%;
      }

      &.wide {
        width: 100%;
      }
    }

    // 表单头部
    .form-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24px 32px 20px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafbfc;

      .header-left {
        display: flex;
        align-items: center;
        gap: 12px;

        .form-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
        }
      }

      .header-right {
        display: flex;
        gap: 12px;
      }
    }

    // 表单主体
    .form-body {
      padding: 32px;

      .default-form-content {
        text-align: center;
        padding: 60px 20px;
      }
    }
  }

  // 底部操作按钮
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 20px 32px;
    border-top: 1px solid #e8eef0;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
    z-index: 999;

    &.compact {
      padding: 16px 24px;
    }

    .actions-container {
      display: flex;
      justify-content: center;
      gap: 16px;
      max-width: 1200px;
      margin: 0 auto;

      .ivu-btn {
        min-width: 120px;
        height: 44px;
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
      }
    }
  }

  // 布局模式样式
  &.layout-compact {
    .form-content {
      padding: 16px;
      padding-bottom: 80px;

      .form-container {
        .form-header {
          padding: 20px 24px 16px;

          .form-title {
            font-size: 16px;
          }
        }

        .form-body {
          padding: 24px;
        }
      }
    }
  }

  &.layout-wide {
    .form-content {
      padding: 24px;
      padding-bottom: 100px;

      .form-container {
        .form-header {
          padding: 28px 40px 24px;

          .form-title {
            font-size: 20px;
          }
        }

        .form-body {
          padding: 40px;
        }
      }
    }
  }

  // 加载状态
  &.loading {
    .form-container {
      opacity: 0.7;
      pointer-events: none;
    }
  }

  // 响应式设计
  &.responsive {
    @media (max-width: 768px) {
      .form-content {
        padding: 12px;
        padding-bottom: 120px;

        .form-container {
          border-radius: 8px;

          .form-header {
            padding: 16px 20px 12px;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;

            .form-title {
              font-size: 16px;
            }

            .header-right {
              width: 100%;
              justify-content: flex-end;
            }
          }

          .form-body {
            padding: 20px;
          }
        }
      }

      .bottom-actions {
        padding: 16px 20px;

        .actions-container {
          flex-direction: column;
          gap: 12px;

          .ivu-btn {
            width: 100%;
            max-width: 300px;
            margin: 0 auto;
          }
        }
      }
    }

    @media (max-width: 480px) {
      .form-content {
        padding: 8px;

        .form-container {
          .form-header {
            padding: 12px 16px 8px;

            .form-title {
              font-size: 14px;
            }
          }

          .form-body {
            padding: 16px;
          }
        }
      }

      .bottom-actions {
        padding: 12px 16px;

        .actions-container {
          .ivu-btn {
            height: 40px;
            font-size: 13px;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-container {
  animation: fadeInUp 0.6s ease-out;
}

// 确保日期选择器弹出层在底部按钮之上显示
/deep/ .ivu-date-picker-dropdown {
  z-index: 1050 !important;
}

// 确保时间选择器弹出层在底部按钮之上显示
/deep/ .ivu-time-picker-dropdown {
  z-index: 1050 !important;
}

// 确保选择器弹出层在底部按钮之上显示
/deep/ .ivu-select-dropdown {
  z-index: 1050 !important;
}

// 确保字典组件弹出层在底部按钮之上显示
/deep/ .ivu-poptip {
  z-index: 1050 !important;
}
</style>
