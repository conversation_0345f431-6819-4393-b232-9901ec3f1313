# FormLayout 快速开始

FormLayout 是专门为表单页面设计的布局组件，与 DynamicForm 完美结合。

## 🚀 快速使用

### 1. 基础用法

```vue
<template>
  <FormLayout
    :form-data="formData"
    @bottom-action="handleAction"
  >
    <template #form="{ formData, updateFormData }">
      <DynamicForm
        :config="formConfig"
        :value="formData"
        @input="updateFormData"
      />
    </template>
  </FormLayout>
</template>

<script>
import { FormLayout } from '@/components/bsp-layout'
import DynamicForm from '@/components/dynamic-form/DynamicForm.vue'

export default {
  components: { FormLayout, DynamicForm },
  data() {
    return {
      formData: {},
      formConfig: [
        {
          title: '基本信息',
          fields: [
            { key: 'name', label: '姓名', type: 'input', required: true },
            { key: 'email', label: '邮箱', type: 'input', required: true }
          ]
        }
      ]
    }
  },
  methods: {
    handleAction({ action }) {
      if (action.name === 'submit') {
        console.log('提交表单:', this.formData)
      }
    }
  }
}
</script>
```

### 2. 带头部的表单

```vue
<template>
  <FormLayout
    :header-config="headerConfig"
    :show-header="true"
    :form-data="formData"
    @header-action="handleHeaderAction"
    @bottom-action="handleBottomAction"
  >
    <template #form="{ formData, updateFormData }">
      <DynamicForm
        :config="formConfig"
        :value="formData"
        @input="updateFormData"
      />
    </template>
  </FormLayout>
</template>

<script>
export default {
  data() {
    return {
      headerConfig: {
        title: '用户信息表单',
        icon: 'ios-person-add',
        actions: [
          { name: 'reset', label: '重置', icon: 'ios-refresh' }
        ]
      },
      // ... 其他数据
    }
  },
  methods: {
    handleHeaderAction({ action }) {
      if (action.name === 'reset') {
        this.formData = {}
      }
    },
    handleBottomAction({ action }) {
      // 处理底部按钮
    }
  }
}
</script>
```

### 3. 自定义底部按钮

```vue
<template>
  <FormLayout :form-data="formData">
    <template #form="{ formData, updateFormData }">
      <DynamicForm
        ref="form"
        :config="formConfig"
        :value="formData"
        @input="updateFormData"
      />
    </template>

    <template #actions>
      <Button @click="goBack">返回</Button>
      <Button @click="saveDraft" :loading="saving">保存草稿</Button>
      <Button type="primary" @click="submit" :loading="submitting">
        提交审核
      </Button>
    </template>
  </FormLayout>
</template>
```

### 4. 紧凑模式

```vue
<template>
  <FormLayout
    layout-mode="compact"
    :form-data="formData"
  >
    <template #form="{ formData, updateFormData }">
      <DynamicForm
        :config="formConfig"
        :value="formData"
        @input="updateFormData"
      />
    </template>
  </FormLayout>
</template>
```

## 📱 响应式支持

FormLayout 自动适配不同屏幕尺寸：

- **桌面端**：标准布局，按钮水平排列
- **平板端**：调整间距，保持水平布局
- **移动端**：按钮垂直排列，全宽显示

## 🎨 布局模式

### default 模式
```vue
<FormLayout layout-mode="default">
  <!-- 标准布局，适合大多数场景 -->
</FormLayout>
```

### compact 模式
```vue
<FormLayout layout-mode="compact">
  <!-- 紧凑布局，减少内边距 -->
</FormLayout>
```

### wide 模式
```vue
<FormLayout layout-mode="wide">
  <!-- 宽屏布局，适合复杂表单 -->
</FormLayout>
```

## 🔧 常用配置

### 头部配置
```javascript
headerConfig: {
  title: '表单标题',
  icon: 'ios-document',
  iconColor: '#5b8ff9',
  actions: [
    { name: 'help', label: '帮助', icon: 'ios-help-circle' },
    { name: 'reset', label: '重置', icon: 'ios-refresh' }
  ]
}
```

### 底部按钮配置
```javascript
bottomActions: [
  { name: 'cancel', label: '取消', type: 'default' },
  { name: 'save', label: '保存', type: 'default', icon: 'ios-save' },
  { name: 'submit', label: '提交', type: 'primary', icon: 'ios-checkmark' }
]
```

## 💡 最佳实践

### 1. 表单验证
```vue
<template>
  <FormLayout @bottom-action="handleAction">
    <template #form="{ formData, updateFormData }">
      <DynamicForm
        ref="dynamicForm"
        :config="formConfig"
        :value="formData"
        @input="updateFormData"
      />
    </template>
  </FormLayout>
</template>

<script>
export default {
  methods: {
    async handleAction({ action }) {
      if (action.name === 'submit') {
        try {
          const valid = await this.$refs.dynamicForm.validate()
          if (valid) {
            // 提交表单
            await this.submitForm()
          }
        } catch (error) {
          this.$Message.error('请检查表单填写')
        }
      }
    }
  }
}
</script>
```

### 2. 加载状态
```vue
<template>
  <FormLayout
    :loading="loading"
    :bottom-actions="bottomActions"
  >
    <!-- 表单内容 -->
  </FormLayout>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      bottomActions: [
        {
          name: 'submit',
          label: '提交',
          type: 'primary',
          loading: this.submitting
        }
      ]
    }
  }
}
</script>
```

### 3. 确认对话框
```vue
<template>
  <FormLayout @bottom-action="handleAction">
    <template #form>
      <!-- 表单内容 -->
    </template>

    <template #modals>
      <Modal
        v-model="showConfirm"
        title="确认提交"
        @on-ok="confirmSubmit"
      >
        <p>确定要提交表单吗？</p>
      </Modal>
    </template>
  </FormLayout>
</template>
```

## 🎯 与 DynamicForm 的完美结合

FormLayout 专门为 DynamicForm 优化：

1. **数据流管理**：自动处理表单数据的双向绑定
2. **验证集成**：支持表单验证和错误提示
3. **响应式布局**：自动适配不同屏幕尺寸
4. **操作按钮**：提供标准的表单操作按钮
5. **加载状态**：统一的加载状态管理

## 📚 更多示例

查看 `FormLayoutExample.vue` 获取完整的使用示例，包括：

- 表单验证
- 异步提交
- 错误处理
- 确认对话框
- 响应式布局

## 🔗 相关组件

- [DynamicForm](../dynamic-form/README.md) - 动态表单组件
- [DetailCardLayout](./DetailCardLayout.md) - 详情卡片布局
- [EasyLayout](./EasyLayout.md) - 简易布局组件
