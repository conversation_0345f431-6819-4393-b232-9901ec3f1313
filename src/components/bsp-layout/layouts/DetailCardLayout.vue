<template>
  <div class="bsp-detail-card-layout" :class="layoutClass">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧面板 -->
      <div class="left-panel" :class="leftPanelClass" :style="leftPanelStyle">
        <div class="card-container">
          <div class="card-header" v-if="leftConfig.showHeader">
            <div class="header-left">
              <Icon
                :type="leftConfig.icon || 'ios-contact'"
                :size="leftConfig.iconSize || 20"
                :color="leftConfig.iconColor || '#5b8ff9'"
              />
              <h3 v-if="!isCollapsed">{{ leftConfig.title || '左侧信息' }}</h3>
            </div>

            <!-- 收缩/展开按钮 -->
            <div class="header-right" v-if="leftConfig.collapsible !== false">
              <Button
                type="text"
                size="small"
                @click="toggleCollapse"
                class="collapse-btn"
                :title="isCollapsed ? '展开' : '收缩'"
              >
                <Icon
                  :type="isCollapsed ? 'ios-arrow-forward' : 'ios-arrow-back'"
                  :size="16"
                />
              </Button>
            </div>
          </div>

          <div class="card-content" v-show="!isCollapsed">
            <slot
              name="left"
              :data="leftConfig.data"
              :update-data="updateLeftData"
              :is-collapsed="isCollapsed"
            >
              <DynamicComponent
                v-if="leftConfig.component"
                :component-config="leftConfig.component"
                :data="leftConfig.data"
                :props="leftConfig.props || {}"
                :events="leftConfig.events || {}"
                @component-event="handleComponentEvent"
                @update:data="updateLeftData"
                @update:props="updateLeftProps"
              />
              <div v-else class="default-content">
                <Alert show-icon>
                  <span slot="desc">请配置左侧组件内容</span>
                </Alert>
              </div>
            </slot>
          </div>

          <!-- 收缩状态下的简化内容 -->
          <div class="collapsed-content" v-show="isCollapsed">
            <slot
              name="left-collapsed"
              :data="leftConfig.data"
              :toggle-collapse="toggleCollapse"
            >
              <div class="collapsed-info">
                <div class="collapsed-avatar" v-if="leftConfig.data && leftConfig.data.avatar">
                  <img :src="leftConfig.data.avatar" :alt="leftConfig.data.name || '用户'" />
                </div>
                <div class="collapsed-icon" v-else>
                  <Icon
                    :type="leftConfig.icon || 'ios-contact'"
                    :size="24"
                    :color="leftConfig.iconColor || '#5b8ff9'"
                  />
                </div>
                <div class="collapsed-text" v-if="leftConfig.data && leftConfig.data.name">
                  <p>{{ leftConfig.data.name }}</p>
                </div>
              </div>
            </slot>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <div class="right-content">
          <!-- 动态渲染右侧卡片 -->
          <div
            v-for="(card, index) in rightCards"
            :key="card.name || index"
            class="card-container"
            :class="card.className"
            :style="{ animationDelay: `${index * 0.1}s` }"
          >
            <div class="card-header" v-if="card.showHeader !== false">
              <Icon
                :type="card.icon || 'ios-information-circle'"
                :size="card.iconSize || 20"
                :color="card.iconColor || '#5b8ff9'"
              />
              <h3>{{ card.title || `卡片 ${index + 1}` }}</h3>

              <!-- 卡片头部操作按钮 -->
              <div class="card-actions" v-if="card.actions && card.actions.length">
                <Button
                  v-for="action in card.actions"
                  :key="action.name"
                  :type="action.type || 'default'"
                  :size="action.size || 'small'"
                  :icon="action.icon"
                  @click="handleCardAction(action, card, index)"
                >
                  {{ action.label }}
                </Button>
              </div>
            </div>

            <div class="card-content">
              <slot
                :name="card.slot || `right-${index}`"
                :card="card"
                :index="index"
                :data="card.data"
                :update-data="(data) => updateCardData(index, data)"
                :update-card="(config) => updateCard(index, config)"
              >
                <DynamicComponent
                  v-if="card.component"
                  :component-config="card.component"
                  :data="card.data"
                  :props="{ ...card.props, cardIndex: index, cardName: card.name }"
                  :events="{ ...card.events, 'card-action': (action) => handleCardAction(action, card, index) }"
                  @component-event="handleComponentEvent"
                  @update:data="(data) => updateCardData(index, data)"
                  @update:props="(props) => updateCardProps(index, props)"
                />
                <div v-else class="default-content">
                  <Alert show-icon>
                    <span slot="desc">{{ card.placeholder || '请配置卡片内容' }}</span>
                  </Alert>
                </div>
              </slot>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions" v-if="showBottomActions">
      <div class="actions-container">
        <slot name="bottom-actions">
          <Button
            v-for="action in bottomActions"
            :key="action.name"
            :type="action.type || 'default'"
            :size="action.size || 'large'"
            :icon="action.icon"
            :loading="action.loading"
            @click="handleBottomAction(action)"
          >
            {{ action.label }}
          </Button>
        </slot>
      </div>
    </div>

    <!-- 模态框插槽 -->
    <slot name="modals"></slot>
  </div>
</template>

<script>
import DynamicComponent from '../DynamicComponent.vue'

export default {
  name: 'DetailCardLayout',
  components: {
    DynamicComponent
  },
  props: {
    // 左侧面板配置
    leftConfig: {
      type: Object,
      default: () => ({
        width: '400px',
        title: '详细信息',
        icon: 'ios-contact',
        iconColor: '#5b8ff9',
        showHeader: true,
        component: null,
        data: {}
      })
    },

    // 右侧卡片配置
    rightCards: {
      type: Array,
      default: () => ([
        {
          name: 'basic-info',
          title: '基本信息',
          icon: 'ios-information-circle',
          slot: 'basic-info',
          component: null,
          data: {}
        }
      ])
    },

    // 底部操作按钮配置
    bottomActions: {
      type: Array,
      default: () => ([
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        }
      ])
    },

    // 是否显示底部操作栏
    showBottomActions: {
      type: Boolean,
      default: true
    },

    // 响应式配置
    responsive: {
      type: Boolean,
      default: true
    },

    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    },

    // 布局数据
    layoutData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 左侧面板收缩状态
      isCollapsed: false
    }
  },
  computed: {
    // 布局样式类
    layoutClass() {
      const classes = []

      if (this.responsive) {
        classes.push('responsive')
      }

      if (this.customClass) {
        classes.push(this.customClass)
      }

      return classes.join(' ')
    },

    // 左侧面板样式类
    leftPanelClass() {
      const classes = []

      if (this.isCollapsed) {
        classes.push('collapsed')
      }

      return classes.join(' ')
    },

    // 左侧面板样式
    leftPanelStyle() {
      const width = this.isCollapsed
        ? (this.leftConfig.collapsedWidth || '60px')
        : (this.leftConfig.width || '400px')

      return {
        flex: `0 0 ${width}`,
        transition: 'all 0.3s ease'
      }
    }
  },
  methods: {
    // 处理组件事件
    handleComponentEvent(event) {
      this.$emit('component-event', event)
    },

    // 处理卡片操作
    handleCardAction(action, card, index) {
      this.$emit('card-action', {
        action,
        card,
        index,
        layoutData: this.layoutData
      })
    },

    // 处理底部操作
    handleBottomAction(action) {
      this.$emit('bottom-action', {
        action,
        layoutData: this.layoutData
      })
    },

    // 切换左侧面板收缩状态
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed

      // 发射收缩状态变化事件
      this.$emit('collapse-change', {
        isCollapsed: this.isCollapsed,
        width: this.isCollapsed
          ? (this.leftConfig.collapsedWidth || '60px')
          : (this.leftConfig.width || '400px')
      })

      // 延迟触发resize事件，让其他组件知道布局发生了变化
      this.$nextTick(() => {
        window.dispatchEvent(new Event('resize'))
      })
    },

    // 设置收缩状态
    setCollapsed(collapsed) {
      if (this.isCollapsed !== collapsed) {
        this.toggleCollapse()
      }
    },

    // 获取收缩状态
    getCollapsed() {
      return this.isCollapsed
    },

    // 添加卡片
    addCard(cardConfig) {
      this.rightCards.push(cardConfig)
    },

    // 移除卡片
    removeCard(index) {
      if (index >= 0 && index < this.rightCards.length) {
        this.rightCards.splice(index, 1)
      }
    },

    // 更新卡片
    updateCard(index, cardConfig) {
      if (index >= 0 && index < this.rightCards.length) {
        this.$set(this.rightCards, index, {
          ...this.rightCards[index],
          ...cardConfig
        })
      }
    },

    // 获取布局信息
    getLayoutInfo() {
      return {
        leftConfig: this.leftConfig,
        rightCards: this.rightCards,
        bottomActions: this.bottomActions,
        layoutData: this.layoutData
      }
    },

    // 更新左侧数据
    updateLeftData(data) {
      const updatedConfig = {
        ...this.leftConfig,
        data: { ...this.leftConfig.data, ...data }
      }
      this.$emit('update:left-config', updatedConfig)
      this.$emit('data-updated', { section: 'left', data })
    },

    // 更新左侧属性
    updateLeftProps(props) {
      const updatedConfig = {
        ...this.leftConfig,
        props: { ...this.leftConfig.props, ...props }
      }
      this.$emit('update:left-config', updatedConfig)
    },

    // 更新卡片数据
    updateCardData(index, data) {
      if (index >= 0 && index < this.rightCards.length) {
        const updatedCards = [...this.rightCards]
        updatedCards[index] = {
          ...updatedCards[index],
          data: { ...updatedCards[index].data, ...data }
        }
        this.$emit('update:right-cards', updatedCards)
        this.$emit('data-updated', { section: 'card', index, data })
      }
    },

    // 更新卡片属性
    updateCardProps(index, props) {
      if (index >= 0 && index < this.rightCards.length) {
        const updatedCards = [...this.rightCards]
        updatedCards[index] = {
          ...updatedCards[index],
          props: { ...updatedCards[index].props, ...props }
        }
        this.$emit('update:right-cards', updatedCards)
      }
    },

    // 获取左侧数据
    getLeftData() {
      return this.leftConfig.data || {}
    },

    // 获取卡片数据
    getCardData(index) {
      if (index >= 0 && index < this.rightCards.length) {
        return this.rightCards[index].data || {}
      }
      return {}
    },

    // 获取所有数据
    getAllData() {
      return {
        left: this.getLeftData(),
        cards: this.rightCards.map(card => card.data || {}),
        layout: this.layoutData
      }
    },

    // 设置左侧数据
    setLeftData(data) {
      this.updateLeftData(data)
    },

    // 设置卡片数据
    setCardData(index, data) {
      this.updateCardData(index, data)
    },

    // 重置所有数据
    resetAllData() {
      // 重置左侧数据
      this.updateLeftData({})

      // 重置所有卡片数据
      this.rightCards.forEach((card, index) => {
        this.updateCardData(index, {})
      })

      this.$emit('data-reset')
    }
  }
}
</script>

<style lang="less" scoped>
.bsp-detail-card-layout {
  background: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 80px; // 为底部按钮留出空间

  // 主要内容区域
  .main-content {
    display: flex;
    gap: 20px;
    padding: 20px;

    // 左侧面板
    .left-panel {
      transition: all 0.3s ease;

      .card-container {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        border: 1px solid #e8f4fd;
        height: fit-content;
        animation: fadeInUp 0.6s ease-out;
        overflow: hidden;
      }

      // 收缩状态样式
      &.collapsed {
        .card-container {
          .card-header {
            .header-left h3 {
              opacity: 0;
              width: 0;
              overflow: hidden;
            }
          }
        }
      }

      // 收缩状态下的简化内容
      .collapsed-content {
        padding: 20px 0;
        text-align: center;

        .collapsed-info {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;

          .collapsed-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid #e8f4fd;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .collapsed-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #e8f4fd;
          }

          .collapsed-text {
            p {
              margin: 0;
              font-size: 12px;
              color: #666;
              font-weight: 500;
              writing-mode: vertical-rl;
              text-orientation: mixed;
              max-height: 60px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }

    // 右侧面板
    .right-panel {
      flex: 1;

      .right-content {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .card-container {
          background: #fff;
          border-radius: 12px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.05);
          border: 1px solid #e8f4fd;
          animation: fadeInUp 0.6s ease-out;
        }
      }
    }
  }

  // 通用卡片样式
  .card-container {
    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 24px 16px;
      border-bottom: 1px solid #f0f0f0;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        min-width: 0;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
          transition: all 0.3s ease;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .header-right {
        flex-shrink: 0;

        .collapse-btn {
          padding: 6px;
          border-radius: 4px;
          transition: all 0.3s ease;
          color: #666;

          &:hover {
            background-color: #f5f7fa;
            color: #5b8ff9;
          }

          .ivu-icon {
            margin: 0;
          }
        }
      }

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        flex: 1;
      }

      .card-actions {
        display: flex;
        gap: 8px;
      }
    }

    .card-content {
      padding: 24px;

      .default-content {
        text-align: center;
        padding: 40px 20px;
      }
    }
  }

  // 底部操作按钮
  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 16px 20px;
    border-top: 1px solid #e8eef0;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
    z-index: 1000;

    .actions-container {
      display: flex;
      justify-content: center;
      gap: 16px;
      max-width: 1200px;
      margin: 0 auto;

      .ivu-btn {
        min-width: 120px;
        height: 40px;
        border-radius: 6px;
        font-weight: 500;
        font-size: 14px;
      }
    }
  }

  // 响应式设计
  &.responsive {
    @media (max-width: 1200px) {
      .main-content {
        flex-direction: column;

        .left-panel {
          flex: none !important;
        }
      }
    }

    @media (max-width: 768px) {
      padding-bottom: 100px; // 移动端增加底部间距

      .main-content {
        padding: 12px;
        gap: 12px;
      }

      .card-container {
        .card-header {
          padding: 16px 20px 12px;

          h3 {
            font-size: 14px;
          }
        }

        .card-content {
          padding: 20px;
        }
      }

      .bottom-actions {
        padding: 20px;

        .actions-container {
          flex-direction: column;

          .ivu-btn {
            width: 100%;
            max-width: 300px;
            margin: 0 auto;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
