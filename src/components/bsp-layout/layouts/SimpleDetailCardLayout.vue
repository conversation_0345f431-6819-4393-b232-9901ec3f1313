<template>
  <div class="simple-detail-card-layout">
    <!-- 左侧面板 -->
    <div class="left-panel" :class="{ collapsed: isCollapsed }" :style="leftPanelStyle">
      <div class="panel-header" v-if="leftTitle || collapsible">
        <div class="title-area">
          <Icon v-if="leftIcon" :type="leftIcon" />
          <h3 v-if="leftTitle && !isCollapsed">{{ leftTitle }}</h3>
        </div>
        <Button
          v-if="collapsible"
          type="text"
          size="small"
          @click="toggle"
          class="toggle-btn"
        >
          <Icon :type="isCollapsed ? 'ios-arrow-forward' : 'ios-arrow-back'" />
        </Button>
      </div>

      <!-- 展开状态内容 -->
      <div v-if="!isCollapsed" class="panel-content">
        <slot name="left" :data="data" :update="updateData">
          <div class="default-content">
            <p>请使用 #left 插槽添加内容</p>
          </div>
        </slot>
      </div>

      <!-- 收缩状态内容 -->
      <div v-else class="collapsed-content">
        <slot name="collapsed" :data="data" :toggle="toggle">
          <div class="default-collapsed">
            <Icon :type="leftIcon || 'ios-person'" size="24" />
          </div>
        </slot>
      </div>
    </div>

    <!-- 右侧卡片区域 -->
    <div class="right-panel">
      <div class="cards-container">
        <!-- 动态卡片 -->
        <div
          v-for="(card, index) in cards"
          :key="card.name || index"
          class="card-item"
        >
          <div class="card-header" v-if="card.title">
            <div class="card-title">
              <Icon v-if="card.icon" :type="card.icon" />
              <h4>{{ card.title }}</h4>
            </div>
            <div class="card-actions" v-if="card.actions">
              <Button
                v-for="action in card.actions"
                :key="action.name"
                :type="action.type || 'default'"
                size="small"
                @click="handleCardAction(action, card, index)"
              >
                <Icon v-if="action.icon" :type="action.icon" />
                {{ action.label }}
              </Button>
            </div>
          </div>

          <div class="card-content">
            <slot
              :name="card.slot || `card-${index}`"
              :card="card"
              :index="index"
              :data="card.data || {}"
              :update="(newData) => updateCardData(index, newData)"
            >
              <div class="default-card-content">
                <p>请使用 #{{ card.slot || `card-${index}` }} 插槽添加内容</p>
              </div>
            </slot>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div v-if="actions && actions.length" class="bottom-actions">
      <div class="actions-container">
        <Button
          v-for="action in actions"
          :key="action.name"
          :type="action.type || 'default'"
          :size="action.size || 'large'"
          @click="handleAction(action)"
        >
          <Icon v-if="action.icon" :type="action.icon" />
          {{ action.label }}
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleDetailCardLayout',
  props: {
    // 左侧配置 - 简化为基础属性
    leftTitle: String,
    leftIcon: String,
    leftWidth: {
      type: String,
      default: '350px'
    },
    collapsedWidth: {
      type: String,
      default: '60px'
    },
    collapsible: {
      type: Boolean,
      default: true
    },

    // 数据
    data: {
      type: Object,
      default: () => ({})
    },

    // 卡片配置 - 简化结构
    cards: {
      type: Array,
      default: () => []
    },

    // 底部操作
    actions: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      isCollapsed: false
    }
  },

  computed: {
    leftPanelStyle() {
      return {
        width: this.isCollapsed ? this.collapsedWidth : this.leftWidth,
        transition: 'width 0.3s ease'
      }
    }
  },

  methods: {
    // 切换收缩状态
    toggle() {
      this.isCollapsed = !this.isCollapsed
      this.$emit('toggle', this.isCollapsed)
    },

    // 更新数据
    updateData(newData) {
      this.$emit('update:data', { ...this.data, ...newData })
    },

    // 更新卡片数据
    updateCardData(index, newData) {
      const updatedCards = [...this.cards]
      updatedCards[index] = {
        ...updatedCards[index],
        data: { ...updatedCards[index].data, ...newData }
      }
      this.$emit('update:cards', updatedCards)
    },

    // 处理卡片操作
    handleCardAction(action, card, index) {
      this.$emit('card-action', { action, card, index })
    },

    // 处理底部操作
    handleAction(action) {
      this.$emit('action', action)
    }
  }
}
</script>

<style lang="less" scoped>
.simple-detail-card-layout {
  display: flex;
  height: 100vh;
  background: #f5f7fa;

  // 左侧面板
  .left-panel {
    flex-shrink: 0;
    background: white;
    border-right: 1px solid #e8eaec;
    display: flex;
    flex-direction: column;

    .panel-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #e8eaec;

      .title-area {
        display: flex;
        align-items: center;
        gap: 8px;

        h3 {
          margin: 0;
          font-size: 16px;
          color: #2c3e50;
        }
      }

      .toggle-btn {
        padding: 4px;

        &:hover {
          background: #f5f7fa;
        }
      }
    }

    .panel-content {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
    }

    .collapsed-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 16px 8px;

      .default-collapsed {
        color: #999;
      }
    }

    &.collapsed {
      .panel-header {
        .title-area h3 {
          display: none;
        }
      }
    }
  }

  // 右侧面板
  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;

    .cards-container {
      flex: 1;
      padding: 16px;
      overflow-y: auto;

      .card-item {
        background: white;
        border-radius: 8px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);

        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px;
          border-bottom: 1px solid #e8eaec;

          .card-title {
            display: flex;
            align-items: center;
            gap: 8px;

            h4 {
              margin: 0;
              font-size: 14px;
              color: #2c3e50;
            }
          }

          .card-actions {
            display: flex;
            gap: 8px;
          }
        }

        .card-content {
          padding: 16px;
        }

        .default-card-content {
          text-align: center;
          color: #999;
          padding: 40px 20px;
        }
      }
    }
  }

  // 底部操作栏
  .bottom-actions {
    background: white;
    border-top: 1px solid #e8eaec;
    padding: 16px;

    .actions-container {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  .default-content {
    text-align: center;
    color: #999;
    padding: 40px 20px;
  }
}

// 响应式
@media (max-width: 768px) {
  .simple-detail-card-layout {
    flex-direction: column;

    .left-panel {
      width: 100% !important;
      height: auto;
      border-right: none;
      border-bottom: 1px solid #e8eaec;

      &.collapsed {
        height: 60px;

        .panel-content {
          display: none;
        }
      }
    }
  }
}
</style>
