<template>
  <div class="bsp-dynamic-layout" :class="layoutClass">
    <component
      :is="layoutComponent"
      v-bind="layoutProps"
      @layout-event="handleLayoutEvent"
      @card-action="handleCardAction"
      @bottom-action="handleBottomAction"
      @component-event="handleComponentEvent"
    >
      <!-- 传递所有插槽给子组件 -->
      <template v-for="(slot, name) in $slots" :slot="name">
        <slot :name="name"></slot>
      </template>

      <!-- 动态插槽内容 -->
      <template v-for="slot in layoutSlots" :slot="slot.name">
        <DynamicComponent
          v-if="slot.component"
          :key="slot.name"
          :component-config="slot.component"
          :data="mergeSlotData(slot)"
          :props="mergeSlotProps(slot)"
          :events="mergeSlotEvents(slot)"
          @component-event="handleComponentEvent"
          @update:data="handleDataUpdate"
          @update:props="handlePropsUpdate"
        />
        <div v-else-if="slot.content" :key="slot.name" v-html="slot.content"></div>
      </template>
    </component>
  </div>
</template>

<script>
import DynamicComponent from './DynamicComponent.vue'
import LayoutRenderer from './LayoutRenderer.vue'
import { layoutConfigs } from './configs/layoutConfigs.js'

export default {
  name: 'DynamicLayout',
  components: {
    DynamicComponent,
    LayoutRenderer
  },
  props: {
    // 布局类型
    layoutType: {
      type: String,
      required: true,
      validator: (value) => {
        return Object.keys(layoutConfigs).includes(value)
      }
    },
    // 布局配置
    layoutConfig: {
      type: Object,
      default: () => ({})
    },
    // 布局数据
    layoutData: {
      type: Object,
      default: () => ({})
    },
    // 是否启用响应式
    responsive: {
      type: Boolean,
      default: true
    },
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      currentLayoutConfig: null,
      layoutSlots: [],
      isLoading: false
    }
  },
  computed: {
    // 布局组件
    layoutComponent() {
      if (this.currentLayoutConfig && this.currentLayoutConfig.component) {
        return this.currentLayoutConfig.component
      }
      return 'LayoutRenderer'
    },

    // 布局样式类
    layoutClass() {
      const classes = []

      if (this.currentLayoutConfig && this.currentLayoutConfig.className) {
        classes.push(this.currentLayoutConfig.className)
      }

      if (this.responsive) {
        classes.push('bsp-layout-responsive')
      }

      if (this.customClass) {
        classes.push(this.customClass)
      }

      return classes.join(' ')
    },

    // 布局属性
    layoutProps() {
      return {
        ...(this.currentLayoutConfig && this.currentLayoutConfig.props ? this.currentLayoutConfig.props : {}),
        ...this.layoutConfig,
        layoutData: this.layoutData
      }
    }
  },
  watch: {
    layoutType: {
      handler: 'initLayout',
      immediate: true
    },
    layoutConfig: {
      handler: 'updateLayoutConfig',
      deep: true
    }
  },
  methods: {
    // 初始化布局
    async initLayout() {
      this.isLoading = true

      try {
        // 获取布局配置
        const config = layoutConfigs[this.layoutType]
        if (!config) {
          throw new Error(`未找到布局类型: ${this.layoutType}`)
        }

        // 深拷贝配置避免污染原始配置
        this.currentLayoutConfig = JSON.parse(JSON.stringify(config))

        // 初始化插槽
        await this.initLayoutSlots()

        // 触发布局初始化事件
        this.$emit('layout-initialized', {
          layoutType: this.layoutType,
          config: this.currentLayoutConfig
        })

      } catch (error) {
        console.error('布局初始化失败:', error)
        this.$emit('layout-error', error)
      } finally {
        this.isLoading = false
      }
    },

    // 初始化布局插槽
    async initLayoutSlots() {
      if (!this.currentLayoutConfig || !this.currentLayoutConfig.slots) {
        this.layoutSlots = []
        return
      }

      this.layoutSlots = await Promise.all(
        this.currentLayoutConfig.slots.map(async (slot) => {
          const slotConfig = { ...slot }

          // 如果插槽有组件配置，进行预处理
          if (slotConfig.component) {
            slotConfig.component = await this.processComponentConfig(slotConfig.component)
          }

          return slotConfig
        })
      )
    },

    // 处理组件配置
    async processComponentConfig(componentConfig) {
      // 如果是字符串，转换为组件配置对象
      if (typeof componentConfig === 'string') {
        return {
          name: componentConfig,
          props: {},
          data: {}
        }
      }

      // 如果是函数，执行获取配置
      if (typeof componentConfig === 'function') {
        return await componentConfig(this.layoutData)
      }

      return componentConfig
    },

    // 更新布局配置
    updateLayoutConfig() {
      if (this.currentLayoutConfig) {
        // 合并新的配置
        this.currentLayoutConfig = {
          ...this.currentLayoutConfig,
          ...this.layoutConfig
        }

        // 重新初始化插槽
        this.initLayoutSlots()
      }
    },

    // 处理布局事件
    handleLayoutEvent(event) {
      this.$emit('layout-event', {
        layoutType: this.layoutType,
        event
      })
    },

    // 处理组件事件
    handleComponentEvent(event) {
      this.$emit('component-event', {
        layoutType: this.layoutType,
        event
      })
    },

    // 处理卡片操作事件
    handleCardAction(event) {
      this.$emit('card-action', {
        layoutType: this.layoutType,
        ...event
      })
    },

    // 处理底部操作事件
    handleBottomAction(event) {
      this.$emit('bottom-action', {
        layoutType: this.layoutType,
        ...event
      })
    },

    // 处理数据更新
    handleDataUpdate(event) {
      // 更新对应插槽的数据
      const { slotName, data } = event
      this.updateSlotData(slotName, data)

      // 向父组件传递数据更新事件
      this.$emit('update:layout-data', {
        ...this.layoutData,
        [slotName]: data
      })
    },

    // 处理属性更新
    handlePropsUpdate(event) {
      const { slotName, props } = event
      this.updateSlotProps(slotName, props)

      // 向父组件传递属性更新事件
      this.$emit('update:layout-config', {
        ...this.layoutConfig,
        slots: this.layoutSlots.map(slot =>
          slot.name === slotName
            ? { ...slot, props: { ...slot.props, ...props } }
            : slot
        )
      })
    },

    // 合并插槽数据
    mergeSlotData(slot) {
      return {
        ...this.layoutData,
        ...slot.data,
        // 传递父组件的数据
        $parent: this.layoutData,
        $slot: slot.name
      }
    },

    // 合并插槽属性
    mergeSlotProps(slot) {
      return {
        ...slot.props,
        // 传递布局相关属性
        layoutType: this.layoutType,
        slotName: slot.name
      }
    },

    // 合并插槽事件
    mergeSlotEvents(slot) {
      return {
        ...slot.events,
        // 添加默认事件处理
        'update:data': (data) => this.handleDataUpdate({ slotName: slot.name, data }),
        'update:props': (props) => this.handlePropsUpdate({ slotName: slot.name, props })
      }
    },

    // 更新插槽内容
    updateSlot(slotName, config) {
      const slotIndex = this.layoutSlots.findIndex(slot => slot.name === slotName)
      if (slotIndex !== -1) {
        this.$set(this.layoutSlots, slotIndex, {
          ...this.layoutSlots[slotIndex],
          ...config
        })
      }
    },

    // 添加插槽
    addSlot(slotConfig) {
      this.layoutSlots.push(slotConfig)
    },

    // 移除插槽
    removeSlot(slotName) {
      const slotIndex = this.layoutSlots.findIndex(slot => slot.name === slotName)
      if (slotIndex !== -1) {
        this.layoutSlots.splice(slotIndex, 1)
      }
    },

    // 更新插槽数据
    updateSlotData(slotName, data) {
      const slotIndex = this.layoutSlots.findIndex(slot => slot.name === slotName)
      if (slotIndex !== -1) {
        this.$set(this.layoutSlots[slotIndex], 'data', {
          ...this.layoutSlots[slotIndex].data,
          ...data
        })
      }
    },

    // 更新插槽属性
    updateSlotProps(slotName, props) {
      const slotIndex = this.layoutSlots.findIndex(slot => slot.name === slotName)
      if (slotIndex !== -1) {
        this.$set(this.layoutSlots[slotIndex], 'props', {
          ...this.layoutSlots[slotIndex].props,
          ...props
        })
      }
    },

    // 获取插槽数据
    getSlotData(slotName) {
      const slot = this.layoutSlots.find(slot => slot.name === slotName)
      return slot ? slot.data : null
    },

    // 获取插槽属性
    getSlotProps(slotName) {
      const slot = this.layoutSlots.find(slot => slot.name === slotName)
      return slot ? slot.props : null
    },

    // 获取布局信息
    getLayoutInfo() {
      return {
        layoutType: this.layoutType,
        config: this.currentLayoutConfig,
        slots: this.layoutSlots,
        isLoading: this.isLoading
      }
    }
  }
}
</script>

<style lang="less" scoped>
.bsp-dynamic-layout {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;

  // 基础响应式样式，配合全局样式使用
  &.bsp-layout-responsive {
    // 全局样式已经处理了响应式逻辑
    // 这里只需要确保基础布局结构正确
    flex-direction: row;
  }
}
</style>
