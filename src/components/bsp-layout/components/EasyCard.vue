<template>
  <div class="easy-card">
    <!-- 卡片头部 -->
    <div v-if="title || $slots.header" class="card-header">
      <slot name="header">
        <div class="header-left">
          <Icon v-if="icon" :type="icon" />
          <h4>{{ title }}</h4>
        </div>
        <div v-if="$slots.actions" class="header-right">
          <slot name="actions"></slot>
        </div>
      </slot>
    </div>
    
    <!-- 卡片内容 -->
    <div class="card-body">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EasyCard',
  props: {
    title: String,
    icon: String
  }
}
</script>

<style lang="less" scoped>
.easy-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 16px;
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #e8eaec;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;
      
      h4 {
        margin: 0;
        font-size: 16px;
        color: #2c3e50;
      }
    }
  }
  
  .card-body {
    padding: 16px;
  }
}
</style>
