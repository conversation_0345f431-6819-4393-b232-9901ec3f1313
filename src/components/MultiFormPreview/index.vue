<template>
    <div class="MultiFormPreview">
        <div class="loadingBox" v-if="showLoad">
            <Spin fix >
                <Icon type="ios-loading" size=38 class="demo-spin-icon-load"></Icon>
                <div>加载中</div>
            </Spin>
        </div>
        <!-- 支持单页模式、双页模式、四页模式-->
        <div :class="errorCorrectionComparisonList.length > 0 ? 'referenceClass2' : 'referenceClass'">
            <div class="mp-title" v-if="errorCorrectionComparisonList.length > 0">原始材料</div>
            <div :class="['previewItemBox',sliptScreenStyle]" v-for="(model,modelIndex) in modelList_copy" :key="modelIndex+'_model'" @mouseenter.capture="modelAction(modelIndex)">
                <div :class="['previewItem',readTypeClassName,modelList_copy.length > 1 ? selectModel == modelIndex ? 'active' : '' : '']">
                    <div  class="list catalogue-panel catalogue-over" id="materialDiv">
                        <div class="img-panel clearfix" v-for="(item,index) in allImgList_new_limit" :key="index+'MM'">
                            <div class="PillarBox"></div>
                            
                            <div :class="['imgBox', selectIndex == index ? 'imgActive' : '']" @click="fileTagLocal_click($event,index,item)" @mouseenter="fileTagLocal($event,index,item)" @mousedown="moveStart($event,index)">
                                
                                <div class="currentBoxMenuWrap">
                                    <ul class="currentBoxMenu" v-show="model.check2 && (selectIndex == index)">
                                        <li @click="bookMarks(item)" title="书签">
                                            <i :class="item.hasLabel ? 'bookmark2' : 'bookmark'"></i>
                                        </li>
                                        <li @click="rotateAction(item,'-90')" class="rotate" title="左旋90" v-if="intelMarkingType !== 'agzx'">
                                            <i class="rotate-left" ></i>
                                        </li>
                                        <li @click="rotateAction(item,'90')" class="rotate" title="右旋90" v-if="intelMarkingType !== 'agzx'">
                                            <i class="rotate-right" ></i>
                                        </li>
                                        <li @click="printAction(item)" class="print" title="打印">
                                            <i class="print"></i>
                                        </li>
                                    </ul>
                                    <div class="currentBoxMenuBtn" @click.stop="closeMenuAction(modelIndex)">
                                        <i class="icon-down-arrow"></i>
                                    </div>
                                </div>
                                <span :id="(index+1)+'SN'" :class="['SerialNumber',((index+1)+'SN')]">{{(index+1)}}</span>
                                <i class="hasLabel" v-if="item.hasLabel"></i>
                                <!-- 签章、签名、捺印 -->
                                <ReviewResultDetail :key="index+'rrd'" v-if="showReviewResult" :fileId="item.id" :reviewResultList="reviewResultList[item.id]"/>
                                <!-- <div class="canvasImgBox"> -->
                                    <el-image class="showView" referrerPolicy="no-referrer" :src="changeUrl(item.url)" lazy></el-image>
                                    <!-- 点击标注 v-show="ShowFileMarks || fileTag"-->
                                    <FileTagList ref="fileTagCOM"  :canMoveMark="canMoveMark" :intelMarkingType="intelMarkingType" :markFilterIds="markFilterIds_cur" :fileId="item.id"  :markerId="markerId" :fileTagList="clickFileMarkList" />
                                    <!-- 框选标注v-show="ShowFileMarks || fileTag" -->
                                    <ui-marker ref="aiPanel-editor" :class="['ai-observer','UM'+item.id, {KX_MODE_STYLE: fileTag}]" 
                                    v-bind:uniqueKey="uuid"
                                    :className="'annotation UM'+item.id "
                                    :isInitMarker="true"
                                    :canMoveMark="canMoveMark_cur"
                                    @vmarker:onAnnoSelected="onAnnoSelected"
                                    @vmarker:onAnnoAdded="onAnnoAdded"
                                    @vmarker:onUpdated="onUpdated"
                                    @vmarker:onReady="onReady" 
                                    @vmarker:onImageLoad="onImageLoad"
                                    @vmarker:onAnnoDataFullLoaded="onAnnoDataFullLoaded"
                                    v-bind:readOnly="showAddCallouts ? (fileTag ? markType == 2 ? true : false : false) : true"
                                    v-bind:imgUrl="''">
                                    </ui-marker>
                                <!-- </div> -->
                                
                                
                                
                                
                            </div>
                        </div>
                        <!-- <p class="loadTips" v-if="loading">加载中...</p> -->
                    </div>
                </div>
                <!-- 每个盒子的操作功能 -->
                <div class="operatBtn" v-if="modelList_copy.length > 1" @click.self="modelAction_operat(modelIndex)"></div>
                <div class="toolBar" v-show="model.check && modelList_copy.length > 1">
                    <div class="pageNumPosition toolBarItem">
                        <label>页码</label>
                        <Input type="number" v-model="PageJumpNum[modelIndex]" class="nobutton" @on-enter="PageJumpAction"/>
                        <span>/{{allImgList.length}}页</span>
                    </div>
                    <div class="progress toolBarItem">
                        <Icon type="md-remove" size="25"  @click="sliderZoomAction('out')"/>
                        <Slider v-model="sliderNum[modelIndex]" :step="10" :max="600" :min="20" show-input @on-input="sliderAction"></Slider>
                        <Icon type="md-add" size="25" @click="sliderZoomAction('in')"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="errorCorrectionComparisonBox" v-if="errorCorrectionComparisonList.length > 0">
            <div class="mp-title">整改材料（标注序号-<i>{{markNum}}</i>）
            <span class="closedBtn" @click="closeEC">
                <i></i> 退出比对
            </span>
            </div>
            <div class="box">
                <template v-for="(item,index) in errorCorrectionComparisonList">
                    <el-image :key="index+'ECCL'" crossOrigin="Anonymous" lazy :src="changeUrl(item.fileUrl)"></el-image>
                </template>
            </div>
        </div>
        <Modal 
            width="400"
            class="imsProgressModalWrap ims"
            v-model="fileTagBox"
            :transfer="false"
            :footer-hide="true"
            @on-ok="AddFileTag"
            @on-cancel="tagCancel"
            :title="fileTagXh+'添加标注'"
            class-name="vertical-center-modal" >
            <div class="textareaBox">
                <textarea class="ContentDescription" v-model="fileTagContent" maxlength="200" placeholder="填写材料比对描述的内容" cols="30" rows="5"></textarea>
                <span class="cost_tpl_title_length">{{fileTagContent ? fileTagContent.length : 0}}/200</span> 
            </div>
            <ul class="operateBox">
                <li @click="tagCancel">取消</li>
                <li @click="AddFileTag(undefined)">确定</li>
            </ul>
        </Modal>
        <PrintView ref="printView"/>
        <TransferModal ref="TransferModal" @confirm="transferModalConfirm"></TransferModal>
    </div>
</template>
<script>
import {AIMarker} from 'vue-picture-bd-marker'
import FileTagList from './fileTags.vue'
import ReviewResultDetail from '@/components/InspectionRecord/ReviewResultDetail.vue'
import PrintView from '@/components/batchPrint/printView.vue'
import Bus from '@/libs/eventBus.js'
import API from '@/api'
import TransferModal from '@/components/TransferModal/index.vue'
export default {
    name: 'MultiFormPreview', //标准模式、横向阅读、多页展示、双页预览、证据比对（分屏2,4）
    props: {
        allImgList: {
            type: Array,
            default: ()=>[]
        },
        fileIdList: {
            type: Array,
            default: ()=>[]
        },
        intelMarkingType: {
            type: String,
            default: 'agzx'
        },
        businessId:{
            type: String,
            default:''
        },
        ajbh:{
            type:String,
            default:''
        },
        jgrybm:{
            type:String,
            default:''
        },
        markFilterIds: {
            type: Object,
            default: ()=>{
                return {
                    showAll: true,
                    markIds: []
                }
            }
        },
        canMoveMark: { // 是否可以移动标注
            type: Boolean,
            default: ()=> false
        },
        showAddCallouts: { // 是否开启添加标注
            type: Boolean,
            default: ()=> true
        }
    },
    components: {
        FileTagList,
        ReviewResultDetail,
        'ui-marker':AIMarker,
        PrintView,
        TransferModal
    },
    computed:{
        changeUrl(){
            return function(originalUrl){
                if(originalUrl!=undefined){
                    // originalUrl = originalUrl.replace(ImsServerConfig.uploadUrl,ImsServerConfig.downloadUrl)
                    // if(!originalUrl.includes('?')){
                    //     originalUrl = originalUrl + '?' + this.imgSize
                    // }
                    return originalUrl.indexOf(serverConfig.minioHttp)>-1?originalUrl:serverConfig.minioHttp+serverConfig.APP_CODE+'/'+originalUrl //.replace(serverConfig.uploadUrl, serverConfig.downloadUrl);
                    // return  originalUrl;
                }
            }
        },
        disabled () {
            return this.loading
        },
    },
    data() {
        return {
            showLoad: true,
            loading: false,
            isFinish:false,
            modelList: [
                {
                    name: 'view_a',
                    check: false,
                    check2: true,
                },
                {
                    name: 'view_b',
                    check: false,
                    check2: false,
                },
                {
                    name: 'view_c',
                    check: false,
                    check2: false,
                },
                {
                    name: 'view_d',
                    check: false,
                    check2: false,
                },
            ],
            markFilterIds_cur: {
                showAll: true,
                markIds: []
            },
            modelList_copy: [],
            sliptScreenStyle: '',
            selectModel: 0,
            markerId:'', //选择的标注id
            readType: '',
            readTypeClassName: 'SinglePageModeStyle',
            PageMatch:'w',
            PageMatch2:'w',
            fileTag: false,
            ShowFileMarks: true,
            fileTagList: [],
            currentNodeFileList: [],
            clickFileMarkList: [],
            uuid: "0da9130",// 标注参数
            KXObj: null,
            markType: 1,
            selectIndex: 0,
            selectIndex3: 1,
            fileTagContent: '',
            fileTagXh:1,
            fileTagBox:false,
            fileTagLocalObj:{},
            kx_className:'',
            checkedImgObj: {},
            initFlag: 0,
            splitFlag: true,
            slideFlag: 0,
            reviewResultList: [],
            showReviewResult: false,
            allImgList_new: [],
            allImgList_new_limit: [],
            PageJumpNum: [1, 1, 1, 1],
            sliderNum: [100, 100, 100, 100],
            errorCorrectionComparisonList: [],
            markNum: '',
            canMoveMark_cur: false,
            imgSize: 'width=400&height=565'
        }
    },
    watch:{
        allImgList(newVal){
            this.allImgList_new_limit = newVal;
        },
        markFilterIds(newVal){
            this.markFilterIds_cur = newVal;
            this.getFileTagList( this.$parent.getMarkList)
        },
        initFlag(newVal){
            if(newVal == 1){
                this.getFileTagList(this.$parent.getMarkList);
            }
        },
        canMoveMark(newVal){
            this.canMoveMark_cur = newVal
        },
        fileIdList(newVal){
            this.fileIdList_cur = newVal
            this.$forceUpdate()
        }
    },
    created(){
        this.fileIdList_cur = this.fileIdList
        this.canMoveMark_cur = this.canMoveMark
        // this.allImgList_new = this.allImgList;
        this.allImgList_new_limit = this.allImgList;
        this.markFilterIds_cur = this.markFilterIds;
    },
    mounted(){
        this.$nextTick(()=>{
            Bus.$on('ReviewResultList',(reviewResultList_copy)=>{ // 签章、签名、捺印数据处理
                let newArr = [];
                let obj = {}
                this.allImgList_new_limit.forEach((item,index)=>{
                    let arr = []
                    item.xh = (index+1)
                    reviewResultList_copy.forEach(item2=>{
                        if(item.id == item2.fileId){
                            arr.push(item2)
                        }
                    })
                    obj[item.id ]= arr
                })
                this.reviewResultList = obj
            })
            this.splitScreenAction('1')
        })
    },
    methods:{
        handleScrollScroll(){
            if(this.allImgList_new_limit.length < 60){ // 图片数大于60才开启加载
                return
            }
            let previewItemHeight = 0, viewHeight = 0, scrollTopNum = 0;
            if(this.readTypeClassName == 'HorizontalPreviewStyle'){
                scrollTopNum = document.getElementsByClassName('previewItem')[this.selectModel].scrollLeft
                previewItemHeight = document.querySelector('.previewItem').clientWidth
                viewHeight = document.querySelector('.list').clientWidth
            }else{
                scrollTopNum = document.getElementsByClassName('previewItem')[this.selectModel].scrollTop
                previewItemHeight = document.querySelector('.previewItem').clientHeight
                viewHeight = document.querySelector('.list').clientHeight
            }
            if(this.loading){
                if(scrollTopNum < 5 && this.allImgList_new_limit[0].xh > 1){
                    var eIndex = this.allImgList_new_limit[0].xh -1,
                        sIndex = (eIndex - 1) > 0 ? (eIndex - 1) : 0;
                    this.load('top', sIndex, eIndex)
                    this.loading = false
                    return
                }else if(scrollTopNum > (viewHeight - previewItemHeight - 5) && this.allImgList_new_limit[this.allImgList_new_limit.length -1].xh < (this.allImgList.length)){
                    var sIndex = this.allImgList_new_limit[this.allImgList_new_limit.length - 1].xh,
                        eIndex = (sIndex + 1) > (this.allImgList.length) ? (this.allImgList.length) : (sIndex + 1) 
                    this.load('bottom', sIndex, eIndex, viewHeight - previewItemHeight)
                    this.loading = false
                    return
                }
            }
        },
        load(type, sIndex, eIndex, scrollTopNum){
            var timer = setTimeout(() => {
                var ybmLength = document.querySelectorAll('.imgBox').length;
                if(ybmLength < this.allImgList.length){
                    if(ybmLength > 0){
                        if(type == 'top'){
                            // if(this.allImgList_new_limit.length > 2){
                                this.allImgList_new_limit.splice(this.allImgList_new_limit.length - 1, this.allImgList_new_limit.length);
                            // }
                            this.allImgList_new_limit.unshift(...this.allImgList.slice(sIndex,eIndex));
                            if(this.readTypeClassName == 'HorizontalPreviewStyle'){
                                document.getElementsByClassName('previewItem')[this.selectModel].scrollLeft = 0
                            }else{
                                document.getElementsByClassName('previewItem')[this.selectModel].scrollTop = 5
                            }
                        }else{
                            this.allImgList_new_limit.splice(0, 1);
                        
                            this.allImgList_new_limit.push(...this.allImgList.slice(sIndex,eIndex));
                         
                            if(this.readTypeClassName == 'HorizontalPreviewStyle'){
                                document.getElementsByClassName('previewItem')[this.selectModel].scrollLeft = scrollTopNum -100;
                            }else{
                                document.getElementsByClassName('previewItem')[this.selectModel].scrollTop = scrollTopNum -100;
                            }
                        }
                    }else{
                        this.allImgList_new_limit = this.allImgList.slice(sIndex,eIndex);
                    }
                    this.loading = true
                }
                clearTimeout(timer)
            }, 1000);
        },
        closeEC(){
            this.errorCorrectionComparisonList = []
            this.$nextTick(()=>{
                this.PageMatchAction(this.PageMatch);
                this.$forceUpdate();
            })
        },
        setErrorCorrectionComparisonList(list, materialFileId, markerId){ // 纠错比对
            if(list && list.length > 0){
                this.errorCorrectionComparisonList = list;
                this.markNum = list[0].markerNumber
                this.$nextTick(()=>{
                    this.PageMatchAction('w');
                    this.PageJumpEventCommon(materialFileId, markerId)
                    this.$forceUpdate();
                })
            }
        },  
        closeMenuAction(modelIndex){
            this.selectModel = modelIndex;
            this.modelList[modelIndex].check2 = !this.modelList[modelIndex].check2;
        },
        sliderZoomAction(type){ // +-缩放
            if(type == 'in'){
                this.sliderNum[this.selectModel] += 10
                if(this.sliderNum[this.selectModel] > 600){
                    this.sliderNum[this.selectModel] = 600
                    return
                }
            }else{
                this.sliderNum[this.selectModel] -= 10
                if(this.sliderNum[this.selectModel] < 50){
                    this.sliderNum[this.selectModel] = 50
                    return
                }
            }
            this.sliderEvent(this.sliderNum[this.selectModel])
            this.$forceUpdate()
        },
        rotateAction(item,val){ // 图片旋转
            if(this.intelMarkingType == 'agzx'){
                let imgBox =  document.querySelector('.UM'+item.id)
                if(imgBox){
                    imgBox.style.transform = "rotateZ("+  val +"deg)";
                }

            }else{
                var params = {
                    materialId: item.materialInfoId,
                    fileId: item.id,
                    fileUrl: item.url,
                    angel: val,
                };
                let url = ''
                if(this.intelMarkingType == 'zfkp'){
                    params.businessId = this.businessId 
                    url = API.DmsApi.ZNYJ_ROTATE_ZFKP
                }else{
                    url = API.DmsApi.ZNYJ_ROTATE
                }
                this.$store.dispatch("postRequest", {
                    url: url,
                    params
                }).then((res) => {
                    if(res.success) {
                        this.allImgList_new_limit[this.selectIndex].url = res.data.url;
                    }
                });
            }
        },
        transferModalConfirm({item}){ // 删除书签
            console.log(item,'transferModalConfirm')
            let params = {
                // jgrybm: this.jgrybm,
                // materiaInfoId: item.materialInfoId,
                materialId: item.materialInfoId,
                fileId:item.id
            }
            let url = ''
            if(this.intelMarkingType == 'zfkp'){
                params.businessId = this.businessId 
                url = API.DmsApi.DMS_REMOVEMATERIALLABEL_ZFKP
            }else{
                url = API.DmsApi.DMS_REMOVEMATERIALLABEL
            }
            this.$store.dispatch("getRequest", {
                url: this.$path.removeMaterialLabel,
                params
            }).then(res=>{
                if(res.success){
                    this.$parent.hasLabel = false;
                    
                    this.$set(this.allImgList_new_limit[this.selectIndex],'hasLabel',false);
                    var flag = false
                    this.allImgList_new_limit.forEach(item2=>{
                        if(item.materialId == item2.materialId && item2.hasLabel){
                            flag = true
                        }
                    })
                    if(!flag){
                        $('#'+item.materialId).hide();
                        this.$parent.nodesCopy.forEach(item2=>{
                            if(item2.id == item.materialId){
                                item2.hasLabel = false
                            }
                        })
                    }
                    this.$Message.success(res.msg || '删除成功')
                    this.$emit('getMaterialLabel')
                    // this.$refs.TransferModal.setTransferModalObj({
                    //     show: true,
                    //     content: res.msg || '删除成功',
                    //     type: 'success'
                    // })
                }
            })
        },
        bookMarks(item){ // 添加书签
            console.log(item,'bookMarks')
            // return
            var params = {
                jgrybm: this.jgrybm,
                // materiaInfoId: item.materialInfoId,
                // materialFileId: item.id,
                materialId:item.materialInfoId,
                // id:item.id,
                fileId:item.id,
                catalogId:item.materialId,
            }
            if(this.$parent.hasLabel){
                this.$refs.TransferModal.setTransferModalObj({
                    show: true,
                    width: '400',
                    content: '确定删除该书签吗？',
                    type: 'warning',
                    showCancelBtn: true,
                    params: {item},
                    title:'温馨提示'
                
                })
            }else{
                let url = ''
                if(this.intelMarkingType == 'zfkp'){
                    params.businessId = this.businessId 
                    url = API.DmsApi.DMS_SAVEMATERIALLABEL_ZFKP
                }else{
                    url = this.$path.materialLabelCreate//API.DmsApi.DMS_SAVEMATERIALLABEL
                }
                this.$store.dispatch("authPostRequest", {
                    url: url,
                    params
                }).then(res=>{
                    if(res.success){
                        this.$parent.hasLabel = true;
                        this.$set(this.allImgList_new_limit[this.selectIndex],'hasLabel',true);
                        $('#'+item.materialId).show();
                        this.$parent.nodesCopy.forEach(item2=>{
                            if(item2.id == item.materialId){
                                item2.hasLabel = true
                            }
                        })
                        this.$Message.success(res.msg || '添加成功')
                        this.$emit('getMaterialLabel')
                        // this.$refs.TransferModal.setTransferModalObj({
                        //     show: true,
                        //     content: res.msg || '添加成功',
                        //     type: 'success'
                        // })
                    }
                })
            }
        },
        printAction(item){ // 打印
            this.$refs.printView.doPrint([item])
        },
        splitScreenAction(val){ // 分屏的方法 val：分屏的数量
            this.modelList_copy = this.modelList.slice(0,val);
            switch (val){
                case '1':
                    this.sliptScreenStyle = 'oneView';
                    break;
                case '2':
                    this.sliptScreenStyle = 'doubleView';
                    break;
                case '4':
                    this.sliptScreenStyle = 'fourView';
                    break;
            }
            if(!this.splitFlag){
                this.$nextTick(()=>{
                    let timer = setTimeout(()=>{
                        clearTimeout(timer)
                        // this.PageMatchAction(this.PageMatch);
                        this.getFileTagList(this.$parent.getMarkList);
                        this.$forceUpdate();
                    },300)
                })
            }
        },
        modelAction(modelIndex){ // 每个盒子点击事件
            this.selectModel = modelIndex;
            this.$parent.selectModel = modelIndex;
        },
        modelAction_operat(modelIndex){ // 每个盒子功能菜单点击事件
            this.selectModel = modelIndex;
            this.modelList[modelIndex].check = !this.modelList[modelIndex].check;
        },
        getUuid() { 
            var s = [];
            var hexDigits = "0123456789abcdef";
            for (var i = 0; i < 32; i++) {
                s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
            }
            s[14] = "4";
            s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);

            var uuid = s.join("");
            return uuid;
        },
        PageJumpAction(){ // 页面跳转定位
            this.showLoad = true
            this.PageJumpNum[this.selectModel] = Number(this.PageJumpNum[this.selectModel])
            if(this.PageJumpNum[this.selectModel] < 1){
                this.PageJumpNum[this.selectModel] = 1;
            }else if(this.PageJumpNum[this.selectModel] > this.allImgList.length){
                this.PageJumpNum[this.selectModel] = this.allImgList_new.length;
            }
            this.$forceUpdate()
            let currentOne = this.allImgList[this.PageJumpNum[this.selectModel] - 1]
            console.log(currentOne,'currentOne')
            this.PageJumpEventCommon(currentOne.id)
        },
        PageJumpEventCommon(id2, markerId,uuid){
            // console.log(id2, markerId,document.querySelector('.UM'+id2),'id2, markerId',uuid)
            this.showLoad = true
            this.$nextTick(()=>{
                let parentNode = document.querySelector('.UM'+id2).parentNode
                if(this.readTypeClassName == 'HorizontalPreviewStyle'){
                    let scrollLeft = parentNode.offsetLeft;
                    document.getElementsByClassName('previewItem')[this.modelList_copy.length > 1 ? this.selectModel : 0].scrollLeft = scrollLeft - 10;
                    
                }else{
                    let scrollTop = parentNode.offsetTop;
                    document.getElementsByClassName('previewItem')[this.modelList_copy.length > 1 ? this.selectModel : 0].scrollTop = scrollTop - 10;
                }

                let SN = parentNode.querySelector('.SerialNumber').innerText;
                this.PageJumpNum[this.selectModel] = Number(SN)
                this.$parent.PageJumpNum = Number(SN)
                this.getFileTagList(this.$parent.getMarkList);
                // -------------------------------------
                
                if(markerId){
                    this.$nextTick(()=>{
                        let previewItem=uuid?document.querySelectorAll('div[data-uuid="'+uuid+'"]'):document.getElementsByClassName('previewItem')[this.modelList_copy.length > 1 ? this.selectModel : 0].getElementsByClassName('selectedMarkStyle')
                        let selectedMarkStyle = Array.prototype.slice.call(previewItem )
                        // console.log(selectedMarkStyle,previewItem,'selectedMarkStyle')
                        if(selectedMarkStyle.length > 0){
                            selectedMarkStyle.forEach(item=>{
                                item.classList.remove('selectedMarkStyle', 'selectedMarkStyle-red', 'selectedMarkStyle-blue', 'selectedMarkStyle-yellow')
                            })
                        }
                        let pMarkIds=uuid?document.querySelectorAll('div[data-uuid="'+uuid+'"]'):document.getElementsByClassName('previewItem')[this.modelList_copy.length > 1 ? this.selectModel : 0].getElementsByClassName('markId-'+markerId)

                        let p_markIds = Array.prototype.slice.call(pMarkIds)
                        // let dataUuid=document.getElementsByAttribute('data-uuid', uuid);
                        // var element = document.querySelector('[data-uuid="'+uuid+'"]');
                        //  console.log(element);
                        // console.log(element,'dataUuid')
                        p_markIds.forEach(item=>{
                            let className = item.getAttribute('class')
                            let colorArr = ['red', 'yellow', 'blue']
                            colorArr.forEach(item2=>{
                                if(className.indexOf(item2) != -1){
                                    item.classList.add('selectedMarkStyle','selectedMarkStyle-'+item2)
                                }
                                if(uuid){
                                    item.classList.add('selectedMarkStyle','selectedMarkStyle-'+item2)
                                }
                            })
                        })
                    })
                }
                let timer = setTimeout(() => {
                    clearTimeout(timer)
                    this.showLoad = false
                }, 1000);
            })
        },
        PageJumpEvent(id2,markerId,uuid){ // 页面定位事件
            if(this.readTypeClassName == 'HorizontalPreviewStyle'){
                this.PageJumpEventCommon(id2,markerId,uuid)
            }else{
                this.PageJumpEventCommon(id2,markerId,uuid)
            }
            // this.$nextTick(()=>{
                // let selectedMarkStyle = Array.prototype.slice.call(document.getElementsByClassName('selectedMarkStyle'))
                // if(selectedMarkStyle.length > 0){
                //     selectedMarkStyle.forEach(item=>{
                //         item.classList.remove('selectedMarkStyle', 'selectedMarkStyle-red', 'selectedMarkStyle-blue', 'selectedMarkStyle-yellow')
                //     })
                // }
                // let p_markIds = Array.prototype.slice.call(document.getElementsByClassName('markId-'+markerId))
                // p_markIds.forEach(item=>{
                //     let className = item.getAttribute('class')
                //     let colorArr = ['red', 'yellow', 'blue']
                //     colorArr.forEach(item2=>{
                //         if(className.indexOf(item2) != -1){
                //             item.classList.add('selectedMarkStyle','selectedMarkStyle-'+item2)
                //         }
                //     })
                // })
                
            // })
        },
        sliderAction(val){ // 滑块 图片放大与缩小
            this.$nextTick(()=>{
                this.sliderEvent(val)
            })
        },
        getFileTagList(list){ // 获取标注信
            console.log(list,'getFileTagList',list.length)
            this.$nextTick(()=>{
                this.clickFileMarkList = [];
                if(list.length > 0){
                    this.fileTagList = list;
                    this.$parent.fileTagList = list;
                    this.fileTagXh = Number(list.length)+1//Number(list[list.length-1].number) + 1;
                    
                    var KXMarkList = []
                    list.forEach(item=>{
                        var markerObj = typeof(item.marker) == 'string' ? JSON.parse(item.marker) : item.marker;
                        // console.log(markerObj,'markerObj')
                        if(markerObj.uuid){
                            markerObj.id = item.id
                            markerObj.materialFileId = item.materialFileId;
                            markerObj.materialId = item.materialId;
                            markerObj.fileId=item.fileId
                            KXMarkList.push(markerObj)
                        }else{
                            this.clickFileMarkList.push(item);
                        }
                        // console.log(this.markFilterIds_cur.showAll,KXMarkList,this.clickFileMarkList,'this.clickFileMarkListthis.clickFileMarkListthis.clickFileMarkList')
                    })
                    console.log(this.clickFileMarkList,'this.clickFileMarkList')
                    if(this.$refs['aiPanel-editor']){
                        this.$refs['aiPanel-editor'].forEach(item=>{
                            let currentKXMark = []
                            if(!this.markFilterIds_cur.showAll){ 
                                KXMarkList.forEach(item2=>{
                                    if(item && item.$el._prevClass && item.$el._prevClass.indexOf(item2.fileId) !== -1 && this.markFilterIds_cur.markIds.indexOf(item2.fileId) !== -1){
                                        currentKXMark.push(item2)
                                    }
                                })
                            }else{ // 标注过滤
                                KXMarkList.forEach(item2=>{
                                    console.log(item,item.$attrs.className,item2,item.$attrs.className.indexOf(item2.fileId))
                                    if(item && item.$attrs  && item.$attrs.className.indexOf(item2.fileId) !== -1){
                                        currentKXMark.push(item2)
                                    }
                                })
                                // console.log(currentKXMark,KXMarkList,'currentKXMarkcurrentKXMark')
                            }
                            item.clearData();
                            item.renderData(currentKXMark);
                        })
                    }
                }else{
                    this.fileTagList = [];
                    this.clickFileMarkList = [];
                    this.fileTagXh = 1;
                    if(this.$refs['aiPanel-editor']){
                        this.$refs['aiPanel-editor'].forEach(item=>{
                            item.clearData();
                            
                        })
                    }
                }
            })
        }, 
        fileTagLocal(ele,index,item){ // 点击--标注位置
            this.selectIndex = index;
            this.$parent.selectIndex = index;
            this.$parent.hasLabel = item.hasLabel;
            this.checkedImgObj = item;
            ele.stopPropagation();
            this.$parent.fileTagLocal(ele)
            this.$parent.nodeLocalById(item.materialId)
        },
        fileTagLocal_click(ele,index,item){
            this.$parent.fileTagLocal_click(ele)
        },
        moveStart(e,index){ // 框选移动标注
            if(this.showAddCallouts){
                this.selectIndex = index;
                this.$parent.moveStart(e)
            }
        },
        onImageLoad(){ //图片加载完成后回调
            
        },
        onReady(){ //当控件准备完成后回调
            this.initFlag++
            if(this.allImgList_new_limit.length == 1 ||  this.initFlag == (this.allImgList_new_limit.length - 1)){
                this.$nextTick(()=>{
                    let timer = setTimeout(()=>{
                        this.showLoad = false
                        clearTimeout(timer)
                        // this.PageMatchAction('h');
                        this.initFlag = 0;
                        if(this.splitFlag){
                            this.getFileTagList( this.$parent.getMarkList)
                            this.$forceUpdate();
                            this.splitFlag = false;
                        }
                    },500)
                })
            }
        },
        onAnnoSelected(data){ //当选中图片上的标注框时回调
            this.$parent.onAnnoSelected(data)
        },
        onAnnoAdded(data){ //当画完一个标注框时回调
            if(this.fileTag  && this.markType == 1){
                data.tagName = this.fileTagXh;
                this.KXObj = data;
            }
        },
        onUpdated(data){ // 当标注框位置或者标框属性发生改动时回调
            
        },
        onAnnoDataFullLoaded(data){
            // if(this.fileTag && this.markType == 1){
            //     console.log(data)
            //     // this.$refs['aiPanel-editor'][this.selectIndex].getMarker().setTag({
            //     //     tagName:this.fileTagXh-1,
            //     //     tag: "0x0001"
            //     // });
            // }
        },
        AddFileTag(moveMarkObj){ // 添加标注内容 
            this.fileTagBox = false
            this.$parent.AddFileTag(moveMarkObj)
        },
        tagCancel(){ //取消添加标注
            this.fileTagBox = false
            if(this.markType == 1){
                var KXitem = document.querySelectorAll('.annotation');
                KXitem = Array.prototype.slice.call(KXitem)
                KXitem.forEach(item=>{
                    let className = item.getAttribute('class');
                    if(className.indexOf('bookmark') == -1 && !className.includes('KXCLASS')){
                        item.remove();
                    }
                })
            }
        },
        PageMatchAction(name){ //页面宽高匹配 默认宽度匹配
            // let imgBoxArr = Array.prototype.slice.call(document.querySelectorAll('.imgBox'));
            // let attrVal = imgBoxArr[0].getAttribute('style')
            // if(name == 'w'){
            //     if(!attrVal || attrVal.indexOf('100%') == -1){
            //         let viewWidth = document.querySelector('.previewItemBox').clientWidth - 32,
            //             imgBoxWidth = document.querySelector('.imgBox').querySelector('.imgFlag').naturalWidth;
            //         let num = (viewWidth / imgBoxWidth).toFixed(2);
            //         this.$parent.sliderNum = num * 100;
            //     }
            // }else if(name == 'h'){
            //     if(imgBoxArr[0].clientHeight !== document.querySelector('.previewItemBox').clientHeight){
            //         let viewHeight = document.querySelector('.previewItemBox').clientHeight - 10,
            //             imgBoxHeight = document.querySelector('.imgBox').querySelector('.imgFlag').naturalHeight;
            //         let num = (viewHeight / imgBoxHeight).toFixed(2);
            //         this.$parent.sliderNum = num * 100;
            //     }
            // }else{
            //     this.$parent.sliderNum = Number(name);
            // }
            // this.sliderEvent(this.$parent.sliderNum,'all')
            let imgBoxArr = Array.prototype.slice.call(document.querySelectorAll('.imgBox'));
            let attrVal = imgBoxArr[0].getAttribute('style')
            if(name == 'w'){
                if(!attrVal || attrVal.indexOf('100%') == -1){
                    let viewWidth = document.querySelector('.previewItemBox').clientWidth - 32,
                        imgBoxWidth = 620;
                    let num = (viewWidth / imgBoxWidth).toFixed(2);
                    this.$parent.sliderNum = num * 100;
                }
            }else if(name == 'h'){ 
                if(imgBoxArr[0].clientHeight !== document.querySelector('.previewItemBox').clientHeight){
                    let viewHeight = document.querySelector('.previewItemBox').clientHeight - 10,
                        imgBoxHeight = 877;
                    let num = (viewHeight / imgBoxHeight).toFixed(2);
                    this.$parent.sliderNum = num * 100;
                }
            }else{
                this.$parent.sliderNum = Number(name);
            }
            this.sliderEvent(this.$parent.sliderNum,'all')
        },
        sliderEvent(val,type){ //滑块 图片放大与缩小
            // if(val > 600){
            //     val = 600
            // }else if(val < 0){
            //     val = 10
            // }
            // this.sliderNum[this.selectModel] = val;
            // let imgBoxWidth = document.querySelector('.imgBox').querySelector('.imgFlag').naturalWidth / 100,
            //     imgBoxHeight = document.querySelector('.imgBox').querySelector('.imgFlag').naturalHeight / 100,
            //     getWidth = imgBoxWidth * val,
            //     getHeight = imgBoxHeight * val;
            // if(type && type == 'all'){
            //     var imgBoxArr = Array.prototype.slice.call(document.querySelectorAll('.imgBox'));
            // }else{
            //     var imgBoxArr = Array.prototype.slice.call(document.getElementsByClassName('previewItem')[this.modelList_copy.length > 1 ? this.selectModel : 0].querySelectorAll('.imgBox'));
            // }

            // imgBoxArr.forEach(item=>{
            //     item.style.width = getWidth  +'px';
            //     item.style.height = getHeight + 'px';
            // })
            
            if(val >= 100){
                this.imgSize = ''
            }else{
                this.showLoad = true
                this.imgSize = 'width=400&height=565'
            }

            this.showLoad = true
            let timer = setTimeout(() => {
                clearTimeout(timer)
                this.showLoad = false
            }, 1500);

            if(val > 600){
                val = 600
            }else if(val < 0){
                val = 10
            }
            let val2 = val / 100
            if(type && type == 'all'){
                var imgBoxArr = Array.prototype.slice.call(document.querySelectorAll('.imgBox'));
            }else{
                var imgBoxArr = Array.prototype.slice.call(document.getElementsByClassName('previewItem')[this.modelList_copy.length > 1 ? this.selectModel : 0].querySelectorAll('.imgBox'));
            }
            imgBoxArr.forEach(item=>{
                // 这里注释的原因是蒙说暂不考虑不规则图片的情况，所以暂不用获取图片真实的宽高比
                // let imgDom = item.querySelector('.el-image__inner')
                // if(imgDom && imgDom.naturalWidth){
                //     let NW = imgDom.naturalWidth, NH = imgDom.naturalHeight, percentage = NW/NH
                    // item.style.width = (620 * val2 / 100) +'rem';
                    // item.style.height = (620 * val2 / 100) / percentage + 'rem';
                // }
                item.style.width = (620 * val2 / 100) +'rem';
                item.style.height = (877 * val2 / 100) + 'rem';
            })
            // 没有固定图片的宽高时，需要标注的图层的宽高同上
            document.querySelectorAll('.annotate').forEach(item=>{
                let style = item.getAttribute('style')
                // item.style.width = (620 * val2 / 100) + 'rem';
                item.style.cssText = style+"height:"+(877 * val2 / 100) + "rem !important";
            })
        },
        readModeEvent(type){ // 阅读模式事件
            switch(type){
                case 'HR': // 横向预览
                    this.readTypeClassName = 'HorizontalPreviewStyle';
                    break;
                case 'MPD': // 多页展示
                    this.readTypeClassName = 'MultiPageDisplayStyle';
                    this.PageMatchAction('h')
                    break;
                case 'SPM': // 单页预览
                    this.readTypeClassName = 'SinglePageModeStyle';
                    this.PageMatchAction('w')
                    break;
                default:

            }
        }
    },
}
</script>
<style lang="less" scoped>
    /deep/.ivu-input-number{
        height: 0.32rem;
        line-height: 0.32rem;
        position: absolute;
        right: -0.45rem;
    }
.canvasImgBox{
    width: 100%;
    height: 100%;
}
/deep/ .vmr-ai-panel{
    background: unset;
}
.KX_MODE_STYLE{
    z-index: 3;
}
.imgFlag{
    width: 0.1rem;
    height: 0.1rem;
    position: absolute;
    top: 0;
    right: 0;
    z-index: -1;
}
.errorCorrectionComparisonBox{
    width: 49.5%;
    height: 100%;
    overflow: auto;
    float: right;
    position: relative;
    .box{
        width: 100%;
        overflow: auto;
    }
    .el-image{
        width: 100%;
        height: 100%;
    }
    .closedBtn{
        display: inline-block;
        width: 1rem;
        height: 0.26rem;
        line-height: 0.26rem;
        background: #c03a5d;
        position: absolute;
        right: 0.1rem;
        top: 0.028rem;
        font-size: 0.14rem;
        text-align: center;
        cursor: pointer;
        border-radius: 0.04rem;
        i{
            display: inline-block;
            width: 0.2rem;
            height: 0.2rem;
            background: url('../../assets/images/dzjz/tc.png') no-repeat;
            background-size: 100% 100%;
            vertical-align: sub;
        }
    }
    .closedBtn:hover{
        opacity: 0.7;
    }
}
.MultiFormPreview{
    width: 100%;
    height: calc(~'100% - 0.4rem');
    background: #63707C;
    box-sizing: border-box;
    // display: flex;
    // justify-content: space-between;
    .loadingBox{
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        font-size: 0.18rem;
        background: #fff;
        z-index: 99;
    }
    .mp-title{
        width: 100%;
        height: 0.32rem;
        line-height: 0.32rem;
        background: #2b507c;
        color: #fff;
        font-size: 0.16rem;
        text-align: left;
        margin-bottom: 0.02rem;
        padding-left: 0.1rem;
        i{
            font-weight: bold;
        }
    }
    .previewItemBox{
        position: relative;
        .previewItem{
            height: 100%;
            overflow: auto;
            margin: 0 0.05%;
            display: flex;
            flex-wrap: wrap;
            padding: 0.1rem;
            &.active{
                border: solid 0.04rem #FFA941;
            }
        }
        .currentBoxMenuWrap{
            width: 2.5rem;
            position: absolute;
            left: 50%; 
            top: 0;   
            z-index: 9;
            transform: translateX(-50%);
            .currentBoxMenuBtn{
                width: 100%;
                height: 0.18rem;
                line-height: 0.18rem;
                background: #319FFF;
                opacity: 0.8;
                border-radius: 0px 0px 0.04rem 0.04rem;
                text-align: center;
                cursor: pointer;
                .icon-down-arrow{
                    display: inline-block;
                    width: 0.12rem;
                    height: 0.1rem;
                    background: url('../../assets/images/znaj/icon-down-arrow.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
            .currentBoxMenu{
                width: 100%;
                height: 0.56rem;
                background: rgba(46, 54, 77,0.8);
                padding: 0 0.13rem;
                display: flex;
                justify-content: space-around;
                align-items: center;
                li{
                    cursor: pointer;
                    width: 0.4rem;
                    height: 0.4rem;
                    background: #1E2837;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 50%;
                    margin: 0 0.08rem;
                    i{
                        display: inline-block;
                        width: 100%;
                        height: 100%;
                    }
                    .bookmark{
                        background: url('../../assets/images/dzjz/znyj/book-mark.png') no-repeat center !important;
                        background-size: 100% 100%;
                    }
                    .bookmark2{
                        background: url('../../assets/images/dzjz/znyj/book-mark-select.png') no-repeat center !important;
                        background-size: 100% 100%;
                    }
                    .rotate-left{
                        background: url('../../assets/images/dzjz/znyj/rotate-left.png') no-repeat center !important;
                        background-size: 100% 100%;
                    }
                    .rotate-right{
                        background: url('../../assets/images/dzjz/znyj/rotate-right.png') no-repeat center !important;
                        background-size: 100% 100%;
                    }
                    .print{
                        background: url('../../assets/images/dzjz/znyj/icon-print.png') no-repeat center !important;
                        background-size: 100% 100%;
                    }
                    .closeMenu{
                        background: url('../../assets/images/dzjz/znyj/cancel-merge.png') no-repeat center !important;
                        background-size: 100% 100%;
                    }
                }
                li:hover{
                    background: #319FFF;
                }
            }
        }
        .normalModeStyle{
            display: flex;
            flex-wrap: wrap;
            // justify-content: center;
            overflow: auto;
            .img-panel{
                margin-bottom: 0.1rem;
            }
        }
        .HorizontalPreviewStyle{
            display: flex;
            flex-wrap: nowrap;
            overflow: auto;
            .list{
                height: 100%;
                display: flex;
                flex-wrap: nowrap;
            }
        }
        .MultiPageDisplayStyle{
            display: flex;
            flex-wrap: wrap;
            // justify-content: center;
            overflow: auto;
            .img-panel{
                margin: 0 auto 0.1rem;
            }
            .list{
                height: 100%;
                display: flex;
                flex-wrap: wrap;
            }
        }
        .SinglePageModeStyle{
            display: block;
            overflow: auto;
            .img-panel{
                margin-bottom: 0.1rem;
            }
        }
        .operatBtn{
            width: 0.24rem;
            height: 0.24rem;
            background: url('../../assets/images/dzjz/dzjz_jzlx.png') no-repeat;
            background-size: 100% 100%;
            position: absolute;
            right: 0.2rem;
            bottom: 0.15rem;
            z-index: 14;
            cursor: pointer;
        }
        .toolBar{
            width: calc(~'100% - 0.15rem');
            height: 0.5rem;
            background: rgba(46, 54, 77,0.8);
            position: absolute;
            left: 0.05rem;
            bottom: 0;
            z-index: 10;
            display: flex;
            align-items: center;
            padding: 0 0.2rem;
            .toolBarItem{
                margin-right: 0.3rem;
            }
            .pageNumPosition{
                display: flex;
                align-items: center;
                float: left;
                color: #fff;
            }
            .progress{
                width: 45%;
                display: flex;
                position: relative;
                margin: 0.2rem 0;
                line-height: 0.4rem;
                justify-content: center;
                align-items: center;
                i{
                    cursor: pointer;
                    color: #fff;
                }
            }
        }
    }
    .previewItemBox:nth-child(even){
        margin-left: 0.2rem;
    }
    .oneView{
        width: 100%;
        height: 100%;
    }
    .doubleView{
        width: calc(~'49% - 0.1rem');
        height: 100%;
    }
    .fourView{
        width: calc(~'49% - 0.1rem');
        height: 48%;
        margin-bottom: 0.1rem;
    }
    .imgActive{
        border: solid 0.04rem #087EFF;
        box-sizing: border-box;
    }
    .hasLabel{
        display: inline-block;
        width: 0.254rem;
        height: 0.389rem !important;
        background-image: url('../../assets/images/Bookmark.png') !important;
        background-size: 100% 100%;
        position: absolute;
        right: 0.9rem;
        top: 0;
        z-index: 3;
    }
    .referenceClass{
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        .imgBox{
            transform-origin:0 0;
            position: relative;
            width: 6.2rem;
            height: 8.77rem;
            margin: 0 auto 0;
        }
        .showView{
            width: 100%;
            height: 100%;
            position:absolute;
            left: 0;
            top: 0;
            z-index: 2;
            background: #fff;
        }
    }
    .referenceClass2{
        width: 49.5%;
        height: calc(~'100% - 0.32rem');
        display: flex;
        flex-wrap: wrap;
        float: left;
        .imgBox{
            width: 6.2rem;
            height: 8.77rem;
            transform-origin:0 0;
            position: relative;
            margin: 0 auto 0;
        }
        .showView{
            width: 100%;
            height: 100%;
            position:absolute;
            left: 0;
            top: 0;
            z-index: 2;
        }
    }
    .img-panel{
        margin: 0 0.05rem;
    }
    
    .SerialNumber{
        display: inline-block;
        padding: 0 0.08rem;
        font-size: 0.18rem;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 9;
        color: #333;
        background: #FAE688;
        font-family: 'MicrosoftYaHei-Bold';
    }
}

.demo-spin-col .circular {
    width:25px;
    height:25px;
}
.demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
}
.demo-spin-col{
    height: 100px;
    position: relative;
    border: 1px solid #eee;
}
/deep/ .vmr-g-image{
    overflow: unset !important;
    .vmr-ai-raw-image{
        display: none !important;
    }
}
/deep/.imsProgressModalWrap{
    .ivu-modal-header{
        background: #087EFF;
    }
}
/deep/ .ivu-modal-content{
    border-radius: 0.04rem;
    overflow: hidden;
    .ivu-modal-body{
        padding: 0;
    }
}

</style>
