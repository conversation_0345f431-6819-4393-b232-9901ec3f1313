<template>
    <div class="room-select">
      <span class="value-inp" @click="showModal"><u class="icon-filter"></u><span class="value-text">{{name}}</span></span>
      <Modal :width="904" v-model="roomSelectModalShow" title="监室选择" >
        <div class="com-modal-container" v-if="roomSelectModalShow">
          <div class="com-module-layout">
            <tabs ref="tabs" v-show="showFormCompnent" mark="roomSelect" @changeTabsMark="changeTabsMark" :params="{}">
            </tabs>
          </div>
        </div>
      </Modal>
    </div>
</template>

<script>
// import { findRoomWarderPageInfo, findBySquadronId } from "@/axios/zhjgBasicBusiness";
import tabs from "@/components/tabs/index.vue";
export default {
  name: "roomSelect",
  components: {
	tabs
  },
  props: {
    roomId: {
      type: String
    },
    roomName: {
      type: String
    }
  },
  data() {
    return {
      roomSelectModalShow: false,
      param1: {
        roomType: "w",
        type: 1
      },
      param2: {
        roomType: "g",
        type: 1,
        pageSize: 999,
        status: 0
      },
      roomLists1: [],
      roomLists2: [],
      squadronNameList: [],
      name: "",
      tabName: "w"
    };
  },
  computed: {
    prisonId() {
      return this.$store.state.userInfo.prisonId;
    }
  },
  model: {
    prop: "roomId",
    event: "change"
  },
  methods: {
    async selectChange() {
      let res2 = await findRoomWarderPageInfo(this.param2);
      this.roomLists2 = res2.data.rows;
    },
    showModal() {
      this.$set(this, "param2", {
        roomType: "g",
        type: 1,
        pageSize: 999,
        status: 0
      });
      this.selectChange();
      this.roomSelectModalShow = true;
    },
    findBySquadronId() {
      findBySquadronId({prisonId: this.prisonId}).then(res => {
        this.squadronNameList = res.data;
      });
    },
    setData(row) {
      this.name = row.roomName;
      this.$emit("change", row.id);
      this.$emit("on-change", row);
    },
    toSelect(row) {
      this.roomSelectModalShow = false;
      this.setData(row);
    },
    async getInfo() {
      let res1 = await findRoomWarderPageInfo(this.param1);
      let res2 = await findRoomWarderPageInfo(this.param2);
      this.roomLists1 = res1.data.rows;
      this.roomLists2 = res2.data.rows;
      if (!this.roomId) {
        if (this.tabName === "w" && this.roomLists1.length > 0) {
          let row = this.roomLists1[0];
          this.setData(row);
        } else  {
          let row = this.roomLists2[0];
          this.setData(row);
        }
      } else {
        this.roomLists2.forEach((item) => {
          if (item.id === this.roomId) {
            this.name = item.roomName;
          }
        });
      }
    }
  },
  async created() {
    // this.findBySquadronId();
    // this.getInfo();
  }
};
</script>

<style lang="less" scoped>
.value-inp {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
}
.icon-filter {
  width:28px;
  height:28px;
  background:url(./images/icon_filter.png) no-repeat center center;
  display:inline-block;
  vertical-align: middle;
}
.value-text {
  color:#2390FF;
  font-size:20px;
  vertical-align: middle;
}
.card-box {
  height:100%;
  position: relative;
  display:flex;
  flex-direction: column;
  padding-bottom: 16px;
  &.has-condition {
    height:calc( 100% - 70px );
  }
  .card-content-box {
    flex: 1;
    overflow: auto;
    padding: 16px 0;
    display: grid;
    grid-template-columns: repeat(4, 204px);
    align-items: center;
    justify-items: center;
    grid-row-gap: 16px;
    justify-content: space-between;
    align-content: start;
  }
}
.card-page-box {
  padding:16px 0;
  display:flex;
  justify-content: flex-end;
  border-top:1px solid #E4EAF0;
}
</style>