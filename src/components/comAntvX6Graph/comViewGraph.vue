<!-- 预览 -->
<template>
  <div id="container-id" ref="containerRef" >
    <div id="graph-container-id"></div>
    <graphToolbar ref="graphToolbar" :graph="graph"></graphToolbar>
  </div>
</template>

<script>
import elementResizeDetector from "element-resize-detector";
import { Graph, Shape } from "@antv/x6";
import { getGraphNodeName, setRightTipAttr } from "@/util";
import graphToolbar from "./graphToolbar.vue";

export default {
  name: "comViewGraph",
  components: {
    graphToolbar,
  },
  props: {
    setting: {
      type: Object,
      default: () => ({}),
    },
    renderJson: {
      type: Object,
      default: () => ({}),
    },
    busCount: {
      type: Object,
      default: () => ({}),
    },
    // 不可点击的节点，id
    disabledNodesId: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    renderJson: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && JSON.stringify(val) !== "{}") {
          this.renderGarph();
        }
      },
    },
  },
  data() {
    return {
      graph: null,
      inInited: true,
    };
  },
  mounted() {
    this.init();
    this.$nextTick(() => {
      this.erd = elementResizeDetector();
      this.erd.listenTo(this.$refs.containerRef, (elem) => this.resize(elem));
    });
  },
  methods: {
    resize(elem) {
      let w = elem.offsetWidth;
      let h = elem.offsetHeight;
      this.graph && this.graph.resize(w, h);
    },
    init() {
      this.graph = new Graph({
        container: document.getElementById("graph-container-id"),
        // grid: true, // 是否显示背景网格
        background: {
          color: "#F7F9FC",
        },
        grid: {
          visible: true,
          type: "dot",
          size: 20,
          args: {
            color: "#E6ECF2", // 网点颜色
            thickness: 4, // 网点大小
          },
        },
        mousewheel: {
          // 画布放缩
          enabled: true,
          zoomAtMousePosition: false, // 是否将鼠标位置作为中心缩放
          guard: (event) => {
            // 判断一个滚轮事件是否应该被处理
            try {
              if (event.deltaY > 0) {
                // 向前滚动（缩小）
                return this.$refs.graphToolbar.zoomDFun();
              } else if (event.deltaY < 0) {
                // 向后滚动（放大）
                return this.$refs.graphToolbar.zoomAFun();
              }
            } catch (e) {
              return false;
            }
          },
        },
        panning: true,
        scaling: {
          min: 0.05, // 默认值为 0.01
          max: 12, // 默认值为 16
        },
        interacting: {
          nodeMovable: false, // 节点是否可以被移动
          edgeMovable: false, // 边是否可以被移动。
        },
        // 连线选项,配置 connecting 可以实现丰富的连线交互
        connecting: {
          router: "manhattan", // 路径样式
          allowEdge: false, // 是否允许边链接到另一个边
          connector: {
            name: "rounded",
            args: {
              radius: 8,
            },
          },
          anchor: "center", // 被连接的节点的锚点
          connectionPoint: "anchor", // 指定连接点
          allowBlank: false,
          snap: {
            radius: 20,
          },
          createEdge() {
            return new Shape.Edge({
              labels: [
                {
                  attrs: {
                    label: {
                      text: "",
                      fontWeight: "500",
                      fill: "#5FADFB",
                      fontSize: 16,
                    },
                  },
                },
              ],
              attrs: {
                line: {
                  stroke: "#A2B1C3",
                  strokeWidth: 2,
                  targetMarker: {
                    name: "block",
                    width: 12,
                    height: 8,
                  },
                },
              },
              zIndex: 0,
            });
          },
        },
        ...this.setting, // 自定义属性，叠加覆盖上面自己定义的属性
      });
      this.registerNode(); // 注册节点
      // 节点点击事件
      this.graph.on("node:click", (e) => {
        let nodeId = e.node.id;
        if (this.disabledNodesId.indexOf(nodeId) > -1) return;
        this.$emit("node:click", e.node, e);
      });
    },
    // 渲染图表
    renderGarph() {
      this.$nextTick(() => {
        this.graph && this.graph.fromJSON(this.renderJson);
        this.graph && this.graph.zoomToFit({ padding: 20 }); // 居中显示
        // 节点置灰处理
        // if (this.disabledNodesId.length > 0) {
        //   this.disabledNodesId.forEach((nodeid) => {
        //     this.setNodeSilent(nodeid);
        //   });
        // }
      });
    },
    // 添加、修改节点右上角通知内容。父组件调用
    renderNodeTagNum(busCount) {
      if (!this.graph) return;
      const nodes = this.graph && this.graph.getNodes();
      nodes.forEach((item) => {
        const nodeName = getGraphNodeName(item);
        // 获取节点名称
        if (nodeName) {
          let textToDoNum = busCount[nodeName]; // 获取节点的待处理数
          if (textToDoNum > 0) {
            item.setAttrs({
              tipBg: {
                visibility: "visible",
              },
              tipNum: {
                visibility: "visible",
                text: textToDoNum,
                ...setRightTipAttr(textToDoNum),
              },
              silent: false,
            }); // 此时会触发节点重绘
          } else {
            item.setAttrs({
              tipBg: {
                visibility: "hidden",
              },
              tipNum: {
                visibility: "hidden",
                text: 0,
              },
              silent: false,
            }); // 此时会触发节点重绘
          }
        }
      });
    },
    // 动态置灰节点，禁止点击
    setNodeSilent(nodeid) {
      const nodes = this.graph && this.graph.getCellById(nodeid);
      if (!this.graph) return;
      let attr = {
        // ...disabledAttr,
        // attrs: {
        //   avatar: {
        //     xlinkHref: replaceBase64Text(icon, /(#2390FF|#FF7700)/g, nodeStrokeDisabledColor),
        //   },
        // },
        silent: false,
      };
      nodes.setAttrs(attr);
    },
    // 注册节点，圆心、矩形，多边形
    registerNode() {
      Graph.registerNode(
        "circle-node",
        {
          width: 58,
          height: 58,
          markup: [
            {
              tagName: "rect",
              selector: "body",
            },
            {
              tagName: "image",
              selector: "avatar",
            },
          ],
          ports: {
            groups: {
              // 输入链接桩群组定义
              top: {
                position: "top", // 定义连接柱的位置，如果不配置，将显示为默认样式
                label: {
                  position: "top", // 定义标签的位置
                },
                attrs: {
                  // 定义连接柱的样式
                  circle: {
                    r: 6, // 半径
                    magnet: true,
                    stroke: "#5FADFB",
                    strokeWidth: 2,
                    fill: "#fff",
                  },
                },
              },
              // 输出链接桩群组定义
              bottom: {
                position: "bottom",
                label: {
                  position: "bottom",
                },
                attrs: {
                  circle: {
                    r: 6,
                    magnet: true,
                    stroke: "#5FADFB",
                    strokeWidth: 2,
                    fill: "#fff",
                  },
                },
              },
              right: {
                position: "right", // 定义连接柱的位置，如果不配置，将显示为默认样式
                label: {
                  position: "right", // 定义标签的位置
                },
                attrs: {
                  // 定义连接柱的样式
                  circle: {
                    r: 6, // 半径
                    magnet: true,
                    stroke: "#5FADFB",
                    strokeWidth: 2,
                    fill: "#fff",
                  },
                },
              },
              left: {
                position: "left", // 定义连接柱的位置，如果不配置，将显示为默认样式
                label: {
                  position: "left", // 定义标签的位置
                },
                attrs: {
                  // 定义连接柱的样式
                  circle: {
                    r: 6, // 半径
                    magnet: true,
                    stroke: "#5FADFB",
                    strokeWidth: 2,
                    fill: "#fff",
                  },
                },
              },
            },
            items: [
              { id: "port1", group: "top" },
              { id: "port2", group: "bottom" },
              { id: "port3", group: "left" },
              { id: "port4", group: "right" },
            ],
          },
          attrs: {
            // 边框
            body: {
              refWidth: "100%",
              refHeight: "100%",
              fill: "#fff",
              stroke: "#ACCDEE",
              strokeWidth: 1,
              rx: 60,
              ry: 60,
            },
            // 图标
            avatar: {
              width: 58,
              height: 58,
            },
          },
        },
        true
      );
      // 图片+名字
      Graph.registerNode(
        "card-node",
        {
          width: 192,
          height: 100,
          ports: {
            groups: {
              // 输入链接桩群组定义
              top: {
                position: "top", // 定义连接柱的位置，如果不配置，将显示为默认样式
                label: {
                  position: "top", // 定义标签的位置
                },
                attrs: {
                  // 定义连接柱的样式
                  circle: {
                    r: 6, // 半径
                    magnet: true,
                    stroke: "#5FADFB",
                    strokeWidth: 2,
                    fill: "#fff",
                  },
                },
              },
              // 输出链接桩群组定义
              bottom: {
                position: "bottom",
                label: {
                  position: "bottom",
                },
                attrs: {
                  circle: {
                    r: 6,
                    magnet: true,
                    stroke: "#5FADFB",
                    strokeWidth: 2,
                    fill: "#fff",
                  },
                },
              },
              right: {
                position: "right", // 定义连接柱的位置，如果不配置，将显示为默认样式
                label: {
                  position: "right", // 定义标签的位置
                },
                attrs: {
                  // 定义连接柱的样式
                  circle: {
                    r: 6, // 半径
                    magnet: true,
                    stroke: "#5FADFB",
                    strokeWidth: 2,
                    fill: "#fff",
                  },
                },
              },
              left: {
                position: "left", // 定义连接柱的位置，如果不配置，将显示为默认样式
                label: {
                  position: "left", // 定义标签的位置
                },
                attrs: {
                  // 定义连接柱的样式
                  circle: {
                    r: 6, // 半径
                    magnet: true,
                    stroke: "#5FADFB",
                    strokeWidth: 2,
                    fill: "#fff",
                  },
                },
              },
            },
            items: [
              { id: "port1", group: "top" },
              { id: "port2", group: "bottom" },
              { id: "port3", group: "left" },
              { id: "port4", group: "right" },
            ],
          },
          markup: [
            {
              tagName: "rect",
              selector: "body",
            },
            {
              tagName: "image",
              selector: "avatar",
            },
            {
              tagName: "image",
              selector: "rightTopImage",
            },
            {
              tagName: "text",
              selector: "name",
            },
            {
              tagName: "rect",
              selector: "tipBg",
            },
            {
              tagName: "text",
              selector: "tipNum",
            },
          ],
          attrs: {
            // 边框
            body: {
              refWidth: "100%",
              refHeight: "100%",
              fill: "#fff",
              stroke: "#5FADFB",
              boxShadow: "10px 10px 5px 1px rgba(0, 0, 0, 0.3)",
              strokeWidth: 1,
              rx: 10,
              ry: 10,
              pointerEvents: "visiblePainted",
              cursor: "pointer",
            },
            // 左侧图标
            avatar: {
              width: 30,
              height: 30,
              refX: 20,
              refY: 34,
            },
            // 流程名称
            // 矩形 x,y轴坐标位于节点右下角
            name: {
              refX: 60,
              refY: 40,
              fontFamily: "Source Han Sans CN, Source Han Sans CN",
              fill: "#00244A", // 字体颜色
              fontSize: 20,
              fontWeight: "400",
              textAnchor: "start",
              cursor: "pointer",
            },
            // 右上角背景图片
            rightTopImage: {
              width: 78,
              height: 66,
              refX: 112,
              refY: 1,
              cursor: "pointer",
            },

            // 未读信息提示,tipBg背景效果，tipNum具体数字
            tipBg: {
              fill: "rgb(254 86 78)",
              opacity: 0.9,
              visibility: "hidden", // hidden、visible
              fontSize: 18,
              width: 40,
              height: 40,
              display: "block",
              rx: 40,
              ry: 40,
              refX: 165,
              refY: -20,
              cursor: "pointer",
            },
            tipNum: {
              refX: 198,
              fill: "#fff", // 字体颜色
              fontSize: 20,
              visibility: "hidden", // hidden、visible
              textAlign: "center",
              fontWeight: "600",
              textVerticalAnchor: "middle",
              textAnchor: "end",
              cursor: "pointer",
            },
          },
        },
        true
      );
    },
  },
};
</script>

<style lang="less" scoped>
#container-id {
  height: 100%;
  width: 100%;
  position: relative;
}
#graph-container-id {
  height: 100%;
  width: 100%;
}
</style>
