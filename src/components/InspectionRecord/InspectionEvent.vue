<template>
    <div class="InspectionEventWrap">
        <ul class="IR_OperateBox">
            <li class="znscBtn" @click="IEAction()" v-if="showInspectionRecord && (!fjid || fjid == '')"><i class="icon-znsc"></i>智能格式化审查</li>
            <li @click="fileTagAction" :class="showMarkMenu ? 'checked' : 'notChecked'" >
                <i :class="showMarkMenu ? 'icon-mark' : 'icon-mark-checked'"></i>{{showMarkMenu ? (markType == 1 ? '框选问题标注' : '点击问题标注') : '添加标注'}}
                <Icon :type="showMarkMenu ? 'md-arrow-dropup' : 'md-arrow-dropdown'" size="20"/>
                <ul v-show="showMarkMenu && showMarkMenuBox" class="addMarkMenuBox">
                    <li :class="{active:markType == 1}" @click.stop="markTypeAction(1)"><i :class="markType == 1 ? 'icon-mark-kx2' : 'icon-mark-kx'"></i>框选问题标注</li>
                    <li :class="{active:markType == 2}" @click.stop="markTypeAction(2)"><i :class="markType == 2 ? 'icon-mark-dj2' : 'icon-mark-dj'"></i>点击问题标注</li>
                    <li>
                        <span @click.stop="fileTagColor(1)" :class="selectIndex3 == 1 ? 'bookmark-red checked' : 'bookmark-red'"></span>
                        <span @click.stop="fileTagColor(2)" :class="selectIndex3 == 2 ? 'bookmark-yellow checked' : 'bookmark-yellow'"></span>
                        <span @click.stop="fileTagColor(3)" :class="selectIndex3 == 3 ? 'bookmark-blue checked' : 'bookmark-blue'"></span>
                    </li>
                </ul>
            </li>
            <!-- <li @click="IRAction()" v-if="showInspectionRecord"><i class="icon-jcjl"></i>检测记录</li> -->
        </ul>
        <FormattedModal ref="FormattedModal" @triggerTest="triggerTest" :ajbh="ajbh" :ajmc="ajmc"/>
        <Modal
            width="480"
            class="imsProgressModalWrap ims"
            class-name="InspectionProgressModal"
            v-model="showInspectionProgressModal"
            :footer-hide="true"
            :mask="false"
            :transfer="false"
            title="检测进度">
            <div class="InspectionProgressBox">
                <template v-for="(item,index) in jcResultList">
                    <div v-if="item.progress < 100" :key="index+'IP'" class="IP_item">
                        <div class="typeAndStatus">
                            <span class="type">{{DetectionItem[item.type]}}</span>
                            <span class="title" :title="item.title">{{item.title}}</span>
                            <span class="status" v-show="item.progress == null"></span>
                        </div>
                        <Progress v-show="item.progress !== null" :percent="item.progress" />
                    </div>
                </template>
                <div v-if="AllTestingCompleted && !cannectError" class="AllTestingCompleted"><i class="icon_bg"></i>全部检测完成</div>
                <div v-if="cannectError" class="AllTestingCompleted"><i class="icon_bg"></i>服务连接失败，请检查重试</div>
            </div>
        </Modal>
        <TransferModal ref="TransferModal"></TransferModal>
    </div>
</template>
<script>
import FormattedModal from './formattedModal.vue'
import Bus from '@/libs/eventBus.js'
import API from '@/api'
import TransferModal from '@/components/TransferModal/index.vue'
export default {
    name: 'InspectionEvent',
    components: {
        FormattedModal,
        TransferModal
    },
    props:{
        intelMarkingType: {
            type: String,
            default: 'agzx'
        },
        ajbh: {
            type: String,
            default: ''
        },
        ajmc: {
            type: String,
            default: ''
        },
        fjid: {
            type: String,
            default: ''
        },
        showAddCallouts: {
            type: Boolean,
            default: () => true
        },
        showInspectionRecord: {
            type: Boolean,
            default: () => true
        },
        showOneClickDetection: {
            type: Boolean,
            default: () => true
        },
        showOtherDetection: {
            type: Boolean,
            default: () => true
        }
    },
    data() {
        return {
            showMarkMenu: true,
            showMarkMenuBox: false,
            selectIndex3:1,
            markType: 1,
            selectIndex:'',
            showInspectionProgressModal: false,
            AllTestingCompleted: false,
            DetectionItem: {
                fingerprintProcess: '捺印',
                signatureProcess: '签名',
                sealProcess: '签章',
                blankProcess: '空白页',
                repeatProcess: '重复页',
                flawProcess: '瑕疵页',
                extractProcess: '文书填写规范',
            },
            selectType: '',
            jcResultList: [],
            cannectError: false,
            firstTimeIn: true, // 考评标注延迟
            WebSocketConnectTimes: 3,
            CheckProcessTimer: null

        }
    },
    created(){
        if(this.intelMarkingType != 'agzx'){
            this.showMarkMenu = true
        }else{
            this.showMarkMenu = false
        }

        Bus.$on('triggerTest', (data)=>{
            if(data){
                let obj = {
                    type: data.processKey,
                    title: data.ajmc+'-'+data.ajbh,
                    progress: 0,
                }
                this.triggerTest([obj])
            }else{
                this.triggerTest()
            }
        })
    },
    mounted(){
        document.onclick = (e)=>{
            if(e.target.className !== 'checked' && e.target.className !== 'icon-mark'){
                if(this.showMarkMenu && this.showMarkMenuBox){
                    this.showMarkMenuBox = false
                }
            }
        }
        setTimeout(() => {
            this.$parent.fileTagAction(this.showMarkMenu)
        }, 2000);
    },
    methods:{
        fileTagAction(){
            // 考虑第一次考评标注延迟问题写法
            if(this.firstTimeIn){
                if(this.showMarkMenu && !this.showMarkMenuBox){
                    this.showMarkMenuBox = true;
                }else{
                    this.showMarkMenu = !this.showMarkMenu;
                    this.showMarkMenuBox = true;
                }
                this.$parent.fileTagAction(this.showMarkMenu)
            }else{
                this.$refs.TransferModal.setTransferModalObj({
                    show: true,
                    content: '正在建立连接，2秒后再试！',
                    type: 'warning'
                })
            }
        },
        markTypeAction(type){ // 标注类型选择
            this.markType = type;
            this.$parent.markTypeAction(type);
        },
        fileTagColor(index){ // 标注颜色选择
            this.selectIndex3 = index;
            this.$parent.selectIndex3 = index;
        },
        IRAction(){ // 检测记录
            let obj = {
                showSearch: false,
                ajbh: this.ajbh,
                ajmc: this.ajmc
            }
            Bus.$emit('showInspectionRecordModal', obj)
        },
        getRandom(index){
            var num =0, num2 = 37, num3 = 82;
            setTimeout(()=>{
                this.jcResultList.forEach((item,index)=>{
                    num = Math.ceil(Math.random()*30);
                    item.progress = num
                })
            },1000)
             setTimeout(()=>{
                 this.jcResultList.forEach((item,index)=>{
                    num2 = Math.floor(Math.random()*(60-num+1)+num);
                    item.progress = num2
                })
            },1500)
            setTimeout(()=>{
                this.jcResultList.forEach((item,index)=>{
                    num3 = Math.floor(Math.random()*(100-num2+1)+num2);
                    item.progress = num3
                })
            },2500)
            setTimeout(()=>{
                this.jcResultList.forEach((item,index)=>{
                    item.progress = 100
                })
                this.AllTestingCompleted = true;
                Bus.$emit('replaceIRResult',true)
            },3000)
            
        },
        IEAction(){
            this.$refs.FormattedModal.showModal = true;
        },
        scjgAction(){
            this.$parent.showReviewResultEvent(true);
            this.$refs.FormattedModal.closeBtn()
        },
        closeBtn(){ 
            this.$refs.FormattedModal.showModal = false;
        },
        createIRData(data){ // 造模拟检测数据,没有需要检测的文书需要模拟检测数据。
            this.jcResultList = data || this.$refs.FormattedModal.getCheckMsg('array')
           
            this.jcResultList.forEach((item,index)=>{
                item.progress = 0
            })
        },
        triggerTest(data){
            this.showInspectionProgressModal = true;
            this.WebSocketConnectTimes = 3
            this.createIRData(data);
            this.AllTestingCompleted = false;
            // 定时获取检测进度
            this.CheckProcessTimer = setInterval(()=>{
                this.getCheckProcessSingle()
            },15000)

        },
        getCheckProcessSingle(){ // 获取检测进度
            let params = {
                ajbh: this.ajbh,
                type: '2'
            }
            this.$store.dispatch("postRequest", {
                url: API.DmsApi.getCheckProcessSingle,
                params
            }).then(res=>{ 
                if(res.success){
                    if(res.data && res.data.status == '1'){ // 0：检测中，1：检测完成
                        // 更新检测完成的进度
                        if(res.data && res.data.process){
                            let newArr = []
                            for(var i in res.data.process){
                                let obj = {
                                    type: i,
                                    title: this.ajmc+'-'+res.data.ajbh,
                                    progress: Number((res.data.process[i] * 100).toFixed(0)),
                                }
                                newArr.push(obj)
                            }
    
                            this.jcResultList = newArr;
    
                            this.AllTestingCompleted = true;
                        }
                        // 清除定时器
                        clearInterval(this.CheckProcessTimer) 
                        // 全部检测完成,需要更新检测结果
                        Bus.$emit('replaceIRResult',true)
                        this.$parent.showReviewResultEvent(true)
                        this.$refs.TransferModal.setTransferModalObj({
                            show: true,
                            content: '检测完成！',
                            type: 'success'
                        })
                    }else{
                        // 更新检测中的进度
                        if(res.data && res.data.process){
                            let newArr = []
                            for(var i in res.data.process){
                                let obj = {
                                    type: i,
                                    title: this.ajmc+'-'+res.data.ajbh,
                                    progress: Number((res.data.process[i] * 100).toFixed(0)),
                                }
                                newArr.push(obj)
                            }
    
                            this.jcResultList = newArr;
    
                            this.AllTestingCompleted = false;
                        }
                    }
                }else{
                    // 清除定时器
                    clearInterval(this.CheckProcessTimer)  
                    this.$refs.TransferModal.setTransferModalObj({
                        show: true,
                        content: res.msg,
                        type: 'warning'
                    })
                }
            })
        },
        checkUserExistEvent(){ // 获取websocket连接状态
            var usreInfo = this.$store.getters.sessionUser;
            var sid = usreInfo.loginId + "-" + usreInfo.name;

            let params = {
                sid: sid
            }
            this.$store.dispatch("postRequest", {
                url: API.DmsApi.checkUserExist,
                params
            }).then(res=>{ 
                if(res.success){
                    if(!res.exist){ // true：连接状态，false：未连接状态
                        this.WebSocketConnectTimes = 3
                        this.openSocket()
                    }
                }else{
                    this.WebSocketConnectTimes = 3
                    this.openSocket()
                }
            })
        },
        openSocket() { // 获取OCR检测进度
            if (!'WebSocket' in window) {
                this.$refs.TransferModal.setTransferModalObj({
                    show: true,
                    content: '您的浏览器不支持WebSocket',
                    type: 'warning'
                })
            } else {
                //实现化WebSocket对象，指定要连接的服务器地址与端口  建立连接
                var usreInfo = this.$store.getters.sessionUser;
                var sid = usreInfo.loginId + "-" + usreInfo.name;
                var socketUrl = ImsServerConfig.websocketUrl + "/webSocket/" + sid + "?access_token=" + usreInfo.token;
                this.socket = new WebSocket(socketUrl);
                //打开事件
                this.socket.onopen =  () =>{
                    console.log("websocket已连接");
                };
                // 记录断连时间，超过50s为断连，后台10s刷新一次，但返回信息时间间隔不固定
                var DisconnectionTime = 0
                let timer = null
                //获得消息事件
                this.socket.onmessage =  (msg)=> {
                    console.log(msg);
                    var data =  JSON.parse(msg.data);
                    // 智能格式化审查
                    if( data.code == '3500' && data.ajbh == this.ajbh && data.jobType == '2' ){
                        console.log("websocket获得消息:",data);
                        // 断连计时---------------------------------------------------------------
                        if(timer){
                            clearInterval(timer)
                        }
                        timer = setInterval(()=>{
                            DisconnectionTime ++
                        },1000)
                        if(DisconnectionTime > 50){
                            if(timer){
                                clearInterval(timer)
                            }
                            DisconnectionTime = 0
                           this.checkUserExistEvent()
                        }
                        // -------------------------------------------------------------------
                        let newArr = []
                        
                        for(var i in data.process){
                            let obj = {
                                type: i,
                                title: this.ajmc+'-'+data.ajbh,
                                progress: Number((data.process[i] * 100).toFixed(0)),
                            }
                            newArr.push(obj)
                        }

                        this.jcResultList = newArr;

                        this.AllTestingCompleted = true;
                        for(var i = 0; i < newArr.length; i++){
                            if(newArr[i].progress < 100){
                                this.AllTestingCompleted = false;
                                return
                            }
                        }
                        if(this.AllTestingCompleted){ // 全部检测完成,需要更新检测结果。
                            Bus.$emit('replaceIRResult',true)
                            this.$parent.showReviewResultEvent(true)
                            this.$refs.TransferModal.setTransferModalObj({
                                show: true,
                                content: '检测完成！',
                                type: 'success'
                            })
                        }
                        
                    }
                    // 区块链篡改信息检测
                    if( data.code == '3401' && data.ajbh == this.ajbh ){
                        if(data.changeFile && data.changeFile.length > 0){
                            Bus.$emit('checkFileIsModified', true)
                        }else{
                            Bus.$emit('checkFileIsModified', false)
                        }
                    }
                    // let arr = ['3001','3002','3003','3004']
                    // if(!arr.includes(data.code) && data.ajbh == this.ajbh){
                    //     this.createIRData()
                    // }
                };
                var WebSocketIsError = false
                //关闭事件
                this.socket.onclose = ()=> {
                    if(!WebSocketIsError){
                        WebSocketIsError = false
                        this.openSocket()
                    }
                    this.socket.close(); //关
                    console.log("WebSocket:已关闭");
                };
                //发生了错误事件
                this.socket.onerror =  ()=> {
                    WebSocketIsError = true
                    this.jcResultList = [];
                    this.cannectError = true;
                    console.log("websocket发生了错误");

                    this.WebSocketConnectTimes --
                    if(this.WebSocketConnectTimes < 0){
                        this.WebSocketConnectTimes = 3
                        return
                    }else{
                        setTimeout(()=>{
                            this.openSocket()
                        },500)
                    }
                }
            }
        },
    },
}
</script>
<style lang="less" scoped>
    /deep/ .ivu-modal-body{
        padding: 0.2rem 0.2rem 0 0.2rem;
    }

    .bookmark-red{
        background-color: #F36279 !important;
    }
    .bookmark-yellow{
        background-color: #FFE008 !important;
    }
    .bookmark-blue{
        background-color: #087EFF !important;
    }
    .InspectionProgressBox{
        min-height: 2rem;
        max-height: 3.5rem;
        overflow: auto;
        .AllTestingCompleted{
            width: 100%;
            min-height: 2rem;
            max-height: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .icon_bg{
                display: inline-block;
                width: 1rem;
                height: 1rem;
                background: url('../../assets/images/dzjz/zwsj_hui.png') no-repeat;
                background-size: 100% 100%;
                margin-bottom: 0.1rem;
            }
        }
        .IP_item{
            margin-bottom: 0.1rem;
            .typeAndStatus{
                display: flex;
                align-items: center;
            }
            .type{
                padding: 0 0.05rem;
                color: #319FFF;
                border: solid 1px #319FFF;
                border-radius: 0.04rem;
                margin-right: 0.05rem;
            }
            .title{
                display: inline-block;
                width: 70%;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                margin-right: 0.1rem;
            }
            .progress{
                float: right;
            }
            .status{
                display: inline-block;
                width: 0.23rem;
                height: 0.23rem;
                background: url('../../assets/images/znaj/dyts_icon.png') no-repeat;
                background-size: 100% 100%;
            }
        }
    } 
   
    .IR_OperateBox{
        display: flex;
        justify-content: flex-end;
        align-items: center;
        li{
            padding: 0 0.08rem;
            height: 0.32rem;
            border-radius: 0.04rem;
            cursor: pointer;
            margin-left: 0.2rem;
            min-width: 0.5rem;
            border: 1px solid #087EFF;
            color: #087EFF;
            background: #FFFFFF;
            display: flex;
            align-items: center;
            position: relative;
            &.gray{
                background: #AFD7FF;
            }
            &.checked{
                background: #087EFF;
                color: #fff;
            }
            &.znscBtn{
                background: #FFFFFF;
                border: 1px solid #087EFF;
                color: #087EFF;
            }
        }
    }
    .addMarkMenuBox{
       width: 1.4rem;
        padding: 0.05rem;
        background: #fff;
        position: absolute;
        left: 0;
        top: 0.38rem;
        z-index: 4;
        border: 1px solid #CEE0F0;
        box-shadow: 0px 3px 5px rgba(28,146,214,0.3000);
        border-radius: 4px;
        li{
            background: #fff;
            color: #7A8699;
            width: 100%;
            height: 0.4rem;
            line-height: 0.4rem;
            margin: 0;
            font-size: 0.14rem;
            border: none;
            &.active{
                background: #EDF5FC;
                color: #087EFF;
            }
        }

        li:last-child{
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #CCE6FF;
            span{
                display: inline-block;
                width: 0.3rem;
                height: 0.2rem;
                border-radius: 2px;
            }
            span.checked{
                background: url('../../assets/images/dzjz/check.png') no-repeat center;
                background-size: contain;
            }
        }
    }
    .demo-spin-icon-load{
        animation: ani-demo-spin 1s linear infinite;
    }
    @keyframes ani-demo-spin {
        from { transform: rotate(0deg);}
        50%  { transform: rotate(180deg);}
        to   { transform: rotate(360deg);}
    }
    .demo-spin-col{
        height: 100px;
        position: relative;
        border: 1px solid #eee;
    }
    .ivu-spin-fix{
        top: 0.5rem;
    }
    /deep/ .ivu-modal-content{
        border-radius: 0.04rem 0.04rem 0 0;
        overflow: hidden;
    }
    .InspectionProgressModal{
        top: unset;
        bottom: 0;
        right: 0;
        left: unset;
        height: auto;
    }
    .InspectionProgressModal .ivu-modal{
        top: 0;
    }
</style>
