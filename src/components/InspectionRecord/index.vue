<template>
    <div>
        <Modal width="1400" class="imsProgressModalWrap ims" v-model="showInspectionRecordModal" :footer-hide="true"
            :mask-closable="false" :transfer="false" title="检测记录">
            <div class="InspectionRecordBox">
                <ul class="tabUl">
                    <li v-for="(item, index) in tabUl" :class="{ active: selectIndex == index }" :key="index"
                        @click="tabAction(index)">{{ item }}</li>
                </ul>
                <div class="searchBox" v-if="showSearch">
                    <Input v-model="searchText" search enter-button style="width: 25%" placeholder="输入案件编号或名称查询"
                        @on-search="getListData()" />
                </div>
                <div v-show="selectIndex == 0">
                    <Table border :columns="columns" :data="tableData" type="index" max-height="500">
                        <template slot-scope="{ row }" slot="process">
                            <Progress :percent="row.process" :stroke-width="32" status="success" text-inside />
                        </template>
                        <template slot-scope="{ row }" slot="action">
                            <Button v-if="row.jobStatus == '3' || row.jobStatus == '0'" type="warning" size="small"
                                @click="DetectionEvent(row)">取消检测</Button>
                        </template>
                    </Table>
                    <Page :total="dataTotal" ref="pages" show-total show-elevator :page-size="pageSize"
                        :current="currentPage" show-sizer @on-change="changePage" @on-page-size-change="changePageSize"
                        prev-text="上一页" next-text="下一页" />
                </div>
                <div v-show="selectIndex == 1">
                    <Table border :columns="columns2" :data="tableData2" type="index" max-height="500">
                        <template slot-scope="{ row }" slot="action">
                            <Button v-if="row.jobStatus != '1'" type="primary" size="small"
                                @click="reTryEvent(row)">重新检测</Button>
                            <Button v-else type="primary" size="small" disabled>重新检测</Button>
                        </template>
                    </Table>
                    <Page :total="dataTotal2" ref="pages" show-total show-elevator :page-size="pageSize2"
                        :current="currentPage2" show-sizer @on-change="changePage" @on-page-size-change="changePageSize"
                        prev-text="上一页" next-text="下一页" />
                </div>
            </div>
        </Modal>
        <TransferModal ref="TransferModal" @confirm="TransferModalConfirm"></TransferModal>
    </div>
</template>
<script>
import Bus from '@/libs/eventBus.js'
import API from '@/api'
import utils from '@/assets/script/util.js'
import TransferModal from '@/components/TransferModal/index.vue'
export default { //检测记录组件
    name: 'InspectionRecord',
    components: {
        TransferModal
    },
    data() {
        return {
            jgrybm: this.$route.query.jgrybm ? this.$route.query.jgrybm : '',
            showInspectionRecordModal: false,
            DetectionItem: [],
            columns: [],
            tableData: [],
            columns2: [],
            tableData2: [],
            tabUl: ['正在检测', '检测完成'],
            selectIndex: 0,
            searchText: '',
            dataTotal: 0,
            pageSize: 20,
            currentPage: 1,
            dataTotal2: 0,
            pageSize2: 20,
            currentPage2: 1,
            selectOne: {},
            dmsRuleSql: '',
            user: this.$store.getters.sessionUser,
            ajbh: '',
            showSearch: true,
            cancelCheckType: '1',
            timer: null,
            isFirstLoad: true
        }
    },
    created() {
        this.initTable()
        this.initTable2()
        Bus.$on('showInspectionRecordModal', (val) => {
            this.showInspectionRecordModal = true
            this.ajbh = val.ajbh
            this.showSearch = val.showSearch
            this.getZrajUserRoleSql()
        })
    },
    watch: {
        showInspectionRecordModal(newVal) {
            if (!newVal) {
                clearTimeout(this.timer)
                this.timer = null
            } else {
                this.isFirstLoad = true
            }
        }
    },
    methods: {
        getZrajUserRoleSql() { // 权限控制
            var params = {
                mark: ImsServerConfig.dmsZrajRule,
                roleIds: this.user.roleIds
            }
            this.$store.dispatch("postRequest", {
                url: API.BspApi.GET_USER_ROLE,
                params
            }).then(res => {
                if (res.code && res.code == '200') {
                    this.dmsRuleSql = res.data;
                    // this.getInspectionRecordList(0)
                }
            })
        },
        initTable() {
            let theadName = ['案件编号', '案件名称', '检测类型', '上传进度', '状态', '操作'];
            let theadKey = ['ajbh', 'ajmc', 'checkTypeName', 'process', 'statusName', 'action'];
            let itemWidth = ['20%', '20%', '15%', '20%', '10%', '15%'];
            let slotArr = ['process', 'action'];
            this.columns = utils.renderThead(theadName, theadKey, itemWidth, [], slotArr);
        },
        initTable2() {
            let theadName = ['案件编号', '案件名称', '检测类型', '状态', '检测时间', '操作'];
            let theadKey = ['ajbh', 'ajmc', 'checkTypeName', 'statusName', 'addTime', 'action'];
            let itemWidth = ['20%', '25%', '15%', '10%', '15%', '15%'];
            this.columns2 = utils.renderThead(theadName, theadKey, itemWidth);
        },
        tabAction(index) {
            this.isFirstLoad = false
            this.searchText = '';
            this.selectIndex = index;
            this.getInspectionRecordList();
        },
        getListData() {
            this.getInspectionRecordList();
        },
        getInspectionRecordList(val) { // 获取检测列表
            let params = {
                dmsRule: ImsServerConfig.dmsZrajRule,
                dmsRuleSql: this.dmsRuleSql,
                jgrybm: this.jgrybm,
                jobStatus: 0,
                page: this.selectIndex == 0 ? this.currentPage : this.currentPage2,
                pageSize: this.selectIndex == 0 ? this.pageSize : this.pageSize2,
                status: val > -1 ? val : this.selectIndex,
                jobType: 2
            }
            this.$store.dispatch("postRequest", {
                url: this.$path.jobCheckList,
                params
            }).then(res => {
                if (res.success) {
                    debugger
                    if (params.status == 0 && res.data && res.data.list && res.data.list.length > 0) { // 存在检测中的数据需要发起延时任务更新检测状态
                        this.selectIndex = 0

                        if (this.timer) {
                            clearTimeout(this.timer)
                            this.timer = null
                        }
                        this.timer = setTimeout(() => {
                            this.getInspectionRecordList(0)
                        }, 5000);

                    } else if (params.status == 0 && this.isFirstLoad) {
                        this.selectIndex = 1
                        // 没有正在检测中的，获取检测完成的列表
                        this.getInspectionRecordList(1)
                    } else if (params.status == 1) {
                        if (this.timer) {
                            clearTimeout(this.timer)
                            this.timer = null
                        }
                    }
                    if (res.data && res.data.list && res.data.list.length > 0) {
                        res.data.list.forEach(item => {
                            item.process = Number((item.process * 100).toFixed(1))
                            if (item.job_status == '0') {
                                item.statusName = '检测中'
                            } else if (item.job_status == '3') {
                                item.statusName = '排队中'
                            } else if (item.job_status == '1') {
                                item.statusName = '检测完成'
                            } else if (item.job_status == '2') {
                                item.statusName = '检测失败'
                            } else if (item.job_status == '4') {
                                item.statusName = 'ocr未开启，不执行'
                            } else if (item.job_status == '5') {
                                item.statusName = '任务取消'
                            }

                            item.process = item.process || 0
                        });
                    }
                    if (params.status == 0) {
                        this.dataTotal = res.total;
                        this.tableData = res.data.list;
                    } else {
                        this.dataTotal2 = res.total;
                        this.tableData2 = res.data.list;
                    }
                } else {
                    this.$refs.TransferModal.setTransferModalObj({
                        show: true,
                        content: res.msg,
                        type: 'warning'
                    })
                }
            })
        },
        getCheckingProcess(jobIdList) { // 获取正在检测的检测进度

        },
        TransferModalConfirm(obj) {
            if (obj.type == 'CancelDetection') {
                this.CancelDetection()
            } else if ('reTryEvent') {
                let params = {
                    id: obj.item.id,
                }
                this.$store.dispatch("postRequest", {
                    url: API.DmsApi.reCheck,
                    params
                }).then(res => { // 开始检测
                    if (res.success) {
                        Bus.$emit('triggerTest', obj.item)
                        this.getInspectionRecordList();
                        this.$refs.TransferModal.setTransferModalObj({
                            show: true,
                            content: res.msg,
                            type: 'success'
                        })
                    } else {
                        this.$refs.TransferModal.setTransferModalObj({
                            show: true,
                            content: res.msg,
                            type: 'warning'
                        })
                    }
                })
            }
        },
        DetectionEvent(row) {
            this.selectOne = row;
            this.$refs.TransferModal.setTransferModalObj({
                show: true,
                content: '确定取消该项检测吗？',
                type: 'warning',
                showCancelBtn: true,
                params: { type: 'CancelDetection' }
            })
        },
        CancelDetection() { // 取消正在检测中的任务
            let params = {
                cancelType: this.cancelCheckType,
                minceId: this.selectOne.id,
            }
            this.$store.dispatch("postRequest", {
                url: this.$path.cancelMinceJob,
                params
            }).then(res => {
                if (res.success) {
                    this.getInspectionRecordList();
                    this.$refs.TransferModal.setTransferModalObj({
                        show: true,
                        content: '操作成功！',
                        type: 'success'
                    })
                } else {
                    this.$refs.TransferModal.setTransferModalObj({
                        show: true,
                        content: res.msg,
                        type: 'warning'
                    })
                }
            })
        },
        reTryEvent(item) { // 重新检测（检测失败的情况）
            this.$refs.TransferModal.setTransferModalObj({
                show: true,
                content: '确定重新检测该条数据吗？',
                type: 'warning',
                showCancelBtn: true,
                params: { type: 'reTryEvent', item: item }
            })
        },
        changePage(page) {
            if (this.selectIndex == 0) {
                this.currentPage = page
            } else {
                this.currentPage2 = page
            }
            this.getInspectionRecordList()
        },
        changePageSize(PageSize) {
            if (this.selectIndex == 0) {
                this.pageSize = PageSize
            } else {
                this.pageSize2 = PageSize
            }
            this.getInspectionRecordList()
        }
    },
}
</script>
<style lang="less" scoped>
.InspectionEventBox {
    min-height: 2rem;
    padding: 0.2rem;
    box-sizing: border-box;

    .DetectionItem {
        margin-top: 0.2rem;
        margin-bottom: 0.4rem;
        display: flex;
        align-items: center;
        justify-content: center;

        label {
            font-weight: bold;
        }

        /deep/ .ivu-radio-wrapper {
            font-weight: normal;
        }
    }

    .SCResult {
        color: #087EFF;
        cursor: pointer;
    }

    .SCResult:hover {
        opacity: 0.7;
    }

    .operateBox {
        width: 100%;
        // height: 0.6rem;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 0.6rem;

        li {
            width: 0.8rem;
            text-align: center;
            line-height: 0.32rem;
            border: solid 1px #087EFF;
            color: #087EFF;
            margin: 0 0.1rem;
            border-radius: 0.04rem;
            width: 80px;
            background: #FFFFFF;
            border-radius: 2px;
            cursor: pointer;
        }

        li:hover {
            opacity: 0.8;
        }

        li:last-child {
            background: #087EFF;
            color: #fff;
        }
    }
}

.InspectionRecordBox {
    min-height: 2rem;

    .tabUl {
        display: flex;
        align-items: center;
        margin-bottom: 0.1rem;

        li {
            width: 1.2rem;
            height: 0.34rem;
            line-height: 0.34rem;
            text-align: center;
            font-size: 0.16rem;
            cursor: pointer;
            background: #CEE2F5;
            color: #333;
            border: 1px solid #cee2f5;
            margin-right: 0.1rem;
            box-sizing: border-box;

            &.active {
                background: #087EFF;
                color: #fff;
                border: 1px solid #087EFF;
            }
        }

        li:hover {
            opacity: 0.7;
        }
    }

    .searchBox {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-bottom: 0.1rem;
    }
}

/deep/.ivu-progress-success .ivu-progress-bg {
    height: 0.2rem !important;
    border-radius: 0.1rem !important;
}

/deep/.ivu-progress-inner-text {
    height: 0.2rem;
    line-height: 0.2rem;
    float: right;
}

/deep/.ivu-table-column-center .ivu-table-cell {
    text-align: center;
}
</style>