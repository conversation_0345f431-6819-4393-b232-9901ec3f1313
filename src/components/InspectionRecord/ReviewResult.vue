<template>
    <div class="ReviewResultWrap">
        <div class="searchBox">
            <Input v-model="searchText" placeholder="搜索材料" @on-change="searchAction"/>
            <i class="search-icon"></i>
        </div>
        <Collapse v-if="mindReviewsResultList.length > 0">
            <Panel v-for="(item,index) in mindReviewsResultList"  :name="index+'min'" :class="'style_'+item.checkType" :key="'Min'+index">
                {{item.checkTypeName}}({{item.count}})
                <template #content>
                    <div class="item-panel  mindReviewsResult" v-for="(item2,index2) in  item.result" :key="'re'+index2" @click="positionEvent(item2)">
                        <div class="top-panel">
                            <span class="xh">{{item2.indexNum}}</span>
                            <p>{{item2.catalogName}}</p>
                            
                        </div>
                        <p v-if="item2.loseInfo" class="type">{{item2.loseInfo}}</p>
                        <div class="bottom-panel">
                            <span>检测时间：{{item2.addTime}}</span>
                            <span class="btn-panel" v-if="!fjid || fjid == ''"> 
                                <i class="icon-delete-gray" @click.stop="delResult(item2)"></i>
                            </span>
                        </div>
                    </div>
                </template>
            </Panel>
        </Collapse>
        <div v-else class="noData">
            <i class="noDataIcon"></i>
            暂无数据
        </div>
        <TransferModal ref="TransferModal" @confirm="delResultEvent"></TransferModal>
    </div>
</template>
<script>
import Bus from '@/libs/eventBus.js'
import API from '@/api'
import TransferModal from '@/components/TransferModal/index.vue'
export default {
    name: 'ReviewResult',
    components: {
        TransferModal
    },
    props:{
        ajbh: {
            type: String,
            default: ''
        },
        currentNodeFileList: {
            type: Array,
            default: ()=>{}
        },
        fjid: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            ReviewResult: {
                total: 0,
                qzCount: 0,
                qmCount: 0,
                nyCount: 0
            },
            ReviewResultList: [],
            currentOne: 1,
            searchText: '',
            allImgList: this.currentNodeFileList,
            mindReviewsResultList: [],
            mindReviewsResultList_copy: []
        }
    },
    watch:{
        currentNodeFileList(newVal){
            if(newVal){
                this.allImgList = newVal
            }
        }
    },
    created(){
        this.getMindReviewsResult()
        Bus.$on('replaceIRResult',(val)=>{
            if(val){
                this.getMindReviewsResult()
            }
        })
    },
    methods:{
        searchAction(){
            if(this.searchText == ''){
                this.mindReviewsResultList = this.mindReviewsResultList_copy
            }else{
                let list = JSON.parse(JSON.stringify(this.mindReviewsResultList_copy))
                let arr = []
                for(var i = 0; i < list.length; i++){
                    let item = list[i]
                    let arr2 = []
                    if(item.result){
                        for(var j = 0; j < item.result.length; j++){
                            let item2 = item.result[j]
                            if(item2.catalogName.includes(this.searchText)){
                                arr2.push(item2)
                            }
                        }
                        item.count = arr2.length
                        item.result = arr2
                    }
                    if(item.count > 0){
                        arr.push(item)
                    }
                }
                this.mindReviewsResultList = arr
            }
        },
        delResultEvent({item}){
            let params = {
                id: item.id
            }
            this.$store.dispatch("postRequest", {
                url: API.DmsApi.deleteCheckResult,
                params
            }).then(res=>{
                if(res.success){
                    this.$refs.TransferModal.setTransferModalObj({
                        show: true,
                        content: '操作成功！',
                        type: 'success'
                    })
                    this.getMindReviewsResult()
                }else{
                    this.$refs.TransferModal.setTransferModalObj({
                        show: true,
                        content: res.msg,
                        type: 'warning'
                    })
                }
            })
        },
        delResult(item){ // 删除审查结果
            this.$refs.TransferModal.setTransferModalObj({
                show: true,
                content: '确定要删除该结果吗？',
                type: 'warning',
                showCancelBtn: true,
                params: {item}
            })
        },
        getMindReviewsResult(){ // 智能审查结果
            let params = {
                ajbh: this.ajbh
            }
            this.$store.dispatch("postRequest", {
                url: API.DmsApi.mindReviewsResult,
                params
            }).then(res=>{
                if(res.success){
                    if(res.data && res.data.length > 0){
                        this.mindReviewsResultList = res.data;
                        this.mindReviewsResultList_copy = res.data;
                        let wtyList = []
                        // let ssfcsimResultArr = []
                        this.mindReviewsResultList.forEach(item=>{
                            if(item.result){
                                item.result.forEach((item2,index2)=>{
                                    wtyList.push(item2)
                                    
                                    // item2.indexNum = String(index2+1).padStart(2,'0')

                                    let str = String(index2 + 1);
                                    if(str && str.length < 2) {
                                        str = '0' + str;
                                    }
                                    item2.indexNum = str;
                                    
                                    // if(item2.ssfcsimResult){
                                    //     item2.ssfcsimResult = JSON.parse(item2.ssfcsimResult);
                                    //     if(!item2.ssfcsimResult.fingerprint.check_res || !item2.ssfcsimResult.seal.check_res || !item2.ssfcsimResult.signature.check_res){
                                    //         ssfcsimResultArr.push(item2)
                                    //     }
                                    // }
                                })
                            }
                        })
                        Bus.$emit('mindReviewsResultList', wtyList)
                        this.dealData(wtyList)
                    }else{
                        this.mindReviewsResultList = [];
                    }
                    
                }else{
                    this.$refs.TransferModal.setTransferModalObj({
                        show: true,
                        content: res.msg,
                        type: 'warning'
                    })
                }
            })
        },
        dealData(list){
            if(list && list.length > 0){
                var testResultsList = []
                let typeObj = {
                    fingerprint: '捺印',
                    seal: '签章',
                    signature: '签名'
                }
                list.forEach(item=>{
                    if(item.ssfcsimResult){
                        item.ssfcsimResult = JSON.parse(item.ssfcsimResult)
                        if(item.checkType == '1'){
                            let item2 = item.ssfcsimResult.seal
                            if(!item2.check_res){
                                item2.detail_res.forEach(item3=>{
                                    let obj = {
                                        id: item.id,
                                        fileId: item.fileId, 
                                        materialId: item.materialId,
                                        seal: true,
                                        typeName: typeObj['seal'],
                                        styleObj: this.getStyleObj(item, item3)
                                    }
                                    testResultsList.push(obj)
                                })
                            }
                        }
                        if(item.checkType == '2'){
                            let item2 = item.ssfcsimResult.signature
                            if(!item2.check_res){
                                item2.detail_res.forEach(item3=>{
                                    let obj = {
                                        id: item.id,
                                        fileId: item.fileId, 
                                        materialId: item.materialId,
                                        signature: true,
                                        typeName: typeObj['signature'],
                                        styleObj: this.getStyleObj(item, item3)
                                    }
                                    testResultsList.push(obj)
                                })
                            }
                        }
                        if(item.checkType == '3'){
                            let item2 = item.ssfcsimResult.fingerprint
                            if(!item2.check_res){
                                item2.detail_res.forEach(item3=>{
                                    let obj = {
                                        id: item.id,
                                        fileId: item.fileId, 
                                        materialId: item.materialId,
                                        fingerprint: true,
                                        typeName: typeObj['fingerprint'],
                                        styleObj: this.getStyleObj(item, item3)
                                    }
                                    testResultsList.push(obj)
                                })
                            }
                        }
                    }
                })
                this.ReviewResultList = testResultsList;
                let newArr = []
                this.allImgList.forEach(item=>{
                    this.ReviewResultList.forEach(item2=>{
                        if(item.id == item2.fileId){
                            newArr.push(item2)
                        }
                    })
                })
                
                this.ReviewResultList = newArr;
                Bus.$emit('ReviewResultList',newArr)
                
            }
        },
        getStyleObj(item,item2){
            var IRwidthVal = item2.axis_loc[1][0] - item2.axis_loc[0][0],
                IRHeightVal = item2.axis_loc[2][1] - item2.axis_loc[1][1],
                IRTopVal = item2.axis_loc[0][1],
                IRLeftVal = item2.axis_loc[0][0];

                IRwidthVal = (IRwidthVal / item.width * 100).toFixed(3);
                IRHeightVal = (IRHeightVal / item.height * 100).toFixed(3);
                IRTopVal = (IRTopVal / item.height * 100).toFixed(3);
                IRLeftVal =  (IRLeftVal / item.width * 100).toFixed(3);
            return {
                width: IRwidthVal + '%',
                height: IRHeightVal + '%',
                position: 'absolute',
                top: IRTopVal + '%',
                left: IRLeftVal + '%',
                zIndex: 2,
                border: '2px solid #FF910B'
            }
            
        },
        positionEvent(item){ // 审查结果根据图片id定位
            this.$emit('IR_positionEvent',item)
        }
    },
}
</script>
<style lang="less" scoped>
@assets:'../../assets/images';
.ReviewResultWrap{
    width: 100%;
    height: calc(~'100% - 0.5rem');
    .icon-delete-gray{
        display: inline-block;
        width: 0.135rem;
        height: 0.144rem;
        background: url('@{assets}/znaj/icon-delete-gray.png') no-repeat center;
        background-size: 100% 100%;
        cursor: pointer;
    }
    .icon-delete-gray:hover{
        background: url('@{assets}/znaj/icon-delete-blue.png') no-repeat center;
        background-size: 100% 100%;
    }
    .type{
        padding-left: 0.25rem;
    }
    .top-panel{
        font-weight: bold;
    }
    .noData{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-top: 50px;
    }
    .xh{
        color: #4CA9FF;
        font-weight: bold;
        margin-right: 9px;
    }
    .searchBox{
        width: 100%;
        margin-bottom: 11px;
        position: relative;
        padding: 0.1rem 0.1rem 0;
        /deep/ .ivu-input-wrapper{
            width: 100%;
            height: 0.32rem;
            margin: 0;
            input{
                height: 0.32rem;
                line-height: 0.32rem;
                padding-left: 14px;
                font-size: 16px;
                border: 1px solid #CEE0F0;
                color: #3E4E66;
            }
        }
        .search-icon{
            display: inline-block;
            width: 0.17rem;
            height: 0.17rem;
            background: url('@{assets}/icon-search.png') no-repeat center !important;
            background-size: 100% 100% !important;
            position: absolute;
            right: 0.2rem;
            top: 0.18rem;
        }
    }
    .mindReviewsResult{
        padding: 0.05rem 0.14rem;
        border: none;
        border-bottom: solid 1px #E1EBF5;
        .bottom-panel{
            justify-content: space-between;
            margin-top: 8px;
            padding-left: 0.25rem;
        }
    }
    
    .operateBox{
        float: right;
        display: flex;
        align-items: center;
        li{
            margin: 0 0.1rem;
        }
        .currentOne{
            i{
                color: #33a7ff;
            }
        }
        .operateBtn{
            width: 0.75rem;
            height: 0.24rem;
            border: solid 1px #D9DBDE;
            border-radius: 0.02rem;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0.14rem;
        }
        .operateBtn:hover{
            opacity: 0.7;
        }
    }
    /deep/ .ivu-collapse::-webkit-scrollbar{
        width: 0.04rem;
    }
    /deep/ .ivu-collapse{
        height: calc(~'100% - 0.5rem');
        overflow: auto;
        background-color: #fff;
        border: none;
        padding: 0 0.1rem 0.1rem;
        
        .ivu-icon-ios-arrow-forward{
            width: 0.2rem;
            height: 0.2rem;
            float: right;
            margin: 0.09rem 0.08rem 0 0;
            font-size: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .ivu-icon-ios-arrow-forward:before{
            content: '';
            display: inline-block;
            width: 0.07rem;
            height: 0.09rem;
            background: url('@{assets}/ztree/img/ztree-zk.png') no-repeat center !important;
            background-size: 100% 100% !important;
        }
        .ivu-collapse-content{
            border: 1px solid #E1EBF5;
            border-top: none;
            padding: 0;
        }
        .ivu-collapse-content-box{
            padding: 0;
        }
        .ivu-collapse-header{
            border: none !important;
        }
        .item-panel{
            margin: 0;
        }
        .ivu-collapse-item{
            margin-bottom: 12px;
            border: none;
            &.style_1{
                background-color: #DBEFFF;
                .ivu-collapse-header{
                    color: #4CA9FF;
                }
                .xh{
                    color: #4CA9FF;
                }
            }
            &.style_2{
                background-color: #DFFAF9;
                .ivu-collapse-header{
                    color: #00E2AC;
                }
                .xh{
                    color: #00E2AC;
                }
            }
            &.style_3{
                background-color: #FDEFE0;
                .ivu-collapse-header{
                    color: #FF8041;
                }
                .xh{
                    color: #FF8041;
                }
            }
            &.style_4{
                background-color: #E9E8FC;
                .ivu-collapse-header{
                    color: #7964F4;
                }
                .xh{
                    color: #7964F4;
                }
            }
            &.style_5{
                background-color: #FFE6E8;
                .ivu-collapse-header{
                    color: #FF2D43;
                }
                .xh{
                    color: #FF2D43;
                }
            }
            &.style_6{
                background-color: #FFF7D9;
                .ivu-collapse-header{
                    color: #FFAF28;
                }
                .xh{
                    color: #FFAF28;
                }
            }
            &.style_7{
                background-color: #DDFAFF;
                .ivu-collapse-header{
                    color: #2ACCE6;
                }
                .xh{
                    color: #2ACCE6;
                }
            }
        }
    }
}
</style>
