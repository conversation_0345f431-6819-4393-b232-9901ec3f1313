<template>
    <ul class="markFillBox">
        <template v-for="(item,index) in fileTagList">
            <li :class="materialFileId == item.materialFileId ? 'zoom-marker' : ''" :id="item.id"  
            :key="index" :style="item.marker.style+'z-index:3'" :title="item.marker.dialog.value" 
            v-if="materialFileId == item.materialFileId" @mousedown="moveStart($event,item)"
            @click="clickFileId($event,item)" >
                <div :class="markerId_new == item.marker.id ? 'zoom-marker1' : ''">
                    <img :src="item.marker.src" class="markFill" />
                    <span :style="item.marker.hint.style" >{{item.marker.hint.value}}</span> 
                </div>
            </li>
        </template>
    </ul>
</template>
<script>
export default {
    name:'FileTagList',
    props:{
        fileTagList: {
            type: Array,
            default: ()=>[]
        },
        materialFileId: {
            type: String,
            default: ''
        },
        markerId:{
            type:String,
            default: ''
        }
    },
    data(){
        return{
            markFillUrl: {
                marketing_fill: require('@/assets/images/dzjz/marketing_fill.png'),
                marketing_fill_blue: require('@/assets/images/dzjz/marketing_fill_blue.png'),
                marketing_fill_yellow: require('@/assets/images/dzjz/marketing_fill_yellow.png'),
            },
            
            isMoving: false,
            moveObj: {},
            moveAfterObj: {},
            markerId_new: '',
            dom: ''
        }
    },
    watch:{
        fileTagList(newVal) {
            if(newVal.length > 0){
                this.dealData(newVal);
            }
            
        },
        materialFileId(newVal) {
            this.materialFileId = newVal;
        },
        markerId(newVal){
            this.markerId_new = newVal
        }
    },
    mounted(){
        if(this.fileTagList.length > 0){
            this.dealData(this.fileTagList);
        }
    },
    methods:{
        clickFileId(e,obj){//点击标注
            e.stopPropagation();
            e.preventDefault();
            if(this.$parent.$parent.clickFileId){
                console.log('执法考评点击了')
                this.$parent.$parent.clickFileId(obj);
            }
        },
        dealData(data){
            let dom = document.getElementsByClassName('imgBox')[0].getElementsByClassName('vmr-ai-raw-image')[0];
            var naturalWidthVal = dom.clientWidth,
                naturalHeightVal = dom.clientHeight;
            data.forEach((item,index)=>{
                item.marker = typeof(item.marker) == 'string' ?  JSON.parse(item.marker) : item.marker;
                if(item.marker.src){
                    if(item.marker.src.indexOf('data:image/png;base64') !== -1){
                        item.marker.src = item.marker.src;
                    }else{
                        var name = item.marker.src.split('/');
                        name = name[name.length - 1];
                        name = name.replace('.png','');
                        item.marker.src =  this.markFillUrl[name];
                    }
                }
                
                let width = (60 / naturalWidthVal * 100).toFixed(3) + '%', height = (60 / naturalHeightVal * 100).toFixed(3) + '%';
                item.marker.style = 'width:'+width+';height:'+height+';position: absolute; left: '+(item.marker.offsetX ? (((item.marker.offsetX - 30)/100)+'rem') : item.marker.newPageX)+'; top: '+(item.marker.offsetY ? (((item.marker.offsetY-60)/100) + 'rem') : item.marker.newPageY)+';'
            })
        },
        moveStart(e,obj){ // 移动标注
            e.stopPropagation();
            e.preventDefault();
            this.$parent.fileTagBox = false;
            this.moveObj = obj;
            
            var moveView = document.getElementsByClassName("imgBox")[this.$parent.selectIndex];
            var eleObj = {};

            moveView.onmousemove = (ele)=>{
                ele.stopPropagation();
                ele.preventDefault();
                var className = ele.target.className;
                if(className !== 'markFill' && className !== ''){
                    eleObj = ele || event;
                    this.isMoving = true;
                    this.getXY(eleObj,this.moveObj);
                }
            }
            document.onmouseup = (ele)=>{
                ele.stopPropagation();
                ele.preventDefault();
                eleObj = ele || event;
                moveView.onmousemove = null;
                document.onmouseup = null;
                var className = ele.target.className;
                if(this.isMoving){
                    this.$parent.AddFileTag(this.moveAfterObj);
                    this.isMoving = false;
                }
            }
        },
        
        getXY(e,obj){ // 获取移动坐标
            // var W = document.getElementsByClassName('vmr-ai-raw-image-mask').width,
            //     NW = document.getElementsByClassName('vmr-ai-raw-image-mask').naturalWidth,
            //     H = document.getElementsByClassName('vmr-ai-raw-image-mask').height,
            //     NH = document.getElementsByClassName('vmr-ai-raw-image-mask').naturalHeight;
            var W = document.getElementsByClassName('imgBox')[this.$parent.selectIndex].clientWidth,
                NW = document.getElementsByClassName('imgBox')[this.$parent.selectIndex].naturalWidth,
                H = document.getElementsByClassName('imgBox')[this.$parent.selectIndex].clientHeight,
                NH = document.getElementsByClassName('imgBox')[this.$parent.selectIndex].naturalHeight;
            var x = e.offsetX/W*NW, 
            y = e.offsetY/H*NH + 30 , 
            pageX = e.offsetX, 
            pageY = e.offsetY,
            newPageX = ((pageX - 25) / W * 100).toFixed(3) + '%',
            newPageY = ((pageY - 40) / H * 100).toFixed(3) + '%';
            obj.marker.x = x;
            obj.marker.y = y;
            obj.marker.pageX = pageX;
            obj.marker.pageY = pageY;
            obj.marker.newPageX = newPageX;
            obj.marker.newPageY = newPageY;
            this.moveAfterObj = JSON.parse(JSON.stringify(obj));
            obj.marker = JSON.stringify(obj.marker);
            this.dealData([obj]);
            
        }
        
    }
    
}
</script>
<style lang="less" scoped>
.markFillBox{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    li{ 
        width: 0.6rem; 
        height: 0.6rem;
        position: relative;
        cursor: pointer;
        div{
            width: 100%;
            height: 100%;
        }
        .markFill{
            font-size: 0.6rem;
            height: 100%;
        }
        span{
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            font-size: 0.14rem !important;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: 0.1rem auto !important;
        }
    }
}
</style>