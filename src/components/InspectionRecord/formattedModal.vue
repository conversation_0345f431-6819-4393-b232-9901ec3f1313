<template>
    <div>
        <Modal
            width="560"
            class="imsProgressModalWrap ims"
            v-model="showModal"
            :footer-hide="true"
            :mask-closable="false"
            :transfer="false"
            title="智能格式化审查">
            <div class="InspectionEventBox">
                <div class="DetectionBox">
                    <div class="DetectionItem" v-for="(item,index) in checkList" :key="index">
                        <label>{{item.name}}:</label>
                        <div :class="['checkBoxItem',{active: item2.check}]" v-for="(item2, index2) in item.list" :key="item2.name" @click="checkAction(index, index2, item)">{{item2.name}}<i class="check"></i></div>
                    </div>
                    <div class="tips">
                        <p>提示：材料检测无误后将更新检测结果。</p>
                    </div>
                </div>
                <ul class="operateBox">
                    <li @click="closeBtn">取消</li>
                    <li @click="confirmBtn">确定</li>
                </ul>
            </div>
        </Modal>
        <TransferModal ref="TransferModal"></TransferModal>
    </div>
</template>
<script>
import  menuJson from "@/assets/script/menu.json";  //{checkList}
import TransferModal from '@/components/TransferModal/index.vue'
import API from '@/api'
export default {
    components: {
        TransferModal
    },
    props: {
        ajbh: {
            type: String,
            default: ''
        },
        ajmc: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            showModal: false,
            testResult: [],
            requireFlag: false,
            checkList: checkList.checkList
        }
    },
    created(){
        
    },
    methods:{
        checkAction(index,index2,item){
            this.checkList[index].list[index2].check = !this.checkList[index].list[index2].check
        },
        getCheckMsg(type){
            var obj = {}, array = []
            this.checkList.forEach(item=>{
                item.list.forEach(item2=>{
                    if(type == 'obj'){
                        if(item2.check){
                            this.requireFlag = true
                        }
                        obj[item2.type] = item2.check ? '1' : '0'
                    }else if(type == 'array'){
                        if(item2.check){
                            let obj = {
                                type: item2.processName,
                                title: this.ajmc+'-'+this.ajbh,
                                progress: 0,
                            }
                            array.push(obj)
                        }
                    }else if(type == 'reset'){
                        // item2.check = false
                    }
                })
            })
            if(type == 'obj'){
                return obj
            }else if(type == 'array'){
                return array
            }
        },
        confirmBtn(){ // 触发检测
            let obj = this.getCheckMsg('obj')
            if(this.requireFlag){
                let params = {
                    ajbh: this.ajbh,
                    ajmc: this.ajmc,
                    checkType: '2'
                }
                params = Object.assign(params, obj)
                this.$store.dispatch("authPostRequest", {
                    url: API.DmsApi.startCheck,
                    params
                }).then(res=>{ 
                    if(res.success){
                        this.$emit('triggerTest')
                        this.closeBtn()
                    }else{
                        this.$refs.TransferModal.setTransferModalObj({
                            show: true,
                            content: res.msg,
                            type: 'warning'
                        })
                    }
                })
            }else{
                this.$refs.TransferModal.setTransferModalObj({
                    show: true,
                    content: '请选择要检测的内容！',
                    type: 'warning'
                })
            }
        },
        closeBtn(){ 
            this.getCheckMsg('reset')
            this.showModal = false;
        },
    },
}
</script>
<style lang="less" scoped>
    @images: '@/assets/images';
    .InspectionEventBox{
        min-height: 2rem;
        box-sizing: border-box;
        .DetectionBox{
            padding: 0.2rem 0.2rem 0 0.2rem;
        }
        .DetectionItem{
            padding: 0.2rem 0.31rem 0.2rem 0.23rem;
            margin-bottom: 0.1rem;
            display: flex;
            align-items: center;
            background: #FAFBFF;
            label{
                font-size: 16px;
                font-weight: bold;
                margin-right: 0.25rem;
                font-family: "Source Han Sans CN-Regular";
            }
            .checkBoxItem{
                padding: 0 0.18rem 0 0.12rem;
                height: 0.32rem;
                line-height: 0.32rem;
                border-radius: 2px;
                background: #fff;
                border: 1px solid #CEE0F0;
                margin-right: 0.2rem;
                letter-spacing: 0.01rem;
                cursor: pointer;
                position: relative;
                color: #3D4E66;
                font-family: "Source Han Sans CN-Regular";
                &.active{
                    color: #309BFF;
                    background: #DFF2FF;
                    border: 1px solid #309BFF;
                    .check{
                        display: inline-block;
                        width: 0.23rem;
                        height: 0.17rem;
                        background: url("~@{images}/check.png") no-repeat;    
                        background-size: 100% 100%;
                        position: absolute;
                        top: -1px;
                        right: -1px;
                    }
                }
            }
            .checkBoxItem:last-child{
                margin-right: 0;
            }
        }
        .tips{
            color: #7A8699;
            padding-left: 0.23rem;
        }
        .operateBox{
            width: 100%;
            height: 0.5rem;
            background: #F7FAFF;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 0.4rem;
            border-top: solid 1px #CEE0F0;
            li{
                width: 0.7rem;
                text-align: center;
                height: 0.3rem;
                line-height: 0.3rem;
                border: solid 1px #087EFF;
                color: #087EFF;
                margin: 0 0.1rem;
                border-radius: 0.04rem;
                background: #FFFFFF;
                border-radius: 4px;
                cursor: pointer;
            }
            li:hover{
                opacity: 0.8;
            }
            li:last-child{
                background: #087EFF;
                color: #fff;
            }
        }
    }
    /deep/.imsProgressModalWrap{
        .ivu-modal-header{
            background: #087EFF;
        }
    }
    /deep/ .ivu-modal-content{
        border-radius: 0.04rem;
        .ivu-modal-body{
            padding: 0;
        }
    }
</style>
