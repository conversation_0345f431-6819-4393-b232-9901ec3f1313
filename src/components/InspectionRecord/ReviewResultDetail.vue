<!--
 * @Author: hanjinxiang && <EMAIL>
 * @Date: 2022-10-26 14:27:58
 * @LastEditors: hanjinxiang && <EMAIL>
 * @LastEditTime: 2022-11-09 00:36:56
 * @FilePath: \ims-component\compoents\src\components\InspectionRecord\ReviewResultDetail.vue
 * @Description: 
-->
<template>
    <ul class="ReviewResultDetail">
        <template v-for="(item,index) in reviewResultList">
            <li :key="'RRL'+index" :style="item.styleObj" :id="item.id" :title="item.typeName">
                <span class="seal" v-show="item.seal"><i></i></span>
                <span class="signature" v-show="item.signature"><i></i></span>
                <span class="fingerprint" v-show="item.fingerprint"><i></i></span>
            </li>
        </template>
    </ul>
</template>
<script>
export default {
    props:{
        reviewResultList: {
            type: Array,
            default: ()=>[]
        },
    },
    name: 'ReviewResultDetail',
    data() {
        return {
            
        }
    },
    watch: {
        
    },
    created(){


    },
    methods:{
        
        
    },
}
</script>
<style lang="less" scoped>
.ReviewResultDetail{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9;
    pointer-events: none;
    span{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 0.26rem;
        height: 0.26rem;
        text-align: center;
        border-radius: 50%;
        margin: 0.05rem 0 0 0.05rem;
        background: #FF910B;
        i{
            display: inline-block;
            width: 0.15rem;
            height: 0.18rem;
        }
    }
    .seal i{
        background: url('../../assets/images/icon-qz.png') no-repeat;
        background-size: 100% 100%;
    }
    .signature i{
        background: url('../../assets/images/icon-qm.png') no-repeat;
        background-size: 100% 100%;
    }
    .fingerprint {
        float: right;
        i{
            background: url('../../assets/images/icon-ny.png') no-repeat;
            background-size: 100% 100%;
        }
    }
}
</style>
