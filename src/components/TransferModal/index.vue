<!--
 * @Author: hanjinxiang && <EMAIL>
 * @Date: 2022-10-24 10:58:38
 * @LastEditors: hanjinxiang && <EMAIL>
 * @LastEditTime: 2022-10-24 15:14:23
 * @FilePath: \ims-component\compoents\src\components\TransferModal\index.vue
 * @Description: 
-->
<template>
    <Modal
        v-model="transferModalObj.show"
        :closable="false"
        :transfer="transferModalObj.transfer"
        :ok-text="transferModalObj.okText"
        :cancel-text="transferModalObj.cancelText"
        :width="transferModalObj.width">
        <template #header>
            <template v-if="transferModalObj.type == 'info'">
                <div class="header-info">
                    <Icon type="ios-information-circle"></Icon>
                    <span>{{transferModalObj.title}}</span>
                </div>
            </template>
            <template v-if="transferModalObj.type == 'success'">
                <div class="header-success">
                    <Icon type="ios-checkmark-circle"></Icon>
                    <span>{{transferModalObj.title}}</span>
                </div>
            </template>
            <template v-if="transferModalObj.type == 'warning'">
                <div class="header-warning">
                    <Icon type="ios-alert"></Icon>
                    <span>{{transferModalObj.title}}</span>
                </div>
            </template>
            <template v-if="transferModalObj.type == 'error'">
                <div class="header-error">
                    <Icon type="ios-close-circle"></Icon>
                    <span>{{transferModalObj.title}}</span>
                </div>
            </template>
        </template>
        <p>{{transferModalObj.content}}</p>
        <template #footer>
            <Button v-if="transferModalObj.showCancelBtn" type="text"  @click="cancel">{{transferModalObj.cancelText}}</Button>
            <Button type="primary"  @click="confirm">{{transferModalObj.okText}}</Button>
        </template>
    </Modal>
</template>
<script>
export default {
    name: 'TransferModal',
    data() {
        return {
            transferModalObj: {
                show: false,
                content: '',
                width: '350',
                transfer: false,
                title: '温馨提示',
                content: '',
                okText: '确定',
                cancelText: '取消',
                type: 'success',
                showCancelBtn: false,
                params: {}
            }
        }
    },
    created(){

    },
    methods:{
        setTransferModalObj(obj){
            for(let i in obj){
                this.transferModalObj[i] = obj[i]
            }
        },
        confirm(){
            if(this.transferModalObj.showCancelBtn){
                this.$emit('confirm', this.transferModalObj.params)
                this.cancel()
            }else{
                this.cancel()
            }
        },
        cancel(){
            this.transferModalObj.show = false
            this.transferModalObj.showCancelBtn = false
            this.transferModalObj.params = {}
        }
    },
}
</script>
<style lang="less" scoped>
/deep/.ivu-modal{
    .ivu-modal-header{
        background: #fff;
        border: none;
        font-size: 0.16rem;
        i{
            font-size: 0.2rem;
        }
    }
    .ivu-modal-body{
        padding: 0.14rem 0.4rem !important;
        font-size: 0.16rem;
    }
    .ivu-modal-footer{
        border: none;
        .ivu-btn{
            min-width: 0.6rem;
            width: 0.6rem;
            height: 0.32rem;
            line-height: 0.32rem;
        }
    }
    .ivu-modal-content{
        overflow: hidden;
    }
}

.header-warning i{
    color: #f90;
}
.header-info i{
    color: #2d8cf0;
}
.header-success i{
    color: #19be6b;
}
.header-error i{
    color: #ed4014;
}
</style>
