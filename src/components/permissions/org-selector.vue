<template>
	<div style="position: relative;height: 100%;">
        <!-- <slot name="func" v-if="$slots.func"></slot>
		<Input v-else ref="input" class="bsp-org-input" :disabled="disabled" clearable :readonly="readonly" @on-clear="clearData"
		search :enter-button="button" v-model="txt"  placeholder="" @on-search="openOrgDialog" />  -->
		<!-- <Modal v-model="openStatus" :mask-closable="false"  :closable="false" class-name="bsp_org_sel_box" :width="900">
			 <div class="flow-modal-title" slot="header">
				<span  style="font-size: 17px !important;">单位选择</span>
				<span @click="cancel" style="position: absolute; right: 6px;font-size: 32px;cursor: pointer;">
					<i class="ivu-icon ivu-icon-ios-close"></i>
				</span>
			</div> -->
			<div class="bsp_org_sel_box"  >
                <div class="left-org-tree content-ht" style="position: relative;">
                    <div class="org-search-box">
                         <Input v-model="searchTxt" suffix="ios-search" @on-change="searchField" placeholder="机构单位查询" />
                    </div>
                    <div class="content-box bsp-scroll">
                        <el-tree :data="rootData"  ref="orgTree"  style="overflow:overlay"
                        node-key="id"
                        :expand-on-click-node="false"
                        :show-checkbox="multiple"
                        :check-strictly="true"
                        :highlight-current="true"
                        :accordion="true"
                        :check-on-click-node="true"
                        :filter-node-method="filterNode"
                        :indent="26"
                        @check="checkChange"
                        :render-content="renderContent" icon-class="el-icon-arrow-right"  ></el-tree>
                    </div>
                     <Spin size="large" fix v-if="spinShow"></Spin>
                </div>
                 <div class="right-org-list content-ht">
                    <div class="org-selected-box">
                        <b>已选单位</b>
                        <div style="float:right;margin-right:2px;" title="删除全部">
                            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAA10lEQVQ4T2P8//8/MwMVACMugz4aGe1k+P/fGdkORgaGI3znzztgsxfDoI+Ghn+IcSD/+fMsKJbAXESsAeiWwAyEu+ijoeFuBgYGR2Jcg6RmP//5864gPsJrjY3M7w4cECPFICEHh1cM9fV/UQyinteMjSsY/v1rIcVFDExMNfxnz3ageo2BgQHkqv///z9mYGLawPD/fzAjA4PUf0bGyVCFuehyyDGHEv1g7zEy7uU/d879k6Hhgf8MDDYwxfjksLpo1CCU8KNxGJGQkPBHPzUMIsEMDKUAY+HyE69FM1YAAAAASUVORK5CYII=" @click="deleteAll" style="cursor: pointer;"/>
                        </div>
                    </div>
                    <div>
                         <div  class="content-box bsp-scroll" style="background:#FAFBFF">
                            <table style="width:100%;table-layout: fixed;">
                                <tr v-for="(item, i) in selectedOrg" :title="item.name + '(' + item.id + ')'" :key="i">
                                    <td class="org-left">{{item.name}}({{item.id}})
                                    </td>
                                    <td class="org-del">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAABEUlEQVQ4T2NkwAL6156RZPj1bwMDw38zVGnGUwxsTAGFwSbP0bUxIgs0NPxn4lc/9YWBgYETmwVIYt8/3jTjaWhg/AcTgxvUv/iMJAPL32cEDECV/sMsVRgLcR3YIKhL/pJkCFTxx5tmzCCXgQ3qW3byCyMjAzc5Bv3/z/C1KMqch7Fz+REpNgbWp8iGuJsrMajLCzNMWnUaxey8MFOGGw/fMuw6eQ/Di4z9y0+eYmBgMEWWAWlgYmJk+PfvP9wwbGJIek6DDPqPzUvIGkHy6AZjRD8ug0AKYYaB2Miuw2YxThdRzSByvEadwMYW/W7mSgwapEY/1RIkVbMIyDCqZFpYuqBKMYKcyCARwLaelIINAPXCscs343ggAAAAAElFTkSuQmCC" @click="deleteSelected(item,i)" class="img-box"/>
                                    </td>
                                </tr>
                            </table>
                         </div>
                    </div>
                </div>
			</div>
            <div class="bsp-base-fotter" style="position: absolute;bottom: 27%;">
                <Button @click="goBack">返 回</Button>
                <Button size="large" @click="comfired" type="primary">保存</Button>
            </div>
			<!-- <div slot="footer">
				<Button type="error" style="margin: 0 15px;" @click="cancel">取&nbsp;&nbsp;消</Button>
				<Button type="primary" @click="comfired">确&nbsp;&nbsp;认</Button>
			</div>
		</Modal> -->
	</div>
</template>

<script>
  	export default {
        props: {
            value: {
                type: String,
                default: ''
            },
            text: {
                type: String,
                default: ''
            },
            return: {
                type: String,
                default: ''
            },
            readonly: {
                type: Boolean,
                default: true
            },
            disabled: {
                type: Boolean,
                default: false
            },
             button: {
                type: String,
                default: '选择'
            },
            multiple: {
                type: Boolean,
                default: true
            },
            bindEvent: {
                type: Boolean,
                default: true
            },
            rootOrgId: {
                type: String,
                default: ''
            },
            defaultText: {
                type: Boolean,
                default: false
            }
        },
        data () {
            return {
                txt: this.text,
                isBuild: false,
                currentNodeId: '',
                searchTxt: '',
                openStatus: false,
                loaded: false,
                defaultProps: {
                    children: 'children',
                    label: 'name'
                },
                selectedOrg: [],
                rootData: [],
                checkedKeys: [],
                expandedKeys: [],
                spinShow: false
            }
        },
        watch: {
            value: {
                handler(val, oldVal) {
                     if(this.defaultText && !this.loaded) {
                       this.getOrgDataByIds()
                    }
                },
                immediate: true
            },
            text: {
                handler(val, oldVal) {
                     this.txt = val
                }
            }
        },
		mounted() {
             this.getOrgData()
		},
		methods: {
                goBack(){
                  this.$emit('goBack')
                },
            openOrgDialog () {
                if(this.value) {
                    this.checkedKeys = this.value.split(",")
                } else {
                    this.checkedKeys = []
                }
                this.openStatus = true
                if(this.isBuild) {
                    this.$refs.orgTree.setCheckedKeys(this.checkedKeys)
                    this.selectedOrg = this.$refs.orgTree.getCheckedNodes()
                    return
                }
                this.getOrgData()
            },
            getOrgData() {
                this.spinShow = true
                let params = {pId: this.rootOrgId}
                this.$store.dispatch('postRequest', { url: '/bsp-uac/uac/org/getOrgsByPid', params: params }).then(resp => {
                    this.spinShow = false
                    if(resp.success) {
                       //let root = this.buildTreeData(resp.data)
                       let roots = this.transData(resp.data)
                       if(roots && roots.length > 0) {
                           for(let root of roots) {
                               root.expanded = true
                           }
                       }
                       let arr = [...roots]

                       this.rootData = arr
                       this.isBuild = true
                      if (this.$listeners['init-callback']) {
                        this.$nextTick(() => this.$emit('init-callback'))
                      }
                    }
                })
            },
            getOrgDataByIds () {
                if(!this.value) {
                    return
                }
                this.loaded = true
                let params = {ids: this.value}
                 this.$store.dispatch('postRequest', { url: '/bsp-uac/uac/org/findByIds', params: params }).then(resp => {
                    if(resp.success) {
                        let datas = resp.data
                        if(datas && datas.length > 0) {
                            let orgNames = []
                             datas.forEach(item => {
                                orgNames.push(item.name)
                             })
                            this.txt = orgNames.join(",")
                        }
                    }
                })
            },
            buildTreeData (datas) {
                let root = []
                let mapping = this.treeIdMapping(datas)
                datas.forEach(el => {
                    if(this.checkedKeys.length > 0 &&  this.checkedKeys.includes(el.id)) {
                        this.selectedOrg.push(el)
                    }
                    if (!el.parentId || el.id === this.rootOrgId) {
                        root = el
                        return
                    }
                    // 用映射表找到父元素
                    const parentEl = datas[mapping[el.parentId]]
                    // 把当前元素添加到父元素的`children`数组中
                    parentEl.children = [...(parentEl.children || []), el]
                })
                return root
            },
            transData(datas){
                var r = [], hash = {}, id = 'id', pid = 'parentId', children = 'children', i = 0, j = 0, len = datas.length
                for(; i < len; i++){
                    hash[datas[i][id]] = datas[i]
                    if(this.checkedKeys.length > 0 &&  this.checkedKeys.includes(datas[i].id)) {
                        this.selectedOrg.push(datas[i])
                    }
                }
                for(; j < len; j++){
                    var aVal = datas[j], hashVP = hash[aVal[pid]]
                    if(hashVP){
                        !hashVP[children] && (hashVP[children] = [])
                        hashVP[children].push(aVal)
                    }else{
                        r.push(aVal)
                    }
                }
                return r
            },
            treeIdMapping (data) {
                return data.reduce((acc, el, i) => {
                    acc[el.id] = i
                    return acc
                }, {})
            },
            renderContent (h, { node, data, store }) {
                if(data.expanded) {
                     node.expanded = true
                }
                let type = 'ios-home-outline'
                if (data.children && data.children.length > 0) {
                    type = 'ios-folder-outline'
                }
                let hArr = []
                if(!this.multiple) {
                    let checked = false
                    if(data.id === this.currentNodeId) {
                        checked = true
                    }
                    hArr.push(h('Radio', {
                            props: {
                                value: checked,
                                size:'large'
                            }
                        }))
                }
                hArr.push(h('Icon', {
                        props: {
                            type: type,
                            color: '#6e95f1',
                        },
                        style: {
                            marginRight: '4px'
                        }
                }))
                hArr.push(h('span', {
                        style: {
                            fontSize: '16px'
                        },
                        attrs: {
                            title: data.name
                        }
                    }, data.name))
                return h('span', hArr)
            },
            filterNode(value, data) {
                if (!value) return true
                return data.name.indexOf(value) !== -1 || data.id.indexOf(value) !== -1
            },
            searchField() {
                var searchTxt = this.searchTxt
                this.$refs.orgTree.filter(searchTxt)
                if(!searchTxt) {
                    this.setAllExpand(this.rootData[0].id, false)
                }
            },
            setAllExpand(id, state){
                var nodes = this.$refs.orgTree.store.nodesMap
                for(var i in nodes){
                    if(id === i) {
                        nodes[i].expanded = true
                    } else {
                        nodes[i].expanded = state
                    }
                    
                }
            },
            treeNodeClick (data,node) {
                this.checkChange(data,node)
                return
            },
            checkChange (data,node) {
                if(!this.multiple) {
                    this.$refs.orgTree.setCheckedNodes([data])
                    this.currentNodeId = data.id
                }
                this.selectedOrg = this.$refs.orgTree.getCheckedNodes()
            },
            deleteSelected(data,idx) {
                this.selectedOrg.splice(idx, 1)
                this.currentNodeId = ''
                this.$refs.orgTree.setCheckedNodes(this.selectedOrg)
            },
            deleteAll () {
                this.selectedOrg = []
                this.currentNodeId = ''
                this.$refs.orgTree.setCheckedNodes(this.selectedOrg)
            },
			// 弹窗取消事件
			cancel () {
				this.openStatus = false
				this.$emit('on-cancel')
			},
            comfired() {
                if (this.selectedOrg.length == 0) {
                    let datas = []
                    this.openStatus = false
                    this.$emit('on-select', datas, "03")
                    return
				}
                let datas = this.buildData(this.selectedOrg)
                this.setData(datas)
                this.openStatus = false
			    this.$emit('on-select', datas, "03")
            },
            buildData (datas) {
                let arr = []
                datas.forEach(item => {
                    let obj  = {orgId:item.id, orgName:item.name, sname:item.sname, fname:item.fname, regId: item.regionId, regName: item.regionName, cityId: item.cityId, cityName: item.cityName}
                    arr.push(obj)
               })
               return  arr
            },
            setData (data) {
                let ret = {}
                Object.keys(data[0]).forEach(key => {
                    ret[key.toLowerCase()] = []
                })
                data.forEach(item => {
                    for(let key  in item){
                        ret[key.toLowerCase()].push(item[key])
                    }
                })
                this.$emit('input', ret['orgid'].join(','))
                this.txt = ret['orgname'].join(',')
                let fields = this.return.split('|')
                if(fields && fields.length > 0) {
                    fields.forEach(item => {
                        let field = item.split(':')
                        if(field && field.length >= 2) {
                            let retValArr = ret[field[1].toLowerCase()]
                            let retVal = ''
                            if(retValArr) {
                                retVal = retValArr.join(',')
                            }
                            this.$emit('update:'+field[0], retVal)
                        }
                    })
                }
            },
            focus() {
				this.$refs['input'].focus()
			},
			clearData () {
				this.$emit('input', '')
				if(this.return) {
					let fields = this.return.split('|')
					if(fields && fields.length > 0) {
						fields.forEach(item => {
							let field = item.split(':')
							if(field && field.length >= 2) {
								this.$emit('update:'+field[0], '')
							}
						})
					}
				}
                this.deleteAll()
				this.$emit('on-clear')
			},
            warnMsg (msg) {
                this.warningModal({ content: msg })
            }
		}
    }
</script>
<style lang="less" scoped>
.footer-btn {
    position: absolute;
    width: 100%;
    height: 50px;
    line-height: 50px;-
    text-align: center;
    background: #fff;
    bottom: 40px;
    .ivu-btn-primary {
      font-size: 16px;
      min-width: 80px;
      height: 36px;
      background: #2b5fd9;
      border-radius: 2px;
    }
}
.bsp_org_sel_box {
	padding: 10px;
    width: 100%;
    height: 96%;
}
.bsp_org_sel_box .content-ht {
     /* height:500px; */
     height: calc(~'100% - 60px');
     width: 100%;
}
.bsp_org_sel_box .left-org-tree {
    float:left;
    border:1px solid #CEE0F0;
    width:75%;
    overflow:hidden;
}
 .bsp_org_sel_box .right-org-list {
     float:right;
     border:1px solid #CEE0F0;
     width:25%;
 }
.bsp_org_sel_box .left-org-tree .org-search-box{
    background: #f2f6fc;
    padding: 5px 25px;
}
.bsp_org_sel_box .org-selected-box {
    background: #f2f6fc;
    padding: 8px 15px;
}

.bsp_org_sel_box .content-box {
    height: 94%;
    overflow: auto;
    padding: 5px 15px;
}
.bsp_org_sel_box .table {
    border: none;
    width:100%;
    table-layout: fixed;
}

.bsp_org_sel_box .table td{
    border: none;
}
.bsp_org_sel_box .org-left{
    width:92%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}
.bsp_org_sel_box .org-del{
    width:8%;
    text-align:right;
}
.bsp_org_sel_box .org-del .img-box {
    margin:7px 0 0px 0;
    cursor: pointer;
}
.bsp_org_sel_box .bsp-scroll {
     overflow: overlay;
}
.bsp_org_sel_box /deep/ .ivu-input {
    height: 30px;
    border-color: #cee0f0;
    font-size: 15px !important;
}

.bsp_org_sel_box /deep/ .ivu-tree ul li {
    margin: 2px 0;
}
.bsp_org_sel_box /deep/ .ivu-input:focus {
  border-color: #337bf5;
  box-shadow: inset 0 0 0 1000px #FFFFFF!important;
}
.bsp_org_sel_box /deep/ .el-tree .ivu-icon {
    font-size: 20px !important;
    font-weight: bold;
}

.bsp_org_sel_box /deep/ .el-tree .el-checkbox__inner{
    height: 16px;
    width: 16px;
    border: 1px solid #cee0f0;
}
.bsp_org_sel_box /deep/ .el-tree .el-checkbox__inner::after {
    height: 8px;
    left: 5px;
    border: 2px solid #FFF;
        border-left: 0;
    border-top: 0;
}

.bsp_org_sel_box /deep/ .el-checkbox__input.is-checked .el-checkbox__inner {
     border-color: #2b5fda;
     background-color: #2b5fda;
}
.bsp_org_sel_box /deep/ .ivu-radio-wrapper {
    margin-right:  4px;
}
.bsp_org_sel_box /deep/  .ivu-radio-inner {
     border-color: #cee0f0;
}
.bsp_org_sel_box /deep/ .ivu-radio-checked .ivu-radio-inner {
     border-color: #2b5fda;

}
.bsp_org_sel_box /deep/ .ivu-radio-checked .ivu-radio-inner:after {
     background-color: #2b5fda;
}
.bsp_org_sel_box /deep/ .ivu-tree .ivu-checkbox-wrapper {
    margin-right: 0px;
}

.bsp_org_sel_box /deep/ .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: #F0F5FF;
}

.bsp_org_sel_box /deep/ .ivu-checkbox-inner,.bsp-base-form  .ivu-radio-inner {
  border-color: #cee0f0;
}
.bsp_org_sel_box .flow-modal-title {
	height: 40px;
	background: #2b5fda;
	width: 100%;
	text-indent: 1em;
	color: #fff;
	line-height: 40px;
}
/deep/ .el-tree {
    height: 100%;
  }
/deep/ .bsp_org_sel_box * {
	font-size: 16px !important;
}
/deep/ .bsp_org_sel_box .ivu-modal-content .ivu-modal-body , /deep/ .bsp_org_sel_box .ivu-modal-header {
      padding: 0px
}
 /deep/ .bsp_org_sel_box .ivu-icon-ios-close {
	font-size: 32px  !important;;
	line-height: 40px;
 }

/deep/ .bsp-org-input .ivu-input-search {
	font-size: 15px;
	padding: 0 !important;
	width: 70px;
	max-width: 70px;
}
 /deep/ .bsp-org-input .ivu-input-icon {
        right: 66px;
        font-size: 20px;
    }

 /deep/ .bsp-org-input .ivu-input  {font-size: 16px;}

.bsp-scroll::-webkit-scrollbar {/*滚动条整体样式*/
  width: 10px;     /*高宽分别对应横竖滚动条的尺寸*/
  height: 10px;

}
.bsp-scroll::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
  border-radius: 3px;
  background: #b7c7dd;

}
.bsp-scroll::-webkit-scrollbar-track {/*滚动条里面轨道*/
  border-radius: 3px;
  background: #EDEDED;
}

</style>
