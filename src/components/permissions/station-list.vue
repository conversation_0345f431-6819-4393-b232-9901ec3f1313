<template>
  <div>
    <div>
      <div class="bsp-base-form">
        <div class="bsp-base-tit">
          <!-- <div style="display: inline-block; font-weight: bold;cursor: pointer" @click="goBack">返回&nbsp;&nbsp;</div> -->
          <div style="display: inline-block; padding: 0px 15px; font-weight: bold">权限控制</div>
        </div>
        <div class="bsp-base-content" style="bottom: 100px;top:48px !important">
          <Row style="height: 100%; flex-flow: row nowrap">
            <!-- <Col flex="360px" class="base-left-flex"> -->
              <!-- <div class="search-box">
                <Input suffix="ios-search" placeholder="配置搜索" v-model="keyword" />
              </div> -->
              <!-- <div class="oper-box">
                <Button type="primary"  @click="addStation" style="margin-right: 10px">添加总台</Button>
                <Button type="primary"  @click="addStationSub" :disabled="!addStationSubEnabled" style="margin-right: 10px">添加总台子页面</Button>
                <Button type="error" size="small" @click="deleteNodes">删除</Button>
              </div> -->
              <!-- <div style="height: calc(100vh - 151px)" class="tree-box">
                <Tree ref="tree" :data="treeData" check-strictly show-checkbox @on-check-change="onCheckTree" @on-select-change="onClickTree"></Tree>
              </div> -->
            <!-- </Col> -->
            <Col flex="auto" style="overflow: hidden">
              <div>
                <Tabs type="card" ref="parentTab" v-model="tabsValue" name="tab1">
                  <!-- <TabPane label="总台子页面" name="bj" tab="tab1">
                    <div style="height: calc(100vh - 110px); background: rgb(235, 238, 245)">
                      <station-sub-editform ref="ztXxkEditForm" :formValidate="ztSubInfo" @refresh-tree="refreshTree" />
                    </div>
                  </TabPane> -->
                  <TabPane label="角色授权" name="jssq" tab="tab1">
                    <div style="height: calc(100vh - 110px); background: rgb(235, 238, 245)">
                      <role-assign ref="roleAssign" :app-id="appId"  :ids="roleIds" @init-callback="initRoleSelect" @choice_confirm="savePermissions" @goBack="goBack"> </role-assign>
                    </div>
                  </TabPane>
                  <TabPane label="区域授权" name="qysq" tab="tab1">
                    <div style="height: calc(100vh - 110px); background: rgb(235, 238, 245)">
                      <area-assign ref="areaAssign" @init-callback="initAreaSelect" @on-select="savePermissions" @goBack="goBack"> </area-assign>
                    </div>
                  </TabPane>
                   <TabPane label="机构授权" name="jjsq" tab="tab1">
                    <div style="height: calc(100vh - 110px); background: rgb(235, 238, 245)">
                      <org-select ref="orgSelect" :ids="orgIds" @init-callback="initOrgSelect" @on-select="savePermissions" @goBack="goBack"> </org-select>
                    </div>
                  </TabPane>
                </Tabs>
              </div>
            </Col>
          </Row>
        </div>
      </div>
          <!-- <div class="bsp-base-fotter">
            <Button @click="goBack">返 回</Button>
          </div> -->
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import roleAssign from './role-assign.vue'
import areaAssign from './area-assign.vue'
import orgSelect from './org-selector.vue'
export default {
  name: 'main-station',
  props: {
    /**
     * 应用ID，无条件必传
     */
    yyid: {
      type: String,
      default: ''
    },
    mark: {
      type: String,
      default: ''
    },
  },
  components: {
    roleAssign,
    areaAssign,
    orgSelect
  },
  data() {
    return {
      operateLevel: 0, // 操作的节点级别，0为还未关联操作节点
      funcName: '',
      // 原始树数据（未构建成树的结构的数据）
      originTreeData: [],
      // 树数据
      treeData: [],
      ztInfo: {},
      ztSubInfo: {},
      // 当前选中菜单ID
      curNodeId: '',
      curSelectedNode: null,
      addStationSubEnabled: false,
      checkedNodeId: [],
      tabsValue: 'jssq',
      displayRoleAssign: false, // 是否显示角色授权tab页
      displayAreaAssign: false, // 是否显示区域授权tab页
      keyword: '',
      appId: serverConfig.APP_ID,
      roleIds: [],
      areaIds: [],
      orgIds: []
    }
  },
  mounted() {
    this.getPermissions()
  },
  watch: {
    keyword(val) {
      this.searchTree(val)
    },
    curSelectedNode(node) {
      // 是否禁用添加总台子页面
      if (!node) {
        this.addStationSubEnabled = false
      } else {
        this.addStationSubEnabled = node.level === 1
      }
    },
    areaIds (val) {
      this.initAreaSelect()
    },
    roleIds (val) {
      this.initRoleSelect()
    },
    orgIds (val) {
      this.initOrgSelect()
    },
    // roleIds (val) {
    //   this.initRoleSelect()
    // }
    // tabsValue(val) {
    //   if (val === 'jssq') {
    //     this.displayRoleAssign = true
    //   } else {
    //     this.displayRoleAssign = false
    //   }
    //   if (val === 'qysq') {
    //     this.displayAreaAssign = true
    //   } else {
    //     this.displayAreaAssign = false
    //   }
    // }
  },
  methods: {
    ...mapActions(['authGetRequest', 'postRequest']),
    goBack () {
      this.$emit('back')
    },
    /**
     * 渲染树
     * @param arr 待渲染数据
     * @param parentId 父级ID
     * @param type 类型 1:第一层根节点，2：有parentId的子节点
     */
    createTree(arr, parentId, type) {
      let tree = []
      arr.forEach(e => {
        let item = null

        if (type == '1' && !e.parentId) {
          // 根节点：parentId为空或者parentId不存在的
          item = e
          item.level = 1
        } else if (type == '2' && parentId == e.parentId) {
          item = e
          item.level = 2
        }

        if (item != null) {
          let node = {}
          node.data = item.bindData
          node.title = item.name
          node.id = item.id
          node.level = item.level
          node.parentId = item.parentId
          node.children = this.createTree(arr, item.id, '2')
          if (JSON.stringify(node.children) != '[]') {
            node.expand = true
          }
          // 渲染被选中的状态
          if (this.curNodeId && item.id == this.curNodeId) {
            node.selected = true
            this.curSelectedNode = node
          }
          if (this.checkedNodeId.indexOf(item.id) > -1) {
            // 清空选中
            node.checked = false
          }
          tree.push(node)
        }
      })
      return tree
    },
    /**
     * 点击树事件
     * @param data 当前选中的数组
     * @param item 当前选中的节点
     */
    onCheckTree(data, item) {
      this.tabsValue = 'jssq'
      if (item.children && item.children.length > 0) {
        // 父级
        if (item.checked) {
          // 选中
          let ids = [item.id]
          ids.push(...this.getAllChildrenData(item.children, item.checked))
          for (let id of ids) {
            if (this.checkedNodeId.indexOf(id) < 0) {
              this.checkedNodeId.push(id)
            }
          }
        } else {
          // 清除父级
          let nodesArray = this.getParentNodes(this.treeData, item.id)
          let idsArr = []
          for (let node of nodesArray) {
            if (node.parentId) {
              idsArr.push(node.parentId)
            }
          }
          this.testTree(this.treeData, idsArr.join(','))

          // 取消选中
          let ids = this.getAllChildrenData(item.children, item.checked)
          let nodes = this.getParentNodes(this.treeData, item.id)
          for (let node of nodes) {
            ids.push(node.id)
          }
          this.checkedNodeId = this.checkedNodeId.filter(item => ids.indexOf(item) < 0)
        }
        // this.treeData = this.createTree(this.originTreeData, '', '1')
      } else {
        // 子级
        let id = item.id
        if (item.checked) {
          // 选中
          if (this.checkedNodeId.indexOf(id) < 0) {
            this.checkedNodeId.push(id)
          }
        } else {
          // 取消选中
          // this.checkedNodeId = this.checkedNodeId.filter(item => item !== id)
          let nodes = this.getParentNodes(this.treeData, id)
          let ids = []
          let pid = ''
          for (let node of nodes) {
            // ids.push(node.id)
            if (node.parentId) {
              ids.push(node.parentId)
            }
            // if (item.parentId === node.id) {
            //   pid = node.id
            // }
          }
          this.checkedNodeId = this.checkedNodeId.filter(it => ids.indexOf(it) < 0)
          this.testTree(this.treeData, ids.join(','))
        }
      }
    },

    // 初始化树形数据
    testTree(datas, idsArr) {
      for (let item of datas) {
        if (item.children && item.children.length > 0) {
          if (item.checked === true && idsArr.indexOf(item.id) > -1) {
            this.$set(item, 'checked', false)
            this.$forceUpdate()
            //   break
          }
          this.testTree(item.children, idsArr)
        }
      }
    },

    getParentNodes(arr1, id) {
      var temp = []
      let idKey = 'id'
      let pIdKey = 'parentId'
      var forFn = function (arr, id) {
        for (var i = 0; i < arr.length; i++) {
          var item = arr[i]
          if (item[idKey] === id) {
            let o = Object.assign({}, item)
            delete o['children']
            temp.unshift(o)
            forFn(arr1, item[pIdKey])
            break
          } else {
            if (item.children) {
              forFn(item.children, id)
            }
          }
        }
      }
      forFn(arr1, id)
      return temp
    },

    getAllChildrenData(datas, parentChecked) {
      let arr = []
      for (let item of datas) {
        if (item.children && item.children.length > 0) {
          arr.push(item.id)
          let childIds = this.getAllChildrenData(item.children, parentChecked)
          arr.push(...childIds)
        } else {
          arr.push(item.id)
        }
        if (parentChecked || parentChecked === true) {
          this.$set(item, 'checked', true)
          this.$forceUpdate()
        } else if (!parentChecked || parentChecked === false) {
          this.$set(item, 'checked', false)
          this.$forceUpdate()
        }
      }
      return arr
    },
    onClickTree(data, item) {
      if (!item.selected) {
        this.curSelectedNode = null
        this.operateLevel = 0
        this.funcName = ''
      } else {
        this.curSelectedNode = item
        this.operateLevel = item.level
        if (item.level === 1) {
          // 父级节点
          this.ztInfo = item.data
          this.funcName = '编辑总台'
        } else if (item.level === 2) {
          // 子级节点
          this.ztSubInfo = item.data
          this.funcName = '编辑总台子页面'
          this.tabsValue = 'jssq'
        }
      }
    },
    /**
     * 添加总台
     */
    addStation() {
      this.ztInfo = {} // 重置
      this.funcName = '添加总台'
      this.operateLevel = 1
    },

    addStationSub() {
      this.ztSubInfo = {} // 重置
      var selectedNodes = this.$refs.tree.getSelectedNodes()
      if (selectedNodes && selectedNodes.length > 0) {
        this.ztSubInfo = { ztId: selectedNodes[0].id }
        this.funcName = '添加总台子页面'
        this.operateLevel = 2
        this.tabsValue = 'jssq'
      } else {
        this.$Notice.error({
          title: '温馨提示',
          desc: '请选中一个父级总台'
        })
      }
    },

    /**
     * 删除节点配置
     */
    deleteNodes() {
      var checkArr = this.$refs.tree.getCheckedNodes()
      if (checkArr.length === 0) {
        this.$Notice.error({
          title: '温馨提示',
          desc: '请勾选待删除的节点配置'
        })
        return false
      }
      var ztIdList = []
      var xxkIdList = []
      checkArr.forEach(node => {
        if (node.parentId) {
          xxkIdList.push(node.id)
        } else {
          ztIdList.push(node.id)
        }
      })
      this.$Modal.confirm({
        title: '是否确认删除？',
        loading: true,
        onOk: async () => {
          this.submitMixDel(ztIdList, xxkIdList)
        }
      })
      // this.confirmModal({ content: '是否确认删除？' }).then(async () => {
      //   this.submitMixDel(ztIdList, xxkIdList)
      // })
    },
    submitMixDel(ztIdList, xxkIdList) {
      let that = this
      if (xxkIdList && xxkIdList.length > 0) {
        // 先删除子节点的配置
        this.postRequest({ url: this.$path.admin_ztpz_root + '/xxk/batchdel', params: { ids: xxkIdList.join(',') } }).then(resp => {
          if (resp.success) {
            this.refreshTreeAfterDelete(xxkIdList, false)
            this.$Notice.success({
              title: '成功提示',
              desc: '删除总台子页面配置成功'
            })
            that.$nextTick(() => that.deleteZtpz(ztIdList))
            // this.successModal({
            //   title: '成功提示',
            //   desc: '删除总台子页面配置成功'
            // }).then(() => {
            //   that.$nextTick(() => that.deleteZtpz(ztIdList))
            // })
          } else {
            this.$Notice.error({
              title: '错误提示',
              desc: resp.msg
            })
          }
        })
      } else {
        this.deleteZtpz(ztIdList)
      }
    },
    deleteZtpz(ztIdList) {
      if (ztIdList && ztIdList.length > 0) {
        this.postRequest({ url: this.$path.admin_ztpz_root + '/batchdel', params: { ids: ztIdList.join(',') } }).then(resp => {
          if (resp.success) {
            this.refreshTreeAfterDelete(ztIdList, true)
            this.$Notice.success({
              title: '成功提示',
              desc: '删除总台配置成功'
            })
          } else {
            this.$Notice.error({
              title: '错误提示',
              desc: resp.msg
            })
          }
        })
      }
    },
    /**
     * 删除节点配置后刷新配置树
     * @param idList 删除了的节点id
     * @param isParent 删除的是否是父节点id列表
     */
    refreshTreeAfterDelete(idList, isParent) {
      // 重置相关属性
      this.operateLevel = 0
      this.funcName = ''
      this.ztInfo = {}
      this.ztSubInfo = {}
      this.curNodeId = ''
      this.curSelectedNode = null
      this.checkedNodeId = []
      this.tabsValue = 'jssq'
      let _this = this
      this.originTreeData = _this.originTreeData.filter(data => {
        if (isParent && !data.parentId && idList.indexOf(data.id) > -1) {
          // 要删除的是父节点，且data是父节点，且data在要删除的列表中
          return false
        }
        if (!isParent && data.parentId && idList.indexOf(data.id) > -1) {
          // 要删除的是子节点，且data是子节点，且data在要删除的列表中
          return false
        }
        return true
      })
      _this.searchTree(_this.keyword)
    },
    /**
     * 刷新树
     * @param type 类型 update或者add
     * @param data 数据
     */
    refreshTree(type, data) {
      let _this = this
      if (type === 'add') {
        // 新增
        if (data.parentId) {
          // 有父级代表的是总台子页面节点
          this.ztSubInfo = data.bindData
        } else {
          // 没有父级代表的是总台节点
          this.ztInfo = data.bindData
        }
        _this.curNodeId = data.id
        _this.originTreeData.push(data)
        _this.searchTree(_this.keyword)
      } else {
        // 更新
        _this.originTreeData = _this.originTreeData.map(item => {
          if (data.id == item.id) {
            return data
          } else {
            return item
          }
        })
        _this.searchTree(_this.keyword)
      }
    },
    searchTree(keyword) {
      this.treeData = this.createTree(this.originTreeData, '', '1')
      if (keyword) {
        for (let i = 0; i < this.treeData.length; i++) {
          let child = this.treeData[i]
          let childResult = this.recursiveTree(child, keyword)
          if (!childResult) {
            this.treeData.splice(i, 1)
            i--
          }
        }
      }
    },
    recursiveTree(treeNode, keyword) {
      let result = 0
      if (treeNode.children && treeNode.children.length > 0) {
        for (let i = 0; i < treeNode.children.length; i++) {
          let child = treeNode.children[i]
          let childResult = this.recursiveTree(child, keyword)
          result |= childResult
          if (!childResult) {
            treeNode.children.splice(i, 1)
            i--
          }
        }
      }
      if (treeNode.title.indexOf(keyword) > -1) {
        result = 1
      }
      return result
    },
    savePermissions(data,type) {
      let ids = ""
      if(type === "01") {
        ids = data.selectedPublicIds.concat(",", data.selectedRoleIds)
      } else if(type === "02") {
        ids = data.join(",")
      } else if(type === "03") {
        ids = data.map(x => x.orgId).join(",")
      }
      let params = {
        id: this.yyid,
        mark:this.mark,
        type:type,
        permissionIds:ids
      }
      this.postRequest({ url: this.$path.acp_dockConfiguration_permission_save, params }).then(resp => {
          if (resp.success) {
            this.$Notice.success({
              title: '成功提示',
              desc: resp.msg
            })
          } else {
            this.$Notice.error({
              title: '错误提示',
              desc: resp.msg
            })
          }
      })
    },
    getPermissions() {
      this.authGetRequest({ url: this.$path.acp_dockConfiguration_findById, params: {id: this.yyid}}).then(resp => {
          if (resp.success) {
            this.roleIds = resp.data.roleIdList
            this.areaIds = resp.data.areaIdList
            this.orgIds = resp.data.orgIdList
          } else {
            this.$Notice.error({
              title: '错误提示',
              desc: resp.msg
            })
          }
        })
    },
    initAreaSelect() {
      let areaAssignCompnent = this.$refs['areaAssign']
      areaAssignCompnent.$refs['tree'].setCheckedKeys(this.areaIds)
      areaAssignCompnent.checkChange()
    },
    initRoleSelect() {
      let roleAssignCompnent = this.$refs['roleAssign']
      roleAssignCompnent.selectRoleByIds(this.roleIds)
    },
    initOrgSelect() {
      let orgComponent = this.$refs['orgSelect']
      orgComponent.$refs['orgTree'].setCheckedKeys(this.orgIds)
      orgComponent.checkChange()
    },
  }
}
</script>

<style lang="less" scoped>
/deep/.bsp-base-form .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  font-weight: bold;
}
</style>
