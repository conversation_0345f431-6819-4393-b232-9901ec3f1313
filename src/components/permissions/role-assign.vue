<template>
  <div class="bsp-role-assign">
    <div class="flex-box" style="position: absolute;">
      <div class="left-box">
        <Tabs ref="childTab" :animated="false" v-model="paneName" @on-click="switchTabs" name="tab2">
          <TabPane v-for="(appItem, index) in appList" :label="appItem.name" :name="appItem.code" :key="index" tab="tab2">
            <div class="search-div">
              <Input search placeholder="搜索角色名称" v-model="searchTxt" @on-change="selectTreeNode" @on-search="selectTreeNode" style="margin-right: 10px; margin-bottom: 5px; width: 300px" />
            </div>
            <div class="roles-box bsp-scroll">
              <Collapse simple :value="openTabsKind">
                <Panel v-for="item in appRolesMap[appItem.code]" :name="item.catName" :key="item.id">
                  <span @click.stop.prevent="checkAllNode(item)" style="color: #333">
                    <Checkbox v-model="item.checkAll" :indeterminate="item.indeterminate" :key="item.id">{{ item.catName }}</Checkbox>
                  </span>
                  <div slot="content">
                    <ul class="ul-box">
                      <li class="li-box" v-for="(subItem, index) in item.roles" :key="index">
                        <Tooltip :content="subItem.name" placement="bottom">
                          <Checkbox v-model="subItem.checked" :key="subItem.id" @on-change="singleCheckNode(subItem, item, index)">{{ subItem.name }}</Checkbox>
                        </Tooltip>
                      </li>
                    </ul>
                  </div>
                </Panel>
              </Collapse>
            </div>
          </TabPane>
        </Tabs>
      </div>
      <div class="right-box">
        <div class="tit-box">
          已授权角色
          <div style="float: right; margin-right: 0px" title="删除全部">
            <img src="./clearorg.png" @click="clearAll()" style="cursor: pointer; margin-top: 8px" />
          </div>
        </div>
        <div>
          <div class="content-box bsp-scroll">
            <table class="role-table">
              <tbody v-for="(app, index) in appList" :key="index">
                <tr v-for="(item, i) in checkedRolesMap[app.code]" :title="app.name + '-' + item.name" :key="i">
                  <td class="role-left">
                    <Tooltip :content="item.name" placement="bottom">
                      <span>{{ app.name }}</span> - {{ item.name }}
                    </Tooltip>
                  </td>
                  <td class="role-del">
                    <div @click="removeRole(item, app)" class="img-box">&nbsp;</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="bsp-base-fotter" style="position: absolute;bottom: 27%;">
      <Button @click="goBack">返 回</Button>
      <Button size="large" type="primary" @click="comfired">保存</Button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'role-assign',
  props: {
    /**
     * 应用ID，无条件必传
     */
    appId: {
      type: String,
      default: ''
    },
    defSelectedIdArr: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      searchTxt: '',
      loadding: false,
      openTabsKind: [],
      appName: '',
      currentApp: {},
      paneName: '',
      appList: [],
      appMap: new Map(),
      appRolesMap: new Map(),
      originalAppRolesMap: new Map(),
      checkedRolesMap: new Map()
    }
  },
  mounted() {
    this.loadRoleTreeByApp()
  },
  methods: {
    focus() {
      this.$refs['input'].focus()
    },
    goBack(){
      this.$emit('goBack')
    },
    checkAllNode(item) {
      item.indeterminate = false
      item.checkAll = !item.checkAll
      let arr = []
      if (this.checkedRolesMap[this.currentApp.code]) {
        arr = this.checkedRolesMap[this.currentApp.code]
      }
      item.roles.forEach(e => {
        if (item.checkAll) {
          e.checked = true
          arr.push(e)
        } else {
          e.checked = false
          for (let x = 0; x < arr.length; x++) {
            if (arr[x].id === e.id) {
              arr.splice(x, 1)
            }
          }
        }
      })
      this.checkedRolesMap[this.currentApp.code] = arr
      this.$forceUpdate()
    },
    warnMsg(msg) {
      this.warningModal({ content: msg })
    },
    removeRole(item, app) {
      let arr = this.checkedRolesMap[app.code]
      for (let x = 0; x < arr.length; x++) {
        if (item.id === arr[x].id) {
          arr.splice(x, 1)
        }
      }
      this.checkedRolesMap[app.code] = arr
      this.appRolesMap[app.code].forEach(kind => {
        let flag = false
        let checkflag = true
        let inflag = false
        kind.roles.forEach(e => {
          if (item.id === e.id) {
            flag = true
            e.checked = false
          }
          if (e.checked) {
            inflag = true
          } else {
            checkflag = false
          }
        })
        if (flag) {
          kind.checkAll = false
          kind.indeterminate = false
          if (!checkflag && inflag) {
            kind.indeterminate = true
          }
          this.$forceUpdate()
          return
        }
      })
    },
    loadRoleTreeByApp() {
      this.$store
        .dispatch('postRequest', {
          url: this.$path.uac_get_app_by_id,
          params: { appId: this.appId }
        })
        .then(resp => {
          if (resp.success) {
            this.appList.push(resp.data)
          }
          let publicApp = {
            id: '',
            name: '通用角色',
            code: 'public'
          }
          this.appList.push(publicApp)
          this.appList.forEach(e => {
            this.appMap[e.code] = e
          })
          this.appName = this.appList[0].name
          this.paneName = this.appList[0].code
          this.currentApp = this.appList[0]
          this.dealAppListRoles()
        })
    },
    dealAppListRoles() {
      if (this.appList.length === 0) {
        return
      }
      let idArr = []
      this.appList.forEach(e => {
        if (e.code !== 'public') {
          idArr.push(e.id)
        }
      })
      this.getRolesByAppIds(idArr.join(','))
    },
    getRolesByAppIds(ids) {
      let _this = this
      this.$store
        .dispatch('postRequest', {
          url: this.$path.get_all_app_roles_by_appIds,
          params: { appIds: ids }
        })
        .then(resp => {
          this.loadding = false
          if (resp.success) {
            _this.groupRolesByAppId(resp.rows)
          } else {
            this.errorModal({ content: resp.msg })
          }
        })
    },
    groupRolesByAppId(arr) {
      let map = new Map()
      arr.forEach(e => {
        if (e.isPublic === '1') {
          if (!map['public']) {
            let tempArr = []
            tempArr.push(e)
            map['public'] = tempArr
          } else {
            map['public'].push(e)
          }
        } else {
          if (!map[e.appId]) {
            let tempArr = []
            tempArr.push(e)
            map[e.appId] = tempArr
          } else {
            map[e.appId].push(e)
          }
        }
      })
      let appId = ''
      let flag = false
      this.appList.forEach(app => {
        if (app.code === 'public') {
          appId = app.code
          flag = true
        } else {
          appId = app.id
          flag = false
        }
        if (map[appId]) {
          let tree = this.buildTree(map[appId], flag, app)
          this.appRolesMap[app.code] = tree
          this.originalAppRolesMap[app.code] = tree
        }
      })
      this.$forceUpdate()
      if (this.defSelectedIdArr && this.defSelectedIdArr.length > 0) {
        // 如果组件属性中有传默认选中的角色id
        this.selectRoleByIds(this.defSelectedIdArr)
      } else {
        // 如果没有传，则通过父组件处理默认选中角色
        if (this.$listeners['init-callback']) {
          this.$nextTick(() => this.$emit('init-callback'))
        }
      }
    },
    buildTree(arr, isPublic, app) {
      // 转换结果
      let result = []
      // Map用于分组取值
      let devideMap = {}
      // 一个分类的角色数组
      let oneGroupRoleList = []
      arr.forEach((item, index) => {
        item.catId = item.catId ? item.catId : 'others'
        item.catName = item.catName ? item.catName : '其他'
        oneGroupRoleList = devideMap[item.catId]
        if (oneGroupRoleList == undefined || oneGroupRoleList == null) {
          oneGroupRoleList = []
          devideMap[item.catId] = oneGroupRoleList

          var obj = {}
          obj.catId = item.catId
          obj.catName = item.catName
          obj.roles = oneGroupRoleList
          result.push(obj)
        }
        oneGroupRoleList.push(item)
      })
      // 添加 多选字段
      result.forEach(res => {
        this.openTabsKind.push(res.catName)
      })
      return result
    },
    singleCheckNode(subItem, item, index) {
      if (subItem.checked) {
        if (!this.checkedRolesMap[this.currentApp.code]) {
          let arr = []
          arr.push(subItem)
          this.checkedRolesMap[this.currentApp.code] = arr
        } else {
          this.checkedRolesMap[this.currentApp.code].push(subItem)
        }
      } else {
        let arr = this.checkedRolesMap[this.currentApp.code]
        for (let x = 0; x < arr.length; x++) {
          if (subItem.id === arr[x].id) {
            arr.splice(x, 1)
            this.checkedRolesMap[this.currentApp.code] = arr
          }
        }
      }
      let inflag = false
      let flag = true
      item.roles.forEach(e => {
        if (e.checked) {
          inflag = true
        } else {
          flag = false
        }
      })
      item.checkAll = flag
      item.indeterminate = false
      if (!flag && inflag) {
        item.indeterminate = true
      }
      this.$forceUpdate()
    },
    comfired() {
      this.loadding = true
      let selectedRoleIds = []
      let selectedPublicIds = []
      let roles = []
      let publicRoles = []
      this.appList.forEach(app => {
        if (this.checkedRolesMap[app.code] && this.checkedRolesMap[app.code].length > 0) {
          this.checkedRolesMap[app.code].forEach(role => {
            if (app.code === 'public') {
              selectedPublicIds.push(role.id)
              publicRoles.push(role)
            } else {
              selectedRoleIds.push(role.id)
              roles.push(role)
            }
          })
        }
      })
      let data = {
        selectedRoleIds: selectedRoleIds.join(','),
        selectedPublicIds: selectedPublicIds.join(',')
      }
      this.$emit('choice_confirm', data, "01")
    },
    selectTreeNode() {
      let arr = this.originalAppRolesMap[this.currentApp.code]
      let searchArr = []
      if (arr && arr.length > 0) {
        arr.forEach(kind => {
          let searchRoles = []
          if (kind.roles && kind.roles.length > 0) {
            kind.roles.forEach(role => {
              if (this.searchTxt !== '' && role.name.indexOf(this.searchTxt) > -1) {
                searchRoles.push(role)
              }
            })
          }
          if (this.searchTxt !== '' && searchRoles.length > 0) {
            let newKind = Object.assign({}, kind)
            newKind.roles = searchRoles
            searchArr.push(newKind)
          }
        })
      }
      if (this.searchTxt !== '') {
        this.appRolesMap[this.currentApp.code] = searchArr
      } else {
        this.appRolesMap[this.currentApp.code] = arr
      }
    },
    switchTabs(name) {
      this.currentApp = this.appMap[name]
      this.appName = this.currentApp.name
      this.appList.forEach(e => {
        if (e.code === name) {
          this.currentApp = e
        }
      })
      this.selectTreeNode()
    },
    clearAll() {
      this.checkedRolesMap = new Map()
      this.appList.forEach(e => {
        if (this.appRolesMap[e.code] && this.appRolesMap[e.code].length > 0) {
          this.appRolesMap[e.code].forEach(kind => {
            kind.checkAll = false
            kind.indeterminate = false
            if (kind.roles && kind.roles.length > 0) {
              kind.roles.forEach(role => {
                role.checked = false
              })
            }
          })
          this.originalAppRolesMap[e.code].forEach(kind => {
            kind.checkAll = false
            kind.indeterminate = false
            if (kind.roles && kind.roles.length > 0) {
              kind.roles.forEach(role => {
                role.checked = false
              })
            }
          })
        }
      })
      this.$forceUpdate()
    },
    selectRoleByIds(roleIdList) {
      let _checkedRolesMap = new Map()
      this.appList.forEach(app => {
        let currAppCheckedRoleArr
        if (_checkedRolesMap[app.code]) {
          currAppCheckedRoleArr = _checkedRolesMap[app.code]
        } else {
          currAppCheckedRoleArr = []
          _checkedRolesMap[app.code] = currAppCheckedRoleArr
        }
        var roleCatArr = this.originalAppRolesMap[app.code]
        if (roleCatArr && roleCatArr.length > 0) {
          roleCatArr.forEach(roleCat => {
            roleCat.indeterminate = false
            let checkAllFlag = true // 当前分类下的角色是否都选中
            let hasChecked = false // 当前分类下的角色是否有选中的
            var curCatRoles = roleCat.roles // 当前分类下的角色
            if (curCatRoles && curCatRoles.length > 0) {
              curCatRoles.forEach(currRole => {
                if (roleIdList.indexOf(currRole.id) > -1) {
                  currRole.checked = true
                  hasChecked = true
                  currAppCheckedRoleArr.push(currRole)
                } else {
                  currRole.checked = false
                  checkAllFlag = false
                }
              })
              if (!checkAllFlag && hasChecked) {
                // 当前分类下的角色有选中但不是全选
                roleCat.indeterminate = true
              }
            } else {
              checkAllFlag = false
            }
            roleCat.checkAll = checkAllFlag
          })
        }
      })
      this.appRolesMap = Object.assign({}, this.originalAppRolesMap)
      this.checkedRolesMap = _checkedRolesMap
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="less" scoped>
.bsp-role-assign {
  width: 100%;
  height: 100%;
  /* padding: 10px; */
  overflow: hidden;
}
.bsp-role-assign .flex-box {
  background-color: #fff;
  width: 100%;
  height: calc(~'100% - 110px');
  display: flex; /*设为伸缩容器*/
  flex-flow: row; /*伸缩项目单行排列*/
}
.bsp-role-assign .flex-box .left-box {
  flex: 1; /*这里设置为占比1，填充满剩余空间*/
  margin: 0px;
  border: 1px solid #cee0f0;
}
.bsp-role-assign .search-div {
  padding: 9px;
  text-align: right;
  background: #f5faff;
  margin-top: -5px;
}
.bsp-role-assign .flex-box .right-box {
  width: 350px; /*固定宽度*/
  border: 1px solid #cee0f0;
  margin: 0 0 0 10px;
}
.bsp-role-assign .ul-box {
  margin-left: 22px;
  overflow: hidden;
}

.bsp-role-assign .ul-box .li-box {
  float: left;
  padding-right: 10px;
  list-style-type: none;
  width: 25%;
  line-height: 30px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/deep/ .bsp-role-assign {
  font-size: 16px !important;
}

.bsp-role-assign /deep/ .ivu-icon-ios-arrow-forward {
  float: right;
  line-height: 38px;
  font-size: 18px;
  /* transform: rotate(90deg); */
}

.bsp-role-assign /deep/ .ivu-collapse,
.bsp-role-assign /deep/ .ivu-collapse > .ivu-collapse-item {
  border-width: 0px;
}
.bsp-role-assign /deep/ .ivu-collapse-content > .ivu-collapse-content-box {
  padding-bottom: 0px;
}
.bsp-role-assign /deep/ .ivu-collapse > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header > i {
  transform: rotate(-90deg);
}

.bsp-role-assign /deep/ .ivu-input {
  height: 30px;
  border-color: #cee0f0;
  font-size: 15px !important;
}
.bsp-role-assign /deep/ .ivu-input:focus {
  border-color: #337bf5;
  box-shadow: inset 0 0 0 1000px #ffffff !important;
}
/* .bsp-role-assign /deep/ .ivu-tabs-bar {margin-bottom: 5px; border-bottom-color: #CEE0F0;}
.bsp-role-assign /deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab{border: none;background: none;height: 32px;line-height: 32px;font-size: 16px; padding: 0; margin: 0 32px 0 0;color: #333333;}
.bsp-role-assign /deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{position: relative;padding: 0;line-height: 32px;color: #2B5FD9;font-weight: bold;}
.bsp-role-assign /deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active::after{
    content: " ";position: absolute;width: 76px;height: 4px;background: #2B5FD9;border-radius: 4px;top: 100%;left: 50%;margin-left: -38px; margin-top: 0px;
}
.bsp-role-assign /deep/ .ivu-tabs-nav-scroll,.bsp-role-assign /deep/ .ivu-tabs-nav-wrap,.bsp-role-assign /deep/ .ivu-tabs-nav-container{overflow: initial;}
.bsp-role-assign /deep/ .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-nav-container{height: 38px;line-height: 38px;padding: 0 22px;position: unset;} */

.bsp-role-assign /deep/ .ivu-checkbox-wrapper {
  display: unset;
}

.bsp-role-assign .flow-modal-title {
  height: 40px;
  background: #2b5fda;
  width: 100%;
  text-indent: 1em;
  color: #fff;
  line-height: 40px;
}

.bsp-role-assign .img-box {
  cursor: pointer;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAABEUlEQVQ4T2NkwAL6156RZPj1bwMDw38zVGnGUwxsTAGFwSbP0bUxIgs0NPxn4lc/9YWBgYETmwVIYt8/3jTjaWhg/AcTgxvUv/iMJAPL32cEDECV/sMsVRgLcR3YIKhL/pJkCFTxx5tmzCCXgQ3qW3byCyMjAzc5Bv3/z/C1KMqch7Fz+REpNgbWp8iGuJsrMajLCzNMWnUaxey8MFOGGw/fMuw6eQ/Di4z9y0+eYmBgMEWWAWlgYmJk+PfvP9wwbGJIek6DDPqPzUvIGkHy6AZjRD8ug0AKYYaB2Miuw2YxThdRzSByvEadwMYW/W7mSgwapEY/1RIkVbMIyDCqZFpYuqBKMYKcyCARwLaelIINAPXCscs343ggAAAAAElFTkSuQmCC')
    no-repeat center center;
}

.bsp-role-assign .job-del {
  padding: 0 0 0 12px;
}
.bsp-role-assign .roles-box {
  height: 450px;
  margin-top: -5px;
  padding: 5px 0;
}
.bsp-role-assign .content-box {
  height: 500px;
  padding: 5px 0px;
  width: 100%;
}

.bsp-role-assign .role-table {
  border: none;
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
}
.bsp-role-assign .role-table tr:hover {
  background: #f0f5ff;
}
.bsp-role-assign .role-table td {
  border: none;
}
.bsp-role-assign .role-table .role-left {
  width: 88%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  height: 36px;
  font-size: 16px;
  padding-right: 10px;
  padding-left: 15px;
}
.bsp-role-assign .role-table .role-left > span {
  color: #2b5fd9;
}
.bsp-role-assign .role-del {
  width: 12%;
  padding-right: 15px;
  text-align: right;
}

.bsp-role-assign .bsp-scroll {
  overflow: overlay;
}

/deep/.ivu-tabs-nav .ivu-tabs-tab {
  font-size: 16px;
}

/deep/ .bsp-role-assign .ivu-modal-content .ivu-modal-body,
/deep/ .bsp-role-assign .ivu-modal-header {
  padding: 0px;
}

/deep/ .bsp-role-assign .ivu-modal-footer {
  border-top: 1px solid #cee0f0;
  padding: 9px 18px 10px 18px;
  background: #f7faff;
}
/**重置对话框 */

.bsp-role-assign .main-button {
  background: #2b5fd9;
  margin-left: 20px;
}

.bsp-role-assign .cancel-btn {
  opacity: 1;
  font-size: 16px;

  border-radius: 2px;
  padding: 0;
  height: 30px;
  width: 70px;
}

.bsp-role-assign .tit-box {
  background: #f2f6fc;
  line-height: 37px;
  padding: 0 15px;
  font-weight: bold;
  border-bottom: 1px solid #cee0f0;
}

.bsp-scroll::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 6px;
}
.bsp-scroll::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 3px;
  background: #b7c7dd;
}
.bsp-scroll::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 3px;
  background: #ededed;
}

/deep/.ivu-collapse-header .ivu-icon-ios-arrow-forward {
  transform: rotate(90deg);
}

.footer-btn {
  width: 100%;
  background-color: #fff;
  line-height: 50px;
  text-align: center;
  margin-top: 10px;
}
.footer-btn .ivu-btn-primary {
  font-size: 16px;
  min-width: 80px;
  height: 36px;
  background: #2b5fd9;
  border-radius: 2px;
}
</style>
