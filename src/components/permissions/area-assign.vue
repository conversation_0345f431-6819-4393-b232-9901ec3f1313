<template>
  <div class="area-assign-container">
    <div class="content-container">
      <div class="content-container-noselect bsp-scroll">
        <div class="content-search-box">
          <Input v-model="searchTxt" size="large" suffix="ios-search" @on-change="searchField" placeholder="区域查询" />
        </div>
        <el-tree
          :data="treeData"
          ref="tree"
          node-key="id"
          :expand-on-click-node="false"
          :show-checkbox="true"
          :check-strictly="true"
          :highlight-current="true"
          :accordion="true"
          :check-on-click-node="true"
          :filter-node-method="filterNode"
          :indent="26"
          @check="checkChange"
          :render-content="renderContent"
          icon-class="el-icon-arrow-right"
        ></el-tree>
      </div>
      <div class="content-container-select bsp-scroll">
        <span>已授权区域</span>
        <div style="float:right;margin-right:2px;" title="删除全部">
                            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAA10lEQVQ4T2P8//8/MwMVACMugz4aGe1k+P/fGdkORgaGI3znzztgsxfDoI+Ghn+IcSD/+fMsKJbAXESsAeiWwAyEu+ijoeFuBgYGR2Jcg6RmP//5864gPsJrjY3M7w4cECPFICEHh1cM9fV/UQyinteMjSsY/v1rIcVFDExMNfxnz3ageo2BgQHkqv///z9mYGLawPD/fzAjA4PUf0bGyVCFuehyyDGHEv1g7zEy7uU/d879k6Hhgf8MDDYwxfjksLpo1CCU8KNxGJGQkPBHPzUMIsEMDKUAY+HyE69FM1YAAAAASUVORK5CYII=" @click="deleteAll" style="cursor: pointer;"/>
        </div>
        <ul v-for="(item, index) in checkAreaList" :key="index">
          <li>
            <div>{{ item.title }}</div>
            <div>
              <img
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAABEUlEQVQ4T2NkwAL6156RZPj1bwMDw38zVGnGUwxsTAGFwSbP0bUxIgs0NPxn4lc/9YWBgYETmwVIYt8/3jTjaWhg/AcTgxvUv/iMJAPL32cEDECV/sMsVRgLcR3YIKhL/pJkCFTxx5tmzCCXgQ3qW3byCyMjAzc5Bv3/z/C1KMqch7Fz+REpNgbWp8iGuJsrMajLCzNMWnUaxey8MFOGGw/fMuw6eQ/Di4z9y0+eYmBgMEWWAWlgYmJk+PfvP9wwbGJIek6DDPqPzUvIGkHy6AZjRD8ug0AKYYaB2Miuw2YxThdRzSByvEadwMYW/W7mSgwapEY/1RIkVbMIyDCqZFpYuqBKMYKcyCARwLaelIINAPXCscs343ggAAAAAElFTkSuQmCC"
                @click="deleteSelected(item, index)"
                class="img-box"
              />
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="bsp-base-fotter" style="position: absolute;bottom: 27%;">
       <Button @click="goBack">返 回</Button>
      <Button size="large" @click="comfired" type="primary">保存</Button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      treeData: [],
      checkedAreaId: [],
      checkAreaList: [],
      curAreaId: '',
      searchTxt: ''
    }
  },
  mounted() {
    this.$store.dispatch('postRequest', { url: '/bsp-uac/uac/area/getAllAreas' }).then(resp => {
      if (resp.success) {
        var treeRootData = this.transData(
          resp.data.map(el => {
            var node = {}
            node.data = el
            node.title = el.name
            node.id = el.id
            node.parentId = el.parentId
            return node
          })
        )
        this.treeData = treeRootData
        this.$nextTick(() => {
          this.$emit('init-callback')
        });
      }
    })
  },
  methods: {
    transData(datas) {
      var r = [],
        hash = {},
        id = 'id',
        pid = 'parentId',
        children = 'children',
        i = 0,
        j = 0,
        len = datas.length
      for (; i < len; i++) {
        hash[datas[i][id]] = datas[i]
      }
      for (; j < len; j++) {
        var aVal = datas[j],
          hashVP = hash[aVal[pid]]
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = [])
          hashVP[children].push(aVal)
        } else {
          r.push(aVal)
        }
      }
      return r
    },
    // 查询
    searchField() {
      var searchTxt = this.searchTxt
      this.$refs.tree.filter(searchTxt)
      if (!searchTxt) {
        this.setAllExpand(this.treeData[0].id, false)
      }
    },
    setAllExpand(id, state) {
      var nodes = this.$refs.tree.store.nodesMap
      for (var i in nodes) {
        nodes[i].expanded = true
      }
    },
    // 渲染内容
    renderContent(h, { node, data, store }) {
      node.expanded = true
      let hArr = []
      hArr.push(
        h(
          'span',
          {
            style: {
              fontSize: '16px'
            },
            attrs: {
              title: data.title
            }
          },
          data.title
        )
      )
      return h('span', hArr)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.title.indexOf(value) !== -1 || data.id.indexOf(value) !== -1
    },
    // 删除选中
    deleteSelected(item, index) {
      this.checkAreaList.splice(index, 1)
      this.$refs.tree.setCheckedNodes(this.checkAreaList)
    },
    // 删除全部
    deleteAll() {
      this.checkAreaList = []
      this.$refs.tree.setCheckedNodes(this.checkAreaList)
    },
    // 选中树节点
    checkChange() {
      this.checkAreaList = this.$refs.tree.getCheckedNodes()
    },
    comfired() {
      var confirmedAreaIdList = this.checkAreaList.map(e => e.id)
      this.$emit('on-select', confirmedAreaIdList, "02")
    },
       goBack(){
                  this.$emit('goBack')
                },
  }
}
</script>

<style lang="less" scoped>
.area-assign-container {
  width: 100%;
  height: 100%;
  position: relative;
  .content-container {
    display: flex;
    justify-content: space-between;
    align-content: center;
    flex-direction: row;
    width: 100%;
    height: calc(~'100% - 200px');
    background: #fff;
    margin-bottom: 10px;
    padding: 10px;
    box-sizing: border-box;
    &-select {
      width: 100%;
      height: 100%;
      overflow-y: scroll;
      padding: 10px;
      border: 1px solid #cee0f0;
      width: 450px;
      margin-left: 20px;
      span {
        font-size: 16px;
        font-weight: bold;
      }
      ul {
        li {
          display: flex;
          justify-content: space-between;
          align-content: center;
        }
      }
    }
    &-noselect {
      width: 100%;
      height: 100%;
      overflow-y: scroll;
      border: 1px solid #cee0f0;
      flex: 1;
      padding: 10px;
      .content-search-box {
        height: 50px;
        line-height: 50px;
        margin-bottom: 10px;
      }
    }
  }
  .footer-btn {
    position: absolute;
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    background: #fff;
    bottom: 50px;
    .ivu-btn-primary {
      font-size: 16px;
      min-width: 80px;
      height: 36px;
      background: #2b5fd9;
      border-radius: 2px;
    }
  }

  /deep/ .ivu-tree-title {
    font-size: 16px;
  }

  /deep/ .el-tree-node__content > .el-tree-node__expand-icon {
    font-size: 16px;
  }

  /deep/ .el-tree .el-checkbox__inner {
    height: 16px;
    width: 16px;
    border: 1px solid #cee0f0;
  }
}
</style>