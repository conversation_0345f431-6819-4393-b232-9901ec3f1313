<template>
	<div style="border: 1px solid #ccc">
		<Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
		<Editor style="min-height: 200px; overflow-y: hidden" v-model="html" :defaultConfig="editorConfig" :mode="mode"
			@onChange="onChange" @onCreated="onCreated" />
	</div>
</template>
<script>
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
export default {
	components: { Editor, Toolbar },
	props: {
		content: {
			type: String,
			default: "",
		},
	},
	watch: {
		content: {
			handler(newVal) {
				if (this.editor && newVal) {
					this.editor.setHtml(newVal); // 关键：回显数据到编辑器[1,6](@ref)
				}
			},
			immediate: true
		},
	},
	data() {
		return {
			editor: null,
			html: "",
			toolbarConfig: {
			},
			editorConfig: {
				placeholder: "请输入内容...",
				MENU_CONF: {
					//   配置上传图片
					uploadImage: {
						server: "",
						fieldName: "file",
						meta: {},
						// 请求头
						headers: { token: localStorage.getItem("token") },
						timeout: 5 * 1000, // 5s 超时时间
						//选择文件时的类型限制，默认为['image/*'] 如不想限制，则设置为 []
						allowedFileTypes: ["image/*"],
						maxFileSize: 30 * 1024 * 1024, //1g //设置大点 不然图片过大会报错
						base64LimitSize: 1000000 * 1024, // 1g 以下插入 base64
						// 自定义插入图片
						customInsert(res, insertFn) {
							// res 即服务端的返回结果
							// console.log(res, "res");
							// 从 res 中找到 url alt href ，然后插图图片
							insertFn(url, alt, href);
						},
					},
					//   配置上传视频
					uploadVideo: {
						server: "",
						fieldName: "file",
						meta: {},
						// 请求头
						headers: { token: localStorage.getItem("token") },
						timeout: 5 * 1000, // 5s 超时时间
						//选择文件时的类型限制，默认为['video/*'] 如不想限制，则设置为 []
						allowedFileTypes: ["video/*"],
						// 自定义插入视频
						customInsert(res, insertFn) {
							insertFn(url, alt, href);
						},
					},
					color: {
						colors: ['#000', '#333', '#666'],
					},
					bgColor: {
						colors: ['#000', '#333', '#666'],
					},
					fontSize: {
						fontSizeList: [
							// 元素支持两种形式
							//   1. 字符串；
							//   2. { name: 'xxx', value: 'xxx' }

							'12px',
							'16px',
							{ name: '24px', value: '24px' },
							'40px',
						],
					},
					fontFamily: {
						fontFamilyList: [
							// 元素支持两种形式
							//   1. 字符串；
							//   2. { name: 'xxx', value: 'xxx' }

							'黑体',
							'楷体',
							{ name: '仿宋', value: '仿宋' },
							'Arial',
							'Tahoma',
							'Verdana',
						],
					},
					lineHeight: {
						lineHeightList: ['1', '1.5', '2', '2.5'],
					},
					emotion: {
						emotions: '😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇 🙂 🙃 😉'.split(' '),
					},
					codeSelectLang: {
						codeLangs: [
							{ text: 'CSS', value: 'css' },
							{ text: 'HTML', value: 'html' },
							{ text: 'XML', value: 'xml' },
							// 其他
						],
					}
					// insertLink:{
					// 	checkLink: customCheckLinkFn, // 也支持 async 函数
					// 	parseLinkUrl: customParseLinkUrl, // 也支持 async 函数
					// }
				},
			},
			mode: "default", // 'default' or 'simple'
		};
	},
	methods: {
		onChange(editor) {
			this.$emit("changeData", this.html, editor);
		},
		clearContent() {
			this.editor.setHtml('');
        this.content = '';
        this.editor.txt.html(''); 
			// if (this.editor?.txt) { // ✅ 双重校验
				
			// }
		},
		onCreated(editor) {
			this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
			if (this.content) {
				this.editor.setHtml(this.content);
			}
			this.toolbarConfig = {
				toolbarKeys: [//自定义菜单选项
					// 菜单 key

					// // 菜单 key
					"bold",
					"italic",
					// // 菜单组，包含多个菜单
					{
						key: "group-more-style", // 必填，要以 group 开头
						title: "更多样式", // 必填
						iconSvg: "<svg>123</svg>", // 可选
						menuKeys: ["through", "code"], // 下级菜单 key ，必填
					},
					"underline",
					"color",
					"bgColor",
					"insertImage",
					"insertLink",
					"|",
					"fontSize",
					"fontFamily",
					"headerSelect",
					"justifyLeft",
					"justifyCenter",
					"justifyRight",
					"codeBlock",
					"blockquote",
					"divider",
					"undo",
					"redo",
					"clearStyle",
					"fullScreen"
				],
				excludeKeys: [
					// 'fullScreen',
					// 'bold', 'underline', 'italic', 'through', 'code', 'sub', 'sup', 'clearStyle', 'color', 'bgColor', 'fontSize', 'fontFamily',
					//  'indent', 'delIndent', 'justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify', 'lineHeight', 'insertImage', 'deleteImage',
					//  'editImage', 'viewImageLink', 'imageWidth30', 'imageWidth50', 'imageWidth100', 'divider', 'emotion', 'insertLink', 'editLink',
					// 'unLink', 'viewLink', 'codeBlock', 'blockquote', 'headerSelect', 'header1', 'header2', 'header3', 'header4', 'header5', 'todo',
					// 'redo', 'undo', 'fullScreen', 'enter', 'bulletedList', 'numberedList', 'insertTable', 'deleteTable', 'insertTableRow',
					// 'deleteTableRow', 'insertTableCol', 'deleteTableCol', 'tableHeader', 'tableFullWidth', 'insertVideo', 'uploadVideo', 'editVideoSize',
					//  'uploadImage', 'codeSelectLang','group-more-style
					"sub",
					"sup",
				],
			};
		},
		onCreatedToolbar(toolbar) {

		},
		customCheckLinkFn(text, url) {
			if (!url) {
				return
			}
			if (url.indexOf('http') !== 0) {
				return '链接必须以 http/https 开头'
			}
			return true
		},
		customParseLinkUrl(url) {
			if (url.indexOf('http') !== 0) {
				return `http://${url}`
			}
			return url
		}
	},
	created() {
		console.log(this.content, 'this.content');
		this.html = this.content;
	},
	mounted() { },
	beforeDestroy() {
		const editor = this.editor;
		if (editor == null) return;
		editor.destroy(); // 组件销毁时，及时销毁编辑器
	},
	watch: {
		content(val) {
			this.html = val;
		}
	},
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>