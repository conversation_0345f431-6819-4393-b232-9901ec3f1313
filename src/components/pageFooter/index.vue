<template>
	<div class="component_box">
		<!-- 标准分页器 -->
		<div class="footer_page_box" v-if="!showMini">
			<div class="left_page_box">
				<span v-show="showLeft">
					共选中
					<span class="strong_text">{{ selectNum }}</span>
					条数据
				</span>
			</div>
			<div class="right_page_box">
				<Page :current="curPage" :total="totalNumber" @on-change="getPageNumber" ref="pageBtn" />
				<div class="go_page_box">
					<span class="text">到第</span>
					<InputNumber :max="totalDataPage" :min="1" v-model="inputValue" />
					<span class="text">页</span>
					<div class="click_btn" @click="getPageNumber(inputValue, 'click')">确定</div>
				</div>
				<span class="text">共{{ totalNumber }}条</span>
				<div class="select_box">
					<Select v-model="pageSizeValue" style="width: 200px" @on-open-change="arrow = !arrow" @on-change="editPageSize" transfer>
						<Option v-for="item in pageSizeList" :value="item.value" :key="item.value">{{ item.name }}</Option>
					</Select>
				</div>
			</div>
		</div>

		<!-- 迷你分页器只有切换页码功能 -->
		<div class="mini_page_box" v-if="showMini">
			<div :class="['arrow_btn_style', this.currentPage <= 1 ? 'arrow_btn_style_stop' : '']" @click="miniChangePage(-1)">
				<i class="ivu-icon ivu-icon-ios-arrow-back"></i>
			</div>
			<span class="mini_text">
				<span class="current_page_color">{{ currentPage }}</span>
				<span class="page_text_color">{{ '/' + totalDataPage }}</span>
			</span>
			<div :class="['arrow_btn_style', this.currentPage >= this.totalDataPage ? 'arrow_btn_style_stop' : '']" @click="miniChangePage(1)">
				<i class="ivu-icon ivu-icon-ios-arrow-forward"></i>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'PageFooter',
	props: {
		showLeftSelect: {
			// 是否展示左侧的选中条数
			type: Boolean,
			default: false
		},
		selectListLength: {
			// 选中的条数
			type: Number,
			default: 0
		},
		totalRowNumber: {
			// 总共条数
			type: Number,
			default: 1
		},
		totalPage: {
			// 总页数
			type: Number,
			default: 0
		},
		limitList: {
			// 限制条数列表
			type: Array,
			default: () => {
				return []
			}
		},
		pageType: {
			// 分页器类型， 不传或传其他 = 标准 、 传mini = 迷你
			type: String,
			default: ''
		},
		curPage: {
			type: Number,
			default: 0
		}
	},
	watch: {
		showLeftSelect(newVal, oldVal) {
			this.showLeft = newVal
		},
		selectListLength(newVal, oldVal) {
			this.selectNum = newVal
		},
		totalRowNumber: {
			handler(newVal, oldVal) {
				this.totalNumber = newVal
				if (Number(newVal) >= 0) {
					this.totalDataPage = Math.ceil(newVal / this.pageSizeValue)
					this.totalDataPage == 0 ? (this.totalDataPage = 1) : ''
				}
			},
			immediate: true
		},
		pageType: {
			handler(newVal, oldVal) {
				if (newVal != '' && newVal == 'mini') {
					this.showMini = true
				}
			},
			immediate: true
		},
		limitList: {
			immediate: true,
			deep: true,
			handler(newVal, oldVal) {
				if (newVal.length != 0) {
					setTimeout(() => {
						this.$refs['pageBtn'].currentPageSize = newVal[0].value
					}, 1000)
					this.pageSizeValue = newVal[0].value
					this.pageSizeList = newVal
				}
			}
		}
	},
	data() {
		return {
			showLeft: false, // 展示左侧选中
			selectNum: 0, // 选中条数
			pageSizeList: [
				// 分页列表
				{
					name: '10条/页',
					value: 10
				},
				{
					name: '20条/页',
					value: 20
				},
				{
					name: '30条/页',
					value: 30
				}
			],
			pageSizeValue: 10,
			inputValue: 1,
			totalNumber: 0, // 总条数
			totalDataPage: 1, // 总页数
			arrow: true, // 三角旋转
			showMini: false, // 是否展示迷你分页器
			currentPage: 1 // mini按钮当前页
		}
	},
	methods: {
		// 获取页数
		getPageNumber(val, type) {
			this.inputValue = val || 1
			if (type == 'click') {
				this.$refs['pageBtn'].changePage(this.inputValue)
			}
			this.$emit('editPageNum', { pageSize: this.pageSizeValue, pageNo: this.inputValue })
		},
		// 修改单页展示条数
		editPageSize(val) {
			this.$refs['pageBtn'].currentPageSize = val
			this.$refs['pageBtn'].currentPage = 1
			this.inputValue = 1
			this.totalDataPage = Math.ceil(this.totalNumber / val)
			this.$emit('editPageSize', { pageSize: val, pageNo: 1 })
		},
		// mini分页器切换页面
		miniChangePage(value) {
			if (value > 0) {
				// 下一页
				if (this.currentPage < this.totalDataPage) {
					this.currentPage++
					this.$emit('editPageNum', { pageNo: this.currentPage })
				}
			} else {
				// 上一页
				if (this.currentPage > 1) {
					this.currentPage--
					this.$emit('editPageNum', { pageNo: this.currentPage })
				}
			}
		}
	}
}
</script>

<style lang="less" scoped>
.component_box {
	display: flex;
	width: 100%;
	align-items: center;
	flex-direction: row-reverse;
}

.footer_page_box {
	width: 100%;
	height: 0.26rem;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 0.2rem;
	.left_page_box {
		font-size: 0.14rem;
		span {
			color: #7a8699;
			font-family: MicrosoftYaHei;

			.strong_text {
				color: #087eff;
				font-weight: 700;
				margin: 0 0.05rem;
			}
		}
	}

	.right_page_box {
		display: flex;
		align-items: center;

		.text {
			margin: 0 0.05rem;
			color: #7a8699;
		}
		/deep/ .ivu-page {
			margin: 0;
			.ivu-page-item {
				height: 0.26rem;
				line-height: 0.26rem;
				min-width: 0.26rem;
				border: none;
				a {
					display: inline-block;
					height: 0.26rem;
					line-height: 0.26rem;
				}
			}
			.ivu-page-item-jump-next {
				height: 0.26rem;
				line-height: 0.26rem;
			}
			.ivu-page-prev,
			.ivu-page-next {
				height: 0.26rem;
				line-height: 0.26rem;
				min-width: 0.26rem;
				border: 1px solid #dae6f9;
				//  border: none;

				a {
					display: flex;
					width: 100%;
					height: 100%;
					align-items: center;
					justify-content: center;

					.ivu-icon {
						font-size: 0.2rem;
						// color: #8C9FBA;
					}
				}
			}
			.ivu-page-prev {
				margin-right: 0.14rem;
			}
			.ivu-page-next {
				margin-left: 0.1rem;
			}
			.ivu-page-item-active {
				border: 1px solid #087eff;
			}
		}
		.go_page_box {
			display: flex;
			align-items: center;
			margin: 0 0.05rem;

			.click_btn {
				cursor: pointer;
				width: 0.48rem;
				height: 0.26rem;
				line-height: 0.26rem;
				font-size: 0.14rem;
				text-align: center;
				border-radius: 0.04rem;
				border: 1px solid #cee0f0;
				color: #7a8699;
				font-family: MicrosoftYaHei;
				margin: 0 0.05rem;
			}

			/deep/ .ivu-input-number {
				width: 0.4rem;
				height: 0.26rem;
				line-height: 0.26rem;

				.ivu-input-number-input-wrap {
					height: 0.26rem;
					line-height: 0.26rem;
				}
				.ivu-input-number-input {
					height: 0.26rem;
					line-height: 0.26rem;
					text-align: center;
					color: #7a8699;
				}
			}
		}
		.select_box {
			position: relative;
			height: 0.26rem;
			display: flex;
			align-items: center;

			/deep/ .ivu-select {
				width: 0.8rem !important;
				margin-left: 0.05rem;

				.ivu-select-selection {
					height: 0.26rem;
				}
				.ivu-select-selected-value {
					height: 0.26rem;
					line-height: 0.26rem;
					font-size: 0.14rem;
					color: #7a8699;
					padding-right: 0.18rem;
				}
			}
		}
	}
}

.mini_page_box {
	display: flex;
	align-items: center;

	.arrow_btn_style {
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 0.26rem;
		height: 0.26rem;
		border: 1px solid #cee0f0;
		border-radius: 4px;

		i {
			color: #8c9fba;
		}

		&_stop {
			cursor: no-drop;
			border: 1px solid #dae6f9;

			i {
				color: #dae6f9;
			}
		}
	}

	.mini_text {
		margin: 0 0.07rem;
		span {
			font-size: 0.16rem;
		}
		.current_page_color {
			color: #087eff;
			font-weight: normal;
		}
		.page_text_color {
			color: #7a8699;
			font-weight: normal;
		}
	}
}
</style>
