<template>
    <div class="replace_icon_box">
        <div class="replace_icon">
            <img :src="iconAddress" :name="type" class="replace_img" alt="">
        </div>
        <div class="replace_icon_text">{{ text }}</div>
    </div>
</template>


<script>
    import iconObj from './icon.js';
    export default {
        name: 'ReplaceIcon',
        props: {
            text: {         //提示文字
                type: String,
                default: '暂无内容'
            },
            type: {         //icon类型
                type: String,
                default: 'no_content'
            },
            size: {         //大小
                type: String,
                default: 'middle'    //  large   middle   small
            }
        },
        computed: {
            iconAddress() {     //切换icon类型
                let address = iconObj[this.type] ? iconObj[this.type] : '';
                if(address) {
                    return require('@/assets/images/default_bg/' + this.type + '.png');
                } else {
                    return require('@/assets/images/default_bg/no_content.png');
                }
            }
        },
        mounted() {
            setTimeout(() => {   //等待图片切换
                this.changeSize()
            }, 100)
        },
        methods: {
            //更改大小
            changeSize() {
                this.$nextTick(() => {
                    let domList = document.getElementsByClassName('replace_img');
                    let chooseDomList = [];
                    for(let i=0; i<domList.length; i++) {
                        if(domList[i].getAttribute('name') == this.type) {
                            chooseDomList.push(domList[i]);     
                        }
                    }
                    chooseDomList.forEach(item => {
                        let dom = item;
                        if(this.size == 'larger') {    //大图标
                            let width1 = (Number(dom.clientWidth) + Number(dom.clientWidth) * 0.3) + 'px';        
                            let height1 = (Number(dom.clientHeight) + Number(dom.clientHeight) * 0.3) + 'px'; 
                            dom.style.width = width1;
                            dom.style.height = height1;
                        } else if(this.size == 'small') {   //小图标
                            let width2 = (Number(dom.clientWidth) - Number(dom.clientWidth) * 0.3) + 'px';        
                            let height2 = (Number(dom.clientHeight) - Number(dom.clientHeight) * 0.3) + 'px'; 
                            dom.style.width = width2;
                            dom.style.height = height2;
                        }
                    })
                })
            }
        }
    }
</script>

<style lang="less" scoped>
    .replace_icon_box {

        width: 100%;
        padding-top: 10px;


        .replace_icon {
            display: flex;
            justify-content: center;
        }


        .replace_icon_text {
            width: 100%;
            margin-top: 10px;
            text-align: center;
            font-size: 20px;
            color: #64759A;
            font-family: Source Han Sans CN;
        }

    }

</style>