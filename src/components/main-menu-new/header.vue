<template>
  <div>
    <div class="my-header">
      <div class="log" style="z-index:99999;margin-right: 80px;min-width: 320px;cursor: pointer;" @click="$router.push('/homePage')" > 
        <img src="@/assets/images/u5.png" alt="" style="width:70px;"/>
        <h1 >{{$store.state.common.orgName}}{{ appName }}</h1>
      </div>
     

      <div class="nav" v-if="showMenu">
        <ul v-show="listTitle.length > 0" style="margin-left: 48%;">
          <li v-for="(item,index) in listTitle" :key="item.name"  v-if="index<7"
              :class="{ active: $store.state.common.activeRootRouterName === item.name }" class="nav_box"
              @click="handleClick(item)">
            <span>{{ item.meta.title }}</span>
          </li>
        </ul>
      </div>
        <img v-if="$route.path !='/representative'" @click="$router.push('/representative')" src="@/assets/images/message.svg" style="cursor: pointer;width:30px;position:absolute;right:10%;top:14px;z-index: 999;"  />
        <span v-if="$route.path !='/representative'" @click="$router.push('/representative')" style="cursor: pointer;width:30px;line-height: 25px;font-size: 16px;position:absolute;right:9%;top:10px;background: #e60012;color:#fff;text-align: center;border-radius: 50%;z-index: 998;" >{{ total }}</span>
      <user :user-avatar="userAvatar" @on-close="handleCloseTag" :list="tagNavList"/>
    </div>
  </div>
</template>

<script type="text/javascript">
import {getMenuByRouter, getPermMenu} from '@/libs/util'
import User from "./components/user"

export default {
  name: 'Header',
  components: {User},
  props: {
    msg: {
      type: String,
      default: 'test msg'
    },
    showMenu: {
      type: Boolean,
      default: true
    },
  },
  methods: {
    getTableData(){
          this.$store.dispatch('authPostRequest', { url: this.$path.app_getDbMsgData, params:{isRead:0,} }).then(res => {
            if (res.success) {
                this.total=res.data.todo.total
            } else {
              this.$Notice.error({
                title: '失败提示',
                desc: res.msg  || '获取失败'
              })
            }
      })
        },
    handleCloseTag(res, type, route) {
      // if (type !== "others") {
      //   if (type === "all") {
      //     if (this.$route.path !== res[0].path) {
      //       this.turnToPage(this.$config.homeName);
      //     }
      //   } else {
      //     if (this.$route.path.indexOf("/help/component") > -1) {
      //       this.closeTag(route)
      //     } else if (routeEqual(this.$route, route)) {
      //       this.closeTag(route)
      //     }
      //   }
      // }
      // this.setTagNavList(res)
    },
    handleClick: function (item) {
      console.log(item,'handleClick',item.children[0].path)
      if (this.$store.state.common.activeRootRouterName === item.name) {
        item.children && item.children.length>0?this.$router.push(item.children[0].path):''
        return
      }
      this.activeName = item.name
      this.$store.commit('setActiveRootRouterName', item.name)
      if (item.path && item.path.startsWith('http')) {
        this.iframeState.showIframe = true
        this.iframeState.iframeSrc = item.path
      } else {
        this.iframeState.showIframe = false
        if (item.children.length === 1 && !item.children[0].children) {
          this.iframeState.showMenu = false
          // if (item.name === "acp:mdhjzmk") {
          // // this.$parent.$parent.$parent.turnToPage('/com/engineCenter')
          // this.$router.push(item.children[0].path)
          // } else {
          //   this.$parent.$parent.$parent.turnToPage(this.getRouterNameByTrue(item.children[0]))
          // }
        } else {
          this.iframeState.showMenu = true
        }
        this.commitIframeState()
        if (item.name === "acp:mdhjzmk") {
          this.$parent.$parent.$parent.turnToPage('/com/engineCenter')
        } else {
          this.$parent.$parent.$parent.turnToPage(this.getRouterNameByTrue(item.children[0]))
        }
        // if(item && item.name=='bsp:com'){
        //   this.$router.push('/com/engine')
        // }else{
        // this.$parent.$parent.$parent.turnToPage(this.getRouterNameByTrue(item.children[0]))

        // }
      }
    },
    goToAppConfig() {
      this.handleClick(this.appConfigRoute)
    },
    // 递归获取有实质组件的第一个路由名称
    getRouterNameByTrue(item) {
      const getRouterName = item => {
        if (item.children && item.children.length) {
          return getRouterName(item.children[0])
        } else {
          return item.path
        }
      }
      return getRouterName(item)
    },
    commitIframeState() {
      this.$emit('getIframeState', this.iframeState)
    }
  },
  data() {
    return {
      total:0,
      appName: serverConfig.APP_NAME,
      isActive: null,
      activeName: this.$store.state.common.activeRootRouterName,
      iframeState: {
        showIframe: false,
        showMenu: true,
        iframeSrc: ''
      }
    }
  },
  mounted(){
    this.getTableData()
  },
  computed: {
    listTitle() {
      // //console.log(getMenuByRouter(getPermMenu(), []).filter(item => item.name !== 'bsp:uac'),'getMenuByRouter(getPermMenu(), []).filter(item => item.name !== )')
      return getMenuByRouter(getPermMenu(), [])
    },
    appConfigRoute() {
      // //console.log(getMenuByRouter(getPermMenu(), []).find(item => item.name === 'bsp:uac'),'getMenuByRouter(getPermMenu(), []).find(item => item.name === 'bsp:uac')')
      return getMenuByRouter(getPermMenu(), []).find(item => item.name === 'bsp:uac')
    },
    userAvatar() {
      return this.$store.state.common.avatarImgPath;
    },
    tagNavList() {
      // console.log(this.$store.state.app.tagNavList,'this.$store.state.app.tagNavList122222222')
      return this.$store.state.app.tagNavList;
    },
  }
}
</script>
<style  scoped>
.my-header {
  height: 60px;
  background:#2b5fda;
  line-height: 60px;
  font-size: 20px;
  color: #fff;
  padding-left: 8px;
  display: flex;
  position: relative;
}

.log {
  font-size: 30px;
  font-weight: 700;
  display: flex;
  align-items: center;
}

.log img {
  display: inline-block;
  /* width: 40px; */
  vertical-align: middle;
}

.log h1 {
  display: inline-block;
  margin: 0 0 0 0px;
  color: #fff;
  font-weight: 600;
  font-size: 30px;
  line-height: 30px;
  vertical-align: middle;
}

.nav {
  display: flex;
  white-space: nowrap;
  /* margin-left: 480px; */
  height: 100%;
  transform: translateX(-50%);
}

.nav > div {
  cursor: pointer;
}

.title_nav {
  background: #1c324e;
  text-align: right;
  color: #fff;
  padding: 5px 0;
  display: flex;
  justify-content: flex-end;
}

.title_nav > div {
  margin: 0 10px;
  cursor: pointer;
}

.nav_list {
  position: absolute;
  width: 100%;
  top: 87px;
  z-index: 10000;
  left: 0;
  background: #fff;
  color: #666;
  padding: 0 8%;
  min-height: 250px;
  display: none;
}

.nav_list > span {
  list-style: none;
  margin: 3%;
}

.nav_box > span {
  padding: 5px 10px;
  font-size: 20px;
}

.nav_box.active {
  background:rgba(36, 79, 179, 1);
  /* // linear-gradient( 180deg, rgba(36, 79, 179, 1) 0%, rgba(2, 187, 230, 0.6) 100%); */
}

.nav_box.active .nav_list {
  display: block;
}

.nav ul {
  display: flex;
}

.nav ul li {
  list-style-type: none;
  padding: 0 30px;
  cursor: pointer;
}

.nav ul li.active span {
  font-size: 20px;
  font-weight: 700;
}

.app-config-wrapper {
  position: absolute;
  /* right: 202px; */
  height: 60px;
  /* margin-top: 15px; */
  width: 700px;
  height: 60px;
  background: url("~@/assets/images/lightBg.png");
  position: relative;
  right: 0;
}

.app-config {
  height: 60px;
  width: 60px;
  /* background-image: url("~@/assets/images/common/option.png"); */
  background-repeat: no-repeat;
  background-size: 24px 24px;
  background-position: center center;
  text-align: center;
  &:hover {
    cursor: pointer;
    background-color:rgba(255, 255, 255, 0.15);
  }
}

.app-config-active {
  /* background-color:rgba(255, 255, 255, 0.6); */
  background: linear-gradient( 180deg, rgba(153, 235, 255, 0.6) 0%, rgba(2, 187, 230, 0.6) 100%);
}
</style>
