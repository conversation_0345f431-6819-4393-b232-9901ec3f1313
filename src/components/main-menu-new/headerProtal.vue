<template>
  <div>
    <div class="my-header" :style="getBackgroundStyle(bg1)">
      <div class="header-top">
        <div class="log" style="margin-right: 80px;min-width: 320px;height: 30px;display: flex;align-items: center;"
          @click="$router.push('/homePage')">
          <img src="@/assets/images/u5.png" alt="" style="width:44px;margin:2px 0px 0 0;" />
          <span class="appName" :style="getTextStyle(skinValue)">{{ appName }}</span>
        </div>
        <!-- <Input search placeholder="搜索11" class="searchBox" /> -->
        <img @click="drupMore" v-if="$route.path != '/agencyNews'"
          :src="skinValue != 'znc' ? require('@/assets/images/message.svg') : require('@/assets/images/header/xx.png')"
          style="cursor: pointer;width:30px;position:absolute;right:11%;top:14px;z-index: 999;" />
        <span @click="drupMore" v-if="$route.path != '/agencyNews'"
          style="cursor: pointer;width:30px;line-height: 25px;font-size: 16px;position:absolute;right:10%;top:10px;background: #e60012;color:#fff;text-align: center;border-radius: 50%;z-index: 998;">{{
          total }}</span>
        <user :user-avatar="userAvatar" @on-close="handleCloseTag" :list="tagNavList" @changeSkin="changeSkin"
          :skinValue="skinValue" @getTheme="getTheme" />
      </div>
      <div class="tag-nav-wrap">
        <tags-nav :showSlide="true" :value="$route" @handleClickTab="handleClick" :listArr="tagNavList"
          @on-close="handleCloseTag" :skinValue="skinValue" />
      </div>
      <!-- <div class="nav">
        <ul v-show="listTitle.length > 0" style="margin-left: 48%;">
          <li v-for="item in listTitle" :key="item.name"
              :class="{ active: $store.state.common.activeRootRouterName === item.name }" class="nav_box"
              @click="handleClick(item)">
            <span>{{ item.meta.title }}</span>
          </li>
        </ul>
      </div> -->
    </div>
  </div>
</template>

<script type="text/javascript">
import { getMenuByRouter, getPermMenu } from '@/libs/util'
import User from "./components/userProtal"
import smr from "@/assets/images/header/bg1.png"
import jwl from "@/assets/images/header/bg2.png"
import znc from "@/assets/images/header/bg3.png"
import TagsNav from "./components/tags-nav-protal/tags-nav.vue"
import { mapMutations, mapActions, mapGetters } from "vuex"

export default {
  name: 'Header',
  components: { User, TagsNav },
  props: {
    msg: {
      type: String,
      default: 'test msg'
    }
  },
  watch: {
    'skinValue': {
      handler(n, o) {
        this.changeSkin(n)
      }, immediate: true, deep: true
    }
  },
  methods: {
    ...mapMutations(['setBreadCrumb', 'setTagNavList', 'addTag', 'setLocal', 'setHomeRoute', 'closeTag']),
    drupMore() {
      this.$router.push('/agencyNews')
      let res = {
        path: '/agencyNews',
        name: "agencyNews",
        meta: { hideInMenu: true, title: "待办消息", notCache: true, icon: "md-home" }
      }
      let arr = localStorage.getItem('tagNaveList') ? JSON.parse(localStorage.getItem('tagNaveList')) : []
      arr.unshift(res)
      this.setTagNavList(arr)
    },
    changeSkin(value) {
      console.log(value, 'headerPro')
      this.skinValue = value
      this.getTextStyle(value)
      switch (value) {
        case 'smr':
          this.bg1 = this.smr
          break;
        case 'jwl':
          this.bg1 = this.jwl
          break;
        case 'znc':
          this.bg1 = this.znc
          break;
      }
      this.getBackgroundStyle(this.bg1)

    },
    handleCloseTag(res, type, route) {
      if (type !== "others") {
        if (type === "all") {
          if (this.$route.path !== res[0].path) {
            this.turnToPage(this.$config.homeName);
          }
        } else {
          if (this.$route.path.indexOf("/help/component") > -1) {
            this.closeTag(route)
          } else if (routeEqual(this.$route, route)) {
            this.closeTag(route)
          }
        }
      }
      this.setTagNavList(res)
    },
    getAppData() {
      let query = { appCode: this.appCode }
      this.$store.dispatch('authGetRequest', { url: this.$path.app_getMenu_url, params: query }).then(resp => {
        if (resp.success) {
          this.menuList = resp.data //this.transData(resp.menu)

        } else {
          this.loadding = false
          this.isPower = false
          this.errMsg = resp.msg
        }
      })
    },
    getTheme(data) {
      this.getSkinData()
    },
    // 获取设置主题
    getSkinData() {
      let query = { appCode: this.appCode }
      this.$store.dispatch('authGetRequest', { url: this.$path.systemTheme_get, params: query }).then(resp => {
        if (resp.success) {
          if (resp.data) {
            this.skinValue = resp.data.theme
            this.changeSkin(resp.data.theme)
          } else {
            this.skinValue = 'smr'
            this.changeSkin(this.skinValue)

          }
        } else {

        }
      })
    },
    handleClick(item, isHome) {
      console.log(item, isHome, 'item, isHome')
      if (this.$route.path === item.path) return;
      if (!isHome) {
        let list = [...this.menuList];
        let searchList = this.treeFilter(list, function (res) {
          return res.path === item.path;
        });
        if (searchList.length > 0) {
          let res = list.filter((item) => item.id === searchList[0].id);
          if (
            res[0].children.length === 1 &&
            !res[0].children[0].children
          ) {
            this.showMenu = false;
          } else {
            this.showMenu = true;
          }
          this.$store.commit(
            "setActiveRootRouterName",
            searchList[0].name
          );
        }


      }
      this.turnToPage(item);
      // for (let i = 0, len = list.length; i < len; i++) {
      //     let it = list[i];
      //     if (it.children && it.children.length > 0) {
      //         let flag = this.comparePath(it.children, item.path);
      //         if (flag) {
      //             //console.log(it)
      //             this.$store.commit("setActiveRootRouterName", it.name);
      //             break;
      //         }
      //     }
      // }
    },
    turnToPage(route, reset = false) {
      console.log(route, 'routemain')
      if (reset) return
      let { path, params, query } = {}
      if (typeof route === "string") {
        path = route
      } else {
        path = route.path || route.url
        params = route.params
        query = route.query
      }

      if (typeof route === 'string' && route.indexOf("http") > -1) {
        window.open(route);
        return;
      }

      if (route == '/service/analysis/overview') {
        window.open('/#/service/analysis/overview', '_blank')
      } else {
        this.$router.push({
          path,
          params,
          query,
        });
      }
    },
    treeFilter(tree, func) {
      // 使用map复制一下节点，避免修改到原树
      return tree
        .map((node) => ({ ...node }))
        .filter((node) => {
          node.children =
            node.children && this.treeFilter(node.children, func);
          if (func(node) || (node.children && node.children.length)) {
            return (
              func(node) ||
              (node.children && node.children.length)
            );
          }
        });
    },
    getBackgroundStyle(imageUrl) {
      return {
        'background-image': `url(${imageUrl})`,
        'background-size': 'cover',
        'background-repeat': 'no-repeat',
        // 'width': '200px', // 设置每个元素的宽度，可以根据需要调整
        // 'height': '150px', // 设置每个元素的高度，可以根据需要调整
        // 'margin': '10px' // 设置元素之间的间距，可以根据需要调整
      };
    },
    getTextStyle(value) {
      return {
        'color': value != 'znc' ? '#fff' : '#2B3346',
        // 'background-size': 'cover',
        // 'background-repeat': 'no-repeat',
        // 'width': '200px', // 设置每个元素的宽度，可以根据需要调整
        // 'height': '150px', // 设置每个元素的高度，可以根据需要调整
        // 'margin': '10px' // 设置元素之间的间距，可以根据需要调整
      };
    },
    getTableData() {
      this.$store.dispatch('authPostRequest', { url: this.$path.app_getDbMsgData, params: { isRead: 0, } }).then(res => {
        if (res.success) {
          this.total = res.data.todo.total
        } else {
          this.$Notice.error({
            title: '失败提示',
            desc: res.msg || '获取失败'
          })
        }
      })
    },
    handleCloseTag(res, type, route) {
      // if (type !== "others") {
      //   if (type === "all") {
      //     if (this.$route.path !== res[0].path) {
      //       this.turnToPage(this.$config.homeName);
      //     }
      //   } else {
      //     if (this.$route.path.indexOf("/help/component") > -1) {
      //       this.closeTag(route)
      //     } else if (routeEqual(this.$route, route)) {
      //       this.closeTag(route)
      //     }
      //   }
      // }
      // this.setTagNavList(res)
    },
    goToAppConfig() {
      this.handleClick(this.appConfigRoute)
    },
    // 递归获取有实质组件的第一个路由名称
    getRouterNameByTrue(item) {
      const getRouterName = item => {
        if (item.children && item.children.length) {
          return getRouterName(item.children[0])
        } else {
          return item.path
        }
      }
      return getRouterName(item)
    },
    commitIframeState() {
      this.$emit('getIframeState', this.iframeState)
    }
  },
  data() {
    return {
      menuList: [],
      appName: serverConfig.APP_NAME,
      appCode: serverConfig.APP_MARK,
      skinValue: 'smr',
      bg1: smr,
      smr: smr,
      jwl: jwl,
      znc: znc,
      total: 0,
      appName: serverConfig.APP_NAME,
      isActive: null,
      activeName: this.$store.state.common.activeRootRouterName,
      iframeState: {
        showIframe: false,
        showMenu: true,
        iframeSrc: ''
      }
    }
  },
  watch: {
    'tagNavList': {
      handler(n, o) {
        console.log(n, o, 'tagNavList22')
      }, deep: true, immediate: true
    }
  },
  mounted() {
    this.getSkinData()
    // this.getAppData()
    this.getTableData()
  },
  computed: {
    ...mapGetters(["errorCount"]),
    tagNavList() {
      let page = {
        meta: { title: "首页" },
        name: "homePage",
        params: {},
        path: "/homePage",
        query: {}
      }
      let arr = localStorage.getItem('tagNaveList') ? JSON.parse(localStorage.getItem('tagNaveList')) : []
      arr.unshift(page)
      const uniqueArr = arr.filter((item, index) =>
        arr.findIndex(i => i.path === item.path && i.name === item.name) === index
      );
      console.log(uniqueArr,'uniqueArr')
      return uniqueArr // this.$store.state.app.tagNavList || localStorage.getItem('tagNaveList');
    },
    listTitle() {
      // //console.log(getMenuByRouter(getPermMenu(), []).filter(item => item.name !== 'bsp:uac'),'getMenuByRouter(getPermMenu(), []).filter(item => item.name !== )')
      return getMenuByRouter(getPermMenu(), [])
    },
    appConfigRoute() {
      // //console.log(getMenuByRouter(getPermMenu(), []).find(item => item.name === 'bsp:uac'),'getMenuByRouter(getPermMenu(), []).find(item => item.name === 'bsp:uac')')
      return getMenuByRouter(getPermMenu(), []).find(item => item.name === 'bsp:uac')
    },
    userAvatar() {
      return this.$store.state.common.avatarImgPath;
    },
    // tagNavList() {
    //   return this.$store.state.app.tagNavList;
    // },
  }
}
</script>
<style lang="less" scoped>
.my-header {
  width: 100%;
  height: 94px;
  /* background:#2b5fda; */
  line-height: 94px;
  font-size: 20px;
  color: #fff;
  padding-left: 8px;
  // display: flex;
  /* position: relative; */
  padding: 14px 23px;
}

.header-top {
  width: 100%;
  height: 30px;
  line-height: 30px;
  display: flex;
  align-items: flex-start;
}

.appName {
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: bold;
  font-size: 24px;
  // color: #FFFFFF;
  line-height: 34px;
}

.log {
  font-size: 30px;
  font-weight: 700;
  display: flex;
  align-items: center;
}

.log img {
  display: inline-block;
  /* width: 40px; */
  vertical-align: middle;
}

.log h1 {
  display: inline-block;
  margin: 0 0 0 0px;
  color: #fff;
  font-weight: 600;
  font-size: 30px;
  line-height: 30px;
  vertical-align: middle;
}

.nav {
  display: flex;
  white-space: nowrap;
  /* margin-left: 480px; */
  height: 100%;
  transform: translateX(-50%);
}

.nav>div {
  cursor: pointer;
}

.title_nav {
  background: #1c324e;
  text-align: right;
  color: #fff;
  padding: 5px 0;
  display: flex;
  justify-content: flex-end;
}

.title_nav>div {
  margin: 0 10px;
  cursor: pointer;
}

.nav_list {
  position: absolute;
  width: 100%;
  top: 87px;
  z-index: 10000;
  left: 0;
  background: #fff;
  color: #666;
  padding: 0 8%;
  min-height: 250px;
  display: none;
}

.nav_list>span {
  list-style: none;
  margin: 3%;
}

.nav_box>span {
  padding: 5px 10px;
  font-size: 20px;
}

.nav_box.active {
  background: rgba(36, 79, 179, 1);
  /* // linear-gradient( 180deg, rgba(36, 79, 179, 1) 0%, rgba(2, 187, 230, 0.6) 100%); */
}

.nav_box.active .nav_list {
  display: block;
}

.nav ul {
  display: flex;
}

.nav ul li {
  list-style-type: none;
  padding: 0 30px;
  cursor: pointer;
}

.nav ul li.active span {
  font-size: 20px;
  font-weight: 700;
}

.app-config-wrapper {
  position: absolute;
  /* right: 202px; */
  height: 60px;
  /* margin-top: 15px; */
  width: 700px;
  height: 60px;
  background: url("~@/assets/images/lightBg.png");
  position: relative;
  right: 0;
}

.app-config {
  height: 60px;
  width: 60px;
  /* background-image: url("~@/assets/images/common/option.png"); */
  background-repeat: no-repeat;
  background-size: 24px 24px;
  background-position: center center;
  text-align: center;

  &:hover {
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.15);
  }
}

.app-config-active {
  /* background-color:rgba(255, 255, 255, 0.6); */
  background: linear-gradient(180deg, rgba(153, 235, 255, 0.6) 0%, rgba(2, 187, 230, 0.6) 100%);
}

.searchBox {
  cursor: pointer;
  width: 100px;
  position: absolute;
  right: 13%;
  top: 14px;
  z-index: 999;
}

.searchBox /deep/ .ivu-input {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15px 15px 15px 15px;
  color: #fff;
  border: rgba(255, 255, 255, 0.3);
}

.tag-nav-wrap {
  width: 100%;
  height: 36px;
}
</style>
