<template>
  <!-- <Layout style="height: 100%;min-width: 1440px;" class="main" v-if="loadMenu"> -->
  <div style="height: 100%;min-width: 1440px;" class="main" v-if="loadMenu">
    <!-- <Layout style="z-index: 999;"> -->
    <div style="z-index: 78;">
      <headerWrap style="width: 100%;height: 94px;z-index:60" />
      <!-- <system-head style="width: 100%;height: 94px;z-index:60" @getIframeState="getIframeState"></system-head> -->
      <!-- <div class="userBox"><img @click="$router.push('/representative')" src="@/assets/images/message.svg" style="cursor: pointer;width:30px;right:0;top:14px;z-index: 999;position: relative;"  />
        <span @click="$router.push('/representative')" style="cursor: pointer;width:30px;right:9%;top:10px;background:#e60012;color:#fff;text-align: center;border-radius: 50%;z-index: 998;" >{{ total }}</span>
        <user :user-avatar="userAvatar" @on-close="handleCloseTag" :list="tagNavList"/></div> -->
    </div>
    <!-- <Layout > -->
    <div class="content-wrap-main-new"
      style="margin-top: -39px; height: calc(100% - 60px);background: transparent !important;">
      <!-- <Sider v-show="showSlide "
        hide-trigger
        collapsible
        :width="openSideMenu?'120':'60'"
        :collapsed-width="64"
        v-model="collapsed"
        class="left-sider"
        :style="{overflow: 'hidden'}"
      > -->
      <div class="bsp-main" v-show="openSideMenu" style="z-index:99;padding-top: 0px;height: 100%;width:126px"
        @mouseleave="changeOpen(false)" @mouseenter="changeOpen(true)">
        <side-menu-new @getThirdMenu="getThirdMenu" />
      </div>
      <!-- </Sider> -->
      <div v-if="!showIframe" :class="[openSideMenu ? 'content-wrap-main-right' : 'content-wrap-main-right-vw']">
        <div class="main-content-con" style="background: transparent!important;">
          <div class="main-layout-con" style="background: transparent !important">
            <div class="tag-nav-wrapper">
              <tags-nav :showSlide="true" :openSideMenu="openSideMenu" :value="$route" @input="handleClick"
                @changeSide="changeSide" :list="tagNavList" @on-close="handleCloseTag" />
            </div>
            <div class="content-wrap">
              <!-- 三级菜单 -->
              <div class="content-wrap-third" v-if="thirdMenuList && thirdMenuList.length > 0">
                <div v-for="(item, index) in thirdMenuList" :key="index" @click="getThirdPage(item)"
                  :class="[curMenuid == item.id ? 'activeSide' : '', 'side-child', item.url == '/' ? 'noDisabled' : '']">
                  <sizeComp v-if="item.imgPath" :type="item.imgPath" :size="28"
                    :color="curMenuid == item.id ? '#fff' : '#92abc8'" />
                  <img v-else
                    :src="item.imgPath ? item.imgPath : (curMenuid == item.id ? defaultIcon_selected : defaultImg)" />
                  <p style="font-size: 16px important;">{{ item.name.substring(0, 6) }}</p>
                </div>
              </div>
              <div
                :class="[thirdMenuList && thirdMenuList.length > 0 ? 'rightw20' : 'rightw100', ($route.path != '/protal' && $route.path != '/window/homepage' && $route.path != '/homePage') ? 'content-wrapper' : 'content-wrapper-protal', !showSlide && 'pure-content']">
                <keep-alive :include="cacheList">
                  <router-view :key="$route.path" />
                </keep-alive>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 外部链接加载到iframe -->
      <iframe v-if="showIframe" :src="iframeSrc" style="height:100%;width:100%;border:0"></iframe>
    </div>
  </div>
</template>
<script>
// import SideMenu from "./components/side-menu-main"
import HeaderBar from "./components/header-bar";
import TagsNav from "./components/tags-nav";
import User from "./components/user";
import ABackTop from "./components/a-back-top";
import Fullscreen from "./components/fullscreen";
import Language from "./components/language";
import ErrorStore from "./components/error-store";
import { mapMutations, mapActions, mapGetters } from "vuex";
// import oldTheme from "@/components/menuBar/theme/old/oldTheme";
// import newTheme from "@/components/menuBar/theme/new/newTheme";
import sideMenuNew from "./sideMenu.vue"; //左侧菜单-一级模块
import sizeComp from '@/components/common-icon/common-icon.vue'
import headerWrap from "@/components/main/headerProtal.vue"

import {
  getNewTagList,
  routeEqual,
  getMenuByRouter,
  getPermMenu,
} from "@/libs/util";
import routers from "@/router/routers";
import minLogo from "@/assets/images/logo-min.jpg";
import maxLogo from "@/assets/images/logo.jpg";
import systemHead from "@/view/header/header-new.vue"; //头部
import "./main.less";
import tem from 'template_js'
import { getToken } from '@/libs/util'

export default {
  name: "Main",
  components: {
    // SideMenu,
    HeaderBar,
    Language,
    TagsNav,
    Fullscreen,
    ErrorStore,
    User,
    ABackTop,
    systemHead,
    sideMenuNew,
    sizeComp,
    headerWrap
    // oldTheme,
    // newTheme
  },
  data() {
    return {
      defaultImg: require("@/assets/side/defaultIcon.png"),
      defaultIcon_selected: require("@/assets/side/defaultIcon_selected.png"),
      total: 0,
      collapsed: false,
      minLogo,
      maxLogo,
      menuList: [],
      // modules: [],
      isFullscreen: false,
      showIframe: false,
      iframeSrc: "",
      loadMenu: true,
      showMenu: true,
      openSideMenu: true,
      isMove: false,
      curChoice: {},
      appCode: serverConfig.APP_CODE,
      thirdMenuList: [],
      curMenuid: ''
    };
  },
  provide() {
    return {
      reload: this.reload,
    };
  },
  computed: {
    ...mapGetters(["errorCount"]),
    routeFilter() {
      let whiteRoute = [
        "/",
        "/menuHome",
        "/dataRecord",
        "/onActivityResult",
        "/warehouseScreenLog",
      ];
      return !(
        this.$route.meta.noHeader || whiteRoute.includes(this.$route.path)
      );
    },
    includeRouter() {
      let token = this.$store && this.$store.state.token;
      return token
        ? this.$store.state.aliveLists.length > 0
          ? ["index", "moduleEntry"]
          : ["index"]
        : [];
    },
    themeTemp() {
      return this.curChoice.module === "OLD" ? "oldTheme" : "newTheme";
    },
    tagNavList() {
      // console.log(this.$store.state.app.tagNavList,'this.$store.state.app.tagNavListthis.$store.state.app.tagNavList',localStorage.getItem('tagNaveList'))
      let arr = this.$store.state.app.tagNavList && this.$store.state.app.tagNavList.length > 0 ? this.$store.state.app.tagNavList : []
      let brr = localStorage.getItem('tagNaveList') ? JSON.parse(localStorage.getItem('tagNaveList')) : []
      let crr = arr.concat(brr)
      let page = {
        meta: { title: "首页" },
        name: "homePage",
        params: {},
        path: "/homePage",
        query: {}
      }
      crr.unshift(page)
      // console.log(arr, '12121', crr)
      const uniqueArr = crr.filter((item, index) =>
        crr.findIndex(i => i.path === item.path || i.name === item.name) === index
      );
      return uniqueArr
    },
    // tagNavList() {
    //   let page = {
    //     meta: { title: "首页" },
    //     name: "homePage",
    //     params: {},
    //     path: "/homePage",
    //     query: {}
    //   }
    //   let arr = localStorage.getItem('tagNaveList') ? JSON.parse(localStorage.getItem('tagNaveList')) : []
    //   arr.unshift(page)
    //   return arr // this.$store.state.app.tagNavList || localStorage.getItem('tagNaveList');
    // },
    tagRouter() {
      return this.$store.state.app.tagRouter;
    },
    userAvatar() {
      return this.$store.state.common.avatarImgPath;
    },
    cacheList() {
      const list = [
        "ParentView",
        ...(this.tagNavList.length
          ? this.tagNavList
            .filter((item) => !(item.meta && item.meta.notCache))
            .map((item) => item.name)
          : []),
      ];
      return list;
    },
    local() {
      return this.$store.state.app.local;
    },
    hasReadErrorPage() {
      return this.$store.state.app.hasReadErrorPage;
    },
    modules() {
      let name = this.$store.state.common.activeRootRouterName;
      this.menuList = getMenuByRouter(getPermMenu(), []);
      // console.log(this.menuList,'this.menuList')
      if (this.menuList.length < 1) {
        localStorage.tagNaveList = "[]";
        return [];
      }
      let activeList = [];
      if (name) {
        activeList = this.menuList.find((it) => it.name === name);
      } else {
        activeList = this.menuList[0];
      }
      //console.log(activeList.children,'activeList.children')
      return activeList.children;
    },
    showSlide() {
      // 是否显示左边菜单
      return !this.showIframe && this.showMenu;
    },
  },
  methods: {
    ...mapMutations([
      "setBreadCrumb",
      "setTagNavList",
      "addTag",
      "setLocal",
      "setHomeRoute",
      "closeTag",
    ]),
    ...mapActions(["postRequest"]),
    getThirdMenu(data, curid, drupMenu) {
      if (curid) {
        this.curMenuid = curid
      }
      // console.log(data, '1212',)
      this.thirdMenuList = data.children ? data.children : []
      if (drupMenu && this.thirdMenuList && this.thirdMenuList.length > 0) {
        this.getThirdPage(this.thirdMenuList[0])
      }
    },
    getThirdPage(item) {
      this.curMenuid = item.id
      // console.log(item, item.openMode && item.openMode == '0', "item.openMode && item.openMode=='0'")
      if (item.url && item.url.indexOf('http') > -1) {
        let req = Object.assign({}, this.$route.query)
        let temValue = { user: this.$store.getters.sessionUser, req: req, token: getToken() }
        let openUrl = this.template(item.url, temValue)
        if (item.openMode && item.openMode == '0') {
          window.location.href = openUrl
        } else {
          window.open(openUrl)
        }
      } else if (item.url && item.url != '/') {
        this.$router.push(item.url)
      } else {
        this.$Message.warning('功能开发中！！')
      }
    },
    template(tpl, data) {
      tem.config({ sTag: '{{', eTag: '}}', escape: true })
      return tem(tpl, data)
    },
    turnToPage(route, reset = false) {
      //console.log(route,'routemain')
      if (reset) return;
      let { path, params, query } = {};
      if (typeof route === "string") {
        path = route;
      } else {
        path = route.path || route.url;
        params = route.params;
        query = route.query;
      }

      if (typeof route === "string" && route.indexOf("http") > -1) {
        window.open(route);
        return;
      }

      if (route == "/service/analysis/overview") {
        window.open("/#/service/analysis/overview", "_blank");
      } else {
        this.$router.push({
          path,
          params,
          query,
        });
      }
    },
    getTableData() {
      this.$store
        .dispatch("authPostRequest", {
          url: this.$path.app_getDbMsgData,
          params: { isRead: 0 },
        })
        .then((res) => {
          if (res.success) {
            this.total = res.data.todo.total;
          } else {
            this.$Notice.error({
              title: "失败提示",
              desc: res.msg || "获取失败",
            });
          }
        });
    },
    handleCollapsedChange(state) {
      this.collapsed = state;
    },
    changeOpen(data) {
      if (this.isMove) {
        this.openSideMenu = data;
      }
    },
    changeSide(data, isMove) {
      console.log(data, this.openSideMenu, isMove, 'changeSide')
      this.isMove = isMove;
      this.openSideMenu = data;
    },
    handleCloseTag(res, type, route) {
      // console.log(res, type, route,'res, type, route',this.$config.homeName)
      if (type !== "others") {
        if (type === "all") {
          // this.closeTag(route);
          this.$router.push('/homePage');
        } else {
          if (routeEqual(this.$route, route)) {
            this.closeTag(route);
          }
        }
      }
      this.setTagNavList(res);
    },
    handleClick(item, isHome) {
      console.log(this.$store.getters.menuList, "this.$store.getters.menuList");
      if (this.$route.path === item.path) return;
      if (!isHome) {
        let list = [...this.$store.getters.menuList];
        let searchList = this.treeFilter(list, function (res) {
          return res.path === item.path;
        });
        if (searchList.length > 0) {
          let res = list.filter((item) => item.id === searchList[0].id);
          if (res[0].children.length === 1 && !res[0].children[0].children) {
            this.showMenu = false;
          } else {
            this.showMenu = true;
          }
          this.$store.commit("setActiveRootRouterName", searchList[0].name);
        }
      }
      this.turnToPage(item);
      // for (let i = 0, len = list.length; i < len; i++) {
      //     let it = list[i];
      //     if (it.children && it.children.length > 0) {
      //         let flag = this.comparePath(it.children, item.path);
      //         if (flag) {
      //             //console.log(it)
      //             this.$store.commit("setActiveRootRouterName", it.name);
      //             break;
      //         }
      //     }
      // }
    },
    treeFilter(tree, func) {
      // 使用map复制一下节点，避免修改到原树
      return tree
        .map((node) => ({ ...node }))
        .filter((node) => {
          node.children = node.children && this.treeFilter(node.children, func);
          if (func(node) || (node.children && node.children.length)) {
            return func(node) || (node.children && node.children.length);
          }
        });
    },
    findMenuItem(tree, currentPath) {
      for (const item of tree) {
        // console.log(item.url === currentPath,tree,item)
        if (item.url === currentPath) {
          return item; // 直接匹配当前节点
        }
        if (item.children) {
          const found = this.findMenuItem(item.children, currentPath);
          if (found) return found; // 在子节点中匹配
        }
      }
      return null; // 未找到
    },
    treeSearch(data, opath) {
      let p = [];
      for (let i = 0; i < data.length; i++) {
        let d = data[i];
        if (d.children && d.children.length > 0) {
          p = p.concat(this.treeSearch(d.children, opath));
        } else {
          if (d.path === opath) {
            p.push(d);
          }
        }
      }
      return p;
    },
    get_menu() {
      this.$store.dispatch("get_permission").then((asyncRouter) => {
        if (asyncRouter[0] == null || asyncRouter[0].length < 1) {
          // 跳转菜单权限页面
          this.setTagNavList();
          this.setHomeRoute(routers);
          let opath = this.$route.path;
          const { path, params, query, meta } = this.$route;
          this.addTag({
            route: { path: opath, params, query, meta },
          });
        } else {
          this.mainInit();
        }
        this.$nextTick(function () {
          this.loadMenu = true;
        });
      });
    },
    comparePath(menuList, path) {
      let target = false;
      for (let i = 0, len = menuList.length; i < len; i++) {
        let item = menuList[i];
        if (item.children && item.children.length > 0) {
          target = this.comparePath(item.children, path);
          if (target) {
            break;
          }
        } else {
          if (item.path === path) {
            target = true;
            break;
          }
        }
      }
      return target;
    },

    getIframeState(data) {
      this.showIframe = data.showIframe;
      this.iframeSrc = data.iframeSrc;
      this.showMenu = data.showMenu;
    },
    mainInit() {
      let that = this;
      this.setTagNavList();
      this.setHomeRoute(routers);
      let opath = this.$route.path;
      const { path, params, query, meta } = this.$route;

      if (opath.indexOf("/help/component") > -1) {
        opath = "/help/component";
        meta.title = "前端组件";
      } else {
        let list = getMenuByRouter(getPermMenu(), []);
        let meunItem = this.treeSearch(list, opath);
        if (meunItem && meunItem.length > 0) {
          meta.title = meunItem[0].meta.title;
        }
      }

      this.addTag({
        route: { path: opath, params, query, meta },
      });
      this.setBreadCrumb(this.$route);

      // 设置初始语言
      // 如果当前打开页面不在标签栏中，跳到homeName页
      if (!this.tagNavList.find((item) => item.path === opath)) {
        this.$router.push({
          path: this.$config.homeName,
        });
      }
      //  初始判断 是否展开左边导航
      if (this.$route.path !== "/home") {
        let list = getMenuByRouter(getPermMenu(), []);
        let searchList = this.treeFilter(list, function (res) {
          if (
            "/help/component" === res.path &&
            that.$route.path.indexOf(res.path) > -1
          ) {
            return true;
          }
          return res.path === that.$route.path;
        });
        if (searchList.length > 0) {
          let res = list.filter((item) => item.id === searchList[0].id);
          if (res[0].children.length === 1 && !res[0].children[0].children) {
            this.showMenu = false;
          } else {
            this.showMenu = true;
          }
          this.$store.commit("setActiveRootRouterName", searchList[0].name);
        }
      }
    },
    reload() {
      this.loadMenu = false;
      this.$nextTick(function () {
        this.get_menu();
        this.menuList = [];
      });
    },
  },
  watch: {
    $route(newRoute) {
      const { path, name, query, params, meta } = newRoute;
      console.log(newRoute,path, 'newRoute3')
      let newPath = "";

      let list = getMenuByRouter(getPermMenu(), []);
      let menuList  = localStorage.getItem(this.appCode+'menuList')?JSON.parse(localStorage.getItem(this.appCode+'menuList')):[]
      let meunItem = this.findMenuItem(menuList, path);
      // console.log(menuList, 'list', meunItem)
      if (meunItem && meunItem.url && meunItem.name) {
        meta.title = meunItem.name;
      }
      newPath = path;

      // console.log(newPath, 'newPathnewPathnewPath', path, name, query, params, meta)
      this.addTag({
        route: { path: newPath, name, query, params, meta },
        type: "push",
      });
      this.setBreadCrumb(newRoute);
      //console.log(getNewTagList(this.tagNavList, {path: newPath, name, query, params, meta}),this.tagNavList,'this.tagNavList')
      this.setTagNavList(
        getNewTagList(this.tagNavList, {
          path: newPath,
          name,
          query,
          params,
          meta,
        })
      );
    },
  },
  mounted() {
    // this.get_menu();
    this.getTableData();
    // 获得菜单模块
    // this.modules = JSON.parse(localStorage.getItem('menu'))
    // this.postRequest({ url: this.$path.get_module_url }).then(data => {
    // this.modules = this.$store.getters.menuList
    // //console.log(this.$store.getters.menuList) 全部菜单
    // this.modules = this.$store.getters.menuList
    // this.navList =this.$store.state.app.tagNavList
    // })
    /**
     * @description 初始化设置面包屑导航和标签导航
     */
  },
};
</script>
<style scoped>
.bsp-main /deep/ .ivu-menu-item {
  padding: 12px 16px;
  font-size: 16px;
  border-left: 0px solid #333;
  display: flex;
  align-content: center;
  align-items: center;
  background: #1f3466;
}

.bsp-main {
  border-radius: 0 6px 0 0;
  border-top: 1px solid #fff;
  backdrop-filter: blur(50px);
  overflow-y: visible;
}

.bsp-main /deep/ .ivu-menu-item .ivu-icon {
  font-size: 18px;
}

.bsp-main /deep/ .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover {
  background: #203d80 !important;
  /* border-left-color: #F0F5FF !important; */
}

.bsp-main /deep/ .ivu-menu-submenu {
  padding: 0 0px !important;
  background: #1f3466 !important;
}

.bsp-main /deep/ .ivu-menu-submenu .ivu-menu-submenu-title>span {
  /* margin-left: 8px !important; */
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #fff;
}

.bsp-main /deep/ .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active,
.bsp-main /deep/ .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active:hover {
  background-color: #203d80 !important;
  border-left-color: #203d80 !important;
}

.bsp-main /deep/ .ivu-menu-item-active.ivu-menu-item-selected {
  background: #203d80 !important;
  color: #fff !important;
}

.ivu-menu-dark /deep/ .ivu-menu-item:hover {
  background-color: #203d80 !important;
  background: #203d80 !important;
  border-left-color: #203d80 !important;
}

.bsp-main /deep/ .ivu-menu-vertical .ivu-menu-item,
.bsp-main /deep/ .ivu-menu-vertical .ivu-menu-submenu-title {
  padding: 15px 18px 14px 12px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #fff;
}

.bsp-main /deep/ .ivu-menu-submenu-title>i,
.bsp-main /deep/ .ivu-menu-submenu-title span>i {
  margin-right: 4px;
  font-size: 18px;
}

.bsp-main /deep/ .ivu-menu-vertical .ivu-menu-submenu-title-icon {
  right: 16px;
  font-size: 18px;
}

.bsp-main /deep/ .ivu-menu-item-active {
  /* background-color: #333; */
}
</style>
<style>
.ivu-layout-sider,
.ivu-menu-dark {
  /* background: #333c4a !important; */
  background: linear-gradient(180deg,
      rgba(255, 255, 255, 0.9) 0%,
      #ffffff 100%) !important;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened {
  /* background: #fff !important; */
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
  /* background: #fff !important; */
}

.user-avatar-dropdown .ivu-icon-md-expand:before,
.user-avatar-dropdown .ivu-dropdown-rel,
.user-avatar-dropdown .ivu-icon-md-arrow-dropdown:before {
  color: #fff;
}

.full-screen-btn-con {
  position: absolute;
  right: 150px;
}

.user-avatar-dropdown {
  position: absolute;
  right: 10px;
  /* height: 60px;
  line-height: 60px; */
}

.ivu-icon-md-expand:before {
  color: #fff;
}

.userBox {
  position: absolute;
  right: 10px;
  width: 260px;
  height: 60px;
  z-index: 999;
  top: 0px;
}
</style>
