<template>
  <Layout style="height: 100%;min-width: 1440px;" class="main" >
    <Layout>
      <Header1 style="position: absolute;width: 100%;height: 60px;" @refreshData="refreshData" @getIframeState="getIframeState"></Header1>
      <div class="app-config-wrapper" style='position: absolute;'></div>
      <user :user-avatar="userAvatar" @on-close="handleCloseTag" :list="tagNavList" />
    </Layout>
    <Layout style="margin-top: 60px; height: calc(100% - 60px)">
      <Layout v-if="!showIframe">
        <Content class="main-content-con">
          <Layout class="main-layout-con" style="height: calc(100vh - 60px);">
            <Content class="content-wrapper">
              <keep-alive :include="cacheList">
                <router-view ref="accessProviderApp" />
              </keep-alive>
            </Content>
          </Layout>
        </Content>
      </Layout>
      <!-- 外部链接加载到iframe -->
      <iframe v-if="showIframe" :src="iframeSrc" style="height:100%;width:100%;border:0"></iframe>
    </Layout>
  </Layout>
</template>
<script>
import SideMenu from './components/side-menu-main'
import HeaderBar from './components/header-bar'
import TagsNav from './components/tags-nav'
import User from './components/user'
import ABackTop from './components/a-back-top'
import Fullscreen from './components/fullscreen'
import Language from './components/language'
import ErrorStore from './components/error-store'
import { mapMutations, mapActions, mapGetters } from 'vuex'
import { getNewTagList, routeEqual, getMenuByRouter, getPermMenu } from '@/libs/util'
import routers from '@/router/routers'
import minLogo from '@/assets/images/logo-min.jpg'
import maxLogo from '@/assets/images/logo.jpg'
import Header1 from '@/view/header/open-header.vue'
import './main.less'
export default {
  name: 'Main',
  components: {
    SideMenu,
    HeaderBar,
    Language,
    TagsNav,
    Fullscreen,
    ErrorStore,
    User,
    ABackTop,
    Header1
  },
  data() {
    return {
      collapsed: false,
      minLogo,
      maxLogo,
      menuList: [],
      // modules: [],
      isFullscreen: false,
      showIframe: false,
      iframeSrc: '',
      loadMenu: false,
      showMenu: true
    }
  },
  provide() {
    return {
      reload: this.reload
    }
  },
  computed: {
    ...mapGetters(['errorCount']),
    tagNavList() {
      return this.$store.state.app.tagNavList
    },
    tagRouter() {
      return this.$store.state.app.tagRouter
    },
    userAvatar() {
      return this.$store.state.common.avatarImgPath
    },
    cacheList() {
      const list = ['ParentView', ...(this.tagNavList.length ? this.tagNavList.filter(item => !(item.meta && item.meta.notCache)).map(item => item.name) : [])]
      return list
    },
    local() {
      return this.$store.state.app.local
    },
    hasReadErrorPage() {
      return this.$store.state.app.hasReadErrorPage
    },
    modules() {
      let name = this.$store.state.common.activeRootRouterName
      this.menuList = getMenuByRouter(getPermMenu(), [])
      if (this.menuList.length < 1) {
        localStorage.tagNaveList = '[]'
        return []
      }
      let activeList = []
      if (name) {
        activeList = this.menuList.find(it => it.name === name)
      } else {
        activeList = this.menuList[0]
      }
      return activeList.children
    },
    showSlide() {
      // 是否显示左边菜单
      return !this.showIframe && this.showMenu
    }
  },
  methods: {
    ...mapMutations(['setBreadCrumb', 'setTagNavList', 'addTag', 'setLocal', 'setHomeRoute', 'closeTag']),
    ...mapActions(['postRequest']),
    turnToPage(route, reset = false) {
      //console.log(route)
      if (reset) return
      let { path, params, query } = {}
      if (typeof route === 'string') {
        path = route
      } else {
        path = route.path
        params = route.params
        query = route.query
      }

      //   /*if (name.indexOf("isTurnByHref_") > -1) {
      //         window.open(name.split("_")[1]);
      //         return;
      //     }*/
      this.$router.push({
        path,
        params,
        query
      })
    },
    handleCollapsedChange(state) {
      this.collapsed = state
    },
    handleCloseTag(res, type, route) {
      if (type !== 'others') {
        if (type === 'all') {
          if (this.$route.path !== res[0].path) {
            this.turnToPage(this.$config.homeName)
          }
        } else {
          if (this.$route.path.indexOf('/help/component') > -1) {
            this.closeTag(route)
          } else if (routeEqual(this.$route, route)) {
            this.closeTag(route)
          }
        }
      }
      this.setTagNavList(res)
    },
    handleClick(item, isHome) {
      if (this.$route.path === item.path) return
      if (!isHome) {
        let list = [...this.$store.getters.menuList]
        let searchList = this.treeFilter(list, function(res) {
          return res.path === item.path
        })
        if (searchList.length > 0) {
          let res = list.filter(item => item.id === searchList[0].id)
          if (res[0].children.length === 1 && !res[0].children[0].children) {
            this.showMenu = false
          } else {
            this.showMenu = true
          }
          this.$store.commit('setActiveRootRouterName', searchList[0].name)
        }
      }
      this.turnToPage(item)
      // for (let i = 0, len = list.length; i < len; i++) {
      //     let it = list[i];
      //     if (it.children && it.children.length > 0) {
      //         let flag = this.comparePath(it.children, item.path);
      //         if (flag) {
      //             //console.log(it)
      //             this.$store.commit("setActiveRootRouterName", it.name);
      //             break;
      //         }
      //     }
      // }
    },
    treeFilter(tree, func) {
      // 使用map复制一下节点，避免修改到原树
      return tree
        .map(node => ({ ...node }))
        .filter(node => {
          node.children = node.children && this.treeFilter(node.children, func)
          if (func(node) || (node.children && node.children.length)) {
            return func(node) || (node.children && node.children.length)
          }
        })
    },
    treeSearch(data, opath) {
      let p = []
      for (let i = 0; i < data.length; i++) {
        let d = data[i]
        if (d.children && d.children.length > 0) {
          p = p.concat(this.treeSearch(d.children, opath))
        } else {
          if (d.path === opath) {
            p.push(d)
          }
        }
      }
      return p
    },
    get_menu() {
      this.$store.dispatch('get_permission').then(asyncRouter => {
        if (asyncRouter[0] == null || asyncRouter[0].length < 1) {
          // 跳转菜单权限页面
          this.setTagNavList()
          this.setHomeRoute(routers)
          let opath = this.$route.path
          const { path, params, query, meta } = this.$route
          this.addTag({
            route: { path: opath, params, query, meta }
          })
        } else {
          this.mainInit()
        }
        this.$nextTick(function() {
          this.loadMenu = true
        })
      })
    },
    comparePath(menuList, path) {
      let target = false
      for (let i = 0, len = menuList.length; i < len; i++) {
        let item = menuList[i]
        if (item.children && item.children.length > 0) {
          target = this.comparePath(item.children, path)
          if (target) {
            break
          }
        } else {
          if (item.path === path) {
            target = true
            break
          }
        }
      }
      return target
    },

    getIframeState(data) {
      this.showIframe = data.showIframe
      this.iframeSrc = data.iframeSrc
      this.showMenu = data.showMenu
    },
    mainInit() {
      let that = this
      this.setTagNavList()
      this.setHomeRoute(routers)
      let opath = this.$route.path
      const { path, params, query, meta } = this.$route

      if (opath.indexOf('/help/component') > -1) {
        opath = '/help/component'
        meta.title = '前端组件'
      } else {
        let list = getMenuByRouter(getPermMenu(), [])
        let meunItem = this.treeSearch(list, opath)
        if (meunItem && meunItem.length > 0) {
          meta.title = meunItem[0].meta.title
        }
      }

      this.addTag({
        route: { path: opath, params, query, meta }
      })
      this.setBreadCrumb(this.$route)

      // 设置初始语言
      // 如果当前打开页面不在标签栏中，跳到homeName页
      if (!this.tagNavList.find(item => item.path === opath)) {
        this.$router.push({
          path: this.$config.homeName
        })
      }

      //  初始判断 是否展开左边导航
      if (this.$route.path !== '/home') {
        let list = getMenuByRouter(getPermMenu(), [])
        let searchList = this.treeFilter(list, function(res) {
          if (res.path === '/help/component' && that.$route.path.indexOf(res.path) > -1) {
            return true
          }
          return res.path === that.$route.path
        })
        if (searchList.length > 0) {
          let res = list.filter(item => item.id === searchList[0].id)
          if (res[0].children.length === 1 && !res[0].children[0].children) {
            this.showMenu = false
          } else {
            this.showMenu = true
          }
          this.$store.commit('setActiveRootRouterName', searchList[0].name)
        }
      }
    },
    reload() {
      this.loadMenu = false
      this.$nextTick(function() {
        this.get_menu()
        this.menuList = []
      })
    },
    refreshData(data) {
      //console.log(data)
      if (this.$route.name == 'accessApp') {
        this.$refs.accessProviderApp.getAccessAppData(data, 'week')
      } else {
        this.$refs.accessProviderApp.getProviderAppData('week', data.split(' ')[0], data.split(' ')[1])
      }
    }
  },
  watch: {
    $route(newRoute) {
      const { path, name, query, params, meta } = newRoute
      console.log(newRoute,'newRoute4')
      let newPath = ''
      if (path.indexOf('/help/component') > -1) {
        newPath = '/help/component'
        meta.title = '前端组件'
      } else {
        let list = getMenuByRouter(getPermMenu(), [])
        let meunItem = this.treeSearch(list, path)
        if (meunItem && meunItem.length > 0) {
          meta.title = meunItem[0].meta.title
        }
        newPath = path
      }

      this.addTag({
        route: { path: newPath, name, query, params, meta },
        type: 'push'
      })
      this.setBreadCrumb(newRoute)
      this.setTagNavList(getNewTagList(this.tagNavList, { path: newPath, name, query, params, meta }))
      if (this.showSlide) {
        this.$nextTick(() => {
          this.$refs.sideMenu.updateOpenName(newPath)
        })
      }
    }
  },
  mounted() {
    this.get_menu()
    // 获得菜单模块
    // this.modules = JSON.parse(localStorage.getItem('menu'))
    // this.postRequest({ url: this.$path.get_module_url }).then(data => {
    // this.modules = this.$store.getters.menuList
    // //console.log(this.$store.getters.menuList) 全部菜单
    // this.modules = this.$store.getters.menuList
    // this.navList =this.$store.state.app.tagNavList
    // })
    /**
     * @description 初始化设置面包屑导航和标签导航
     */
  }
}
</script>
<style scoped>
.bsp-main /deep/ .ivu-menu-item {
  padding: 12px 16px;
  font-size: 16px;
  border-left: 0px solid #333;
  padding-left: 30px !important;
  display:flex;
  align-content: center;
  align-items: center;
  background: #1f3466;
}
.bsp-main /deep/ .ivu-menu-item .ivu-icon {
  font-size: 18px;
}
.bsp-main /deep/ .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover {
  /* background-color: #F0F5FF !important;
  border-left-color: #F0F5FF !important; */
  background:#203d80!important;
}
.bsp-main /deep/ .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active{
  background: #F0F5FF;
  height: 48px;
}
.bsp-main /deep/ .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active:hover {
  background-color: rgba(70, 148, 244, 0.4) !important;
  border-left-color: #6694ff !important;
}

.bsp-main /deep/ .ivu-menu-vertical .ivu-menu-item,
.bsp-main /deep/ .ivu-menu-vertical .ivu-menu-submenu-title {
  padding: 15px 18px 14px 14px;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 16px;
  color: #fff;
  display:flex;
  align-content: center;
  align-items: center;
}
.bsp-main /deep/ .ivu-menu-submenu-title > i,
.bsp-main /deep/ .ivu-menu-submenu-title span > i {
  margin-right: 4px;
  font-size: 18px;
}
.bsp-main /deep/ .ivu-menu-vertical .ivu-menu-submenu-title-icon {
  right: 16px;
  font-size: 18px;
}
.bsp-main /deep/ .ivu-menu-item-active {
  /* background-color: #333; */
}
</style>
<style>
.ivu-layout-sider,
.ivu-menu-dark {
  /* background: #333c4a !important; */
}
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened {
  background: #fff !important;
}
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
  /* background: #fff !important; */
}
.user-avatar-dropdown .ivu-icon-md-expand:before,
.user-avatar-dropdown .ivu-dropdown-rel,
.user-avatar-dropdown .ivu-icon-md-arrow-dropdown:before {
  color: #fff;
}
.full-screen-btn-con {
  position: absolute;
  right: 150px;
}
.user-avatar-dropdown {
  position: absolute;
  right: 10px;
  /* height: 60px;
    line-height: 60px; */
  margin-top: 15px;
}
.ivu-icon-md-expand:before {
  color: #fff;
}
</style>
