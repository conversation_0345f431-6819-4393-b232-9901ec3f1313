import CommonIcon from '@/components/common-icon/common-icon.vue'
import { showTitle } from '@/libs/util'
export default {
  components: {
    CommonIcon
  },
  methods: {
    showTitle (item) {
      return showTitle(item, this)
    },
    showChildren (item) {
      return item.children && (item.children.length > 0 || (item.meta && item.meta.showAlways))
    },
    getNameOrHref (item, children0) {
      if(item.path && item.path.indexOf('/help/component') > -1) {
        return '/help/component'
      }
      return item.href ? `isTurnByHref_${item.href}` : (children0 ? item.children[0].path : item.path)
    }
  }
}
