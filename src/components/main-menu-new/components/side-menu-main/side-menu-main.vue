<template>
  <div class="side-menu-wrapper">
    <!-- <slot></slot> -->
    <Menu ref="menu" v-show="!collapsed" :active-name="active_name" :open-names="openedNames" :accordion="accordion" :theme="theme" width="auto" @on-select="handleSelect">
      <template v-for="item in menuList">
        <template v-if="item.children && item.children.length === 1">
          <side-menu-item v-if="showChildren(item) && openSideMenu " :key="`menu-${item.name}`" :parent-item="item" :openSideMenu="openSideMenu"></side-menu-item>
          <menu-item v-else :name="getNameOrHref(item, true)" :key="`menu-${item.children[0].name}`">
            <common-icon :type="item.children[0].icon || ''" /><span v-if="openSideMenu">{{ showTitle(item.children[0]) }}</span>
          </menu-item>
        </template>
        <template v-else>
          <side-menu-item v-if="showChildren(item) && openSideMenu" :key="`menu-${item.name}`" :parent-item="item" :openSideMenu="openSideMenu"></side-menu-item>
          <menu-item v-else :name="getNameOrHref(item)" :key="`menu-${item.name}`">
            <common-icon :type="item.icon || ''" /><span v-if="openSideMenu">{{ showTitle(item) }}</span>
          </menu-item>
        </template>
      </template>
    </Menu>
    <div class="menu-collapsed" v-show="collapsed" :list="menuList">
      <template v-for="item in menuList">
        <collapsed-menu
          v-if="item.children && item.children.length > 1"
          @on-click="handleSelect"
          hide-title
          :root-icon-size="rootIconSize"
          :icon-size="iconSize"
          :theme="theme"
          :parent-item="item"
          :key="`drop-menu-${item.name}`"
        ></collapsed-menu>
        <Tooltip transfer v-else :content="showTitle(item.children && item.children[0] ? item.children[0] : item)" placement="right" :key="`drop-menu-${item.name}`">
          <a @click="handleSelect(getNameOrHref(item, true))" class="drop-menu-a" :style="{ textAlign: 'center' }">
            <common-icon :size="rootIconSize" :color="textColor" :type="item.icon || (item.children && item.children[0].icon)" />
          </a>
        </Tooltip>
      </template>
    </div>
  </div>
</template>
<script>
import SideMenuItem from './side-menu-item.vue'
import CollapsedMenu from './collapsed-menu.vue'
import { getUnion } from '@/libs/tools'
import mixin from './mixin'
import { setTimeout } from 'timers'

export default {
  name: 'SideMenu',
  mixins: [mixin],
  components: {
    SideMenuItem,
    CollapsedMenu
  },
  props: {
    menuList: {
      type: Array,
      default() {
        return []
      }
    },
    collapsed: {
      type: Boolean
    },
    openSideMenu: {
      type: Boolean
    },
    theme: {
      type: String,
      default: "dark"
      // light
    },
    rootIconSize: {
      type: Number,
      default: 20
    },
    iconSize: {
      type: Number,
      default: 16
    },
    accordion: Boolean,
    activeName: {
      type: String,
      default: ''
    },
    openNames: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      openedNames: [],
      active_name: ''
    }
  },
  methods: {
    handleSelect(name) {
      console.log(name,'212')
      let reset = false
      if (this.$route.path === name) {
        reset = true
      }
      this.$emit('on-select', name, reset)
    },
    getOpenedNamesByActiveName(name) {
      let list = [...this.$store.getters.menuList]
      let menuList = []
      for (let i = 0, len = list.length; i < len; i++) {
        if (list[i].name === this.$store.state.common.activeRootRouterName) {
          menuList = list[i].children
          break
        }
      }
      let arr = []
      if (menuList.length > 0) {
        let curentMenu = this.comparePath(menuList, name)
        //console.log(menuList,curentMenu,'curentMenu')
        if (curentMenu) {
          arr.push(curentMenu.id)
        }
      }
      //console.log(arr,'openSideMenu')
      return arr
    },
    comparePath(menuList, path) {
      let target = null
      for (let i = 0, len = menuList.length; i < len; i++) {
        let item = menuList[i]
        if (item.children && item.children.length > 0) {
          target = this.comparePath(item.children, path)
          if (target) {
            target = item
            break
          }
        } else {
          if (item.path === path) {
            target = item
            break
          }
        }
      }
      return target
    },
    updateOpenName(name) {
      if (name === this.$config.homeName) this.openedNames = []
      else this.openedNames = this.getOpenedNamesByActiveName(name)
    }
  },
  computed: {
    textColor() {
      return this.theme === 'dark' ? '#fff' : '#495060'
    }
  },
  watch: {
    activeName(oname) {
      let name = oname
      if (name.indexOf('/help/component') > -1) {
        name = '/help/component'
      }
      if (this.accordion) {
        this.openedNames = this.getOpenedNamesByActiveName(name)
      } else {
        this.openedNames = getUnion(this.openedNames, this.getOpenedNamesByActiveName(name))
      }
      this.$nextTick(function () {
        // 页面加载完成后执行
        this.active_name = name
      })
    },
    openNames(newNames) {
      this.openedNames = newNames
    },
    openedNames() {
      this.$nextTick(() => {
        this.$refs.menu.updateOpened()
      })
    },
    getMenuList() {
      return this.menuList
    }
  },
  mounted() {
    if (this.activeName.indexOf('/help/component') > -1) {
      this.active_name = '/help/component'
    } else {
      this.active_name = this.activeName
    }
    this.openedNames = getUnion([this.openedNames], this.getOpenedNamesByActiveName(this.active_name))
  }
}
</script>
<style lang="less">
@import './side-menu.less';
</style>
