<template>
    <a
        @click="handleChange"
        type="text"
        v-if="showMenu"
        :class="['sider-trigger-a', collapsed ? 'collapsed' : '']"
    >
        <Icon
            :type="icon"
            :size="size"
        />
    </a>
</template>
<script>
export default {
    name: "siderTrigger",
    props: {
        collapsed: <PERSON><PERSON><PERSON>,
        showMenu: <PERSON><PERSON><PERSON>,
        icon: {
            type: String,
            default: "navicon-round",
        },
        size: {
            type: Number,
            default: 26,
        },
    },
    methods: {
        handleChange () {
            this.$emit("on-change", !this.collapsed);
        },
    },
};
</script>
<style lang="less">
@import "./sider-trigger.less";
</style>
