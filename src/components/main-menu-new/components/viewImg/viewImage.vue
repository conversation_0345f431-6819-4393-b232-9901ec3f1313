<template>
  <span class="view-container">
    <img ref="galleyImg" v-if="imgUrl" :src="imgUrl" @click.stop="handleView" :alt="alt" >
  </span>
</template>

<script>
import 'viewerjs/dist/viewer.css'
import Viewer from 'viewerjs'

export default {
  props: {
    // 是否支持预览
    isView: {
      type: Boolean,
      default: false
    },
    // 桶名
    bucketName: {
      type: String,
      require: true
    },
    // 服务标识
    serviceMark: {
      type: String,
      require: true
    },
    // 文件路径
    objectName: {
      type: String,
      default: ''
    },
    // 文件名
    fileName: {
      type: String,
      require: true
    },
    // 属性提示
    alt: {
      type: String,
      default: ''
    }
  },
  created () {
    this.getPresignDown()
    if (this.isView && this.imgUrl) {
      this.initViewTool()
    }
  },
  beforeDestroy () {
    // 组件销毁前清理viewer实例
    if (this.viewer) {
      try {
        this.viewer.destroy()
      } catch (error) {
        console.warn('销毁viewer实例失败:', error)
      }
      this.viewer = null
    }
  },
  data () {
    return {
      imgUrl: '',
      viewer: null
    }
  },
  watch: {
    isView: {
      handler (val, newVal) {
        if (newVal && this.imgUrl) {
          this.initViewTool()
        }
      },deep:true,
      immediate: true
    },
    imgUrl: {
      handler (val, newVal) {
        if (val && this.isView) {
          this.initViewTool()
        }
      },deep:true,
      immediate: true
    },
    bucketName: {
      handler (val) {
        if (val && this.objectName && this.serviceMark) {
          this.getPresignDown()
        }
      },
      immediate: true
    }
  },
  methods: {
    initViewTool () {
      console.log('initViewTool被调用')

      // 如果已有viewer实例，先销毁
      if (this.viewer) {
        try {
          this.viewer.destroy()
        } catch (error) {
          console.warn('销毁viewer实例失败:', error)
        }
        this.viewer = null
      }

      const galley = this.$refs.galleyImg
      console.log('galley元素:', galley)

      if (galley) {
        try {
          this.viewer = new Viewer(galley, {
            title: function (image) {
              return image.alt + ' (' + (this.index + 1) + '/' + this.length + ')'
            }
          })
          console.log('viewer初始化成功:', this.viewer)
        } catch (error) {
          console.error('初始化viewer失败:', error)
          this.viewer = null
        }
      } else {
        console.warn('galley元素不存在')
      }
    },
    // 预览
    handleView () {
      console.log('handleView被调用', {
        isView: this.isView,
        hasViewer: !!this.viewer,
        imgUrl: this.imgUrl
      })

      if (this.isView) {
        if (!this.viewer) {
          console.log('viewer不存在，尝试初始化')
          this.initViewTool()
        }

        if (this.viewer) {
          try {
            this.viewer.update()
            this.viewer.view()
          } catch (error) {
            console.error('图片预览失败:', error)
            // 如果viewer出错，重新初始化
            this.initViewTool()
            if (this.viewer) {
              this.viewer.view()
            }
          }
        } else {
          console.warn('无法初始化viewer实例')
        }
      }
    },
    /**
     * 获取图片地址
     * @params bucketName 桶名
     * @params fileName 文件名
     */
    // 获取图片预览地址
    getPresignDown () {
      let params = {
        serviceMark: this.serviceMark,
        bucket: this.bucketName,
        objectName: this.objectName,
        expireTime: 3600
      }
      this.$store.dispatch('authGetRequest', { url: '/bsp-com/com/oss/presign/getObject', params: params }).then(resp => {
        if (resp.success) {
          this.imgUrl = resp.data.presignedUrl
        } else {
          this.$Modal.error({
            title: '温馨提示',
            content: resp.msg
          })
        }
      })
    }
  }
}
</script>
<style scoped>
</style>
