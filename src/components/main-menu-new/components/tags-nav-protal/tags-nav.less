.no-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.size {
  width: 100%;
  height: 100%;
}

.tags-nav-main {
  width: inherit !important;
  // position: relative;
  // max-width: 98% !important;
  // background: #fff;
  height: 46px !important;
  display: flex;
  align-content: center;
  align-items: center;
  flex-wrap: nowrap;
  overflow: hidden;
  padding: 8px;
  margin:9px 0px 0;
  .no-select;
  .size;

  .close-con {
    position: absolute;
    right: 40px;
    top: 7px;
    height:32px;
    width: 32px;
    text-align: center;
    z-index: 19;
    background: #F5F6F9;
    border-radius: 4px;
    &:hover{
      background: #E6E9F0 !important;
      .tab-close{
        background: url('~@/assets/images/closeActive.png');
      }
    }
  }
  .ivu-icon-ios-arrow-back:hover{
    color: #316CF5 !important;
  }
  .ivu-icon-ios-arrow-forward:hover{
    color: #316CF5 !important;
  }
  .ivu-btn-text:hover{
    background: #E6E9F0 !important;
  }

  .btn-con {
    position: absolute;
    top: 7px;
    height: 32px;
    background: #F5F6F9;
    border-radius: 4px;
    z-index: 10;
    font-size: 20px;
    font-weight: 700;
    &:hover{
      background: #E6E9F0 !important;
    }
    button {
      height: 32px;
      text-align: center;
      padding: 0px !important;
    }

    &.left-btn {
      left: 0px;
      font-size: 20px;
      font-weight: 700;
      
      // background: url('~@/assets/images/left.png');
      // background-size: 100% 100%;
      &.ivu-icon{
        font-size: 20px;
        font-weight: 700;
      }
    }

    &.right-btn {
      right: 5px;
      // border-right: 1px solid #F0F0F0;
      font-size: 20px;
      font-weight: 700;
      // background: url('~@/assets/images/right.png');
      // background-size: 100% 100%;
      &.ivu-icon{
        font-size: 20px;
        font-weight: 700;
      }
    }
  }

  .scroll-outer {
    position: absolute;
    left: 28px;
    right: 61px;
    top: 0;
    bottom: 0;
    box-shadow: 0px 0 3px 2px rgba(100, 100, 100, .1) inset;

    .scroll-body {
      height: ~"calc(100% - 1px)";
      display: inline-block;
      padding: 1px 4px 0;
      position: absolute;
      overflow: visible;
      white-space: nowrap;
      transition: left .3s ease;

      .ivu-tag-dot-inner {
        transition: background .2s ease;
        display: none;
      }
    }
  }

  .contextmenu {
    position: absolute;
    margin: 0;
    padding: 5px 0;
    // background: #fff;
    z-index: 1000;
    list-style-type: none;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .1);

    li {
      margin: 0;
      padding: 5px 15px;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }
}
.side-action{
  z-index: 99;
  position: absolute;
  left: -37px;
  top: 9px;
  width: 28px;
  height: 28px;
  background: #FFFFFF;
  border: 1px solid #E6E6E6;
  border-radius: 0 50% 50% 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.left-btn-fff{
  width: 60px;
  height: 46px;
  // background: #fff;
  position: absolute;
  top: -2px;
  left: -32px;
  z-index: 6;
}
.right-btn-fff{
  width: 110px;
  height: 46px;
  // background: #fff;
  position: absolute;
  top: -2px;
  right: -3px;
  z-index: 8;
}

.tags-nav-main /deep/ .ivu-tag-text {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #fff;
  display: inline-block;
  // max-width: 128px !important;
  min-width: 55px !important;
  // overflow: hidden;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;
}
.tags-nav-main /deep/ .znc .ivu-tag-text {
  color: #2B3346;
}
.tags-nav-main .ivu-tag.znc /deep/ .ivu-icon-ios-close{
  color: #2B3346 !important;
}
.tags-nav-main /deep/ .znc .ivu-tag-color-white{
  color: #2B3346 !important;
}
.tags-nav-main .ivu-tag /deep/ .ivu-icon-ios-close{
  color: #fff !important;
}
.tags-nav-main /deep/ .ivu-tag.active{
  background: linear-gradient( 180deg, #FFFFFF 0%, #FFFFFF 26%, #E9EEF5 100%) !important;

}
.tags-nav-main /deep/ .ivu-tag.active .ivu-tag-text{
  color: #2B3346 !important;
}
.tags-nav-main /deep/ .ivu-tag.active  .ivu-icon-ios-close{
  display: none !important;
}
.tab-close{
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url('~@/assets/images/close.png');
  background-size: 100% 100%;

}
.tags-nav .ivu-tag-dot:nth-of-type(n+2){
  min-width: 125px;
}
.tags-nav .ivu-tag-dot:first-of-type{
  min-width:65px;
}

.tags-nav .ivu-tag-dot {
  height: 32px;
  background: transparent!important;
  // background: #F3F4F5;
  border-radius: 4px 4px 0px 0px;
  border: 1px solid transparent  !important;
  margin: 8px 8px 8px 0 !important;
  padding: 8px 16px 8px 16px !important;
  display: inherit !important;
  align-items: center !important;
  align-content: center !important;
  cursor: pointer;
  &:hover{
    background:#E6E9F0 ;
  }
}
// .tags-nav  .ivu-tag-dot:hover{
//   background:#E6E9F0 ;
// }
.tags-nav .ivu-tag-dot .ivu-icon-ios-close{
  width: 12px !important;
  height: 12px !important;
  background: #C1C6C8;
  display: flex;
  align-items: center;
  align-content: center;
  color: #FFFFFF;
  border-radius: 10px;
  font-size: 20px;
  color:#fff !important ;
  font-size: 12px!important ;
}
.tags-nav-main .ivu-tag{
  height: 36px;
  display: flex;
  align-items: center;
  border: none !important;
  margin: 0 !important;
  border-radius: 4px 4px 0px 0px;
  padding: 0 22px;
}
.tags-nav-main .ivu-tag:first-of-type{
  padding-left: 0 ;
}
.tags-nav-main .ivu-tag /deep/ .ivu-icon-ios-close{
   top: 0 !important;
   margin-left: 16px ;//32px;
   display: inline-block;
}
.tags-nav-main .tags-nav.active{
  background-color: linear-gradient( 180deg, #FFFFFF 0%, #FFFFFF 26%, #E9EEF5 100%) !important;

}
.tags-nav-main .ivu-tag{
  background-color: transparent;
  color: #fff;
  cursor: pointer;
  position: relative;
}
.tags-nav-main .ivu-tag::after{
  content: '';
  width: 2px;
  height: 16px;
  background: rgba(67, 77, 101, 1);
  display: inline-block;
  right: 0;
  position: absolute;
}
.tags-nav-main .ivu-tag.active::after{
  display: none !important;
}
.tags-nav-main .ivu-tag-primary:hover{
   background-color: rgba(255, 255, 255, 0.2);
   color: #fff;
}
.tags-nav-main .ivu-tag:hover{
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}
.tags-nav-main .tags-nav .active .ivu-tag-text{
  color: #317FF5 !important;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  min-width: 60px;
  // max-width: 100px;
  // overflow: hidden;
}
// .tags-nav .scroll-body{
//   width: 92%;
// }
// .tags-nav .scroll-outer{
//   width:100% !important;
// }
.tags-nav .scroll-body,.tags-nav .scroll-outer{
  height: 48px !important;
  // background: #fff !important;
  margin-top: -1px !important;
  width:100% !important;

}
.tags-nav .scroll-body span{
  // width: 92%;
  display: flex;
  // overflow: hidden;
}
.back{
  width: 12px;
  height: 12px;
  display: inline-block;
  background: url('~@/assets/images/leftImg.png');
  background-size: 100% 100%;
  &:hover{
    background: url('~@/assets/images/leftActive.png');
    background-size: 100% 100%;
  }
}
.forward{
  width: 12px;
  height: 12px;
  display: inline-block;
  background: url('~@/assets/images/rightImg.png');
  background-size: 100% 100%;
  &:hover{
    background: url('~@/assets/images/rightActive.png');
    background-size: 100% 100%;
  }
}
.closeAll{
  background-image: url('~@/assets/images/header/closeAll.png');
  background-position: 100% 100%;
  display: inline-block;
  margin-left: 16px;
  width: 14px;
  height: 16px;
  cursor: pointer;
}