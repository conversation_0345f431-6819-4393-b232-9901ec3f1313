<template>
          <Modal v-model="modal" class-name="skin-change-modal" width="500" title="皮肤更换" 
            :closable="false" :mask-closable="false">
            <div class="skin-main">
               <div v-for="(item,index) in skinData" :key="index" class="skin-wrap" @click="getSkin(item.checked)">
                   <div><img :src="item.bgImg"  style=" margin-left: -12px; height: 104px;" /></div>
                   <p class="RadioType">
                    <RadioGroup v-model="skinCheck" @on-change="getSkin">
                     <Radio :label="item.checked">{{ item.title }}</Radio>
                    </RadioGroup>
                   </p>
               </div>
            </div>
            <div style="font-size:12px;text-align:center;color:#999;line-height:50px;margin-top:20px;">
            </div>
            <div slot="footer" style="text-align: center !important;">
                <Button  @click="cancelModel()" class="main-button cancel-btn">关&nbsp;&nbsp;闭</Button>
                <Button type="primary" @click="saveSkin()" class="main-button cancel-btn">确&nbsp;&nbsp;定</Button>
            </div>
        </Modal>
</template>

<script>
export default {
   props:{
    skinValue: {
            type: String,
            default: "",
        },
        changeSkinModal:{
            type:Boolean,
            value:false
        }
   },
   watch:{
      'skinValue':{
          handler(n){
             this.skinCheck=n
          },deep:true,immediate:true
      }
   },
   data(){
    return{
        modal:false,
        skinCheck:this.skinValue?this.skinValue:'smr',
        skinData:[
            {title:'深墨染',checked:'smr',bgImg:require('@/assets/images/header/1.png')},
            {title:'警务蓝',checked:'jwl',bgImg:require('@/assets/images/header/2.png')},
            {title:'智能彩',checked:'znc',bgImg:require('@/assets/images/header/3.png')},
        ]
    }
   },
   mounted(){
    // console.log(this.skinValue,this.changeSkinModal,'this.skinValue')
   },
   methods:{
    cancelModel(tag){
        this.modal=false
        console.log(this.modal)
        this.$emit('close',tag)
    },
    getSkin(value){
        console.log(value,'getSkin')
        this.$emit('changeSkin',value)
    },
    saveSkin(value){
        let query = {theme:this.skinCheck}
        this.$store.dispatch('authPostRequest', {url: this.$path.systemTheme_update, params: query}).then(resp => {
            if (resp.success) {
                this.modal=false
                this.cancelModel(true)
                this.$Notice.success({
                            title: '温馨提示',
                            desc: '保存成功'
                        })
            } else {
                this.$Notice.error({
                            title: '错误提示',
                            desc: '保存失败'
                        })
            }
        })
        this.$emit('changeSkin',value)
    },
   }
}
</script>

<style  scoped>
.skin-main{
  display: flex;
  margin: 30px 4%;
  justify-content: space-around;
}
.skin-wrap{
    width: 31%;
    background: #C4E2FC;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #E9EDF5;
    height: 90px;
    height: 114px;
    margin-right: 20px;
    position: relative;
}
.skin-wrap:hover{
    border:1px solid #2b5fda;
}
.skin-wrap:last-of-type{
    margin-right: 2px;
}
.RadioType{
   text-align: center;
   height: 32px;
   background: #FFFFFF;
   display: flex;
   align-content: center;
   align-items: center;
   position: absolute;
   bottom: 0;
   width: 100%;   
   border-radius: 0px 0px 8px 8px;
   justify-content: center;
   font-weight: 400;
    font-size: 16px;
    color: #2B3346;
}
</style>