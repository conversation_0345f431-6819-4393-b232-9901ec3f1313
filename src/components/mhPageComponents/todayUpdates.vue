<template>
    <div class="todayUpdates">
        <div class="home-title">
        <span>今日动态</span>
        </div>
        <div style="margin: 0 16px;">
           <Row type="flex" class="title-update">
                <Col span="6" ></Col>
                <Col span="6" >今日会见登记</Col>
                <Col span="4" >已签到</Col>
                <Col span="4" >已完结</Col>
                <Col span="4" >未完结</Col>
            </Row>
            <div>
                <Row type="flex" v-for="(item,index) in updateArr" :key="index" class="title-update-row">
                    <Col span="6" class="title-col">
                        <template v-if="item.businessname !== '数量总计'">
                            <img :src="item.imgUrl" style="width: 40px;height: 40px;margin-right: 16px;" />
                        </template>
                        <template v-else>
                            <span style="width: 40px;height: 40px;margin-right: 16px;"></span>
                        </template>
                        {{item.businessname}}
                    </Col>
                    <Col span="6" class="update-num">{{item.todaymeetingtotal}}</Col>
                    <Col span="4" class="update-num">{{item.signin}}</Col>
                    <Col span="4" class="update-num">{{item.unfinished}}</Col>
                    <Col span="4" class="update-num">{{item.completed}}</Col>
                </Row>
            </div>
        </div>
    </div>
</template>

<script>
export default {
   data(){
    return{
        updateArr:[
            // {imgUrl:require('@/assets/images/window/1.png'),title:'提讯/询',hj:15,yqd:10,ywj:10,wwc:1},
            // {imgUrl:require('@/assets/images/window/1.png'),title:'提解',hj:15,yqd:10,ywj:10,wwc:1},
            // {imgUrl:require('@/assets/images/window/1.png'),title:'律师会见',hj:15,yqd:10,ywj:10,wwc:1},
            // {imgUrl:require('@/assets/images/window/1.png'),title:'提讯/询',hj:15,yqd:10,ywj:10,wwc:1},
            // {imgUrl:require('@/assets/images/window/1.png'),title:'家属会见',hj:15,yqd:10,ywj:10,wwc:1},
            // {imgUrl:require('@/assets/images/window/1.png'),title:'使馆领事会见',hj:15,yqd:10,ywj:10,wwc:1},
            // {imgUrl:'',title:'数量总计',hj:15,yqd:10,ywj:10,wwc:1},
        ]
    }
   },
   created() {
    this.getToDayMeetingUpdates()
   },
   methods: {
    getToDayMeetingUpdates() {
        let totalCompleted = 0;
        let totalSignin = 0;
        let totalTodayMeetingTotal = 0;
        let totalUnfinished = 0;

        this.$store.dispatch('authGetRequest',{
            url: this.$path.acp_homeWorkbench_getToDayMeetingUpdates
        }).then(res => {
            if(res.success) {
                console.log(res,'res今日动态');
                this.updateArr = res.data

                this.updateArr.forEach((item, index) => {
                    item.imgUrl = require(`@/assets/images/jrdt/${index + 1}.png`);
                });
                
                this.updateArr.forEach(item => {
                    totalCompleted += Number(item.completed) || 0;
                    totalSignin += Number(item.signin) || 0;
                    totalTodayMeetingTotal += Number(item.todaymeetingtotal) || 0;
                    totalUnfinished += Number(item.unfinished) || 0;
                });

                const totalObj = {
                    businessname: '数量总计',
                    todaymeetingtotal: totalTodayMeetingTotal,
                    signin: totalSignin,
                    unfinished: totalUnfinished,
                    completed: totalCompleted,
                    imgUrl: ''
                };

                this.updateArr.push(totalObj);
            } else {
                this.$Modal.error({
                    title: '温馨提示',
                    content: res.msg || '接口操作失败!'
                })
            }
        })
    }
   }
}
</script>

<style lang="less" scoped>

.title-update{
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: normal;
    font-size: 14px;
    color: #5F709A;
    line-height: 28px;
    text-align: center;
    padding: 16px;
}
.title-update-row{
    background: linear-gradient( 167deg, #FFFFFF 0%, #F0F8FF 100%);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(206,224,240,0.7);
    margin-bottom: 8px;
}
.update-num{
   font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #2B3346;
}
.title-col{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
    display: flex;
    text-align: center;
    align-items: center;
}
.title-update-row:last-of-type{
    .title-col,.update-num{
      color: #2390FF;
    }
}
.update-num{
    text-align: center;
    line-height: 46px;
}
</style>