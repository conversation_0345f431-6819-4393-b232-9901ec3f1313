<template>
    <div class="window-msg">
        <div class="home-title">
          <span>消息通知</span><span>查看更多<Icon type="ios-arrow-forward" /></span>
        </div>
        <div class="window-msg-wrap">
            <p v-for="(item,index) in messageData" :key="index" style="border-bottom: 1px solid #E9EDF5;display: flex;align-items: center;padding: 10px 0;">
                <span class="window-msg-wrap-type textOverflow">{{ item.typeName }}</span>
                <span class="window-msg-wrap-title textOverflow">{{ item.title }}</span>
                <span class="window-msg-wrap-msgTitle textOverflow">{{ item.msgTitle }}</span>
                <span class="window-msg-wrap-addTime textOverflow"><Icon type="md-time" />{{ item.addTime }}</span>
            </p>
        </div>
    </div>
</template>

<script>
export default {
    data(){
        return{
            messageData:[
                {type:'tz',typeName:'通知',title:'【系统升级维护公告】',msgTitle:'在线预约系统暂停服务通知（2023年11月25日） ，因系统升级维护，2023年11月28日…',addTime:'2024-03-23 13:26:02'},
                {type:'tz',typeName:'通知',title:'【系统升级维护公告】',msgTitle:'在线预约系统暂停服务通知（2023年11月25日） ，因系统升级维护，2023年11月28日…',addTime:'2024-03-23 13:26:02'},
                {type:'tz',typeName:'通知',title:'【系统升级维护公告】',msgTitle:'在线预约系统暂停服务通知（2023年11月25日） ，因系统升级维护，2023年11月28日…',addTime:'2024-03-23 13:26:02'},
                {type:'tz',typeName:'通知',title:'【系统升级维护公告】',msgTitle:'在线预约系统暂停服务通知（2023年11月25日） ，因系统升级维护，2023年11月28日…',addTime:'2024-03-23 13:26:02'},
                {type:'tz',typeName:'通知',title:'【系统升级维护公告】',msgTitle:'在线预约系统暂停服务通知（2023年11月25日） ，因系统升级维护，2023年11月28日…',addTime:'2024-03-23 13:26:02'},
                {type:'tz',typeName:'通知',title:'【系统升级维护公告】',msgTitle:'在线预约系统暂停服务通知（2023年11月25日） ，因系统升级维护，2023年11月28日…',addTime:'2024-03-23 13:26:02'},
                {type:'tz',typeName:'通知',title:'【系统升级维护公告】',msgTitle:'在线预约系统暂停服务通知（2023年11月25日） ，因系统升级维护，2023年11月28日…',addTime:'2024-03-23 13:26:02'},
            ]
        }
    },
    methods:{

    }

}
</script>

<style lang="less" scoped>
.window-msg{
    height: 100%;
}
.window-msg-wrap{
    width: 100%;
    height:calc(~'100% - 60px');
    overflow-y: overlay !important;
    margin: 0 16px;
    cursor: pointer;
    .window-msg-wrap-type{
        padding:2px 6px;
        font-family: MicrosoftYaHei, MicrosoftYaHei;
        font-weight: normal;
        font-size: 14px;
        color: #4B81FF;
        width: 6%;
        display: inline-block;
        background: #E9F0FF;
        border-radius: 11px 11px 11px 11px;
        text-align: center;
    }
    .window-msg-wrap-title{
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #2B3346;
        line-height: 20px;
        width: 20%;
        display: inline-block;
    }
    .window-msg-wrap-msgTitle{
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-size: 16px;
        color: #2B3346;
        line-height: 20px;
        width: 50%;    
        display: inline-block;
    }
}
</style>