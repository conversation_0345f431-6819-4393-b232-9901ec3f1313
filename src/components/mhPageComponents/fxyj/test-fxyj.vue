<template>
  <div class="yj">
    <p class="titleMh">风险预警（<span class="numRed">0</span>）</p>
    <ul class="yjList">
      <li v-for="(item, index) in yjList" :key="index" :class="item.classNmae">
        <span class="fontTitle">{{ item.title }}</span
        ><span class="fontNum">{{ item.num }}</span>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  props: {
    itemData: {
      type: Object,
      default: () => ( {})
    },
    itemOrder: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      yjList: [
        { id: 1, title: '红色预警', num: 0, classNmae: 'redJb' },
        { id: 2, title: '橙色预警', num: 0, classNmae: 'orgJb' },
        { id: 3, title: '黄色预警', num: 0, classNmae: 'yellowJb' },
        { id: 4, title: '蓝色预警', num: 0, classNmae: 'blueJb' }
      ]
    }
  },
  methods: {

  }
}
</script>
<style scoped>
@import './mhPage.css';
.yjList li {
  min-width: 195px;
  display: flex;
  justify-content: space-between;
  margin: 0 0px 16px 0px;
  height: 60px;
  line-height: 60px;
  padding: 0 10px;
  border-radius: 3px;
  color: #fff;
  align-items: center;
}
.yjList {
  width: 100%;
  display: flex;
  justify-content: space-around;
  min-height: 80px;
  flex-wrap: wrap;
}
</style>