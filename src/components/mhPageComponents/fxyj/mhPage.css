

  


  
  

  

  
  
  .titleMh {
    border-left: 8px solid #2b5fda;
    padding-left: 10px;
    text-align: left;
    font-size: 18px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 700;
    color: #2b3646;
    line-height: 20px;
    margin-bottom: 15px;
  }
  
  
  .textEllipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  
  .yj {
    height: 100%;
    background: #fff;
    padding: 14px 0 0px 0;
    margin-bottom: 10px;
    overflow: hidden;
  }
  
  .seamless-warp {
    overflow: hidden;
    max-height: 85%;
  }
  
  .fontTitle {
    font-size: 16px;
    font-family: Microsoft YaHei Regular, Microsoft YaHei Regular-Regular;
    font-weight: 400;
    text-align: left;
    color: #ffffff;
    line-height: 24px;
  }
  
  .fontNum {
    font-size: 28px;
    font-family: Arial Bold, Arial Bold-Bold;
    font-weight: 700;
    text-align: right;
    color: #ffffff;
    line-height: 24px;
  }
  
  .redJb {
    background: linear-gradient(#f53d3d 0%, #fc9e7e 100%);
  }
  
  .orgJb {
    background: linear-gradient(#f5821d 0%, #fcca72 100%);
  }
  
  .yellowJb {
    background: linear-gradient(#f5b800 0%, #fce572 100%);
  }
  
  .blueJb {
    background: linear-gradient(#4280fe 0%, #78bef0 100%);
  }
  
  .blue {
    color: #3c81f4;
  }
  
  .org {
    color: #ff9933;
  }
  
  .pur {
    color: #7a72ea;
  }
  
  .blueTop {
    border-top: 4px solid #3c81f4;
  
  }
  
  .blueTop .leftTitle {
    background: #f7fbfc;
  }
  
  .orgTop {
    border-top: 4px solid #ff9933;
  }
  
  .orgTop .leftTitle {
    background: #fcfbfa;
  }
  
  .purTop {
    border-top: 4px solid #7a72ea;
  }
  
  .purTop .leftTitle {
    background: #fafaff;
  }
  
  .leftTitleHover .leftTitle,
  .purTop:hover .leftTitle,
  .orgTop:hover .leftTitle,
  .blueTop:hover .leftTitle {
    background: #fff !important;
  }
  

  
  .titleText {
    font-size: 18px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 700;
    text-align: center;
    color: #3d4e66;
    line-height: 24px;
    margin-top: 26px;
  }
  
  .titleNum {
    font-size: 48px;
    font-family: Arial, Arial;
    font-weight: 700;
    text-align: center;
    line-height: 56px;
  }
  
  
  .yjList::-webkit-scrollbar {
    display: none !important;
  }
  
  .infor::-webkit-scrollbar {
    display: none !important;
  }
  
  .tips {
    margin-top: 2% !important;
  }