<template>
  <div class="icp-common-form icp-bottom-all content ">
    <!-- 全部应用 -->
    <ul class="appUl">
      <template >
        <li  class="appLi" v-for="(item, i) in appTypeList" :key="i">
          <div class="leftTitle">
            <span>{{ item.typeName }}</span
            >(<span class="numApp">{{ item.typeList.length || 0 }}</span
            >)
          </div>
          <div class="rightTitle">
            <!-- 全选 -->
            <div class="iconDiv" @click="selectAll(i)">
              <p class="iconImg addApp"><Icon :type="item.selectText ? 'md-checkmark' : 'md-remove'" :color="'#4C91FF'" :size="32" class="addIconA" /></p>
              <p class="appName">{{ item.selectText ? '全选' : '取消全选' }}</p>
            </div>

            <div v-for="(val, index) in item.typeList" :key="index + 'A'" class="iconDiv" @click="selectCur(val, i)">
              <p :class="['iconImg','animationIcon']">
                <img :src="val.imgAppUrl" class="tbIcon" alt="" />
                <Icon v-if="val.check" type="ios-checkmark-circle" class="selectG" :color="'#4C91FF'" :size="20" /></p>
              <p class="appName">{{ val.yymc }}</p>
            </div>
          </div>
        </li>
      </template>
    </ul>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  props: ['myApp', 'appTypeListModel'],
  data() {
    return {
      appTypeList: [],
      xzApp: []
    }
  },
  watch:{
    myApp:{
      handler(n,o){
        this.xzApp=n
      },
      deep:true,
      immediate:true

    }
  },
  mounted() {
    this.getData()
  },
  created() {},
  methods: {
    ...mapActions(['authPostRequest', 'postRequest', 'authGetRequest']),
    getData() {
      let params={
        systemId:serverConfig.APP_ID,
        yylx:0
      }
      this.authGetRequest({ url: `${this.$path.admin_getAllApply}`, params:params }).then(res => {
        // let res={"msg":"","data":[{"typeList":[{"ljdz":"/#/jq/bdwjq","sfgg":"1","flmc":"执法办案","yymc":"警情处置","yyjs":"警情处置","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(106).png","sfjy":"1","id":"B3AA1DC8772D43B1910532E41779A2AF","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-03-18 02:18:31","order_id":1},{"djhxgrzh":"512501196512305186","ljdz":"/#/ajcz/xzaj","sfgg":"1","flmc":"执法办案","djhxgdwdm":"849999990000","yymc":"案件处置","yyjs":"132","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(111).png","sfjy":"1","djhxgdwmc":"测试海关缉私局","id":"D9D9833A94554BEE9AA61C379454E76D","djhxgrxm":"超级管理员","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-02-17 15:47:40","order_id":2},{"ljdz":"http://10.89.12.27:8840/#/singleSignOn?redirect=/&encry=false&access_token={{:=token}}","sfgg":"1","flmc":"执法办案","yymc":"涉案财物","yyjs":"涉案财物","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(68).png","sfjy":"1","id":"C559292772EC4A68A90E419C8C327D04","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-05-24 09:55:36","order_id":3},{"ljdz":"http://10.89.12.27:8830/#/dms/home?singleSignOnToken={{:=token}}","sfgg":"1","flmc":"执法办案","yymc":"电子卷宗","yyjs":"电子卷宗","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(3).png","sfjy":"1","id":"4AD9D1A599244FA7897BFD0C31D1AEC4","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-06-10 17:01:16","order_id":4},{"ljdz":"/#/rycz/ryqzcs","sfgg":"1","flmc":"执法办案","yymc":"人员处置","yyjs":"人员处置","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(32).png","sfjy":"1","id":"1481830829981437952","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-03-18 02:19:48","order_id":8},{"ljdz":"/#/zdaj/xzaj","sfgg":"1","flmc":"执法办案","yymc":"只读案件","yyjs":"只读案件","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(10).png","sfjy":"1","id":"1501813219843641344","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-03-10 18:00:36","order_id":11},{"ljdz":"/#/aj/zjaj","sfgg":"1","flmc":"执法办案","yymc":"自建案件","yyjs":"自建案件","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(20).png","sfjy":"1","id":"1517073702838341632","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-04-21 18:57:25","order_id":12},{"ljdz":"/#/jq/zjjq","sfgg":"1","flmc":"执法办案","yymc":"自建警情","yyjs":"自建警情","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(22).png","sfjy":"1","id":"1531844500543246336","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-06-01 11:46:04","order_id":13},{"ljdz":"/#/icp/lcgl","sfgg":"0","flmc":"执法办案","yymc":"流程管理","yyjs":"设置自定义相关流程","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(19).png","sfjy":"1","id":"E37622BD66094393A8D19981BCB96167","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-03-18 02:19:17","order_id":94},{"ljdz":"http://10.89.12.28:28080/zfpt","sfgg":"1","flmc":"执法办案","yymc":"深警平台","yyjs":"22","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(75).png","sfjy":"1","id":"9CB2C1EC2FB348D6892D6886C1641E9C","fl_id":"304E5B2150B34105BD11CA4191A540F3","isdel":"0","sjgxsj":"2022-03-10 15:00:48","order_id":98}],"typeName":"执法办案"},{"typeList":[{"ljdz":"http://gxx.sundun.cn:999/#/app/system?appId=683791C4FACF42AC9A632F6C555E14B4&singleSignOnToken={{:=token}}","sfgg":"0","flmc":"系统管理","yymc":"系统管理","yyjs":"系统管理","sfnb":"0","yylx":"0","yytb":"appIcon/icon_(13).png","sfjy":"1","id":"D0236B14AA2E48BEB460453E1D426197","fl_id":"EAD506C159324DFA9DD578EA4FD091B8","isdel":"0","sjgxsj":"2022-06-10 17:01:23","order_id":98},{"ljdz":"/admin/#/manager/cspz/list","sfgg":"0","flmc":"系统管理","yymc":"系统设置","sfnb":"1","yylx":"0","yytb":"appIcon/icon_(103).png","sfjy":"1","id":"A985E29290C5444AA2B1F05DBA08D43E","fl_id":"EAD506C159324DFA9DD578EA4FD091B8","isdel":"0","sjgxsj":"2022-01-04 15:32:16","order_id":99}],"typeName":"系统管理"}],"success":true}
        if (res.success) {
          this.appTypeList = res.data
          this.appTypeList.forEach((item, i) => {
            item.selectText = true
            item.typeList.forEach(val => {
              val.imgAppUrl=val.yytb  //require(`../../../../public/image/${val.yytb}`)
                this.xzApp.forEach(myAppItem => {
                  if (myAppItem.id == val.id) {
                    this.$set(val, 'check', true)
                    this.selectCur(val,i,true) 
                    this.$forceUpdate()
                  }
                  
                })
              })
            })
        }
      })
    },
    selectAll(i) {
            this.appTypeList[i].selectText = !this.appTypeList[i].selectText
      if (this.appTypeList[i].selectText) {
        this.appTypeList[i].typeList.forEach(val => {
          this.$set(val, 'check', false)
        })
      } else {
        this.appTypeList[i].typeList.forEach(val => {
          this.$set(val, 'check', true)
        })
      }

      this.$forceUpdate()
    },
    selectCur(val, i,tag) {
        if(!tag){
           this.$set(val, 'check', !val.check)
        }
      
      this.appTypeList[i].selectText = this.appTypeList[i].typeList.some(val => {
        return val.check != true
      })
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="less" scoped>
@import url('./addApply.less');
.icp-common-content {
  width: 98% !important;
  padding-bottom: 0 !important;
}
/deep/ .ric-base-footer {
  position: absolute;
  padding: 10px 0;
  text-align: right;
  justify-content: space-between;
}
.content {
  min-width: 1024px;
  max-width: 1024px;
  height: 600px !important;
}
.iconDiv {
  width: 105px !important;
  position: relative;
}
.selectG {
  position: absolute;
  right: -20px;

}
</style>
