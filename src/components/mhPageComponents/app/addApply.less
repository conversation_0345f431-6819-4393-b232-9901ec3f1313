/deep/ .icp-common-details .background .content .ivu-row .ivu-col .ivu-tooltip .ivu-tooltip-popper {
  left: 10px !important;
}

.bsp-role-assign .flow-modal-title {
  height: 40px;
  background: #2b5fda;
  width: 100%;
  text-indent: 1em;
  color: #fff;
  line-height: 40px;
}

/deep/ .ivu-modal-header {
  padding: 0px 0px !important;
}

/deep/ .ivu-tabs-bar {
  margin-bottom: 7px !important;
}

/deep/ .ivu-btn.ivu-btn-text {
  border: 1px solid #57a3f3;
  color: #57a3f3;
}

.appMain {
  // margin: 0 24px;
  height: 100%;
  overflow: hidden;
}

.appUl::-webkit-scrollbar {
  display: none !important;
}

/deep/ .ivu-tabs-content {
  height: 80%;
}

/deep/ .ivu-tabs {
  height: 100%;
  overflow: hidden;
}

/deep/ .ivu-tabs-tab {
  font-size: 18px;
  font-family: Microsoft YaHei Bold, Microsoft YaHei Bold-Bold;
  font-weight: 700;
  text-align: left;
  color: #3d4e66;
  line-height: 30px;
}

/deep/ .ivu-tabs-tab-active {
  color: #2b5fda;
}

/deep/ .ivu-tabs-ink-bar {
  height: 4px;
}

.appUl {
  width: 100%;
  height: 96%;
  overflow: auto;
  margin-bottom: 16px;
}

.numApp {
  color: #2b5fda;
  margin: 0 3px;
  display: inline-block;
}

.appLi {
  width: 100%;
  display: flex;

  .leftTitle {
    width: 106px;
    min-width: 106px;
    font-size: 16px;
    font-family: Microsoft YaHei Bold, Microsoft YaHei Bold-Bold;
    font-weight: 700;
    text-align: left;
    color: #1a2133;
    line-height: 30px;
    margin-right: 24px;
  }

  .rightTitle {
    flex: 1;
    display: flex;
    flex-wrap: wrap;

    .iconDiv {
      width: 104px;
      height: 96px;
      margin: 0 22px 16px 0;
      text-align: center;
      background: #fff;
      cursor: pointer;
      border-radius: 3px;

      &:hover {
        background: #f2f5fc;
        .animationIcon {
          animation: myfirst 0.8s; // 动画名称，播放时间
          animation-iteration-count: infinite; //无限播放
        }
      }

      .iconImg {
        width: 60px;
        height: 60px;
        // background: url(~@/assets/mhPage/1.png) no-repeat;
        background-size: 100% 100%;
        border-radius: 3px;
        margin: 8px auto 0;
        position: relative;

        
      }
      

      .AtestApp1 {
        background: url(~@/assets/mhPage/icon_1.png) no-repeat;
      }

      .A1234 {
        background: url(~@/assets/mhPage/icon_2.png) no-repeat;
      }

      .AtestApp2 {
        background: url(~@/assets/mhPage/icon_3.png) no-repeat;
      }

      .AtestApp3 {
        background: url(~@/assets/mhPage/icon_4.png) no-repeat;
      }

      .AGRE {
        background: url(~@/assets/mhPage/icon_5.png) no-repeat;
      }

      .appName {
        font-size: 16px;
        font-family: Microsoft YaHei Regular, Microsoft YaHei Regular-Regular;
        font-weight: 400;
        text-align: center;
        color: #2b3646;
        line-height: 15px;
        margin: 8px 0 13px;
      }
    }
  }
}

.tbIcon{
  width: 100%;
  height: 100%;
}

.addApp {
  width: 60px;
  height: 60px;
  border-radius: 3px;
  margin: 8px auto 0;
  background: #f0f4ff;
  border: 1px dashed #cee0f0;
  border-radius: 5px;

  .addIconA {
    margin: 14px auto;
  }
}

@keyframes myfirst {
  0% {
    transform: rotate(-7deg; );
  }

  25% {
    transform: rotate(7deg; );
  }

  50% {
    transform: rotate(-7deg; );
  }

  75% {
    transform: rotate(7deg; );
  }

  100% {
    transform: rotate(-7deg; );
  }
}
