<template>
    <div class="home-com">
        <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
            <span>谈话教育排行榜</span>       
            <ul class="tabList">
                <li :class="['tabTag', { active: item.check }]" v-for="(item, i) in sxDate" :key="i" @click="changeTab(item)">{{ item.title }}</li>
            </ul>
        </p>
        <div class="zyqk-wrap" style="height: 80%">
            <Row type="flex" class="rowFirst">
              <Col span="4" >排名</Col>
              <Col span="6" >管教名字</Col>
              <Col span="5" >个别教育</Col>
              <Col span="5" >集体教育</Col>
              <Col span="4" >总数</Col>
          </Row>
          <div v-if="rankData && rankData.length>0">
          <Row type="flex" v-for="(item,index) in rankData" :key="index" class="rowSecond">
              <Col span="4" style="text-align: center;display: flex;align-items: center;justify-content: center;" ><img src="@/assets/homeSite/n1.png" style="width: 28px;" v-if="index==0" /><img src="@/assets/homeSite/n2.png" style="width: 28px;" v-if="index==1" /><img src="@/assets/homeSite/n3.png" style="width: 28px;" v-if="index==2" /><i v-if="index>2">{{index}}</i></Col>
              <Col span="7" >{{item.instructor_name}}</Col>
              <Col span="5" >{{item.individual_education}}</Col>
              <Col span="5" >{{item.group_education}}</Col>
              <Col span="3" >{{item.total_count}}</Col>
          </Row>
          </div>
          <div v-else><noEmpty  /></div>
        </div>
        <Spin size="large" fix v-if="spinShow"></Spin>

    </div>
</template>

<script>
import { getUserCache, formatDateparseTime } from '@/libs/util'
import noEmpty from "@/components/bsp-empty"

export default {  
    components:{noEmpty}, 
   data(){
    return{
        appCode:serverConfig.APP_CODE,
        curTotal:0,
        curTotalCs:0,
        curN:0,
        curL:0,
        ljjy:0,
        curcs:0,
        nPercent:0,
        sxDate: [
        { title: '本周', check: true ,type:2},
        { title: '本月', check: false,type:3 },
        { title: '本年', check: false,type:4 }
      ],
      dataArr:[],
      dataAll:[],
      spinShow:false,
      yyDicList:[],
      rankData:[],
      type:2

    }
   },
   mounted(){
    this.getData()
   },
   methods:{
    changeTab(item) {
      if (item.title == '本周') {
      }else if (item.title == '本月') {
      } else if (item.title == '本年') {
      }
      this.sxDate.forEach(val => {
        this.$set(val, 'check', false)
      })
      this.$set(this,'type',item.type)
      this.$set(item, 'check', true)
      this.getData()
    },

    getData(mark) {
        this.spinShow=true
        let params={
          modelId:this.globalAppCode+':sy-thjypxb',
          condis:`[{"name":"timeRange","op":"=","value":"${this.type}","valueType":"string"}]`

        }
          this.$store.dispatch("postRequest", {
            url:this.$path.get_query_grid,
            params: params,
          }).then(res => {
            if (res.success) {
                this.spinShow=false
                this.rankData=res.rows
            }else{
              this.spinShow=false
            }
          })
      },
   }
}
</script>

<style lang="less" scoped>
.zyqk-wrap-pie{
    width: 100%;
    height: 50%;
    margin-top: 8px;
}
.zyqk-wrap{
    // background: #DDF5EE;
    border-radius: 4px 4px 4px 4px;
    // margin: 16px;
    padding: 0 16px 8px;
    //height: calc(~'100% - 90px');
    .rowFirst{
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 12px;
      color: #5F709A;
      padding: 16px 0;
      text-align: center;

    }
    .rowSecond{
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #2B3646;
      line-height: 50px;
      text-align: center;
      &:hover{
        background: #F5FBFF;
        font-weight: 700;
        cursor: pointer;

      }
    }
}
.dqzy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
    line-height: 50px;
}
.curTotal{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 24px;
    color: #2B3646;
    line-height: 30px;
}
.flex-box-cs{
    display: flex;
    justify-content: space-between;
}
.curNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 20px;
    color: #2B3346;
    line-height: 28px;
}
/deep/ .ivu-progress-success-bg{
    background:  linear-gradient( 90deg, #5284E1 0%, #97B9E8 100%) !important;
}
.ljjy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #2B3346;
}
.tabList {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #CFD9F0;
    display: flex !important;
    justify-content: space-between;
    display: inline-block;
    // height: 24px;
    .tabTag {
      cursor: pointer;
      width:44px;
      background: #fafafa;
      text-align: center;
      display: inline-block;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #4c6a99;
    //   line-height: 28px;
    }
    .active {
      background: #538ef9 !important;
      color: #fff !important;
    }
  }
  .yy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #2B3646;
    // line-height: 40px;
  }
  .yyNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
  }
</style>