<template>
    <div class="home-com">
        <div class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
            <span>{{orgType=='01'?'监室':'拘室'}}人员动态</span>  
            <div class="rydt-room">    
                <span style="font-weight: 300;">监室号:</span>
                <Select v-model="jsh" style="width:200px" @on-change="SelectRoom">
                 <Option v-for="item in roomData" :value="item.roomCode" :key="item.roomCode">{{ item.roomName }}</Option>
                </Select>
            </div> 
        </div>
        <div class="room-person-wrap">
            <div class="room-person-wrap-left">
               <template v-for="(item,index) in roomPerson"   >
                <div @click="getIndex(item,index)"  :key="index" :class="['room-person-wrap-left-type',curType==index?'activeChild':'']" >
                    <span>{{ item.title }}</span><span>{{ item.total?item.total:0 }}</span>
                </div>
               </template>
            </div>
            <div class="room-person-wrap-right" v-if="roomPerson[curType] && roomPerson[curType].rows && roomPerson[curType].rows.length>0">
                     <div v-for="(ele,i) in roomPerson[curType].rows" :key="i+'person'" style="margin-right: 6px;text-align: center;">
                         <img :src="ele.front_photo" style="width: 68px;height: 68px;"  />
                         <p>{{ ele.xm }}</p>
                     </div>
            </div>
            <noEmpty v-else class="room-person-wrap-right"></noEmpty>

        </div>
        <Spin size="large" fix v-if="spinShow"></Spin>

    </div>

</template>

<script>
import noEmpty from "@/components/bsp-empty"

export default {
    components:{noEmpty},
   data(){
    return{
        orgType: localStorage.getItem('orgType'),
        roomData:[],
        roomPerson:[],
        kssData:[
            {title:'判处死刑人员',num:0,modelId:this.globalAppCode+':sy-rydt-zxry',type:'01'},
            {title:'高风险人员',num:0,modelId:this.globalAppCode+':sy-rydt-gfxry',type:'01'},
            {title:'加戴械具人员',num:0,modelId:this.globalAppCode+':sy-rydt-jdjjry',type:'01'},
            {title:'禁闭人员',num:0,modelId:this.globalAppCode+':sy-rydt-jbry',type:'01'},
            {title:'重病号人员',num:0,modelId:this.globalAppCode+':sy-rydt-zbhry',type:'01'},],
        jlsData:[    
            // {title:'敏感人员',num:0,modelId:this.globalAppCode+':sy-rydt-gfxry',type:'02'},
            {title:'外籍人员',num:0,modelId:this.globalAppCode+':sy-rydt-wjry',type:'02'},
            // {title:'待遣人员',num:0,modelId:this.globalAppCode+':sy-rydt-jbry',type:'02'},
            {title:'病号（传染病）',num:0,modelId:this.globalAppCode+':sy-rydt-bhcrb',type:'02'},
        ],
        personData:[],
        jsh:'',
        curType:0,
        spinShow:false

    }
   },
   mounted(){
    if(this.orgType && this.orgType=='01'){
       this.roomPerson=this.kssData
    }else{
       this.roomPerson=this.jlsData
    }
    this.getRoomList()

   },
   methods:{
        SelectRoom(data){
             this.jsh=data
             this.curType=0
             this.roomPerson.forEach((item,index)=>{
                  this.getData(item.modelId,index,'roomPerson')
             })
             
        },
        getIndex(item,index){
            this.curType=index
        },
        getRoomList() {
            this.$store.dispatch('authPostRequest',{
                url: '/acp-com/base/pm/areaPrisonRoom/list',
                params: {
                orgCode: this.$store.state.common.orgCode,
                status:1
                }
            }).then(res => {
                if(res.success) {
                 this.roomData = res.data
                 this.jsh=this.roomData && this.roomData.length>0?this.roomData[0].roomCode:''
                 console.log(this.jsh,'this.jsh')
                     this.roomPerson.forEach((item,index)=>{
                            this.getData(item.modelId,index,'roomPerson')
                     })
                }
            })
        },
        getData(mark,index,dataTag) {
            this.spinShow=true
            let params={
                modelId: mark,
                condis:`[{"name":"jsh","op":"=","value":"${this.jsh}","valueType":"string"}]`

            }
            this.$store.dispatch("postRequest", {
                url:this.$path.get_query_grid,
                params: params,
            }).then(res => {
                if (res.success) {
                    this.spinShow=false
                    Object.assign(this[dataTag][index],res)
                    this.$forceUpdate()
                    // console.log(this.daysData,'this.daysData')
                }else{
                this.spinShow=false
                }
            })
        },
   }

}
</script>

<style lang="less" scoped>
.room-person-wrap{
   width:100%;
   display:flex;
   height: calc(~'100% - 60px');
   .room-person-wrap-left{
    width:46%;
    border-left:1px solid #D8E7F4;
      .room-person-wrap-left-type{
         margin: 8px 16px;
         background: linear-gradient( 90deg, #FAFBFE 0%, #F6F8FD 80%, #F1F4FB 100%);
         border-radius: 4px 4px 4px 4px;
         padding: 8px 16px;
         display: flex;
         justify-content: space-between;
         cursor: pointer;
         &:hover{
            background: linear-gradient( 271deg, #66C3FF 0%, #4681F8 100%);
            color: #fff;
         }
      }
   }
   .room-person-wrap-right{
      width:54%;
      margin: 16px 0;
      display: flex;
      flex-wrap: wrap;
      height: 86%;
      overflow-y: overlay;
   }
}
.rydt-room{
    font-weight: 400 !important;
    font-size: 14px;
}
.activeChild{
    background: linear-gradient( 271deg, #66C3FF 0%, #4681F8 100%) !important;
     color: #fff!important;
}
</style>