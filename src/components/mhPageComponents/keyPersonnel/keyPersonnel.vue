

<style>
@import url("../common.less");
</style>
<template>
  <div class="risk">
    <div id="myChartBox" class="myChartBox" ref="chartContainer"></div>
    <Spin size="large" fix v-if="spinShow"></Spin>
  </div>
</template>
    
    <script>
import { mapActions } from "vuex";
import { getUserCache, formatDateparseTime } from "@/libs/util";
import * as echarts from "echarts";
const elementResizeDetectorMaker = require("element-resize-detector");
export default {
  props: {
    itemData: {
      type: Array,
      default: () => [],
    },
    itemOrder: {
      type: Number,
      default: 0,
    },
    curTitle: {
      type: String,
      default: "本月",
    },
    yrb: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      option: {},
      myChart: "",
      chartConfig: {},
      spinShow: false,
      total: 0,
    };
  },
  watch: {
    curTitle: {
      handler(n, o) {
        this.getData(this.itemData);
      },
      immediate: true,
      deep: true,
    },
    itemData: {
      handler(n, o) {
        this.getData(this.itemData);
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.getData(this.itemData);
    // 监听myChartBox元素宽度的变化 让图表自适应相应
    let erd = elementResizeDetectorMaker();
    let that = this;
    erd.listenTo(document.getElementsByClassName("risk")[0], (ele) => {
      if (that.myChart) {
        that.myChart.resize();
      }
    });
  },
  methods: {
    ...mapActions([
      "authPostRequest",
      "postRequest",
      "authGetRequest",
      "getRequest",
    ]),
    init() {
      this.createChart();
    },

    initChart(eOptions) {
      if (this.myChart && this.myChart.dispose) {
        //移去上次渲染的结果，确保本次不受影响
        this.myChart.dispose();
      }

      const eContainer = this.$refs.chartContainer;
      if (eContainer) {
        //存在容器&&可以进一步判断数据是否为空
        if (this.xzList.length > 0) {
          this.myChart = echarts.init(eContainer);
          this.myChart.setOption(eOptions, true);
        } else {
          //没有数据的时候
          this.initInnerHTML(eContainer);
        }
      } else {
        //容器不存在
      }
    },

    initInnerHTML(eContainer) {
      eContainer.innerHTML = `
            <div class="tips" style='text-align:center'>
             <img
              src="data:image/png;base64,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"
              alt=""
            />
              <p style="line-height:40px;">暂无数据</p>
            </div>
          `;
    },
    getData(itemData) {
      this.dataList = [];
      this.xzList = [
        { name: "一级风险", value: "0" },
        { name: "二级风险", value: "0" },
        { name: "三级风险", value: "0" },
      ];
      this.spinShow = true;
      let itemStyleArr = [
        {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [
            { offset: 0, color: "rgba(83, 133, 225, 1)" },
            { offset: 1, color: "rgba(145, 178, 224, 1)" },
          ]),
        },
        {
          color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [
            { offset: 0, color: "rgba(175, 235, 231, 1)" },
            { offset: 1, color: "rgba(82, 190, 182, 1)" },
          ]),
        },
      ];
      this.spinShow = false;
      this.total = 0;
      this.dataList = [];
      itemData.forEach((item, index) => {
        for (let i in item) {
          if (
            i.indexOf("risk") > -1 &&
            (this.curTitle == "本月"
              ? i.indexOf("month") > -1
              : i.indexOf("year") > -1)
          ) {
            this.total +=
              i.indexOf(this.curTitle == "本月" ? "month" : "year") > -1
                ? item[i]
                : 0;
            this.dataList.push(
              i.indexOf(this.curTitle == "本月" ? "month" : "year") > -1
                ? item[i]
                : 0
            );
          }
        }
      });
      this.xzList = this.processRiskData(itemData);
      this.init();
    },
    processRiskData(dataArray) {
      // 存储处理结果的数组
      const result = [];
      // 循环处理每个数据对象
      dataArray.forEach((item) => {
        // 创建month数组（风险等级1→2→3）
        const month = [
          { name: "一级风险", value: item.month_risk1 || 0 },
          { name: "二级风险", value: item.month_risk2 || 0 },
          { name: "三级风险", value: item.month_risk3 || 0 },
        ];

        // 创建year数组（风险等级1→2→3）
        const year = [
          { name: "一级风险", value: item.year_risk1 || 0 },
          { name: "二级风险", value: item.year_risk2 || 0 },
          { name: "三级风险", value: item.year_risk3 || 0 },
        ];

        // 添加到结果数组（可扩展其他字段）
        result.push({
          org_code: item.org_code,
          month,
          year,
          // 可添加其他需要保留的字段，如：
          // itemStyle: item.itemStyle
        });
      });

      return result;
    },
    createChart() {
      var arr = this.xzList && this.xzList.length>0?(this.curTitle == "本月"
                ? this.xzList[0].month
                : this.xzList[0].year):[]
      this.option = {
        grid: {
          left: "-10%",
        },
        legend: {
          type: "scroll",
          orient: "vertical",
          left: "52%",
          align: "left",
          top: "middle",
          icon: "circle", //标记类型
          itemGap: 10, // 设置图例间距
          itemWidth: 8, // 设置图例宽度
          itemHeight: 8, // 设置图例高度
          height: 140,
          formatter: (name) => {
            let data = arr;
            let total = 0;
            let target;
            for (let i = 0, l = data.length; i < l; i++) {
              total += data[i].value;
              if (data[i].name == name) {
                target = data[i].value ? data[i].value : 0;
              }
            }
            let arrList = `{a|${name}}{c|${target}}{d|人/}{b|${(
              (total > 0 ? target / total : 0) * 100
            ).toFixed(0)}%}`;
            return arrList;
          },
          textStyle: {
            rich: {
              a: {
                width: 80,
                fontSize: 14,
                color: "#475066",
                align: "left",
                fontStyle: "normal",
                fontFamily: "微软雅黑",
                fontWeight: "400",
                padding: [0, 0, 0, 0],
              },
              b: {
                // width: 80,
                fontSize: 14,
                align: "center",
                color: "#FF7B02",
                padding: [0, 15, 0, 0],
              },
              c: {
                fontSize: 20,
                fontfamily: "Arial-Bold, Arial",
                fontWeight: "400",
                color: "#2B3646",
              },
              d: {
                width: 1,
                height: 16,
                fontSize: 14,
                color: "#A2ACC6",
                background: "#CEE2F5",
                padding: [0,24, 0, 3],
              },
            },
          },
        },
        title: [
          {
            text: "{b|总数}\n{a|" + this.total + "人}", // 显示标题
            show: true, // 是否显示
            x: "20%", // x轴位置
            y: "center", // y轴位置
            textStyle: {
              // 样式配置
              rich: {
                a: {
                  fontSize: 28, // 字体大小
                  color: "#151D48", // 字体颜色
                  padding: [10, 0, 15, 0], // 边距
                  textAlign: "center",
                },
                b: {
                  fontSize: 16, // 字体大小
                  lineHeight: 20,
                  color: " #475066", // 字体颜色
                  textAlign: "center",
                  padding: [10, 0, 0, 0], // 边距
                },
              },
            },
          },
        ],
        tooltip: {
          trigger: "item",
          formatter: "{b}: {c} ({d}%)",
        },
        series: [
          {
            type: "pie",
            right: "48%",
            radius: ["54%", "74%"],
            itemStyle: {
              normal: {
                borderWidth: 2,
                // borderColor: '#E0F0FF',
                color: function (colors) {
                  var colorList = ["#E94256", "#FF7B02", "#FECC04"];
                  return colorList[colors.dataIndex];
                },
              },
            },
            labelLine: {
              show: false,
            },
            label: {
              show: false,
              // formatter: '{d|{c}}\n\n  {b|{b}：}\n{c|{d}%} ',
              // borderWidth: 2,
              // borderColor: '#fff',
              // borderRadius: 4,
              // rich: {
              //   b:{
              //     fontWeight: 400,
              //     fontSize: 14,
              //     color: '#2B3346',
              //   },
              //   c:{
              //     // fontWeight: 400,
              //     fontSize: 12,
              //     color: '#2B3346',
              //     lineHeight:20,
              //   },
              //   d:{
              //     fontWeight: 700,
              //     fontSize: 16,
              //   }
              // }
            },
            data: this.xzList && this.xzList.length>0?
             (this.curTitle == "本月"
                ? this.xzList[0].month
                : this.xzList[0].year):[] //this.dataList
          },
        ],
      };
      this.initChart(this.option);
    },
  },
};
</script>
    <style lang="less" scoped>
// @import url('./compontents.css');
.tips {
  margin-top: 10%;
}
.risk {
  padding: 0px 0 0px 0;
  width: 100%;
  height: 60%;
}
.myChartBox {
  width: 100%;
  height: 100%;
  margin: 0px 0px;
  overflow: auto;
  background: rgba(240, 240, 240, 0.1);
  position: relative;
}
.myChartBox > div {
  width: 100% !important;
  height: 100%;
}
.myChartBox::-webkit-scrollbar {
  display: none !important;
}
.zfqkTitle {
  width: 100%;
  height: 20px;
  display: flex;
  justify-content: space-between;
  .tabList {
    width: 210px;
    display: flex;
    justify-content: space-between;
    display: inline-block;
    margin-right: 40px;
    .tabTag {
      cursor: pointer;
      width: 60px;
      height: 28px;
      background: #fafafa;
      border-radius: 14px;
      text-align: center;
      margin-left: 8px;
      display: inline-block;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #4c6a99;
      line-height: 28px;
    }
    .active {
      background: #538ef9 !important;
      color: #fff !important;
    }
  }
}
</style>
    