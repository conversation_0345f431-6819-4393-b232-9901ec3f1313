<template>
    <div class="message-wrap">
        <Tabs :value="tabValueTEP" :key="2332" @on-click="changeTab">
            <TabPane label="待办消息" name="01">
            </TabPane>
            <TabPane label="通知消息" name="02">
            </TabPane>
        </Tabs>
        <message style="height: calc(100% - 50px);" :type="tabValueTEP" v-if="showTab" :tit="tabValueTEP=='01'?'待办':'通知'" />

    </div>
</template>

<script>
import message from './maessage.vue';
export default {
    components: { message },
    data() {
        return {
            tabValueTEP: '01',
            showTab:true
        }
    },
    methods: {
        changeTab(data, index) {
            this.$set(this,'tabValueTEP',data)
           this.showTab=false
           setInterval(()=>{
            this.showTab=true
           },800)
        }
    }
}
</script>

<style>
.message-wrap {
    padding: 16px;
    height: 100%;
}

.message-wrap .ivu-tabs-tab-active {
    font-family: Source <PERSON>, Source <PERSON>N;
    font-weight: bold !important;
    font-size: 16px !important;
}
</style>
