<template>
    <div style="height: 100%;" class="msg-box">
        <div class="db-message">
            <div class="db-message-wrap">
                <div v-for="(item, index) in dbArr" :key="index"
                    :class="['db-message-wrap-box', item.className, curIndex == index ? 'activeTab' : '']"
                    @click="getCur(item, index)">
                    <p class="db-num">{{ item.typeCode === 'weekDone'?
                      item.totalDone : item.typeCode === 'monthDone'? item.totalDone : item.totalNotDone}}</p>
                    <p class="db-title">{{ item.title }}</p>
                </div>
            </div>
            <div class="db-message-wrap-right">
                <div v-for="(item, index) in mounthPro" :key="index" class="pro-wrap">
                    <span class="pro-title" style="width: 100px;">{{ item.title }}</span>&nbsp;<el-progress style="width: 200px;"
                        :text-inside="true" :stroke-width="24" :color="customColors"
                        :percentage="item.total>0?Math.ceil((item.finish / item.total).toFixed(2) * 100):0"
                        status="success"></el-progress><span class="pro-title">&nbsp;{{ item.finish }}/{{ item.total
                        }}</span>
                </div>
            </div>
        </div>
        <!-- 类型 -->
        <div v-if="tabValue" style="height: calc(100% - 90px);">
            <Tabs :value="tabValue" key="12122" @on-click="getTabDataChild" style="height: 100%;">
                <TabPane :label="ele.name" :name="ele.code" v-for="(ele, index) in tabArr" :key="index + '2tabArr'" class="xx-main" >
                    <div v-if="messageData && messageData.length > 0">
                        <div v-for="(item, index) in messageData" :key="index + 'messageData'"
                            :class="['messageData-wrap', 'A' + item.typPrefixName]">
                            <Row style="width: 100% !important;">
                                <Col span="21">
                                <span class="typeBOrder">{{ item.busTypeName && item.busTypeName
                                    !='null'?item.busTypeName:'暂无分类' }} &nbsp;&nbsp;</span>
                                <span class="typeBOrder">{{ item.spstatus }}</span>
                                <span>{{ item.title }}</span>
                                </Col>
                                <!-- <Col span="4"><span class="qxTitle">{{ item.spbt }}{{ item.time }}</span></Col> -->
                                <Col span="2" v-if="typeCode !='weekDone' && typeCode !='monthDone'"><Button type="primary" style="margin-top: -6px;" @click="detail(item)">去处理</Button></Col>
                            </Row>
                        </div>

                    </div>
                    <noEmpty style="margin-top: 5%;" v-else />

                </TabPane>
            </Tabs>
        </div>
    </div>
</template>

<script>
import noEmpty from "@/components/bsp-empty/index.vue"
export default {
    props: {
        type: String,
        tit:String,
    },
    components: { noEmpty },
    data() {
        return {

            tabValue: '200',
            curIndex: 0,
            msgTypes: '01',
            customColors: [
                { color: '#22D28D', percentage: 40 },
                { color: '#94ECA2', percentage: 100 }
            ],
            dbArr: [
                { title: '今日'+this.tit, typeCode: "today", num: 7, color: '#E8F3FF', className: 'cur' },
                { title: '本周'+this.tit, typeCode: "week", num: 56, color: '#E8F3FF', className: 'cur' },
                { title: '本月'+this.tit, typeCode: "month", num: 21, color: '#E8F3FF', className: 'cur' },
                { title: this.tit=='待办'?'本周已办':'本周已看', typeCode: "weekDone", num: 16, color: '#FFF7E8', className: 'yellow' },
                { title: this.tit=='待办'?'本月已办':'本月已看', typeCode: "monthDone", num: 8, color: '#FFECE8', className: 'red' },
            ],
            mounthPro: [
                { title: '本日进度', total: 20, finish: 2 },
                { title: '本月进度', total: 120, finish: 80 },
            ],
            tabArr: [
                { title: '全部', type: 'All' },
                { title: '谈话教育', type: 'A199' },
                { title: '监室事务', type: 'A104' },
                { title: '社会矛盾', type: 'A101' },
                { title: '械具使用', type: 'A199' },
                { title: '家属会见', type: 'A103' },
            ],
            messageData: [
                { type: '械具使用', typeName: 'A199', spstatus: '待审批 ', content: 'A01监室 王大锤于9月6日18:00入所，请尽快展开入所24小时谈话！', time: '30分钟', qktype: '01', spbt: '已经延期：' },
                { type: '械具使用', typeName: 'A199', spstatus: '待审批 ', content: 'A05监室 李晓明于9月6日18:00入所，请尽快展开入所24小时谈话！', time: '30分钟', qktype: '01', spbt: '距离延期：' },
                { type: '械具使用', typeName: 'A199', spstatus: '待审批 ', content: 'A01监室 王大锤于9月6日18:00入所，请尽快展开入所24小时谈话！', time: '30分钟', qktype: '01', spbt: '距离延期：' },
                { type: '谈话教育', typeName: 'A102', spstatus: '待审批 ', content: 'A01监室 王大锤于9月6日18:00入所，请尽快展开入所24小时谈话！', time: '30分钟', qktype: '01', spbt: '距离延期：' },
                { type: '社会矛盾', typeName: 'A101', spstatus: '待审批 ', content: 'A01监室 王大锤于9月6日18:00入所，请尽快展开入所24小时谈话！', time: '17天18小时', qktype: '01', spbt: '距离延期：' },
                { type: '家属会见', typeName: 'A103', spstatus: '待审批 ', content: 'A01监室 王大锤于9月6日18:00入所，请尽快展开入所24小时谈话！', time: '17天18小时', qktype: '01', spbt: '距离延期：' },
                { type: '监室事务', typeName: 'A104', spstatus: '待审批 ', content: 'A01监室 王大锤于9月6日18:00入所，请尽快展开入所24小时谈话！', time: '17天18小时', qktype: '01', spbt: '距离延期：' },

            ],
            typeCode: "today"

        }
    },
    mounted() {
        this.dicName('ZD_MSG_BUSTYPE_PREFIX')
    },
    watch: {
        type: {
            handler(val, old) {
                if (val != old) {
                    this.msgTypes = val
                    this.getMsgData();
                }
            },
            immediate: true,
            deep: true
        },
    },
    methods: {
        detail(item){
          if(item.url){
            this.$router.push(item.url.replace('/#',''))
          }
        },
        getCur(item, index) {
            this.tabValue = ''
            this.typeCode = item.typeCode
            this.curIndex = index
            setTimeout(() => {
                this.tabValue = '200'
                this.getTabDataChild(this.tabValue)
            }, 800)
        },
        getTabDataChild(data) {
            this.dbArr.forEach(ele => {
                if (ele.typeCode == this.typeCode) {
                    if (ele.items && ele.items.length > 0) {
                        let arr = ele.items.filter(item => {
                            return item.busTypeCode == data
                        })
                        this.$set(this, 'messageData', arr && arr.length > 0 ? arr[0].msgList : [])
                    } else {
                        this.$set(this, 'messageData', [])
                        return
                    }
                }
            })

        },
        getMsgData() {
            let params = {
                msgTypes: this.msgTypes
            }
            this.$store.dispatch('postRequest', { url: this.$path.com_getDbMsgDataComponent, params: params }).then(resp => {
                if (resp.success) {
                    resp.data[this.msgTypes].forEach(ele => {
                        this.dbArr.forEach(item => {
                            if (item.typeCode == ele.typeCode) {
                                Object.assign(item, ele)
                                if (item.typeCode == 'today') {
                                    this.$set(this.mounthPro[0], 'total', resp.data[this.msgTypes][0].total? resp.data[this.msgTypes][0].total:0)
                                    this.$set(this.mounthPro[0], 'finish', resp.data[this.msgTypes][0].totalDone? resp.data[this.msgTypes][0].totalDone: 0)
                                } else if (item.typeCode == 'month') {
                                    this.$set(this.mounthPro[1], 'total', resp.data[this.msgTypes][2].total)
                                    this.$set(this.mounthPro[1], 'finish', resp.data[this.msgTypes][2].totalDone)
                                }

                            }
                        })
                    });
                    this.$forceUpdate()
                    resp.data[this.msgTypes].forEach(eleItem => {
                        if (eleItem.items && eleItem.items.length > 0) {
                            let obj = {
                                busType: "全部业务",
                                busTypeCode: "200",
                                msgList: []
                            }
                            eleItem.items.unshift(obj)

                            eleItem.items.forEach(ele => {
                                if (ele.busTypeCode != '200') {
                                    eleItem.items[0].msgList = ele && ele.msgList ? eleItem.items[0].msgList.concat(ele.msgList) : eleItem.items[0].msgList
                                }
                            })
                        }
                    })

                    this.messageData = resp.data[this.msgTypes][0].items && resp.data[this.msgTypes][0].items.length > 0 && resp.data[this.msgTypes][0].items[0].msgList ? resp.data[this.msgTypes][0].items[0].msgList : []

                }
            })
        },
        // 获取类型·字典
        dicName(dicName, appCode) {
            let name = []
            return new Promise((resolve, reject) => {
                this.$store.dispatch('axiosGetRequest', {
                    url: '/bsp-com/static/dic/bsp/' + `${dicName}` + '.js'
                }).then(res => {
                    if (res.status === 200) {
                        let arr = []
                        let func = { getData: eval('(' + res.data + ')') }
                        arr = func.getData()
                        this.tabArr = arr
                        resolve(arr)
                    } else {
                    }
                })
            })
        },
    }
}
</script>

<style lang="less" scoped>
.db-message {
    width: 100%;
    display: flex;
}

.db-message-wrap {
    width: 60%;
    display: flex;
    align-items: center;
    margin: 0 16px;

    .db-message-wrap-box {
        width: 120px;
        padding: 16px 0;
        border-radius: 4px 4px 4px 4px;
        display: flex;
        align-items: center;
        text-align: center;
        margin-right: 16px;
        flex-wrap: wrap;
        cursor: pointer;

        p {
            width: 100%;
        }
    }

    .cur {
        background: #E8F3FF;
    }

    .yellow {
        background: rgba(255, 247, 232, 0.7);

        .db-num,
        .db-title {
            color: rgba(255, 125, 0, 0.90);
        }
    }

    .red {
        background: rgba(255, 236, 232, 0.7);

        .db-num,
        .db-title {
            color: rgba(245, 63, 63, 0.90);
        }
    }
}

.db-message-wrap-right {
    width: 32%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .pro-wrap {
        width: 100%;
        display: flex;
        flex-wrap: nowrap;
    }
}

.db-num {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 24px;
    line-height: 30px;
    color: #2B3346;
}

.db-title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 16px;
    color: #2B3346;
}

.activeTab {
    background: linear-gradient(47deg, #2250E7 0%, #579BEF 100%) !important;
    box-shadow: 0px 5px 15px 1px rgba(0, 51, 255, 0.33) !important;
    position: relative;

    .db-num,
    .db-title {
        color: #fff !important;
    }

    // &::after{
    //     content:'';
    //     width: 24px;
    //     height: 24px;
    //     background: linear-gradient( 47deg, #2250E7 0%, #579BEF 100%);
    //     box-shadow: 0px 5px 15px 1px rgba(0,51,255,0.33);
    //     border-radius: 2px 2px 2px 2px;
    //     position: absolute;
    // }
}

.el-progress-bar__innerText {
    color: #fff !important;
}

.messageData-wrap {
    width: 100%;
    height: 50px;
    display: flex;
    align-content: center;
    align-items: center;
    border-bottom: 1px solid #E9F0FF;
    padding-right: 10px;
}

.typeBOrder {
    background: #FFECE8;
    border-radius: 11px 11px 11px 11px;
    padding: 0 6px;
    text-align: center;
    display: inline-block;
}

.A199 .typeBOrder {
    background: #FFECE8;
}

.A101 .typeBOrder {
    background: #E9F0FF;
}

.A102 .typeBOrder {
    background: #FFF7E8;

}

.A199 .typeBOrder {
    color: red !important;
}

.A102 .typeBOrder {
    color: #FF8A18 !important;
}

.A102 .qxTitle {
    color: #FF8A18 !important;
}

.A101 .typeBOrder {
    color: #4B81FF !important;
}

.A101 .qxTitle {
    color: #4B81FF !important;
}

.A199 .qxTitle {
    color: red !important;
}
.xx-main{
    height: calc(~"100% - 40px");
}
.xx-main>div{
    overflow-y: auto;
    height: 100%;
}
.msg-box /deep/ .ivu-tabs-content{
   height: 100% !important;
}
</style>
