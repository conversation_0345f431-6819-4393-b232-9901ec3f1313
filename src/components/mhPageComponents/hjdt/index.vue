<template>
    <div class="home-com">
        <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
            <span>会见动态</span>       
            <ul class="tabList">
                <li :class="['tabTag', { active: item.check }]" v-for="(item, i) in sxDate" :key="i" @click="changeTab(item)">{{ item.title }}</li>
            </ul>
        </p>
        <div class="zyqk-wrap">
              <div class="zyqk-wrap-top flex-box-cs">
                <div class="zyqk-wrap-top-left">
                    <p class="dqzy">总人数</p>
                    <p class="curTotal">{{ totalNum }}</p>
                </div>
                <div class="zyqk-wrap-top-right" style="position: relative;right: -10px;"><img src="@/assets/homeSite/3.png" style="width: 72px;" /></div>
             </div>
        </div>
        <div class="zyqk-wrap1" style="height: 66%;margin:0 16px;">
             <div style="margin-top: 10px;height: 100%;width: 100%;overflow: overlay;background:transparent" class="csqk-box">
                <div class="zyqk-wrap-center"  v-for="(item,index) in dataArr" :key="index">
                    <!-- <Progress :percent="100" :success-percent="nPercent" hide-info :stroke-width="8" stroke-color="#DDE8FF" /> -->
                    <div class="zyqk-wrap-foot flex-box-cs" style="line-height: 40px;">
                        <span class="yy name"><img :src="item.imgUrl" style="width: 40px;" />{{item.title}}</span>
                        <span class="yyNum">{{type==1?item.day_count:(type==2?item.week_count:(type==3?item.month_count:item.year_count)) }}&nbsp;</span>
                    </div>
                </div>
             </div>

        </div>
        <Spin size="large" fix v-if="spinShow"></Spin>

    </div>
</template>

<script>
import { getUserCache, formatDateparseTime } from '@/libs/util'

export default {  
    components:{}, 
   data(){
    return{
        appCode:serverConfig.APP_CODE,
        curTotal:28,
        curTotalCs:0,
        curN:0,
        curL:0,
        ljjy:0,
        curcs:0,
        nPercent:0,
        sxDate: [
        { title: '今日', check: true ,type:1},
        { title: '本周', check: false,type:2},
        { title: '本月', check: false,type:3},
        { title: '本年', check: false,type:4}
      ],
       orgType: localStorage.getItem('orgType'),
      dataArr:[
        {title:this.orgType=='01'?'提讯':'提询',imgUrl:require('@/assets/homeSite/4.png'),day_count:0,modelId:this.globalAppCode+':sy-hjdt-tx' },
        {title:'提解',imgUrl:require('@/assets/homeSite/5.png'),day_count:0,modelId:this.globalAppCode+':sy-hjdt-tj'},
        {title:'律师会见',imgUrl:require('@/assets/homeSite/6.png'),day_count:0,modelId:this.globalAppCode+':sy-hjdt-lshj' },
        {title:'家属会见',imgUrl:require('@/assets/homeSite/7.png'),day_count:0,modelId:this.globalAppCode+':sy-hjdt-jshj'},
        {title:'使馆领事会见',imgUrl:require('@/assets/homeSite/8.png'),day_count:0,modelId:this.globalAppCode+':sy-hjdt-slghj' },
      ],
      dataAll:[],
      spinShow:false,
      yyDicList:[],
      type:1,

    }
   },
   computed:{
      totalNum() {
          let   filed='day_count'
           switch(this.type){
            case 1:
              filed='day_count'
              break;
            case 2:
              filed='week_count'
              break;
            case 3:
              filed='month_count'
              break;
            case 3:
              filed='year_count'
              break;
           }
          return this.dataArr.reduce((acc, item) => acc + item[filed], 0);
        }
   },
   mounted(){
    this.dataArr.forEach((item,index)=>{
      this.getData(item.modelId,index)
    })
   },
   methods:{
    changeTab(item) {
      this.spinShow=true
      this.type=item.type
      this.sxDate.forEach(val => {
        this.$set(val, 'check', false)
      })
      this.$set(item, 'check', true)
      this.spinShow=false
    },

    getData(mark,index) {
        this.spinShow=true
        let total=0
        let params={
          modelId: mark,
          // condis:`[{"name":"timeRange","op":"=","value":"${this.type}","valueType":"string"}]`

        }
          this.$store.dispatch("postRequest", {
            url:this.$path.get_query_grid,
            params: params,
          }).then(res => {
            if (res.success) {
                this.spinShow=false
                Object.assign(this.dataArr[index],res.rows[0])
            }else{
              this.spinShow=false
            }
          })
      },
   }
}
</script>

<style lang="less" scoped>
.zyqk-wrap-pie{
    width: 100%;
    height: 50%;
    margin-top: 8px;
}
.zyqk-wrap{
    background: url('~@/assets/homeSite/hjpg.png');
    border-radius: 4px 4px 4px 4px;
    margin:10px 16px;
    padding: 0 16px 8px;
    //height: calc(~'100% - 90px');
}
.dqzy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
    line-height: 50px;
}
.curTotal{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 24px;
    color: #2B3646;
    line-height: 30px;
}
.flex-box-cs{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.curNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 20px;
    color: #2B3346;
    line-height: 28px;
}
/deep/ .ivu-progress-success-bg{
    background:  linear-gradient( 90deg, #5284E1 0%, #97B9E8 100%) !important;
}
.ljjy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #2B3346;
}
.tabList {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #CFD9F0;
    display: flex !important;
    justify-content: space-between;
    display: inline-block;
    // height: 24px;
    .tabTag {
      cursor: pointer;
      width: 60px;
    //   height: 24px;
      background: #fafafa;
      text-align: center;
    //   margin-top: -2px;
      display: inline-block;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #4c6a99;
    //   line-height: 28px;
    }
    .active {
      background: #538ef9 !important;
      color: #fff !important;
    }
  }
  .yy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #2B3646;
    display: flex;
    align-items: center;
  }
  .yyNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #2B3346;
  }
  .csqk-box .zyqk-wrap-center{
    display: flex;
    align-content: center;
    width: 100%;
    background: linear-gradient( 167deg, #FFFFFF 0%, #F0F8FF 100%);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(206,224,240,0.7);
    padding:4px 8px;
    margin-bottom: 8px;
    .name{
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 400;
      font-size: 16px;
      color: #5F709A;
    }
  }
</style>