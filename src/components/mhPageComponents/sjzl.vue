<template>
    <div class="zl">
        <p class="titleSke">社会矛盾化解数据总览</p>
        <div class="boxData">
            <statisticalAnalysis mark='acpshmdhjmdhjzs'  />
            <statisticalAnalysis mark='acpshmdhjsjzlnpsqs'  />
            <statisticalAnalysis mark='acpshmdhjsjzlmdhjdjs'  />
            <statisticalAnalysis mark='acpshmdhjsjzldjcgs'  />
            <statisticalAnalysis mark='acpshmdhjsjzldjz'  />
            <statisticalAnalysis mark='acpshmdhjsjzlbzdj'  />
            <statisticalAnalysis mark='acpshmdhjsjzlsary'  />
            <statisticalAnalysis mark='acpshmdhjsjzlsaje'  />
        </div>
    </div>
</template>

<script>
import {statisticalAnalysis }  from 'sd-statistical-analysis'
export default {
   components:{statisticalAnalysis}
}
</script>

<style>
@import url("./common.less");
</style>
<style scoped>
.boxData{
    display: flex;
    align-content: center;

}
</style>