<template>
    <div class="home-com">
        <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
            <span>{{orgType =='01'?'收押出所':'出入所'}}情况</span>       
            <ul class="tabList">
                <li :class="['tabTag', { active: item.check }]" v-for="(item, i) in sxDate" :key="i" @click="changeTab(item)">{{ item.title }}</li>
            </ul>
        </p>
        <div class="zyqk-wrap">
              <div class="zyqk-wrap-top flex-box-cs">
                <div class="zyqk-wrap-top-left">
                    <p class="dqzy">{{orgType =='01'?'收押':'收拘'}}</p>
                    <p class="curTotal">{{ curTotal }}</p>
                </div>
                <div class="zyqk-wrap-top-right" style="position: relative;right: -10px;"><img src="@/assets/images/header/ksrs.png" /></div>
             </div>
             <div class="zyqk-wrap-foot flex-box-cs" style="margin: 16px 0 8px;">
                <span style="font-size: 14px;">今年累计{{orgType =='01'?'收押':'收拘'}}</span>
                <span class="ljjy">{{ljjy }}</span>
             </div>
        </div>
        <div class="zyqk-wrap" style="background: #EBEBFF;height: 48%">
              <div class="zyqk-wrap-top flex-box-cs">
                <div class="zyqk-wrap-top-left">
                    <p class="dqzy">出所</p>
                    <p class="curTotal">{{ curTotalCs }}</p>
                </div>
                <div class="zyqk-wrap-top-right" style="position: relative;right: -10px;"><img src="@/assets/images/header/syrs.png" /></div>
             </div>
             <!-- <div class="zyqk-wrap-foot flex-box">
                <span style="font-size: 14px;">今年累计收押</span>
                <span class="ljjy">{{ljjy }}</span>
             </div> -->
             <div style="margin-top: 10px;height: 40%;width: 103%;overflow: overlay;background:transparent" class="csqk-box">
                <div class="zyqk-wrap-center"  v-for="(item,index) in dataArr" :key="index">
                    <!-- <Progress :percent="100" :success-percent="nPercent" hide-info :stroke-width="8" stroke-color="#DDE8FF" /> -->
                    <div class="zyqk-wrap-foot flex-box-cs" style="line-height: 40px;">
                        <span class="yy">{{item.name}}</span>
                        <span class="yyNum">{{item.num }}&nbsp;</span>
                    </div>
                </div>
             </div>
             <div class="zyqk-wrap-foot flex-box-cs" style="margin: 10px 0 8px;">
                <span style="font-size: 14px;">今年累计{{orgType =='01'?'收押':'收拘'}}</span>
                <span class="ljjy">{{curcs }}</span>
             </div>

        </div>
        <Spin size="large" fix v-if="spinShow"></Spin>

    </div>
</template>

<script>
import { getUserCache, formatDateparseTime } from '@/libs/util'

export default {  
    components:{}, 
   data(){
    return{
        appCode:serverConfig.APP_CODE,
        curTotal:0,
        curTotalCs:0,
        curN:0,
        curL:0,
        ljjy:0,
        curcs:0,
        nPercent:0,
        sxDate: [
        { title: '今日', check: true },
        { title: '昨日', check: false },
        { title: '本月', check: false }
      ],
      dataArr:[],
      dataAll:[],
      spinShow:false,
      yyDicList:[],
      orgType: localStorage.getItem('orgType'),


    }
   },
   mounted(){
    this.dicName ('ZD_KSS_CSYY',this.appCode)
   },
   methods:{
     dicName (dicName,appCode) {
      let name = []
      return new Promise((resolve, reject) => {
        this.$store.dispatch('axiosGetRequest', {url: '/bsp-com/static/dic/' + appCode + '/' + `${dicName}` + '.js'
      }).then(res => {
          if (res.status === 200) {
            let arr = []
            let func = { getData: eval('(' + res.data + ')') }
            arr = func.getData()
            this.yyDicList=arr
            this.getData(this.globalAppCode+':sysycsqktj')
            resolve(arr)
          } else {
            this.getData(this.globalAppCode+':sysycsqktj')
          }
        })
      })
    },
    changeTab(item) {
      if (item.title == '今日') {
                this.dataArr=this.dataAll[0].today_csyy_chusuo && this.dataAll[0].today_csyy_chusuo.value?JSON.parse(this.dataAll[0].today_csyy_chusuo.value):[]
                this.ljjy=this.dataAll[0].year_total_detain
                this.curTotal=this.dataAll[0].today_total_detain
                this.curcs=this.dataAll[0].year_total_chusuo
                this.curTotalCs=this.dataAll[0].today_chusuo
      } else if (item.title == '昨日') {
                this.dataArr=this.dataAll[0].yesterday_csyy_chusuo && this.dataAll[0].yesterday_csyy_chusuo.value?JSON.parse(this.dataAll[0].yesterday_csyy_chusuo.value):[]
                this.ljjy=this.dataAll[0].year_total_detain
                this.curTotal=this.dataAll[0].yesterday_total_detain
                this.curcs=this.dataAll[0].year_total_chusuo
                this.curTotalCs=this.dataAll[0].yesterday_chusuo
      } else if (item.title == '本月') {
                this.dataArr=this.dataAll[0].month_csyy_chusuo && this.dataAll[0].month_csyy_chusuo.value?JSON.parse(this.dataAll[0].month_csyy_chusuo.value):[]
                this.ljjy=this.dataAll[0].year_total_detain
                this.curTotal=this.dataAll[0].month_total_detain
                this.curcs=this.dataAll[0].year_total_chusuo
                this.curTotalCs=this.dataAll[0].month_chusuo

      }
      this.dataArr.forEach(item=>{
                    this.yyDicList.forEach(ele=>{
                        if(item.csyyCode==ele.code){
                            this.$set(item,'name',ele.name)
                        }
                    })
                })
      this.sxDate.forEach(val => {
        this.$set(val, 'check', false)
      })
      this.$set(item, 'check', true)
      this.getData()
    },

    getData(mark) {
        this.spinShow=true
        let params={
          modelId: mark
        }
          this.$store.dispatch("postRequest", {
            url:this.$path.get_query_grid,
            params: params,
          }).then(res => {
            if (res.success) {
                this.spinShow=false
                this.dataAll=res.rows
             if(res.rows && res.rows.length>0){
                this.dataArr=res.rows[0].today_csyy_chusuo && res.rows[0].today_csyy_chusuo.value?JSON.parse(res.rows[0].today_csyy_chusuo.value):[]
                this.ljjy=res.rows[0].year_total_detain
                this.curTotal=res.rows[0].today_total_detain
                this.curcs=res.rows[0].year_total_chusuo
                this.curTotalCs=res.rows[0].today_chusuo
                this.dataArr.forEach(item=>{
                    this.yyDicList.forEach(ele=>{
                        if(item.csyyCode==ele.code){
                            this.$set(item,'name',ele.name)
                        }
                    })
                })
             }

            }else{
              this.spinShow=false
            }
          })
      },
   }
}
</script>

<style lang="less" scoped>
.zyqk-wrap-pie{
    width: 100%;
    height: 50%;
    margin-top: 8px;
}
.zyqk-wrap{
    background: #DDF5EE;
    border-radius: 4px 4px 4px 4px;
    margin: 16px;
    padding: 0 16px 8px;
    //height: calc(~'100% - 90px');
}
.dqzy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
    line-height: 50px;
}
.curTotal{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 24px;
    color: #2B3646;
    line-height: 30px;
}
.flex-box-cs{
    display: flex;
    justify-content: space-between;
}
.curNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 20px;
    color: #2B3346;
    line-height: 28px;
}
/deep/ .ivu-progress-success-bg{
    background:  linear-gradient( 90deg, #5284E1 0%, #97B9E8 100%) !important;
}
.ljjy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #2B3346;
}
.tabList {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #CFD9F0;
    display: flex !important;
    justify-content: space-between;
    display: inline-block;
    // height: 24px;
    .tabTag {
      cursor: pointer;
      width: 60px;
    //   height: 24px;
      background: #fafafa;
      text-align: center;
    //   margin-top: -2px;
      display: inline-block;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #4c6a99;
    //   line-height: 28px;
    }
    .active {
      background: #538ef9 !important;
      color: #fff !important;
    }
  }
  .yy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #2B3646;
    // line-height: 40px;
  }
  .yyNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
  }
</style>