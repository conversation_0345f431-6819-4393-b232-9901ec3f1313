<template>
    <div class="home-com">
        <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
            <span>出入所动态</span>       
            <!-- <ul class="tabList">
                <li :class="['tabTag', { active: item.check }]" v-for="(item, i) in sxDate" :key="i" @click="changeTab(item)">{{ item.title }}</li>
            </ul> -->
        </p>
        <div class="zyqk-wrap">
              <div class="zyqk-wrap-top flex-box-cs">
                <div class="zyqk-wrap-top-right" style="position: relative;right: -10px;"><img src="@/assets/images/header/ksrs.png" style="width: 60px;margin-right: 8px;" /></div>
                <div class="zyqk-wrap-top-left rb">
                    <p class="dqzy">新入所人数</p>
                    <p class="curTotal">{{ curNewTotal }}<span class="dw-r">人</span></p>
                </div>
                <div v-if="daysData && daysData.length>0">
                   <p v-for="(item,index) in daysData" :key="index" class="flex-box-cs" style="width: 160px;"><span class="dayTitle">{{ item.dayTitle }}</span><span class="dayNum">{{ item.num }}<span class="dw-r">人</span></span></p>
                </div>
                <div v-else><noEmpty /></div>
             </div>
             <div class="zyqk-wrap-top flex-box-cs">
                <div class="zyqk-wrap-top-right" style="position: relative;right: -10px;"><img src="@/assets/homeSite/2.png" style="width: 60px;margin-right: 8px;" /></div>
                <div class="zyqk-wrap-top-left">
                    <p class="dqzy">出所人数</p>
                    <p class="curTotal">{{ curTotal }}<span class="dw-r">人</span></p>
                </div>
             </div>
        </div>
        <div class="zyqk-wrap" style="height: 55%">
             <div style="margin-top: 10px;height: 100%;width: 100%;overflow: overlay;background:transparent" class="csqk-box">
                <detainees :itemData="lineData" />
             </div>

        </div>
        <Spin size="large" fix v-if="spinShow"></Spin>

    </div>
</template>

<script>
import { getUserCache, formatDateparseTime } from '@/libs/util'
import detainees from './detainees'
import noEmpty from "@/components/bsp-empty"
export default {  
    components:{detainees,noEmpty}, 
   data(){
    return{
        appCode:serverConfig.APP_CODE,
        curTotal:0,
        curTotalCs:0,
        curN:0,
        curL:0,
        ljjy:0,
        curcs:0,
        nPercent:0,
        curNewTotal:0,
        sxDate: [
        { title: '今日', check: true },
        { title: '昨日', check: false },
        { title: '本周', check: false },
        { title: '本月', check: false }
      ],
      dataArr:[],
      dataAll:[],
      spinShow:false,
      yyDicList:[],
      daysData:[
        {dayTitle:'入所1-3天',num:5,key:'t13'},
        {dayTitle:'入所4-7天',num:15,key:'t47'},
        {dayTitle:'入所8-15天',num:5,key:'t815'},
      ],
      lineData:{
        month:[],
        csData:[],
        rsData:[]
      }

    }
   },
   mounted(){
    this.getData(this.globalAppCode+':sy-crsdt-jrcsrs','curTotal')
    this.getData(this.globalAppCode+':sy-crsdt-xrsrs','daysData')
    this.getData(this.globalAppCode+':sy-crsdt-yf','month')
    this.getData(this.globalAppCode+':sy-crsdt-csbhqs','csData')  //出所变化趋势
    this.getData(this.globalAppCode+':sy-crsdt-rsbhqs','rsData')  //入所变化趋势
   },
   methods:{

    getData(mark,filed) {
        this.spinShow=true
        let params={
          modelId: mark
        }
          this.$store.dispatch("postRequest", {
            url:this.$path.get_query_grid,
            params: params,
          }).then(res => {
            if (res.success) {
                this.spinShow=false
                if(filed=='daysData'){
                  if(res.rows && res.rows.length>0){
                    for(let i in res.rows[0]){
                        this.daysData.forEach(item=>{
                          if(i==item.key){
                            this.$set(item,'num',res.rows[0][i])
                          }
                        })
                    }
                    this.$set(this,'curNewTotal',res.rows[0].zs)
                  }else{
                    this.$set(this,'curNewTotal',0)
                  }
                }else if(filed=='curTotal'){
                  this.$set(this,filed,res.rows && res.rows.length>0 && res.rows[0].jrcsrs?res.rows[0].jrcsrs:0)
                }else{
                   this.$set(this.lineData,filed,res.rows?res.rows:[])
                }
            }else{
              this.spinShow=false
            }
          })
      },
   }
}
</script>

<style lang="less" scoped>
.dw-r{
  font-size: 14px;
  font-weight: 300;
}
.zyqk-wrap-pie{
    width: 100%;
    height: 50%;
    margin-top: 8px;
}
.zyqk-wrap{
    // background: #DDF5EE;
    border-radius: 4px 4px 4px 4px;
    margin: 16px 0px 16px 0;
    padding: 0 16px 8px;
    display: flex;
    justify-content: space-between;
}
.dqzy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
    // line-height: 50px;
}
.curTotal{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 24px;
    color: #2B3646;
    line-height: 30px;
}
.flex-box-cs{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.curNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 20px;
    color: #2B3346;
    line-height: 28px;
}
/deep/ .ivu-progress-success-bg{
    background:  linear-gradient( 90deg, #5284E1 0%, #97B9E8 100%) !important;
}
.ljjy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #2B3346;
}
.tabList {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #CFD9F0;
    display: flex !important;
    justify-content: space-between;
    display: inline-block;
    // height: 24px;
    .tabTag {
      cursor: pointer;
      width: 60px;
    //   height: 24px;
      background: #fafafa;
      text-align: center;
    //   margin-top: -2px;
      display: inline-block;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #4c6a99;
    //   line-height: 28px;
    }
    .active {
      background: #538ef9 !important;
      color: #fff !important;
    }
  }
  .yy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #2B3646;
    // line-height: 40px;
  }
  .yyNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
  }
  .zyqk-wrap-top{
    background: linear-gradient( 193deg, #FFFFFF 0%, #EEFAF6 100%);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(206,224,240,0.7);
    padding: 16px;
  }
  .rb{
    margin-right: 16px;
    border-right: 1px solid #E9EDF5;
    padding-right: 16px;
  }
  .dayTitle{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
  }
  .dayNum{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 16px;
    color: #2B3346;
  }
</style>