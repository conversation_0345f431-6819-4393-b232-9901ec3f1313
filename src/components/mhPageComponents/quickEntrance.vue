<template>
    <div class="todayUpdates">
        <div class="home-title">
        <span>快捷入口</span>
        </div>
        <div class="quickEntrance-box">
            <p v-for="(item,index) in dataArr" :key="index">
                <img :src="item.imgUrl" style="width: 60px;height: 50px;"  />
                <span>{{ item.title}}</span>
            </p>
        </div>
    </div>
</template>

<script>
export default {
    data(){
        return{
            dataArr:[
                {title:'提询/讯',imgUrl:require('@/assets/images/window/1.png'),path:''},
                {title:'提解',imgUrl:require('@/assets/images/window/2.png'),path:''},
                {title:'律师会见',imgUrl:require('@/assets/images/window/3.png'),path:''},
                {title:'家属会见',imgUrl:require('@/assets/images/window/4.png'),path:''},
                {title:'使馆领事会见',imgUrl:require('@/assets/images/window/5.png'),path:''},
                {title:'物品顾送',imgUrl:require('@/assets/images/window/6.png'),path:''},

            ]
        }
    }
}
</script>

<style lang="less" scoped>
.quickEntrance-box{
    display: flex;
    align-content: center;
    margin:8px 16px;
    p{
        width: 168px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        background: rgba(224, 240, 255, 0.5);
        border-radius: 4px 4px 4px 4px;
    }
}
</style>