<template>
   <div class="home-com" style="height: 100%;">
        <p class="home-title">重点关注</p>
       <Tabs :value="tabValue" key="12122" style="margin-top: 16px;">
        <TabPane :label="item.title" :name="index+''" v-for="(item,index) in tabArr" :key="index+'2tabArr'" >
            <div class="ryData-main" style="height:180px;background:#fff;display: flex;margin: 5px 5%;">
                <div v-for="(item,index) in ryData" :key="index+'messageData'" :class="['ryData-wrap',item.typeName]">
                     <div class="" style="margin:16px 0px 16px 16px;text-align: center;">
                        <div style="width: 82px;height: 110px;">
                            <img :src="item.img" style="width: 82px;height: 110px;"  />
                            <span class="dcfz">{{ item.name }}</span>
                        </div>
                        <p class="zm">{{ item.zm }}</p>
                     </div>

                       <!-- <Carousel v-model="valueCarousel" loop>
                        <CarouselItem v-for="(ele,index) in item.children" :key="index+'children'">
                            <div class="demo-carousel">
                                <div>{{ele.name}}</div>
                                <div>{{ele.name}}</div>
                            </div>
                        </CarouselItem>
                    </Carousel>               -->
                </div>
            </div>
        </TabPane>
    </Tabs>
    </div>
</template>

<script>
export default {
    data(){
        return{
            tabValue:'1',
            valueCarousel:0,
            tabArr:[
                {title:'A01监室',},
                {title:'A02监室',},
                {title:'A03监室',},
                {title:'A04监室',},                
                {title:'A05监室',},                
                {title:'A06监室',},
                {title:'A07监室',},                
                {title:'A08监室',},                
                {title:'A09监室',},
            ],
            ryData:[
                {name:'万达里',zm:'多次犯罪',img:require('@/assets/images/zdgz/100.jpg')},
                {name:'马克',zm:'酒驾',img:require('@/assets/images/zdgz/101.jpg')},
                {name:'威廉',zm:'打架斗殴',img:require('@/assets/images/zdgz/102.jpg')},
                {name:'好来',zm:'逃逸',img:require('@/assets/images/zdgz/103.jpg')},
                 {name:'马聪',zm:'抢劫',img:require('@/assets/images/zdgz/104.jpg')},
                {name:'杜小曼',zm:'逃学',img:require('@/assets/images/zdgz/105.jpg')},
                 {name:'都语儿',zm:'滋事',img:require('@/assets/images/zdgz/106.jpg')},
 
            ]
        }
    }
}
</script>

<style >

.ryData-wrap{
    height: 100%;
}
.demo-carousel{
    height: 100%;
}
.dcfz{
    height: 24px;
    background: #E9F0FF;
    border-radius: 0px 0px 4px 4px;
    display: inline-block;
    width: 100%;
    text-align: center;
    position: relative;
    top: -24px;
    color: #4B81FF;
}
.zm{
    line-height: 50px;
}
</style>