<template>
    <div class="meetingRoomStatus-wrap">
        <div class="home-title">
        <span>会见室状态</span>
        </div>
        <div class="meetingRoomStatus-wrap-room">
            <div v-for="(item,index) in roomData" :key="index" class="meetingRoomStatus-wrap-room-child">
                <div class="flex-meeting-room">
                    <span class="roomNameType">{{ item.roomType }}</span>
                    <div>
                        <span style="margin-left: 6px; font-size: 14px; background: #FFF9ED; border-radius: 4px 4px 4px 4px; font-size: 14px; color: #FF8A18; display: inline-block; padding: 2px 4px;">使用中&nbsp;{{ item.inuseCount }}</span>
                        <span style="margin-left: 6px; font-size: 14px; border: 1px solid #CADAEA; border-radius: 4px 4px 4px 4px; font-size: 14px; color: #1ABD79; display: inline-block; padding: 2px 4px;">空闲&nbsp;{{ item.idleCount }}</span>
                        <span style="margin-left: 6px; font-size: 14px; background: #EFF1F5; border-radius: 4px 4px 4px 4px; font-size: 14px; color: #8D99A5; display: inline-block; padding: 2px 4px;">暂停使用&nbsp;{{ item.pauseCount }}</span>
                    </div>
                </div>
                <div class="meetingRoomStatus-wrap-room-flex">
                    <span v-for="(ele,i) in item.roomList" :key="i+'bbb'" :class="['roomName','room'+ele.status]">{{ ele.roomName }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data(){
        return{
            roomData:[
                {
                    type:'01',
                    roomNameType:'提讯/询室',
                    statusArr:[
                        {roomStatusName:'使用中',roomStatus:'01',num:3},
                        {roomStatusName:'空闲',roomStatus:'02',num:1},
                        {roomStatusName:'暂停使用',roomStatus:'03',num:31},
                    ],
                    children:[
                        {roomName:'0410',roomStatus:'01'},{roomName:'0411',stastus:true},{roomName:'0412',stastus:true},
                        {roomName:'0413',roomStatus:'01'},{roomName:'0414',roomStatus:'02'},{roomName:'0415',roomStatus:'02'},
                        {roomName:'0416',roomStatus:'01'},{roomName:'0417',roomStatus:'02'},{roomName:'0418',roomStatus:'02'},
                        {roomName:'0419',roomStatus:'02'},{roomName:'0420',roomStatus:'02'},{roomName:'0421',roomStatus:'02'},
                    ]
                },
                {
                    type:'02',
                    roomNameType:'律师会见室',
                    statusArr:[
                        {roomStatusName:'使用中',roomStatus:'01',num:3},
                        {roomStatusName:'空闲',roomStatus:'02',num:1},
                        {roomStatusName:'暂停使用',roomStatus:'03',num:31},
                    ],
                    children:[
                        {roomName:'0410',stastus:true},
                        {roomName:'0411',stastus:true},
                        {roomName:'0412',stastus:true},
                        {roomName:'0413',roomStatus:'02'},
                        {roomName:'0414',roomStatus:'02'},
                        {roomName:'0415',roomStatus:'02'},
                        {roomName:'0416',roomStatus:'02'},
                        {roomName:'0417',roomStatus:'02'},
                        {roomName:'0418',roomStatus:'02'},
                        {roomName:'0419',roomStatus:'02'},
                        {roomName:'0420',roomStatus:'02'},
                        {roomName:'0421',roomStatus:'02'},
                    ]
                },
                {
                    type:'03',
                    roomNameType:'家属会见室',
                    statusArr:[
                        {roomStatusName:'使用中',roomStatus:'01',num:3},
                        {roomStatusName:'空闲',roomStatus:'02',num:1},
                        {roomStatusName:'暂停使用',roomStatus:'03',num:31},
                    ],
                    children:[
                        {roomName:'0410',stastus:true},{roomName:'0411',stastus:true},{roomName:'0412',stastus:true},
                        {roomName:'0413',roomStatus:'02'},{roomName:'0414',roomStatus:'02'},{roomName:'0415',roomStatus:'02'},
                        {roomName:'0416',roomStatus:'02'},{roomName:'0417',roomStatus:'02'},{roomName:'0418',roomStatus:'02'},
                        {roomName:'0419',roomStatus:'02'},{roomName:'0420',roomStatus:'02'},{roomName:'0421',roomStatus:'02'},
                    ]
                },
            ]
        }
    },
    created() {
        this.getMeetingRoomStatus()
    },
    methods: {
        getMeetingRoomStatus() {
            this.$store.dispatch('authGetRequest',{
                url: this.$path.acp_homeWorkbench_getMeetingRoomStatus
            }).then(res => {
                if(res.success) {
                    console.log(res,'res会见室状态');
                    
                    this.roomData = res.data
                } else {
                    this.$Modal.error({
                        title: '温馨提示',
                        content: res.msg || '接口操作失败!'
                    })
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
.meetingRoomStatus-wrap-room{
    width: 100%;
    display: flex;
    margin: 16px 0 16px 10px;
    .meetingRoomStatus-wrap-room-child{
        width: 32%;
        padding: 0 3px;
        margin-right: 10px;
        background: linear-gradient( 167deg, #FFFFFF 0%, #F0F8FF 100%);
        border-radius: 4px 4px 4px 4px;
        border: 1px solid rgba(206,224,240,0.7);
        // height: ;
    }
}
.flex-meeting-room{
    width: 100%;
    display: flex;
    border-bottom: 1px solid #E4EAF0;
    padding: 6px 12px;

}
.room0{
    color: #1ABD79;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #CADAEA;
}
.room1{
    color: #FF8A18;
    background: #FFF9ED;
}
.room2{
    color: #8D99A5;
    background: #EFF1F5;
}
.roomNameType{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 16px;
    color: #00244A;
    text-align: left;
}
.meetingRoomStatus-wrap-room-flex{
   display: flex;
   width: 100%;
//    min-height: 120px;
   flex-wrap: wrap;
   margin-top: 6px;
   .roomName{
     width: 23%;
     margin: 0 5px 6px 0;
     display: inline-block;
     padding: 4px 3px;
     text-align: center;
     cursor: pointer;
     font-size: 14px;
   }
    .roomName:nth-of-type(5n){
       margin-right: 0 !important;
      }
}
</style>