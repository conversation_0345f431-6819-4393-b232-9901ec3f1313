/**列表 */
<template>
  <div class="xxzx-list">
    <ul style="height: calc(100vh - 220px)">
      <li v-for="(item, index) in listData" :key="index">
        <div>
          <!-- <p @click="handleOpenUrl(item.url.indexOf('pcid') > -1 ? item.url + '&id=' + item._id : item.url + '&id=' + item._id + '&pcid=' + item.pcid, item)"> -->
          <p @click="handleOpenUrl(item.url, item)">
            <span v-if="item.busTypeName" class="textOverflow" :title="item.busTypeName">{{ item.busTypeName }}</span>
            <span v-else>暂无类型</span>
            {{ item.title }}
          </p>
  
        </div>
        <div class="msg-content">
          <p style="width: 17%">来源人：{{ item.fUserName }}</p>
          <p style="width: 30%">业务模块：{{ item.systemMarkName }}</p>
          <p style="width: 28%" class="textOverflow" :title="item.fOrgName">来源单位：{{ item.fOrgName }}</p>
          <p style="width: 25%">发送时间：{{ item.sTime }}</p>
        </div>
      </li>
    </ul>
    <!-- <template style="margin: 20px 0">
      <div class="page_box">
        <div style="display: flex; align-items: center">
          <Page
            :total="total"
            @on-change="handlePage"
            :current="page.pageNo"
            @on-page-size-change="changePageSize"
            :page-size="page.pageSize"
            :page-size-opts="page.pageSizeOpts"
            size="small"
            show-elevator
            show-sizer
            style="display: inline-block; margin: 10px 0"
          />
          <span class="Total">共 {{ total }} 条 </span>
        </div>
      </div>
    </template> -->

    <s-general-audit
      v-if="actInstId"
      :key="timer"
      ref="approvalAudit"
      @audit-close="audit_close"
      :showFileUpload="false"
      :beforeOpen="beforeOpen"
      :actInstId="actInstId"
      :showcc="false"
      :businessId="getBussinId"
      :module="module"
      :extraOrgId="extraOrgId"
      :extraRegId="extraRegId"
      extraCityId=""
      :variables="variables"
      :selectUsers="selectUsers"
      :modalWidth="modalWidth"
      :auditComplete="approvalSuccess"
      :approvalContent="approvalContent"
    >
    </s-general-audit>
  </div>
</template>

<script>
import { mapActions } from "vuex";
// import cryptoObj from '@/libs/crypto.js'
import { getUserCache, getToken } from "@/libs/util";
import { sGeneralAudit } from "gxx-general-audit";
import { parseQuery } from "@/libs/util.js";

export default {
  name: "xxzx-list",
  components: { sGeneralAudit },
  props: {
    listData: {
      type: Array,
      default() {
        return [];
      },
    },
    msgType: {
      type: String,
      default: "",
    },
    total: {
      type: Number,
      default: 0,
    },
    pageCurrent: {
      type: Number,
      default: 0,
    },
    isRead: {
      type: String,
      default: "-1",
    },
  },
  watch: {
    isRead() {
      //console.log(this.isRead)
    },
    pageCurrent() {
      this.page.pageNo = this.pageCurrent;
    },
  },
  data() {
    return {
      approvalContent: "同意调解，按有关规定办理",
      variables: {},
      globalCheck: false,
      customSelectFlag: false,
      single: true,
      btnBool: true,
      page: {
        pageNo: this.pageCurrent,
        pageSize: 5,
        pageSizeOpts: [10, 20, 50, 100],
      },
      iframeUrlFa: serverConfig.iframeUrlFa,
      actInstId: "",
      getBussinId: "",
      module: "acp",
      modalWidth: "600",
      timer: "",
      selectUsers: "",
      extraOrgId: this.$store.state.common.orgCode,
      extraRegId: this.$store.state.common.regCode,
      paramsSubmit: {},
      eventCode: "",
    };
  },
  methods: {
    ...mapActions(["authGetRequest", "authPostRequest", "postRequest"]),
    audit_close() {
      this.$refs.approvalAudit.isOpen = false;
      this.getBussinId = "";
      this.actInstId = "";
      // this.showAudit=false
      // 重新加载子组件
      this.timer = new Date().getTime();
    },
    openAuidt(row) {
      this.timer = new Date().getTime();
      this.showAudit = true;
      this.getBussinId = row._id;
      this.actInstId = row.actInstId;
      this.eventCode = parseQuery(row.url).eventCode;
      //console.log(row,'row')
      setTimeout(() => {
        this.$refs["approvalAudit"].openAudit();
      }, 500);
    },
    beforeOpen() {
      return new Promise((resolve, reject) => {
        resolve(true);
      });
    },
    approvalSuccess(data) {
      //console.log(data,data.data.bpmTrail)
      this.handleSubmit(data.data.bpmTrail);
    },
    handleSubmit(data) {
      this.paramsSubmit.approvalComments = data.approvalContent;
      this.paramsSubmit.approvalResult = data.isApprove;
      this.paramsSubmit.approverSfzh = data.executeUserId;
      this.paramsSubmit.approverTime = data.endTime;
      this.paramsSubmit.approverXm = data.executeUserName;
      this.paramsSubmit.eventCode = this.eventCode;
      this.$store
        .dispatch("authPostRequest", {
          url: this.$path.app_socialconflict_approve,
          params: this.paramsSubmit,
        })
        .then((res) => {
          if (res.success) {
            this.$Notice.success({
              title: "成功提示",
              desc: res.msg || "审批成功",
            });
            this.$emit("reFresh");
            // this.on_refresh_table()
          } else {
            this.$Notice.error({
              title: "失败提示",
              desc: res.msg || "审批失败",
            });
          }
        });
    },
    // 标记
    init() {
      this.page.pageNo = 1;
    },

    handleTag() {
      this.btnBool = !this.btnBool;
      this.listData.forEach((msg) => {
        msg.checked = false;
      });
    },
    selectAll() {
      if (this.customSelectFlag) {
        this.listData.forEach((msg) => {
          msg.checked = this.globalCheck;
        });
      } else {
        this.listData.forEach((msg) => {
          msg.checked = !msg.checked;
        });
      }
      this.customSelectFlag = false;
      this.$nextTick(() => {
        this.$emit("selectAll", this.listData);
      });
    },
    handleRead(isRead) {
      const idArray = this.listData
        .filter((msg) => msg.checked === true)
        .map((x) => x._id);
      //console.log(idArray)
      if (idArray.length == 0) {
        this.warningModal({ content: "请勾选一个" });
        return;
      }
      let param = {
        ids: idArray.join(","),
        msgType: this.msgType,
        isRead: isRead,
      };
      this.authGetRequest({
        url: this.$path.icp_ajcz_msgUpdate,
        params: param,
      }).then((data) => {
        if (data.success) {
          this.successModal({ content: data.msg }).then(async () => {
            this.$emit("reFresh");
            this.btnBool = true;
          });
        } else {
          this.errorModal({ content: data.msg });
        }
      });
    },
    thBack(){

    },
    handleOpenUrl(url, item) {
      /*        let path = url.split("?")[0]
        let params = url.split("?")[1]
        let param = params.split("&")
        let obj = {}
        param.forEach(item => {
          let key = item.split("=")[0]
          let value = item.split("=")[1]
          obj[key] = value
        })
        let appendStr = this.$Base64.encode(obj)
        let target = `${path}?params=${appendStr}`
        window.open(target)*/
      // let urlPath = this.getUrl(url, item)
      // if (urlPath) window.open(urlPath)
      this.$emit("handleOpenUrl", url, item);
      // // this.$set(this.rowData,'eventCode','')
      // return
    },
    getUrl(url, item) {
      let drupUrl = url;
      switch (item.systemMark) {
        case "icp":
          drupUrl = url;
          break;
        // case 'sacw':
        //   let obj = {
        //     redirect: url,
        //     idCard: getUserCache.getIdCard()
        //   }
        //   Object.keys(obj).forEach(item => (obj[item] = cryptoObj.encrypt(obj[item])))
        //   const { redirect, idCard } = obj
        //   drupUrl = `${PymServerConfig.PYM_APP_URL}/#/singleSignOn?redirect=${redirect}&idCard=${idCard}`
        //   break
        case "zfjd":
          drupUrl =
            this.iframeUrlFa +
            url.split("?")[0] +
            "?singleSignOnToken=" +
            getToken() +
            "&" +
            url.split("?")[1];
          // drupUrl = serverConfig.iframeUrlFa + cryptoObj.encrypt(getToken()) + '&redirect=' + cryptoObj.encrypt(url.split('?')[0].replace('#','')) + url.split('?')[1] //`http://10.89.12.33:58300/#/externalLogin?token=` + cryptoObj.encrypt(getToken())
          break;
      }
      return drupUrl;
    },
    // 翻页
    changePageSize(pageSize) {
      this.page.pageNo = 1;
      this.page.pageSize = pageSize;
      this.searchListData();
    },
    handlePage(pageNo) {
      // //console.log(pageNo)
      this.page.pageNo = pageNo;
      this.searchListData();
    },
    searchListData() {
      // //console.log(this.search)
      let param = {
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
      };
      this.$emit("page", param);
    },
    customSelect(result) {
      this.globalCheck = result;
      this.customSelectFlag = true;
    },
  },
};
</script>

<style lang="less" scoped>
.textOverflow {
  // width: 200px; /* 设定一个宽度限制 */
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 溢出时显示省略号 */
}
.xxzx-list {
  padding: 0px 15px;
  ul {
    overflow: auto;
    padding-right: 5px;

    li {
      padding: 10px 25px;
      background: #fff;
      border-bottom: 1px solid #ebf0f5;
      cursor: pointer;

      &:hover,
      &.active {
        background: #f0f6fc;
      }

      > div {
        &:first-child {
          display: flex;
          justify-content: space-between;

          > p {
            &:first-child {
              font-size: 16px;
              font-weight: 700;
              text-align: left;
              color: #333333;

              > span {
                /*display: inline-block;*/
                width:200px;
                height: 24px;
                line-height: 24px;
                /*background: #e5efff;*/
                /*font-size: 14px;*/
                font-weight: 700;
                text-align: left;
                /*color: #3179f5;*/
                margin-right: 15px;
              }
            }

            &:last-child {
              display: flex;
              align-items: center;

              > span {
                font-size: 16px;
                font-weight: 400;
                color: #333333;
                margin-right: 25px;
                position: relative;

                &.active {
                  &::after {
                    content: "";
                    width: 12px;
                    height: 12px;
                    background: #e60012 !important;
                    display: inline-block;
                    border-radius: 50px;
                    position: absolute;
                    left: -25px;
                    top: 6px;
                  }
                }

                // &::after {
                //   content: "";
                //   width: 12px;
                //   height: 12px;
                //   background: green;
                //   display: inline-block;
                //   border-radius: 50px;
                //   position: absolute;
                //   left: -25px;
                //   top: 6px;
                // }
              }

              /deep/ .ivu-checkbox-wrapper {
                position: relative;
                top: 2px;

                .ivu-checkbox-inner {
                  width: 20px;
                  height: 20px;
                  background: #ffffff;
                  border-radius: 4px;
                }

                &.ivu-checkbox-wrapper-checked {
                  .ivu-checkbox-inner {
                    background: #2b5fda;

                    &:after {
                      width: 7px;
                      height: 12px;
                    }
                  }
                }

                .ivu-checkbox-disabled {
                  .ivu-checkbox-inner {
                    border-color: #dcdee2 !important;
                    background-color: #f5f6fa !important;
                  }
                }
              }
            }
          }
        }

        &:nth-child(2) {
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
          /*padding: 0 25px;*/

          > P {
            font-size: 16px;
            font-weight: 400;
            text-align: left;
            color: #666666;
          }
        }
      }
    }
  }

  .page_box {
    border-top: none;
    height: 50px;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: flex-end;
    background: #f0f3fa;
    margin-top: 8px;

    /deep/ .ivu-page {
      .ivu-page-item-active {
        width: 32px;
        height: 26px;
        line-height: 28px;
        background: #3179f5;
        border-radius: 2px;
      }

      .ivu-page-item {
        background: none;
        margin: 0 5px;
      }

      .ivu-page-prev,
      .ivu-page-next {
        background: none;

        .ivu-icon {
          font-size: 18px;
          position: relative;
          top: 2px;
        }
      }
    }

    .Total {
      display: inline-block;
      height: 24px;
      line-height: 24px;
      vertical-align: top;
      margin: 10px 0px;
      padding: 0 15px;
      border-radius: 2px;
      cursor: pointer;
      font-size: 15px;
    }

    /deep/ .btn-box {
      display: flex;
      align-items: center;

      > span {
        display: inline-block;
        width: 2px;
        height: 24px;
        background: #cccccc;
        margin-right: 15px;
      }

      .ivu-btn-default {
        border: 1px solid transparent;
        border-color: #dcdee2;
        color: #333333 !important;
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        margin-right: 10px;

        &:hover {
          border: 1px solid #2b5fd9;
          color: #2b5fd9 !important;
        }
      }

      .ivu-btn-primary {
        font-size: 16px;
        font-weight: 400;
        margin-right: 10px;
      }
    }
  }
}
</style>
