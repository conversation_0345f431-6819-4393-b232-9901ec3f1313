<template>
  <div class="icp-common-form">
    <div class="icp-common-content" style="border: 1px solid #cee0f0">
      <Form ref="kzForm" :model="formData" :label-width="200" :label-colon="true" class="base-form-container">
        <Row>
          <Col span="12">
            <FormItem label="出警时间" prop="cjsj">
              <el-date-picker
                v-model="formData.cjsj"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="cjsjOptions"
                class="date-input"
                size="small"
                placeholder="选择日期"
                @change="handleCjsj"
              ></el-date-picker>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="到达现场时间" prop="ddxcsj">
              <el-date-picker
                v-model="formData.ddxcsj"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="ddxcsjOptions"
                class="date-input"
                size="small"
                placeholder="选择日期"
                @change="handleDdsj"
              ></el-date-picker>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="12">
            <FormItem label="出动警力" prop="cdjlrs" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.cdjlrs" placeholder="" maxlength=""><span slot="append">人</span></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="出动机动车" prop="cdjdcsl" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.cdjdcsl" placeholder="" maxlength=""> <span slot="append">辆</span></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="12">
            <FormItem label="出动航空器" prop="cdhkqsl" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.cdhkqsl" placeholder="" maxlength=""><span slot="append">架</span></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="出动船只" prop="cdczsl" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.cdczsl" placeholder="" maxlength=""><span slot="append">艘</span></Input>
            </FormItem>
          </Col>
        </Row>
        <!--        <Row>-->
        <!--          <Col span="12">-->
        <!--            <FormItem label="派出警员姓名" prop="pcjyxm">-->
        <!--              <Input v-model="formData.pcjyxm" placeholder="" maxlength="" />-->
        <!--            </FormItem>-->
        <!--          </Col>-->
        <!--          <Col span="12">-->
        <!--            <FormItem label="处警人" prop="cjr">-->
        <!--              <Input v-model="formData.cjr" placeholder="" maxlength="" />-->
        <!--            </FormItem>-->
        <!--          </Col>-->
        <!-- <Col span="12">
            <FormItem label="处警单位" prop="cjdwdm">
              &lt;!&ndash; <Input v-model="formData.cjdwdm" placeholder="" maxlength="" />&ndash;&gt;
              <s-dicgrid v-model="formData.cjdwdm" :isSearch="false" dicName="ZD_ORG_ID" />
            </FormItem>
          </Col> -->
        <!--        </Row>-->
        <!-- <Row>
          <Col span="12">
            <FormItem label="处警人身份证号" prop="cjrsfzh">
              <Input v-model="formData.cjrsfzh" placeholder="" maxlength="" />
            </FormItem>
          </Col>
        </Row> -->
        <Row>
          <Col span="12">
            <FormItem label="受伤人数" prop="ssrs" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.ssrs" placeholder="" maxlength=""><span slot="append">人</span></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="死伤人数" prop="swrs" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.swrs" placeholder="" maxlength=""><span slot="append">人</span></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="12">
            <FormItem label="救助受伤人数" prop="jzssrs" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.jzssrs" placeholder="" maxlength=""><span slot="append">人</span></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="解救被拐卖儿童" prop="jjbgmetrs" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.jjbgmetrs" placeholder="" maxlength=""><span slot="append">人</span></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="12">
            <FormItem label="解救被拐卖妇女" prop="jjbgmfnrs" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.jjbgmfnrs" placeholder="" maxlength=""><span slot="append">人</span></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="解救群众人数" prop="jjqzrs" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.jjqzrs" placeholder="" maxlength=""><span slot="append">人</span></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="12">
            <FormItem label="直接经济损失" prop="zjjjss" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.zjjjss" placeholder="" maxlength=""><span slot="append">元</span></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="涉案财物总值" prop="sacwzz" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.sacwzz" placeholder="" maxlength=""><span slot="append">元</span></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="12">
            <FormItem label="逃犯人数" prop="tfrs" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.tfrs" placeholder="" maxlength=""><span slot="append">人</span></Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="抓获的人数" prop="zhrs" :rules="{ validator: this.$tools.validateNumOnly, message: '请正确填写数字' }">
              <Input v-model="formData.zhrs" placeholder="" maxlength=""><span slot="append">人</span></Input>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="24">
            <FormItem label="损失情况" prop="ssqk" >
              <Input v-model="formData.ssqk" placeholder="" type="textarea" :rows="4" maxlength="" />
            </FormItem>
          </Col>
        </Row>
      </Form>

      <!-- <div class="tz-btn">
        <Button @click="close">关闭</Button>
        <Button type="primary" :loading="custom_loading" @click="handleSubmit()">提交</Button>
      </div> -->
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch:{
    'formData.cjsj': {
      handler() {
        if (this.formData.cjsj) {
          // this.handleCjsj();
        }
        this.handleDdsj()
      },
      immediate: true
    },

    'formData.ddxcsj': {
      handler() {
        if (this.formData.cjsj) {
          // this.handleCjsj();
        }
        this.handleDdsj()
      },
      immediate: true
    },
  },
  data() {
    return {
      cjsjOptions: {
        disabledDate(date) {
          return date && date.valueOf() > new Date()
        }
      },
      ddxcsjOptions: {
        disabledDate(date) {}
      },
      custom_loading: false
    }
  },
  methods: {
    ...mapActions(['authGetRequest', 'authPostRequest']),
    handleSubmit() {
      this.authPostRequest({
        url: this.$path.new_jqcz_save,
        params: this.formData
      }).then(resp => {
        if (resp.success) {
          this.successModal({ content: resp.msg })
        } else {
          this.errorModal({ content: resp.msg || '保存失败' })
        }
      })
    },
    // 计算时间
    handleCjsj() {
      let _this = this
      if (new Date(_this.formData.cjsj).getTime() > new Date(_this.formData.ddxcsj).getTime()) {
        _this.formData.ddxcsj = ''
      }
      this.ddxcsjOptions = {
        disabledDate(date) {
          let delay = _this.formData.cjsj
          // //console.log(date.valueOf() , new Date(delay).getTime(),date.valueOf() < new Date(delay).getTime(),'21212')
            // return date && (date.valueOf() < new Date(delay).getTime() || date.valueOf() >= (new Date(new Date().toLocaleDateString()).getTime()  + 24 * 60 * 60 * 1000))
                   return date && ( date.valueOf() >= (new Date(new Date().toLocaleDateString()).getTime()  + 24 * 60 * 60 * 1000))

          }
      }
    },
    handleDdsj() {
      let _this = this
      if ((new Date(_this.formData.cjsj).getTime() > new Date(_this.formData.ddxcsj).getTime())&&(_this.formData.cjsj)&&(_this.formData.ddxcsj)) {
        _this.formData.ddxcsj = ''
        this.infoModal({ content: "到达时间不能早于出警时间" })
      }
    },
    close() {
      this.$emit('handleClose')
    }
  }
}
</script>
