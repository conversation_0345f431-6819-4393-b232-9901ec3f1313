<template>
    <div class="drawer-wrapper">
      <Drawer class-name="history" v-model="val" @on-close="onClose" :width="1300" :closable="false">
        <div class="title" style="margin-bottom: 15px">
          <div class="title-container">
            <span>扩展信息</span>
          </div>
        </div>
        <div class="fl-container icp-common-form">
          <div class="icp-common-content">
            <tz ref="tzForm" :formData="formData" @handleClose="handleClose"></tz>
          </div>
        </div>
        <div class="icp-base-footer" v-if="val">
          <Button style="margin: 0 20px" @click="handleClose()">关 闭</Button>
          <Button style="margin: 0 20px" type="primary" :loading="loading" @click="handleSubmit()">保 存</Button>
        </div>
      </Drawer>
    </div>
  </template>
  
  <script>
  import { mapActions } from 'vuex'
  import tz from './tz'
  export default {
    components: {
      tz
    },
    props: {
      formData: {
        type: Object,
        default() {
          return {}
        }
      },
      drawerVal: {
        type: Boolean,
        default() {
          return false
        }
      }
    },
    watch: {
      drawerVal(val) {
        this.val = val
      }
    },
    data() {
      return {
        val: false,
        loading: false
        // bool: this.drawerVal
      }
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
      ...mapActions(['authGetRequest', 'authPostRequest']),
      onClose() {
        this.$emit('update:drawerVal', this.val)
      },
      handleClose() {
        this.val = false
        this.$emit('on_Close')
      },
      // 提交
      handleSubmit() {
        this.$refs.tzForm.handleSubmit() // 扩展信息
      }
    }
  }
  </script>
  <style lang="less" scoped>
  .icp-common-form {
    .icp-common-content {
      padding: 0 !important;
      height: calc(~'100vh - (60px + 60px + 30px)');
    }
  }
  
  .icp-base-footer {
    text-align: center;
    position: absolute;
    bottom: 0;
    width: 100%;
    background: #fbfcfd;
    height: 60px;
    line-height: 60px;
    z-index: 2;
  }
  /deep/ .ivu-drawer-body {
    overflow: hidden;
  }
  
  /deep/ .ivu-tabs-bar {
    margin-bottom: 0px;
  }
  
  /deep/ .ivu-tabs-nav-container {
    line-height: 2;
    font-size: 16px;
    font-weight: bold;
    color: #2b3646;
  }
  
  /deep/ .ivu-tabs-nav .ivu-tabs-tab:hover {
    color: #2b3646;
  }
  
  /deep/ .ivu-tabs-nav .ivu-tabs-tab-active {
    color: #2b3646;
  }
  
  /deep/ .ivu-tabs-ink-bar {
    height: 4px;
    color: #2b5fd9;
  }
  </style>
  