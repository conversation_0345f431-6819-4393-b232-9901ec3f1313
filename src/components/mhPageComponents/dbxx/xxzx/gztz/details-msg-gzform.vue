/**基本信息 */
<template>
  <div class="icp-common-details base-form bsp-base-form icp-common-form">
    <police-history :formData="formData"></police-history>
    <!--    <div class="oper-container">-->
    <!--      <div class="oper-item logoper" @click="kzopen">-->
    <!--        <Icon type="ios-navigate-outline" size="20" />-->
    <!--        扩展信息-->
    <!--      </div>-->
    <!--    </div>-->

    <div class="background">
      <div class="title-1 first-title">
        <div>
          <img src="@/assets/images/title.png" alt="" />
          <span>关注案件案件信息</span>
        </div>
      </div>
      <div class="content">
        <Row>
          <Col span="8">
            <span>案件编号</span>
            <p>{{ formData.ajxx.ajbh }}</p>
          </Col>
          <Col span="8">
            <span>案件名称</span>
            <p>{{ formData.ajxx.ajmc }}</p>
          </Col>
          <Col span="8">
            <span>案件类型</span>
            <p>{{ formData.ajxx.ajlx }}</p>
          </Col>
        </Row>
        <Row>
          <Col span="12">
            <span>简要案情</span>
            <p>{{ formData.ajxx.jyaq }}</p>
          </Col>
          <Col span="12">
            <span>办案单位</span>
            <p>{{ formData.ajxx.badwmc }}</p>
          </Col>
        </Row>
      </div>
      <div class="title-1">
        <div>
          <img src="@/assets/images/title.png" alt="" />
          <span>通知消息</span>
        </div>
      </div>
      <div class="">
        <div class="content">
          <Row>
            <Col span="12">
              <span>标签类型</span>
              <p>{{ formData.icpAjbqGzList.gzbqmc }}</p>
            </Col>
            <Col span="12">
              <span>关注时间</span>
              <p>{{ formData.icpAjbqGzList.lrsj }}</p>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <span>关注原因</span>
              <p>{{ formData.icpAjbqGzList.reason }}</p>
            </Col>
          </Row>

          <Row>
            <Col span="12">
              <span>通知消息</span>
              <p>{{ formData.msgContent }}</p>
            </Col>
          </Row>
        </div>
      </div>
      <div class="title-1">
        <div>
          <img src="@/assets/images/title.png" alt="" />
          <span>取消关注</span>
        </div>
      </div>
      <div class='icp-common-content' style="padding:0 !important">
        <div class="base-form">
          <Form ref="formValidate" :model="formRequest" :label-width="180" :label-colon="true" class="base-form-container">
            <Row :gutter="10">
              <Col span="8">
                <FormItem label="是否取消" prop="sfqx" :rules="{ required: true, message: '不能为空', trigger: 'blur,change' }" >
                  <RadioGroup v-model="formRequest.sfqx">
                    <Radio label="1">是</Radio>
                    <Radio label="0">否</Radio>
                  </RadioGroup>
                  <!-- <label title="是">是</label>
                  <input type="radio" class="format-ctrl not-pie-item" name="sfqx" value="1" checked="checked" />
                  <label title="否">否</label>
                  <input type="radio" class="format-ctrl not-pie-item" name="sfqx" value="0" /> -->
                </FormItem>
              </Col>
            </Row>
            <Row :gutter="10">
              <Col span="18">
                <FormItem label="取消原因" prop="qxyy" name="qxyy" :rules="{ required: true, message: '取消原因不能为空' }" >
                  <!-- <textarea cols="100" rows="10" name="qxyy" v-model=""></textarea> -->
                  <Input v-model="formRequest.qxyy" type="textarea" :autosize="{minRows: 2,maxRows: 5}" placeholder=""></Input>
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    </div>

    <div class="icp-base-footer">
      <Button type="primary" :loading="custom_loading" @click="updateByIdFzSp('formValidate')">提交</Button>
      <!-- <Button type="primary" class="close-btn" @click="cancel">关闭</Button> -->
    </div>
  </div>
</template>

<script>
// import Process from '../../jqcz/zt/components/process.vue'
import { mapActions, mapMutations } from 'vuex'
// import { getUserCache } from '@/libs/util'
import { userAssign } from '@/components/selector/user-assign'
import PoliceHistory from './PoliceHistory.vue'

export default {
  name: 'AlertAjgzInfo',
  components: {
    userAssign,
    PoliceHistory
  },
  data() {
    return {
      custom_loading: false,
      formData: {
        ajxx:{},
        icpAjbqGzList:{}
      },
      formRequest: {sfqx:0}
    }
  },
  mounted() {
     this.handleEdit()
  },
  methods: {
    ...mapActions(['authGetRequest', 'authPostRequest', 'postRequest']),
    ...mapMutations(['setMassageTag']),
    // var url = window.location.href;
    // var id = this.$route.query.ywbh;
    handleEdit() {
      var id = this.$route.query.id
      var msgType = this.$route.query.msgType
      this.authGetRequest({
        url: `${this.$path.icp_ajgz_msg}` + '?id=' + id + '&msgType=' + msgType,
        params: {}
      }).then(data => {
        if (data.success) {
          this.$nextTick(() => {
            this.formData = Object.assign({}, JSON.parse(data.data))
            // 初始化日志
            setTimeout(() => {
              this.$tools.initForm(this.formData)
            }, 0)
          })
        }
      })
    },
    // 提交
    updateByIdFzSp(name) {
      this.$refs[name].validate((valid) => {
        //console.log(valid)
        if (valid) {
          this.custom_loading = true
          if (this.formRequest.sfqx===1){
            this.confirmModal({ content: '是否确认提交该表单' }).then(async () => {
              this.custom_loading =false

              this.authPostRequest({url:`${this.$path.icp_ajgz_cancel}`,
                params: {
                  reason: this.formRequest.qxyy,
                  ajbh: this.formData.ajxx.ajbh
                }
              }).then(data => {
                if (data.success) {
                  this.$nextTick(() => {
                    // 初始化日志
                    setTimeout(() => {
                      this.$tools.initForm(this.formData)
                    }, 0)
                  })
                }
              })
            })
          }

        } else {
          this.custom_loading = false
        }
      })

    },
    // 关闭
    cancel(){

    }
  }
}
</script>

<style lang="less" scoped>
.icp-common-details {
  .background {
    margin: 0;
    padding: 15px 15px 60px 15px;
    > div {
      .process {
        background: #f7f9fc;
        border: 1px solid #cee0f0;
        padding: 20px;
        margin-top: -1px;
        h6 {
          font-size: 16px;
          font-weight: 700;
          color: #333333;
        }
      }
    }
  }
}
</style>
