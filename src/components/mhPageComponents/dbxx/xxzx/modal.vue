/**弹窗 */
<template>
  <span>
    <Modal v-model="openStatus" :mask-closable="false" :closable="false" class-name="common-modal" :width="700">
      <div class="flow-modal-title" slot="header">
        <span style="font-size: 16px; font-weight: 400">精准查询</span>
        <span @click="cancel" style="position: absolute; right: 10px; font-size: 36px; cursor: pointer">
          <i class="ivu-icon ivu-icon-ios-close" style="position: relative; top: -3px"></i>
        </span>
      </div>
      <div class="icp-common-form">
        <div class="icp-common-content" style="padding: 20px!important">
          <div class="base-form" style="display: flex; justify-content: space-between; padding-bottom: 0" v-if="formData.wssh">
            <Form ref="jbForm" :model="formQuery" :rules="ruleValidate" :label-width="150" :label-colon="true" style="width: 90%; margin-right: 0px" class="base-form-container">
              <Row>
                <Col span="24">
                  <FormItem label="文书名称" prop="ywmc">
                    <Input v-model="formQuery.ywmc" placeholder="" maxlength="11" clearable />
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col span="24">
                  <FormItem label="案件名称" prop="ajmc">
                    <Input v-model="formQuery.ajmc" placeholder="" maxlength="11" clearable />
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col span="24">
                  <FormItem label="审核结果" prop="isApproveStr">
                    <Input v-model="formQuery.isApproveStr" placeholder="" maxlength="11"  clearable />
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col span="24">
                  <FormItem label="发送时间" prop="approvalTime" name="csrq">
                    <el-date-picker
                      v-model="formQuery.approvalTime"
                      type="daterange"
                      :picker-options="pickerOptions"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      class="date-input"
                      size="small"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      align="right"
                    >
                    </el-date-picker>
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </div>
          <div class="base-form" style="display: flex; justify-content: space-between; padding-bottom: 0" v-if="!formData.wssh">
            <Form ref="jbForm" :model="formQuery" :rules="ruleValidate" :label-width="150" :label-colon="true" style="width: 90%; margin-right: 0px" class="base-form-container">
              <Row>
                <Col span="24">
                  <FormItem label="消息标题" prop="xxbt">
                    <Input v-model="formQuery.xxbt" placeholder="" maxlength="11" clearable />
                  </FormItem>
                </Col>
              </Row>
               <Row>
                <Col span="24">
                  <FormItem label="业务模块" prop="systemMark">
                    <s-dicgrid v-model="formQuery.systemMark" dicName="ZD_APP" />
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col span="24">
                  <FormItem label="消息类型" prop="busType">
                    <s-dicgrid v-model="formQuery.busType" dicName ="ZD_MSG_BUSTYPE" />
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col span="24">
                  <FormItem label="发送时间" prop="fssj" name="csrq">
                    <el-date-picker
                      v-model="formQuery.fssj"
                      type="daterange"
                      :picker-options="pickerOptions"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      class="date-input"
                      size="small"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      align="right"
                    >
                    </el-date-picker>
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </div>

        </div>
      </div>
      <div slot="footer">
            <Button class="cancel-btn" @click="cancel()">关 闭</Button>
            <Button type="primary" class="cancel-btn main-button" @click="handleSearch()">查 询</Button>
          </div>
    </Modal>
  </span>
</template>

<script>
export default {
  name: 'Assistant',
  props: {
    openStatus: {
      type: Boolean,
      default() {
        return false
      }
    },
    formData: {
      type: Object,
      default() {
        return {
          xxbt: '',
          fssj: '',
          dqsj: '',
          systemMark: '',
          busType: '',
          fOrgName: ''
        }
      }
    }
  },
  watch: {
    formData() {
      //console.log(this.formData)
    }
  },
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      ruleValidate: {},
      formQuery: this.formData
    }
  },
  mounted() {},
  methods: {
    handleSearch() {
      this.$set(this.formQuery,'systemMark',serverConfig.APP_CODE)
      this.$emit('search', this.formQuery)
    },
    cancel() {
      this.$emit('update:openStatus', false)
    }
  }
}
</script>
<style lang="less" scoped>
.icp-hasside-footer {
  position: absolute;
  left: 0;
}
/deep/ .common-modal {
  .ivu-modal-body {
    min-height: 300px;
  }
}
</style>
