/**消息中心 */
<template>
       <div>
  <div class="xxzx-index" style="height: 100%;">
    <div class="main" v-if="showFormCompnent" style="height: 100%;">
      <div class="top">
        <div class="tabs-box">
          <Tabs v-model="searchParam.msgType" @on-click="switchMsgType">
            <TabPane :label="label_1" name="01"></TabPane>
            <TabPane :label="label_2" name="02"></TabPane>
          </Tabs>
          <span style="position: relative;top: -5px;cursor: pointer;" @click="drupMore">更多</span>
        </div>
      </div>
      <div class="content" style="height:80%;">
        <div v-show="listData.length > 0">
          <div >
            <list-content
              @handleOpenUrl="handleOpenUrl"
              ref="listContent"
              :listData="listData"
              :total="total"
              :isRead="isReadArr[searchParam.msgType]"
              :msgType="searchParam.msgType"
              :pageCurrent="pageObj[searchParam.msgType]"
              @page="handlePage"
              @selectAll="selectAll"
              @reFresh="handleSearch"
            />
          </div>
        </div>
        <div class="tips" style="height: calc(100% - 165px)" v-show="listData.length == 0">
          <!-- <img src="@/assets/images/no-people.png" alt="" /> -->
          <p>暂无消息</p>
        </div>
      </div>
    </div>
    <div v-if="showModal">
      <modal-view :openStatus.sync="showModal" :formData="modalArr[searchParam.msgType]" @search="modalSearch" />
    </div>

    <div v-if="!showFormCompnent" style="height: 86vh;overflow: auto;">
        <component v-bind:is='component' :isPs="true" left="0" @on_show_table="on_show_table"   :rowData="rowData" :modalTitle="modalTitle"></component>
    </div>
  </div>
  </div>
</template>

<script>
import headerWrap from "@/components/main/header.vue"
import listContent from './xxzx-list'
import wsshListContent from './wssh-list'
import modalView from './modal'
import { mapActions,mapMutations } from 'vuex'
import editForm  from "@/view/LedBy/representative/editForm.vue"
import {parseQuery,getToken} from "@/libs/util.js"

export default {
  name: 'xxzx-index',
  components: {
    listContent,
    modalView,
    wsshListContent,
    editForm,headerWrap
  },
  data() {
    return {
      appCode:serverConfig.APP_CODE,
      component:null,
      modalTitle:'社会矛盾调解-审批',
      rowData:{},
      showFormCompnent:true,
      label_1: h => {
        return h('div', [
          h('span', '待办消息'),
          h('Badge', {
            props: {
              showZero: true,
              count: this.countArray[0]
            }
          })
        ])
      },
      label_2: h => {
        return h('div', [
          h('span', '通知消息'),
          h('Badge', {
            props: {
              showZero: true,
              count: this.countArray[1]
            }
          })
        ])
      },

      showModal: false,
      spinShow: false,
      listData: [],
      wsshListData: [],
      total: 0,
      searchParamForTitle: {
        title: ''
      },
      searchParamForAjmc: {
        ajmc: ''
      },
      searchParam: {
        msgType: '',
        isRead: '0',
        isProc: '0',
        title: '',
        pageNo:1,
        pageSize:5
        // systemMark:serverConfig.APP_CODE,
      },
      pageObj: {
        '01': 1,
        '02': 1,
        '03': 1,
        '04': 1
      },
      wsshParam: {
        ywmc: '',
        isApproveStr: ''
      },
      isReadArr: {
        '01': '0',
        '02': '0',
        '03': '0'
      },
      searchArr: {
        '01': '',
        '02': '',
        '03': ''
      },
      modalArr: {
        '01': {
          xxbt: '',
          fssj: '',
          dqsj: '',
          systemMark: '',
          busType: '',
          fOrgName: ''
        },
        '02': {
          xxbt: '',
          fssj: '',
          dqsj: '',
          systemMark: '',
          busType: '',
          fOrgName: ''
        },
        '03': {
          xxbt: '',
          fssj: '',
          dqsj: '',
          systemMark: '',
          busType: '',
          fOrgName: ''
        },
        '04': {
          ywmc: '',
          fssj: '',
          dqsj: '',
          ajmc: '',
          busType: '',
          isApproveStr: '',
          wssh: '04'
        }
      },
      countArray: [0, 0, 0, 0],
      todoData: [],
      alertData: [],
      alarmData: [],
      dicList:[]
    }
  },

  methods: {
    ...mapActions(['authGetRequest', 'authPostRequest', 'postRequest']),
    ...mapMutations(['setBreadCrumb', 'setTagNavList', 'addTag', 'setLocal', 'setHomeRoute', 'closeTag']),
    dicName (dicName,appCode) {
      let name = []
      return new Promise((resolve, reject) => {
        this.$store.dispatch('axiosGetRequest', {url: '/bsp-com/static/dic/' + appCode + '/' + `${dicName}` + '.js'
      }).then(res => {
          if (res.status === 200) {
            let arr = []
            let func = { getData: eval('(' + res.data + ')') }
            arr = func.getData()
            this.dicList=arr
            resolve(arr)
          } else {
          }
        })
      })
    },
    drupMore(){
      this.$router.push('/agencyNews')
      let res={
        path:'/agencyNews',
        name: "agencyNews",
        meta: {hideInMenu: true, title: "待办消息", notCache: true, icon: "md-home"}
      }
      let arr=localStorage.getItem('tagNaveList')?JSON.parse(localStorage.getItem('tagNaveList')):[]
      arr.unshift(res)
      this.setTagNavList(arr)
    },
    handleModal() {
      this.showModal = true
    },
    on_show_table(){
      this.showFormCompnent=true
      this.handleSearch()
    },
    handleOpenUrl(url,item){
      let params =parseQuery(url)
      if(params && params.eventCode && item.busType && item.busType=='05'){
        Object.assign(this.rowData,params,params)
        console.log(url,item,this.rowData,params,params)
        this.dicList.forEach(ele=>{
           if(ele.code== item.systemMark){
            console.log(ele.name+item.url,'ele.name+item.url')
            let url=ele.name+item.url
            if(item.systemMark=='acp'){
              url=url+'&spInfo=true'
            }
            let res={
              path:'/agencyNews',
              name: "agencyNews",
              meta: {hideInMenu: true, title: "待办消息", notCache: true, icon: "md-home"}
            }
            let arr=localStorage.getItem('tagNaveList')?JSON.parse(localStorage.getItem('tagNaveList')):[]
            arr.unshift(res)
            this.setTagNavList(arr)
             window.open(url)
           }
        })
        // this.showFormCompnent=false
        // this.component='editForm'
      }else if(params && params.jgrybm && item.busType && item.busType=='06'){
          this.$router.push(item.url)
          // this.$router.push('/conflict/registration?jgrybm=JDS-440400121220216073100')
      }else{
        this.dicList.forEach(ele=>{
           if(ele.code== item.systemMark){
            console.log(ele.name+item.url,'ele.name+item.url')
            let url=ele.name+item.url+'&singleSignOnToken='+getToken()
            let res={
              path:'/agencyNews',
              name: "agencyNews",
              meta: {hideInMenu: true, title: "待办消息", notCache: true, icon: "md-home"}
            }
            let arr=localStorage.getItem('tagNaveList')?JSON.parse(localStorage.getItem('tagNaveList')):[]
            arr.unshift(res)
            this.setTagNavList(arr)
             window.open(url)
           }
        })
        // console.log(item.url+'&singleSignOnToken='+getToken(),'12212',getToken())

      }

    },

    searchForAjmc() {
      this.wsshParam = {}
      this.wsshParam.ajmc = this.searchParamForAjmc.ajmc
      this.handleWsxxSearch()
      this.$refs['wsshListContent'].init()
    },
    handleWsxxSearch(bool) {
      this.spinShow = true
      this.authGetRequest({
        url: this.$path.icp_ajcz_getWsListByIdCard,
        params: this.wsshParam
      }).then(res => {
        this.spinShow = false
        if (res.success) {
          this.wsshListData = res.rows
          this.countArray[3] = res.total
          this.switchMsgType()
          // 2022-4-8 新加的
          if (this.$route.query.tabs && bool) {
            this.searchParam.msgType = this.$route.query.tabs
            this.switchMsgType()
          }
        } else {
          this.errorModal({ content: res.msg })
        }
      })
    },
    handleSearch() {
      if (this.searchParam.msgType === '04') {
        this.handleWsxxSearch()
        return
      }
      this.spinShow = true
      this.authGetRequest({
        url: this.$path.app_getDbMsgData,//icp_ajcz_getDbMsgData,
        params: this.searchParam
      }).then(res => {
        this.spinShow = false
        if (res.success) {
          if (res.data.data) {
            this.listData = res.data.data.list || []
            this.total = res.data.data.total
            switch (this.searchParam.msgType) {
              case '01':
                this.todoData = res.data.data.list || []
                this.countArray[0] = this.total
                break
              case '02':
                this.alertData = res.data.data.list || []
                this.countArray[1] = this.total
                break
              case '03':
                this.alarmData = res.data.data.list || []
                this.countArray[2] = this.total
                break
              default:
                return
            }
          } else {
            this.todoData = res.data.todo.list
            this.countArray[0] = res.data.todo.total

            this.alertData = res.data.alert.list
            this.countArray[1] = res.data.alert.total

            this.alarmData = res.data.alarm.list
            this.countArray[2] = res.data.alarm.total

            switch (this.$route.query.tabs) {
              case '01':
                this.listData = this.todoData ? this.todoData : []
                this.total = this.countArray[0]
                break
              case '02':
                this.listData = this.alertData ? this.alertData : []
                this.total = this.countArray[1]
                break
              case '03':
                this.listData = this.alarmData ? this.alarmData : []
                this.total = this.countArray[2]
                break
              case '04':
                this.searchParam.msgType = this.$route.query.tabs
                this.switchMsgType()
                break
              default:
                this.listData = this.todoData ? this.todoData : []
                this.total = this.countArray[0]
                return
            }
          }
          this.listData.forEach(msg => {
            msg.checked = false
          })
          // 2022-4-8 新加的
          // if (this.$route.query.tabs) {
          //    this.searchParam.msgType = this.$route.query.tabs
          //    this.switchMsgType()
          //  }
        } else {
          this.errorModal({ content: res.msg })
        }
      })
    },
    handleSearchForTitle() {
      this.searchArr[this.searchParam.msgType] = this.searchParamForTitle.title
      let titleParam = {
        isProc: this.searchParam.isProc,
        isRead: this.searchParam.isRead,
        msgType: this.searchParam.msgType,
        title: this.searchParamForTitle.title,
        ajmc:this.searchParamForTitle.ajmc,
        // systemMark:serverConfig.APP_CODE,
      }
      this.spinShow = true
      this.authGetRequest({
        url: this.$path.app_getDbMsgData,//icp_ajcz_getDbMsgData,
        params: titleParam
      }).then(res => {
        this.spinShow = false
        if (res.success) {
          // msgType not blank
          if (res.data.data) {
            this.listData = res.data.data.list || []
            this.total = res.data.data.total
            switch (this.searchParam.msgType) {
              case '01':
                this.todoData = res.data.data.list || []
                this.countArray[0] = this.total
                break
              case '02':
                this.alertData = res.data.data.list || []
                this.countArray[1] = this.total
                break
              case '03':
                this.alarmData = res.data.data.list || []
                this.countArray[2] = this.total
                break
              default:
                return
            }
          } else {
            this.todoData = res.data.todo.list
            this.countArray[0] = res.data.todo.total

            this.alertData = res.data.alert.list
            this.countArray[1] = res.data.alert.total

            this.alarmData = res.data.alarm.list
            this.countArray[2] = res.data.alarm.total

            this.listData = this.todoData ? this.todoData : []
            this.total = this.countArray[0]
          }
          this.listData.forEach(msg => {
            msg.checked = false
          })
        } else {
          this.errorModal({ content: res.msg })
        }
      })
      this.$refs.listContent.init()
    },
    selectAll(data) {
      this.listData = []
      this.$nextTick(() => {
        this.listData = data
      })
    },
    handlePage(data) {
      this.pageObj[this.searchParam.msgType] = data.pageNo
      if (this.searchParam.msgType == '04') {
        this.wsshParam.pageNo = data.pageNo
        this.wsshParam.pageSize = data.pageSize
        this.handleWsxxSearch()
      } else {
        this.searchParam.pageNo = data.pageNo
        this.searchParam.pageSize = data.pageSize
        this.handleSearch()
      }
    },
    modalSearch(data) {
      this.showModal = false
      this.searchParam.sStartTime = data.fssj && data.fssj.length > 0 ? data.fssj[0] : ''
      this.searchParam.sEndTime = data.fssj && data.fssj.length > 1 ? data.fssj[1] : ''
      this.searchParam.pStartTime = data.dqsj && data.dqsj.length > 0 ? data.dqsj[0] : ''
      this.searchParam.pEndTime = data.dqsj && data.dqsj.length > 1 ? data.dqsj[1] : ''
      this.searchParam.title = data.xxbt
      this.searchParam.systemMark = data.systemMark
      this.searchParam.busType = data.busType
      this.searchParam.fOrgName = data.fOrgName
      this.wsshParam.isApproveStr = data.isApproveStr
      this.wsshParam.ywmc = data.ywmc
      this.wsshParam.ajmc = data.ajmc
      this.wsshParam.startTime = data.approvalTime && data.approvalTime.length > 0 ? data.approvalTime[0] : ''
      this.wsshParam.endTime = data.approvalTime && data.approvalTime.length > 1 ? data.approvalTime[1] : ''
      if (data.wssh) {
        this.handleWsxxSearch()
      } else {
        this.handleSearch()
      }
    },
    switchMsgType() {
      this.listData = []
      this.searchParam.isRead = this.isReadArr[this.searchParam.msgType]
      this.searchParamForTitle.title = this.searchArr[this.searchParam.msgType]
      switch (this.searchParam.msgType) {
        case '01':
          this.listData = this.todoData ? this.todoData : []
          this.total = this.countArray[0]
          break
        case '02':
          this.listData = this.alertData ? this.alertData : []
          this.total = this.countArray[1]
          break
        case '03':
          this.listData = this.alarmData ? this.alarmData : []
          this.total = this.countArray[2]
          break
        case '04':
          this.listData = this.wsshListData ? this.wsshListData : []
          this.total = this.countArray[3]
          break
        default:
          return
      }
    },
    switchReadType() {
      this.isReadArr[this.searchParam.msgType] = this.searchParam.isRead
      if (this.searchParam.msgType === '01') {
        this.searchParam.isProc = this.searchParam.isRead
      } else {
        this.searchParam.isProc = '-1'
      }
      this.handleSearch()
    }
  },
  mounted() {
    this.dicName ('ZD_SZPTZXTDZ',this.appCode)
    let that = this
    function receiveMessage(event) {
      that.handleSearch()
    }
    window.addEventListener('message', receiveMessage, false)
    this.handleSearch()
    // this.handleWsxxSearch(true)
    this.searchParam.msgType = this.$route.query.tabs ? this.$route.query.tabs : '01'
  }
}
</script>

<style lang="less" scoped>
.xxzx-index {
  //background: #e6e9f0;
  height: 100%;
  // padding: 20px;

  .main {
    height: 100%;
    background: #ffffff;
    // border-radius: 4px;
    // box-shadow: 0px 2px 6px 0px #ced1d6;

    .top {
      padding: 5px 0 5px 0;
      margin: 0 15px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ebf0f5;

      .tabs-box {
        width: 100%;
        position: relative;
        top: 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        /deep/ .ivu-tabs {
          padding-left: 15px;

          .ivu-tabs-bar {
            border-bottom: none;

            .ivu-tabs-tab {
              padding: 8px 0px;

              span:first-child {
                font-size: 16px;
                font-weight: 400;
                text-align: center;
                color: #333333;
                margin-right: 8px;
              }

              .ivu-badge-count {
                width: 32px;
                height: 18px;
                line-height: 18px;
                background: #e60012;
                border-radius: 9px;
                font-size: 14px;
                font-weight: 400;
                padding: 0 3px;
              }
            }

            .ivu-tabs-tab-active {
              span:first-child {
                font-weight: 700;
                color: #2b5fda;
              }
            }

            .ivu-tabs-ink-bar {
              height: 2px;
              width: 65px !important;
              background: #2b5fda;
            }
          }
        }
      }

      .search-box {
        display: flex;
        justify-content: flex-end;

        > div {
          display: flex;
          justify-content: center;
          align-items: center;

          &:first-child {
            width: 450px;

            .ivu-input-wrapper {
              width: 320px;
              height: 32px;
              background: #ffffff;

              .ivu-input {
                border: 1px solid #cccccc;
              }

              .ivu-input-group-append {
                .ivu-btn {
                  width: 32px;
                  min-width: 32px;
                  height: 30px;
                  background: #6298f5;

                  /deep/ .ivu-icon-ios-search {
                    color: #fff;
                    font-size: 18px;
                  }
                }
              }
            }

            .ivu-btn-primary {
              width: 95px;
              min-width: 95px;
              height: 32px;
              line-height: 32px;
              background: #2b5fda;
              border-radius: 4px;
              font-size: 16px;
              font-weight: 400;
              margin-left: 15px;
            }
          }

          &:last-child {
            width: 400px;
            margin-left: 20px;
            .ivu-radio-group {
              .ivu-radio-wrapper {
                font-size: 16px;
                font-weight: 400;
                color: #333333;
                margin-right: 20px;

                /deep/ .ivu-radio {
                  .ivu-radio-inner {
                    width: 20px;
                    height: 20px;

                    &:after {
                      width: 8px;
                      height: 8px;
                      left: 5px;
                      top: 5px;
                      background: #2b5fda;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .content {
      .tips {
        text-align: center;
        display: flex;
        flex-flow: column;
        align-items: center;
        justify-content: center;
        margin-top: 0%;

        > img {
          width: 133px;
          height: 108px;
        }

        > p {
          font-size: 18px;
          font-weight: 400;
          text-align: center;
          color: #666666;
          line-height: 50px;
          letter-spacing: 2px;
        }
      }
    }
  }
}
</style>
