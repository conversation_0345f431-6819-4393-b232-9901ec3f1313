/**列表 */
<template>
  <div class="wssh-list">
    <ul style="height: calc(100vh - 243px)">
      <li v-for="(item, index) in listData" :key="index">
        <div>
          <p>{{item.ywmc ? item.ywmc: ''}}</p>
        </div>
        <div class="msg-content">
          <p style="width: 25%" @click="toAjzt(item)">案件名称：{{item.ajmc}}</p>
          <p style="width: 25%">审核时间：{{item.approvalTime?item.approvalTime:''}}</p>
          <p style="width: 25%">审核结果：{{item.isApproveStr}}</p>
          <Button type="primary" @click="viewWs(item)">查看</Button>
        </div>
      </li>
    </ul>
    <template style="margin: 20px 0">
      <div class="page_box">
        <div style="display: flex; align-items: center">
          <Page
            :total="total"
            @on-change="handlePage"
            :current="page.pageNo"
            @on-page-size-change="changePageSize"
            :page-size="page.pageSize"
            :page-size-opts="page.pageSizeOpts"
            size="small"
            show-elevator
            show-sizer
            style="display: inline-block; margin: 10px 0"
          />
          <span class="Total">共 {{ total }} 条 </span>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
  import {mapActions} from "vuex";

  export default {
    name: 'wssh-list',
    props: {
      listData: {
        type: Array,
        default() {
          return []
        }
      },
      msgType: {
        type: String,
        default: ''
      },
      total: {
        type: Number,
        default: 0
      },
      pageCurrent: {
        type: Number,
        default: 0
      },
      isRead:{
        type: String,
        default: '-1'
      }
    },
    watch: {
      isRead() {
        //console.log(this.isRead)
      },
      pageCurrent() {
        this.page.pageNo = this.pageCurrent
      }
    },
    data() {
      return {
        globalCheck: false,
        customSelectFlag: false,
        single: true,
        btnBool: true,
        page: {pageNo: this.pageCurrent, pageSize: 10, pageSizeOpts: [10, 20, 50, 100]},
      }
    },
    methods: {
      ...mapActions(['authGetRequest', 'authPostRequest', 'postRequest']),
      toAjzt(item) {
        if (!item.ajmc) {
          return
        }
        let url = this.$router.resolve({
          path: '/icp/xzaj/ajxx',
          query: {
            params: this.$Base64.encode({
              ywbh: item.ywbh,
              ztbs: 'ajzt',
              ajlx: item.ajlx
            })
          }
        });
        window.open(url.href) //打开新窗口
      },
      init() {
        this.page.pageNo = 1
      },
      // 标记
      viewWs(item){
        let url = item.operateUrl
        url = url.replace("/fm/#/", "/fm#/")
        window.open(url);
      },
      handleTag() {
        this.btnBool = !this.btnBool
        this.listData.forEach(msg => {
          msg.checked = false
        })
      },
      selectAll() {
        if (this.customSelectFlag) {
          this.listData.forEach(msg => {
            msg.checked = this.globalCheck
          })
        } else {
          this.listData.forEach(msg => {
            msg.checked = !msg.checked
          })
        }
        this.customSelectFlag = false
        this.$nextTick(() => {
          this.$emit('selectAll', this.listData)
        })
      },
      handleRead(isRead) {
        const idArray = this.listData.filter(msg => msg.checked === true).map(x => x._id)
        //console.log(idArray)
        if (idArray.length == 0) {
          this.warningModal({content: "请勾选一个"})
          return
        }
        let param = {
          ids: idArray.join(','),
          msgType: this.msgType,
          isRead: isRead
        }
        this.authGetRequest({
          url: this.$path.icp_ajcz_msgUpdate,
          params: param
        }).then(data => {
          if (data.success) {
            this.successModal({content: data.msg}).then(async () => {
              this.$emit('reFresh')
              this.btnBool = true
            })
          } else {
            this.errorModal({content: data.msg})
          }
        })
      },
      handleOpenUrl (url) {
        //console.log(url)
        // let url = `/fm/#/${urlPrefix}?ajbh=${row.ajbh}&cqywId=${row.cqyw_id}&flwsId=${row.ws_id}&zjz=${row.id}&isPreview=0`;
        if(url) window.open(url)
      },
      // 翻页
      changePageSize(pageSize) {
        this.page.pageNo = 1
        this.page.pageSize = pageSize
        this.searchListData()
      },
      handlePage(pageNo) {
        // //console.log(pageNo)
        this.page.pageNo = pageNo
        this.searchListData()
      },
      searchListData() {
        // //console.log(this.search)
        let param = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
        }
        this.$emit('page', param)
      },
      customSelect(result) {
        this.globalCheck = result
        this.customSelectFlag = true
      }
    }
  }
</script>

<style lang="less" scoped>
  .wssh-list {
    padding: 10px 15px;

    ul {
      overflow: auto;
      padding-right: 5px;

      li {
        padding: 20px 25px;
        background: #fff;
        border-bottom: 1px solid #ebf0f5;
        cursor: pointer;

        &:hover,
        &.active {
          background: #f0f6fc;
        }

        > div {
          &:first-child {
            display: flex;
            justify-content: space-between;

            > p {
              &:first-child {
                font-size: 16px;
                font-weight: 700;
                text-align: left;
                color: #333333;

                > span {
                  display: inline-block;
                  width: 120px;
                  height: 24px;
                  line-height: 24px;
                  background: #e5efff;
                  font-size: 14px;
                  font-weight: 400;
                  text-align: center;
                  color: #3179f5;
                  margin-right: 15px;
                }
              }

              &:last-child {
                display: flex;
                align-items: center;

                > span {
                  font-size: 16px;
                  font-weight: 400;
                  color: #333333;
                  margin-right: 25px;
                  position: relative;

                  &.active {
                    &::after {
                      content: '';
                      width: 12px;
                      height: 12px;
                      background: #e60012 !important;
                      display: inline-block;
                      border-radius: 50px;
                      position: absolute;
                      left: -25px;
                      top: 6px;
                    }
                  }

                  &::after {
                    content: '';
                    width: 12px;
                    height: 12px;
                    background: green;
                    display: inline-block;
                    border-radius: 50px;
                    position: absolute;
                    left: -25px;
                    top: 6px;
                  }
                }

                /deep/ .ivu-checkbox-wrapper {
                  position: relative;
                  top: 2px;

                  .ivu-checkbox-inner {
                    width: 20px;
                    height: 20px;
                    background: #ffffff;
                    border-radius: 4px;
                  }

                  &.ivu-checkbox-wrapper-checked {
                    .ivu-checkbox-inner {
                      background: #2b5fda;

                      &:after {
                        width: 7px;
                        height: 12px;
                      }
                    }
                  }

                  .ivu-checkbox-disabled {
                    .ivu-checkbox-inner {
                      border-color: #dcdee2 !important;
                      background-color: #f5f6fa !important;
                    }
                  }
                }
              }
            }
          }

          &:nth-child(2) {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            /*padding: 0 25px;*/

            > P {
              font-size: 16px;
              font-weight: 400;
              text-align: left;
              color: #666666;
            }
          }
        }
      }
    }

    .page_box {
      border-top: none;
      height: 50px;
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: flex-end;
      background: #f0f3fa;
      margin-top: 8px;

      /deep/ .ivu-page {
        .ivu-page-item-active {
          width: 32px;
          height: 26px;
          line-height: 28px;
          background: #3179f5;
          border-radius: 2px;
        }

        .ivu-page-item {
          background: none;
          margin: 0 5px;
        }

        .ivu-page-prev,
        .ivu-page-next {
          background: none;

          .ivu-icon {
            font-size: 18px;
            position: relative;
            top: 2px;
          }
        }
      }

      .Total {
        display: inline-block;
        height: 24px;
        line-height: 24px;
        vertical-align: top;
        margin: 10px 0px;
        padding: 0 15px;
        border-radius: 2px;
        cursor: pointer;
        font-size: 15px;
      }

      /deep/ .btn-box {
        display: flex;
        align-items: center;

        > span {
          display: inline-block;
          width: 2px;
          height: 24px;
          background: #cccccc;
          margin-right: 15px;
        }

        .ivu-btn-default {
          border: 1px solid transparent;
          border-color: #dcdee2;
          color: #333333 !important;
          font-size: 16px;
          font-weight: 400;
          color: #333333;
          margin-right: 10px;

          &:hover {
            border: 1px solid #2b5fd9;
            color: #2b5fd9 !important;
          }
        }

        .ivu-btn-primary {
          font-size: 16px;
          font-weight: 400;
          margin-right: 10px;
        }
      }
    }
  }
</style>
