<template>
    <div class="home-com">
        <p class="home-title">在{{orgType=='01'?'押':'拘'}}情况</p>
        <div class="zyqk-wrap">
              <div class="zyqk-wrap-top flex-box-cs">
                <div class="zyqk-wrap-top-left">
                    <p class="dqzy">当前在{{orgType=='01'?'押':'拘'}}</p>
                    <p class="curTotal">{{ curTotal }}</p>
                </div>
                <div class="zyqk-wrap-top-right" style="position: relative;right: -10px;"><img src="@/assets/images/header/gyl.png" /></div>
             </div>
             <div class="zyqk-wrap-center" style="margin-top: 10px;">
                 <p class="flex-box-cs">
                    <span class="curNum flex-box-cs"><img src="@/assets/images/header/L.png" style="width: 26px;" />{{ curN }}</span>
                    <span class="curNum flex-box-cs">{{ curL }}<img src="@/assets/images/header/N.png" style="width: 26px;" /></span>
                 </p>
                 <Progress :percent="100" :success-percent="nPercent" hide-info :stroke-width="4" stroke-color="#F84F6A" />
             </div>
             <div class="zyqk-wrap-pie">
                <yrb :itemData="arr" :yrb="yrb" />
             </div>
             <div class="zyqk-wrap-foot flex-box-cs">
                <span style="font-size: 14px;">累计{{orgType=='01'?'羁押':'拘留'}}</span>
                <span class="ljjy">{{ljjy }}</span>
             </div>

        </div>
        <Spin size="large" fix v-if="spinShow"></Spin>
    </div>
</template>

<script>
import yrb from "./yrb.vue"
export default {  
    components:{yrb}, 
   data(){
    return{
        capacity:0,
        curTotal:0,
        curN:0,
        curL:0,
        ljjy:0,
        nPercent:0,
        arr:[
            {name:'累计羁押',value:0},
            {name:'羁押容量',value:0},
        ],
        yrb:0,
        spinShow:false,
        orgType: localStorage.getItem('orgType'),


    }
   },
   mounted(){
    this.getData(this.globalAppCode+':syzyqktj')
   },
   methods:{
    getData(mark) {
        this.spinShow=true
        let params={
          modelId: mark
        }
          this.$store.dispatch("postRequest", {
            url:this.$path.get_query_grid,// '/bsp-com/com/form/handle/executeMultiQuery',
            params: params,
          }).then(res => {
            if (res.success) {
                this.spinShow=false
             if(res.rows && res.rows.length>0){
                this.capacity=res.rows[0].plan_capacity
                this.ljjy=res.rows[0].total_detain
                this.curTotal=res.rows[0].total_detain_num
                this.curN=res.rows[0].total_man_detain
                this.curL=res.rows[0].total_women_detain
                this.arr[0].value=this.ljjy
                this.arr[1].value=this.capacity
                this.yrb=this.capacity>0?(this.curTotal/this.capacity) * 100:0
                this.nPercent=(this.curN / this.curTotal) * 100
             }

            }else{
              this.spinShow=false
            }
          })
      },
   }

}
</script>

<style lang="less" scoped>
.zyqk-wrap-pie{
    width: 100%;
    height: 50%;
    margin-top: 8px;
}
.zyqk-wrap{
    background: #E0F0FF;
    border-radius: 4px 4px 4px 4px;
    margin: 16px;
    padding: 0 16px;
    height: calc(~'100% - 90px');
}
.dqzy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
    line-height: 50px;
}
.curTotal{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 24px;
    color: #2B3646;
    line-height: 30px;
}
.flex-box-cs{
    display: flex;
    justify-content: space-between;
}
.curNum{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 20px;
    color: #2B3346;
    line-height: 28px;
}
/deep/ .ivu-progress-success-bg{
    background: #42A3FE !important;
}
.ljjy{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 14px;
    color: #2B3346;
}
</style>