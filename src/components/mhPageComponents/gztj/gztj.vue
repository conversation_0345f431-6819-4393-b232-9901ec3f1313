/** 工作统计 */
<template>
  <div class="gztj">
    <ul class="gztjUl">
      <li class="gztjLi" v-for="(item, i) in dataList" :key="i" @click="handleWay(item.params)">
        <img :src="item.imgUrl" alt="" />
        <div>
          <p>{{ item.num }}</p>
          <p>{{ item.title }}</p>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    },
    itemOrder: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {

      dataList: [
        { title: '待办事项', num: '0', imgUrl: require('@/assets/images/mhzj/icon-1.png'), params: 1 },
        { title: '工作提醒', num: '0', imgUrl: require('@/assets/images/mhzj/icon-2.png'), params: 2 },
        { title: '监督提醒', num: '0', imgUrl: require('@/assets/images/mhzj/icon-3.png'), params: 3 },
        { title: '我的审核文书', num: '0', imgUrl: require('@/assets/images/mhzj/icon-4.png'), params: 4 },
        { title: '在办案件', num: '0', imgUrl: require('@/assets/images/mhzj/icon-5.png'), params: 5 }
      ]
    }
  },
  mounted() {
    // this.getDate()
  },
  methods: {
    ...mapActions(['authGetRequest']),

    getDate() {
      this.authGetRequest({ url: `/icp-ajcz/msg/collectMsgCount` }).then(res => {
        if (res.success) {
          this.dataList[0].num = res.todoTotal
          this.dataList[1].num = res.alertTotal
          this.dataList[2].num = res.alarmTotal
          this.dataList[3].num = res.wsTotal
          this.dataList[4].num = res.ajTotal
        }
      })
    },
    // 点击事件
    handleWay(params) {
      if (params < 5) {
        let url = this.$router.resolve({
          path: '/xxzx',
          query: {
            tabs: '0' + params
          }
        })
        window.open(url.href) //打开新窗口
      }  else if (params == 5) {
        this.infoModal({ content: '功能开发中。。。' })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.gztj {
  .gztjUl {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    padding-left: 5px;
    .gztjLi {
      min-width: 272px;
      width: 15%;
      height: 88px;
      display: inline-block;
      display: flex;
      align-items: center;
      background: #f5f7fc;
      border: 1px solid #e8eefc;
      border-radius: 4px;
      margin: 16px 15px 0 15px;
      cursor: pointer;
      > img {
        width: 72px;
        height: 72px;
        margin: 15px 15px 0 15px;
      }
      > div {
        > p {
          &:first-child {
            height: 35px;
            font-size: 28px;
            font-family: Arial;
            font-weight: bold;
            color: #2b3646;
          }
          &:nth-child(2) {
            font-size: 16px;
            font-weight: 400;
            color: #7a8699;
          }
        }
      }
    }
    .fake {
      content: '';
      width: 15%;
      overflow: hidden;
    }
  }
}
</style>
