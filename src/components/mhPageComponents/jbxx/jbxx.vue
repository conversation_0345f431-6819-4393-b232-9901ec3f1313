<!-- 基本信息组件 -->
<template>
  <div class="jbxx-view"   >
    <div class="jbxxTitle">
      <!-- <img src="@/assets/images/title.png" alt="" /> -->
      <Icon type="ios-contacts" size="28" color="#2b5fd9" class="iconTitle" />人员基本信息
    </div>
    <div class="content-view" slot="content">
      <Row type="flex" justify="center" align="top" class="code-row-bg">
        <Col span="6" class="col">
          <span class="leftLabel">姓名</span>
          <p class="rightLabel">{{ formData.ryxm }}</p>
        </Col>
        <Col span="2" style="position: relative">
          <p class="UserPicture"><img src="" alt="" /></p>
        </Col>
        <Col span="8" class="col">
          <span class="leftLabel">身份证号</span>
          <p class="rightLabel">{{ formData.zjhm }}</p>
        </Col>
        <Col span="8" class="col">
          <span class="leftLabel">性别</span>
          <p class="rightLabel">{{ formData.xbName}}</p>
        </Col>
      </Row>
      <!-- 出生日期   民族      工作单位 -->
      <Row type="flex" justify="center" align="top" class="code-row-bg">
        <Col span="6" class="col">
          <span class="leftLabel">出生日期</span>
          <p class="rightLabel">{{ formData.csrq }}</p>
        </Col>
        <Col span="2">
          <p></p>
        </Col>
        <Col span="8" class="col">
          <span class="leftLabel">民族</span>
          <p class="rightLabel">{{ formData.mzName }}</p>
        </Col>
        <Col span="8" class="col">
          <span class="leftLabel">工作单位</span>
         <Tooltip v-if=" formData.gzdw && formData.gzdw.length>20"
                :content="formData.gzdw"
                theme="light"
                max-width="500"
                placement="bottom"
              >
              <p class="rightLabel slText ellipsisText" > {{formData.gzdw}}</p>
              </Tooltip>
          <p class="rightLabel xzdz" v-else>{{ formData.gzdw }}</p>
        </Col>
      </Row>
      <!--  联系电话   现住地址  户籍地址 -->
      <Row type="flex" justify="center" align="top" class="code-row-bg">
        <Col span="6" class="col">
          <span class="leftLabel">联系电话</span>
          <p class="rightLabel">{{ formData.lxdh }}</p>
        </Col>
        <Col span="2">
          <p></p>
        </Col>
        <Col span="8" class="col">
          <span class="leftLabel">现住地址</span>
          
           <Tooltip v-if=" formData.xzzdz && formData.xzzdz.length>20"
                :content="formData.xzzdz"
                theme="light"
                max-width="500"
                placement="bottom"
              >
              <p class="rightLabel slText ellipsisText" > {{formData.xzzdz}}</p>
              </Tooltip>
          <p class="rightLabel xzdz" v-else>{{ formData.xzzdz }}</p>
        </Col>
        <Col span="8" class="col">
          <span class="leftLabel">户籍地址</span>
           <Tooltip v-if="formData.hjdz && formData.hjdz.length>20"
                :content="formData.hjdz"
                theme="light"
                max-width="500"
                placement="bottom"
              >
              <p class="rightLabel slText ellipsisText" > {{formData.hjdz}}</p>
              </Tooltip>
    
          <p class="rightLabel xzdz" v-else>{{ formData.hjdz }}</p>
     
        </Col>
      </Row>
    </div>

    <div class="jbxxTitle">
      <!-- <img src="@/assets/images/title.png" alt="" /> -->
      <Icon type="ios-paper" size="28" color="#2b5fd9" class="iconTitle"  />案件信息
    </div>
    <div class="content-view">
      <Row type="flex" justify="center" align="top" class="code-row-bg">
        <Col span="8" class="col">
          <span class="leftLabel">案件编号</span>
          <p class="rightLabel">{{ formData.ajbh }}</p>
        </Col>

        <Col span="8" class="col">
          <span class="leftLabel">案件名称</span>
          <p class="rightLabel">{{ formData.ajmc }}</p>
        </Col>
        <Col span="8" class="col">
          <span class="leftLabel">立案时间</span>
          <p class="rightLabel">{{ formData.lasj }}</p>
        </Col>
      </Row>
      <Row type="flex" justify="center" align="top" class="code-row-bg">
        <Col span="8" class="col">
          <span class="leftLabel">主办民警</span>
          <p class="rightLabel">{{ formData.zbrxm }}</p>
        </Col>

        <Col span="16" class="col">
          <span class="leftLabel">主办单位</span>
          <p class="rightLabel">{{ formData.badwmc }}</p>
        </Col>

      </Row>
    </div>
    <div class="jbxxTitle">
      <!-- <img src="@/assets/images/title.png" alt="" /> -->
      <Icon type="md-clipboard"  size="28" color="#2b5fd9" class="iconTitle"  />取保候审信息
    </div>
    <div class="content-view">
      <!-- 取保类型: 人保/财保    取保时间   保证金额/保证人(财保显示保证金额，人保显示保证人。保证人 -->
      <Row type="flex" justify="center" align="top" class="code-row-bg">
        <Col span="8" class="col">
          <span class="leftLabel">取保类型</span>
          <p class="rightLabel">{{ formData.gklx == "1" ? "人保" : "财保" }}</p>
        </Col>

        <Col span="8" class="col">
          <span class="leftLabel">取保时间</span>
          <p class="rightLabel">{{ formData.ksrq }}至{{ formData.jsrq }}</p>
        </Col>
        <Col span="8" class="col" v-if="formData.gklx == '1'">
          <span class="leftLabel">保证人</span>
          <p class="rightLabel" style="cursor: pointer;" @click="openUser">{{ formData.bzrxm }}（查看详情）</p>
        </Col>
        <Col span="8" class="col" v-if="formData.gklx == '2'">
          <span class="leftLabel">保证金额</span>
          <p class="rightLabel">{{ formData.bzje }}</p>
        </Col>
      </Row>
      <!--   执行民警    执行单位    执行状态 -->
      <Row type="flex" justify="center" align="top" class="code-row-bg">
        <Col span="8" class="col">
          <span class="leftLabel">执行民警</span>
          <p class="rightLabel">{{ formData.zxmjxm }}</p>
        </Col>

        <Col span="8" class="col">
          <span class="leftLabel">执行单位</span>
          <p class="rightLabel">{{ formData.zxdwmc }}</p>
        </Col>
        <Col span="8" class="col">
          <span class="leftLabel">执行状态</span>
          <p class="rightLabel">{{ formData.gkztName }}</p>
        </Col>
      </Row>
    </div>

  </div>
</template>
<script>
import { mapActions } from "vuex";


export default {
    components:{
    },
  props: {
    rkryId: {
      type: String,
      default: "",
    },
  },
  watch:{
    formData:{
      handler(n,o){
          // this.formData=n
      },
      deep:true,
      immediate:true
    },
    rkryId:{
      handler(n,o){
        this.gkryId=n
      },
      deep:true,
      immediate:true
    }
  },
  data() {
    return {
      showData:false,
      // formDataTemp: {
      //   gklx: 1,
      // },
      gkryId:this.rkryId,
      formData:{
        gklx: 1,
      },
      showModal:false,
     
    };
  },
  mounted() {
    // this.getInfo();
  },
  methods: {
    ...mapActions(["authPostRequest", "postRequest", "authGetRequest"]),
    openUser() {
        this.showModal=true
    },
    getInfo() {
      const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(255,255,255, 0.7)'
        });
      let params = {
        id: this.gkryId,
      };
      this.authGetRequest({
        url: `${this.$path.ric_dbtx_getGkryInfoById}`,
        params: params,
      }).then((res) => {
        if (res.success) {
          loading.close();
          this.showData=true
          this.formData= res.data;
          this.$forceUpdate()
        }
      });
    },
  },
};
</script>
<style scoped lang="less">
.jbxx-view {
  
  width: 100%;
  padding: 20px 20px 10px 20px; 
  margin-left: -5px;
  background: #fff;
  .jbxxTitle {
    width: 100%;
    height: 40px;
    border: 1px solid #cee0f0;
    display: flex;
    align-items: center;
    align-self: center;
    font-size: 16px;
    font-weight: 700;
    color: #333333;
    margin-left: 5px;
    position: relative;
    background-color: #eff6ff;

    .img {
      margin: 0 20px;
      display: inline-block;
    }
    .iconTitle{
      margin: 0 5px 0 5px;
    }

  }
  .jbxxTitle:nth-of-type(n + 1) {
    margin-top: -1px;
  }
}

// 头像
.UserPicture {
  width: 100%;
  height: 120px;
  position: absolute;
  text-align: center;
  border-left: 1px solid #cee0f0 !important;
  img{
      height: 100%;
      margin-top: 1px;
  }
}
.content-view {
  position: relative;
  width: 100%;
  left: 5px;
  border-right:1px solid #cee0f0 !important;
  .code-row-bg {
    .col {
      height: 42px;
      line-height: 40px;
      //   border: 1px solid #cee0f0 !important;
      display: flex;
      align-items: center;
      margin-top: -1px;
      .leftLabel {
        width: 150px;
        height: 42px;
        display: inline-block;
        text-align: right;
        padding-right: 10px;
        background: #f2f5fc;
        border: 1px solid #cee0f0 !important;
        font-size: 16px;
        color: #2b3646;
        font-weight: 400;
      }
      .rightLabel {
        width: ~"calc(100% - 150px)";
        height: 42px;
        line-height: 40px;
        // margin-left: -1px;
        border: 1px solid #cee0f0 !important;
        border-left: none;
        text-indent: 5px;
        font-size: 16px;
        color: #3e4e66;
        font-weight: 400;
        border-right: none !important;
        border-left: none !important;
      }
    }
  }
}

/deep/ .slText{
  width: 100% !important;
}
/deep/ .ivu-tooltip{
  width: 73% !important;
 
}
 /deep/.ivu-tooltip-rel{
        width: 100%;
    margin-top: 14px;
  }
 .ivu-tooltip-light   /deep/ .ivu-tooltip-inner {
     color: #515a6e !important;
}
.ellipsisText{overflow:hidden;
text-overflow:ellipsis;
white-space:nowrap;}
</style>
