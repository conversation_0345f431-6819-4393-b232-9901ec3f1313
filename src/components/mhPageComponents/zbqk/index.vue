<template>
    <div class="home-com">
        <div class="home-title"><span>值班情况 </span>
            <div class="btns-zb"><Button type="default"
                    @click="$router.push('/comprehensive/policeDuty/dutyPublish')">值班情况</Button>&nbsp;<Button
                    type="default" disabled>值班签到</Button></div>
        </div>
        <div class="zbqk-list-wrap">
            <div v-for="(item, index) in dataList" :key="index" class="zbqk-list-wrap-para">
                <span class="sld">{{ item.title }}</span>
                <div class="zbqk-list-wrap-para-right">
                    <p v-for="(ele, i) in item.arrRy" :class="['arrRy-box', ele.check ? 'activeImg' : '']"
                        :key="i + 'arrRy'"><img style="width: 34px;"
                            :src="ele.photo ? ele.photo : require('@/assets/images/manPb.png')" />
                        <Icon type="md-checkmark-circle" color="#22D28D" v-if="ele.check" class="selectIvon" />
                        &nbsp;&nbsp;<span class="sldbx">{{ ele.policeName }}</span>
                    </p>
                </div>
            </div>
            <div class="tips" v-if="dataList.length == 0">
                <img src="@/assets/images/no-people.png" alt="" />
                <p>暂无排班</p>
            </div>
        </div>
        <!-- <div class="btn-zb"><Button type="default"
                @click="$router.push('/comprehensive/policeDuty/dutyPublish')">值班情况</Button>&nbsp;<Button type="default"
                disabled>值班签到</Button></div> -->
    </div>
</template>

<script>
export default {
    data() {
        return {
            dataList: []
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        getData() {
            this.$store.dispatch('getRequest', { url: this.$path.com_listByDutySingleDateIndex, params: {} }).then(resp => {
                if (resp.success) {
                    this.dataList = resp.data
                    this.dataList.forEach(item => {
                        for (let i in item) {
                            this.$set(item, 'title', i)
                            this.$set(item, 'arrRy', item[i])
                        }

                    })
                }
            }
            )
        }
    }

}
</script>

<style lang="less" scoped>
.zbqk-list-wrap {
    height: calc(~'100% - 75px');
    overflow: auto;
}

.zbqk-list-wrap-para {
    margin: 16px 16px 0 16px;
    background: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #E9EDF5;
    display: flex;
    min-height: 56px;
    align-items: center;
    // flex-wrap: wrap;
}
.zbqk-list-wrap-para-right{
    display: flex;
    min-height: 56px;
    align-items: center;
    flex-wrap: wrap;
}

.sld {
    border-left: 4px solid #CEE0F0;
    display: inline-block;
    border-radius: 4px 0px 0px 4px;
    height: 56px;
    line-height: 56px;
    padding: 0 16px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #5F709A;
    min-width: 100px;
}

.sldbx {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #2B3346;
    max-width: 200px;
    min-width: 60px;
    display: inline-block;
}

.arrRy-box {
    display: flex;
    align-items: center;
    margin-right: 16px;
}

.activeImg span {
    color: #22D28D !important;
}

.selectIvon {
    position: relative;
    bottom: -10px;
    left: -10px;
}

.btn-zb {
    width: 100%;
    display: flex;
    justify-content: center;

    button {
        width: 47%;
    }
}

.tips {
    text-align: center;
    display: flex;
    height: 100%;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    margin-top: 5%;
}

.tips>img {
    width: 133px;
    height: 108px;
}

.tips>p {
    font-size: 18px;
    font-weight: 400;
    text-align: center;
    color: #666666;
    line-height: 50px;
}
</style>