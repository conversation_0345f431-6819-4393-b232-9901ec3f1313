import * as echarts from 'echarts'
export const initPatrolControlEcharts = (that) => {
  let dom = document.getElementById('patrolControl');
  if (!dom) return
  that.patrolControlCharts && that.patrolControlCharts.dispose(dom);
  that.patrolControlCharts = echarts.init(dom);
  const data = [
    { name: "紧急报告", value: 7, per: "14%" },
    { name: "生病呼叫", value: 9, per: "18%" },
    { name: "其他异常", value: 13, per: "26%" },
    { name: "设备异常", value: 21, per: "42%" },
  ]
  const colorList = ['#ff8515', '#feda47', '#a060f6', '#6f99e1']
  let option = {
    title: {
      text: '巡控登记总数',
      subtext: '50',
      textStyle: {
        fontSize: 13,
        color: '#67779f',
        lineHeight: 20
      },
      subtextStyle: {
        fontSize: 20,
        color: '#333'
      },
      textAlign: 'center',
      left: '19%',
      top: '33%'
    },
    tooltip: {
      trigger: 'item',
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: '0%',
      top: 'center',
      itemGap: 10,
      selectedMode: false,
      icon: '',
      data: ['紧急报告', '生病呼叫', '其他异常', '设备异常'],
      textStyle: {
        color: '#77899c',
        rich: {
          uname: {
            width: 100
          },
          unum: {
            color: 'black',
            width: 20,
            align: 'right',
            fontSize: 22
          },
          color_0: {
            color: colorList[0]
          },
          color_1: {
            color: colorList[1]
          },
          color_2: {
            color: colorList[2]
          },
          color_3: {
            color: colorList[3]
          }
        }
      },
      formatter(name, b) {
        let targetIndex = data.findIndex(v => v.name == name)
        let target = data[targetIndex]
        return `{uname|${name}}{unum|${target.value}} 次 / {color_${targetIndex}|${target.per}}`
      }
    },
    color: colorList,
    series: [
      {
        name: '',
        type: 'pie',
        clockwise: false,
        radius: [55, 65],
        center: ['20%', '50%'],
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        itemStyle: {
          borderWidth: 3,
          borderColor: '#fff'
        },
        data: data,
      }
    ]
  }

  that.patrolControlCharts.setOption(option)
}

// 渲染违规等级分析
export const initViolationCharts = (that) => {
  let dom_1 = document.getElementById('violation_1');
  let dom_2 = document.getElementById('violation_2');
  if (!dom_1) return
  that.violationCharts_1 && that.violationCharts_1.dispose(dom_1);
  that.violationCharts_1 = echarts.init(dom_1);

  that.violationCharts_2 && that.violationCharts_2.dispose(dom_2);
  that.violationCharts_2 = echarts.init(dom_2);

  const initOption = (type = false) => {
    return {
      title: {
        text: type ? '40%' : '60%',
        y: 'center',
        left: '26%',
        textStyle: {
          fontWeight: 'normal',
          color: '#808eaf',
          fontSize: '18'
        }
      },
      color: ['#dde8ff'],
      tooltip: {
        show: false,
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        show: false,
        itemGap: 10,
        data: []
      },
      toolbox: {
        show: false,
        feature: {
          mark: {
            show: true
          },
          dataView: {
            show: true,
            readOnly: false
          },
          restore: {
            show: true
          }
        }
      },
      series: [
        {
          name: '',
          type: 'pie',
          clockWise: true,
          center: ['50%', '50%'],
          radius: [35, 40],
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              }
            }
          },
          hoverAnimation: false,

          data: [
            {
              value: type ? 40 : 60,
              name: '',
              itemStyle: {
                normal: {
                  color: {  // 完成的圆环的颜色
                    colorStops: [{
                      offset: 0, color: type ? '#dee8ff' : '#a0f1de' // 0% 处的颜色
                    }, {
                      offset: 1, color: type ? '#ff8b21' : '#3dccab' // 100% 处的颜色
                    }]
                  },
                  label: {
                    show: false
                  },
                  labelLine: {
                    show: false
                  }
                },
                emphasis: {
                  color: '#00cefc' // 鼠标悬浮上去完成的圆环的颜色
                }
              }
            },
            {
              value: 100 - (type ? 40 : 60),
              name: '',
            }
        ]
      }
      ]
    }
  }

  // 一般违规数量
  that.violationCharts_1.setOption(initOption(false))
  // 严重违规数量
  that.violationCharts_2.setOption(initOption(true))
}