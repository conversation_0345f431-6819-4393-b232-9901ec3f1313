.d-flex {
  display: flex;
}

.ai-center {
  align-items: center;
}

.jc-end {
  justify-content: end;
}

.jc-between {
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 标题样式
.title {
  width: 100%;
  padding: 6px 0px;
  font-size: 16px;
  color: #2B3346;
  margin-bottom: 10px;
}

.seekMedicalAdvice {
  width:100%;
  height:  calc(~'100% - 60px');
  padding: 10px;
  box-sizing: border-box;

  // 左侧盒子样式
  .tilePreview {
    width: calc(~'50% - 8px');
    height: 100%;
    .line {
      width:  100%;
      height: 30%;
      justify-content: space-around;
      .violation {
        // width: 25%;
        height: 100%;
        display: flex;
        align-items: center;
        /deep/ .el-progress-circle{
          width: 64px !important;
          height: 64px !important;
          }
      }
      .desc_pie {
        width: 25%;
        padding-left: 15px;
        box-sizing: border-box;
        .num {
          font-size: 22px;
          font-weight: bold;
          margin-right: 3px;
        }

        .end {
          font-size: 16px;
        }

        .pie_text {
          color: gray;
          font-size: 13px;
        }
      }
    }

    .line_patrolControl{
      height: 50%;
      .patrolControl {
        height: 100%;
      }
      
    }
  }

  // 右侧盒子样式
  .dynamics {
    width: calc(~'50% - 8px');
    height: 100%;

    .list {
      width: 100%;
      height: calc(~'100% - 40px');
      overflow-y: auto;

      .caseLine {
        margin-bottom: 6px;

        .flex-line {
          width: 100%;
          padding: 3px 0px;
          box-sizing: border-box;
          .tip {
            display: block;
            font-size: 12px;
            background-color: #e9f0ff;
            padding: 3px 6px;
            margin-right: 5px;
            border-radius: 11px 11px 11px 11px;
            color: #FF7B02;
            background: #FFF9EF;
          }
          .xkdj{
            color: #4B81FF;
            background: #E9F0FF;
          }

          .name {
            display: inline-block;
            flex: 1;
            font-weight: bold;
            font-size: 14px;
            margin-right: 10px;
          }

          .time {
            font-size: 13px;
          }

          .desc {
            display: inline-block;
            flex: 1;
            font-size: 13px;
            margin-right: 10px;
          }
        }
      }

      

    }
  }
}
.tabList {
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #CFD9F0;
    display: flex !important;
    justify-content: space-between;
    display: inline-block;
    // height: 24px;
    .tabTag {
      cursor: pointer;
      width:44px;
      background: #fafafa;
      text-align: center;
      display: inline-block;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #4c6a99;
    //   line-height: 28px;
    }
    .active {
      background: #538ef9 !important;
      color: #fff !important;
    }
  }