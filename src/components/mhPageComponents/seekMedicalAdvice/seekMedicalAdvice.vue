<template>
  <div style="width: 100%;height: 100%;">
          <p class="home-title" style="display: flex;justify-content: space-between;padding: 15px 16px;">
            <span>{{orgType=='01'?'风险':'巡视'}}管控情况</span>       
            <ul class="tabList">
                <li :class="['tabTag', { active: item.check }]" v-for="(item, i) in sxDate" :key="i" @click="changeTab(item)">{{ item.title }}</li>
            </ul>
        </p>
        <div class="seekMedicalAdvice d-flex ai-center jc-between">
          <div class="tilePreview ">
            <div class="title">违规登记分析</div>
            <div class="line d-flex ai-center">
              <div class="violation">
                <el-progress type="circle" :width= '150' :stroke-width="14" stroke-linecap="round" define-back-color="#DDE8FF" color="#3DCCAB"  :percentage="20"></el-progress>
              </div>
              <div class="desc_pie">
                <span class="num">60</span><span class="end">人</span>
                <div class="pie_text">一般违规数量</div>
              </div>
              <div class="violation" >
                  <el-progress type="circle"  :width= '150' :stroke-width="14" stroke-linecap="round"  define-back-color-="#DDE8FF" color="#FF7B02"   :percentage="30"></el-progress>
              </div>
              <div class="desc_pie">
                <span class="num">40</span><span class="end">人</span>
                <div class="pie_text">严重违规数量</div>
              </div>
            </div>

            <div class="title">巡控登记分析</div>
            <div class="line_patrolControl">
               <seekUpdatesVue />
            </div>
          </div>

          <div class="dynamics">
            <div class="title">所情动态</div>

            <div class="list" v-if="optionData.dynamics && optionData.dynamics.length>0">
              <div class="caseLine" v-for="(item, i) in optionData.dynamics" :key="i">
                <div class="flex-line d-flex ai-center flex-wrap">
                  <span :class="['tip',item.lx]">{{item.lx=='xkdj'?'巡控登记':'违规登记'}}</span>
                  <span class="desc text-ellipsis">登记人：{{ item.operator_xm?item.operator_xm:'-' }}</span>
                  <span class="time">{{ item.operator_time }}</span>
                </div>
                <div class="flex-line d-flex ai-center">
                  <span class="name text-ellipsis" :title="item.context">{{ item.context }}</span>
                  <span class="time"></span>
                </div>
              </div>
            </div>
            <noEmpty  v-else />
          </div>
        </div>
        <Spin size="large" fix v-if="spinShow"></Spin>

  </div>
</template>

<script>
import { initPatrolControlEcharts, initViolationCharts } from "./seekMedicalAdvice"
import seekUpdatesVue from "./seekUpdates.vue"
import noEmpty from "@/components/bsp-empty"
export default {
  components:{seekUpdatesVue,noEmpty},
  data() {
    return {
      orgType: localStorage.getItem('orgType'),
      violationCharts_1: null,
      violationCharts_2: null,
      patrolControlCharts: null,
      sxDate: [
        { title: '今日', check: true ,type:1},
        { title: '本周', check: false ,type:2},
        { title: '本月', check: false,type:3 },
        { title: '本年', check: false,type:4 }
      ],
      type:1,
      spinShow:false,
      optionData: {
        // 一般违规数量
        general : 60,
        // 严重违规数量
        serious: 40,
        // 巡控登记分析
        patrolControl: [],
        dynamics: []

      }
    }
  },
  mounted(){
    // this.initEcharts();
    this.getData(this.globalAppCode+':sy-fxgkqk-sqdt','dynamics')
  },
  methods: {
     getData(mark,filed) {
        this.spinShow=true
        let params={
          modelId: mark,
          condis:`[{"name":"timeRange","op":"=","value":"${this.type}","valueType":"string"}]`
        }
          this.$store.dispatch("postRequest", {
            url:this.$path.get_query_grid,
            params: params,
          }).then(res => {
            if (res.success) {
                this.spinShow=false
                if(filed=='daysData'){
                 }else{
                   this.$set(this.optionData,filed,res.rows?res.rows:[])
                }
            }else{
              this.spinShow=false
            }
          })
      },
    // 初始化图实例
    initEcharts() {
      // 初始化巡控等级分析
      initPatrolControlEcharts(this)
      // 初始化违规等级分析
      initViolationCharts(this)
    },
        changeTab(item) {
          
      this.sxDate.forEach(val => {
        this.$set(val, 'check', false)
      })
      this.$set(this,'type',item.type)
      this.$set(item, 'check', true)
      this.getData(this.globalAppCode+':sy-fxgkqk-sqdt','dynamics')
    },
  }
}
</script>

<style lang="less" scoped>
@import "./seekMedicalAdvice.less";
</style>