# BodyMarkerComponent 人体标记组件

## 功能描述

BodyMarkerComponent 是一个用于在人体模型上进行标记的可复用组件，主要用于健康检查、伤情登记等场景。

## 主要功能

1. **人体模型显示**：显示人体轮廓图，支持自定义背景图片
2. **交互式标记**：点击人体模型添加标记点，自动编号
3. **图片拍照**：集成相机功能，为每个标记点拍照
4. **描述编辑**：为每个标记点添加文字描述
5. **数据管理**：支持增删改查标记数据
6. **只读模式**：支持只读模式用于数据展示

## 使用方法

### 基本用法

```vue
<template>
  <div>
    <BodyMarkerComponent 
      v-model="markerData"
      @change="handleMarkerChange"
    />
  </div>
</template>

<script>
import BodyMarkerComponent from '@/components/body-marker/BodyMarkerComponent.vue'

export default {
  components: {
    BodyMarkerComponent
  },
  data() {
    return {
      markerData: []
    }
  },
  methods: {
    handleMarkerChange(data) {
      console.log('标记数据变更:', data)
    }
  }
}
</script>
```

### 只读模式

```vue
<template>
  <BodyMarkerComponent 
    v-model="markerData"
    :readonly="true"
  />
</template>
```

### 自定义配置

```vue
<template>
  <BodyMarkerComponent 
    v-model="markerData"
    :background-image="customImage"
    :model-size="{ width: 500, height: 400 }"
  />
</template>

<script>
export default {
  data() {
    return {
      markerData: [],
      customImage: '~@/assets/images/custom-body.png'
    }
  }
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value / v-model | Array | [] | 标记数据数组 |
| readonly | Boolean | false | 是否只读模式 |
| backgroundImage | String | '~@/assets/images/detentionEnter/fullBodyPhoto.png' | 人体模型背景图片路径 |
| modelSize | Object | {width: 401, height: 334} | 模型框尺寸 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | markerData | v-model 双向绑定事件 |
| change | markerData | 标记数据变更事件 |

## 数据格式

### 标记数据结构

```javascript
[
  {
    serialNumber: 1,        // 序号
    id: null,              // 数据库ID（新增时为null）
    ms: '描述信息',         // 描述
    zb: { x: 100, y: 150 }, // 坐标位置
    zp: 'image_url',       // 图片URL
    zpList: ['image_url']  // 图片列表（用于预览）
  }
]
```

## 方法

组件提供以下公共方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getMarkerData | - | Array | 获取当前标记数据 |
| setMarkerData | data: Array | - | 设置标记数据 |
| clearMarkers | - | - | 清空所有标记 |

### 方法使用示例

```vue
<template>
  <div>
    <BodyMarkerComponent ref="bodyMarker" v-model="markerData" />
    <Button @click="getData">获取数据</Button>
    <Button @click="clearData">清空数据</Button>
  </div>
</template>

<script>
export default {
  methods: {
    getData() {
      const data = this.$refs.bodyMarker.getMarkerData()
      console.log('当前标记数据:', data)
    },
    clearData() {
      this.$refs.bodyMarker.clearMarkers()
    }
  }
}
</script>
```

## 样式自定义

组件使用 scoped 样式，如需自定义样式，可以通过以下方式：

```vue
<style>
/* 自定义标记点样式 */
.body-marker-component .marker {
  background-color: blue !important;
}

/* 自定义模型框样式 */
.body-marker-component .man-model-box {
  border: 2px solid #333 !important;
}
</style>
```

## 依赖组件

- `camera-modal`: 相机拍照组件
- `iView`: UI 组件库（Table, Button, Input, Icon, Col, Row）
- `element-ui`: el-image 组件用于图片预览

## 注意事项

1. 确保项目中已安装并配置好相机组件
2. 背景图片路径需要正确配置
3. 在只读模式下，所有交互功能将被禁用
4. 组件会自动处理标记点的重新编号
5. 图片数据需要确保 URL 的有效性

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的标记功能
- 集成拍照功能
- 支持只读模式
