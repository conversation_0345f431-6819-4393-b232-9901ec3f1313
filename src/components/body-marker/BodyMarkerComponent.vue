<template>
  <div class="body-marker-component">
    <Row>
      <Col span="9">
        <div>
          <div class="man-model-box" @click="addLocation">
            <div
              v-if="markerData && markerData.length"
              v-for="(data, index) in markerData"
              :key="index"
              class="marker"
              :style="{ left: data.zb.x + 'px', top: data.zb.y + 'px' }"
              @click.stop="handleMarkerClick(data, index)"
            >
              {{ index + 1 }}
            </div>
          </div>
        </div>
      </Col>
      <Col span="15">
        <Table :columns="tableColumns" :data="markerData" border class="bodyTable">
          <template slot-scope="{ row, index }" slot="picture">
            <div v-if="!row.zp" @click="takePhoto(row, index)" style="width: 80px; height: 80px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 100px;">
              <Icon type="ios-add" size="60" color="rgb(187 187 187)" />
            </div>
            <div v-if="row.zp">
              <el-image
                style="width: 80px; height: 80px"
                :src="row.zp"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="row.zpList"
                fit="cover"
              />
            </div>
          </template>
          <template slot-scope="{ row, index }" slot="description">
            <Input type="text" v-model="row.ms" @on-change="onDescriptionChange(row, index)"/>
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <Button type="error" size="small" @click="removeMarker(index)">删除</Button>
          </template>
        </Table>
      </Col>
    </Row>

    <!-- 相机模态框 -->
    <camera-modal ref="cameraModal" @takePhoto="takePhotoItem"></camera-modal>
  </div>
</template>

<script>
import cameraModal from '@/components/camera/camera-modal.vue'

export default {
  name: 'BodyMarkerComponent',
  components: {
    cameraModal
  },
  props: {
    // 初始标记数据
    value: {
      type: Array,
      default: () => []
    },
    // 是否只读模式
    readonly: {
      type: Boolean,
      default: false
    },
    // 人体模型背景图片路径
    backgroundImage: {
      type: String,
      default: '~@/assets/images/detentionEnter/fullBodyPhoto.png'
    },
    // 模型框尺寸
    modelSize: {
      type: Object,
      default: () => ({
        width: 401,
        height: 334
      })
    }
  },
  data() {
    return {
      markerData: [],
      currentMarkerIndex: 0,
      emitTimer: null, // 防抖定时器
      tableColumns: [
        {
          type: 'index',
          width: 80,
          align: 'center',
          title: '序号'
        },
        {
          title: '图片',
          slot: 'picture',
          width: 150,
          align: 'center',
        },
        {
          title: '描述',
          slot: 'description',
          align: 'center',
        },
        {
          title: '操作',
          slot: 'action',
          width: 100,
          align: 'center'
        }
      ]
    }
  },
  watch: {
    value: {
      handler(newVal) {
        // 避免无限循环：只有当外部值真正改变时才更新内部数据
        if (JSON.stringify(newVal) !== JSON.stringify(this.markerData)) {
          this.markerData = this.processInitialData(newVal)
        }
      },
      immediate: true,
      deep: true
    },
    markerData: {
      handler(newVal) {
        // 防抖处理，避免频繁触发
        if (this.emitTimer) {
          clearTimeout(this.emitTimer)
        }
        this.emitTimer = setTimeout(() => {
          this.$emit('input', newVal)
          this.$emit('change', newVal)
        }, 50)
      },
      deep: true
    }
  },
  computed: {
    modelBoxStyle() {
      return {
        width: this.modelSize.width + 'px',
        height: this.modelSize.height + 'px',
        backgroundImage: `url(${this.backgroundImage})`
      }
    }
  },
  methods: {
    /**
     * 处理初始数据，确保数据格式正确
     */
    processInitialData(data) {
      if (!Array.isArray(data)) return []

      return data.map((item, index) => ({
        serialNumber: index + 1,
        id: item.id || null,
        ms: item.ms || '',
        zb: item.zb || { x: 0, y: 0 },
        zp: item.zp || '',
        zpList: item.zpList || (item.zp ? [item.zp] : [])
      }))
    },

    /**
     * 在人体模型上添加标记点
     */
    addLocation(event) {
      if (this.readonly) return

      try {
        let target = event.currentTarget
        let rect = target.getBoundingClientRect()
        // 计算相对于 div 左上角的坐标
        let x = event.clientX - rect.left
        let y = event.clientY - rect.top

        // 边界检查
        if (x < 0 || y < 0 || x > this.modelSize.width || y > this.modelSize.height) {
          return
        }

        let newMarker = {
          serialNumber: this.markerData.length + 1,
          id: null,
          zb: { x, y },
          ms: '',
          zp: '',
          zpList: []
        }

        this.markerData.push(newMarker)
      } catch (error) {
        console.error('添加标记点失败:', error)
        this.$Message.error('添加标记点失败')
      }
    },

    /**
     * 处理标记点点击事件
     */
    handleMarkerClick(data, index) {
      if (this.readonly) return
      // 可以在这里添加标记点的特殊处理逻辑
      // 比如显示详情、编辑等
      console.log('点击标记点:', data, index)
    },

    /**
     * 拍照
     */
    takePhoto(row, index) {
      if (this.readonly) return

      try {
        this.currentMarkerIndex = index
        this.$refs.cameraModal.open({})
      } catch (error) {
        console.error('打开相机失败:', error)
        this.$Message.error('打开相机失败，请检查相机权限')
      }
    },

    /**
     * 拍照完成回调
     */
    takePhotoItem(imgUrl) {
      if (this.currentMarkerIndex >= 0 && this.currentMarkerIndex < this.markerData.length) {
        this.$set(this.markerData[this.currentMarkerIndex], 'zp', imgUrl)

        if (!this.markerData[this.currentMarkerIndex].zpList) {
          this.$set(this.markerData[this.currentMarkerIndex], 'zpList', [])
        }
        this.markerData[this.currentMarkerIndex].zpList.push(imgUrl)
      }
    },

    /**
     * 描述变更
     */
    onDescriptionChange(row, index) {
      if (index >= 0 && index < this.markerData.length) {
        this.$set(this.markerData[index], 'ms', row.ms)
      }
    },

    /**
     * 删除标记点
     */
    removeMarker(index) {
      if (this.readonly) return

      if (this.markerData.length > 0 && index >= 0 && index < this.markerData.length) {
        this.markerData.splice(index, 1)
        // 重新编号
        this.markerData.forEach((item, idx) => {
          item.serialNumber = idx + 1
        })
      }
    },

    /**
     * 获取标记数据
     */
    getMarkerData() {
      return this.markerData
    },

    /**
     * 设置标记数据
     */
    setMarkerData(data) {
      this.markerData = this.processInitialData(data)
    },

    /**
     * 清空所有标记
     */
    clearMarkers() {
      if (this.readonly) return
      this.markerData = []
    }
  },

  beforeDestroy() {
    // 清理定时器
    if (this.emitTimer) {
      clearTimeout(this.emitTimer)
      this.emitTimer = null
    }
  }
}
</script>

<style scoped lang="less">
.body-marker-component {
  .man-model-box {
    position: relative;
    width: 401px;
    height: 334px;
    border: 1px solid #E5E5E5;
    background: url("~@/assets/images/detentionEnter/fullBodyPhoto.png");
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;

    &.readonly {
      cursor: default;
    }
  }

  .marker {
    position: absolute;
    width: 15px;
    height: 15px;
    background-color: red;
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 15px;
    font-weight: bold;
    user-select: none;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
  }

  .bodyTable {
    ::v-deep(.input-container) {
      width: 80px !important;
      height: 80px !important;
      margin: 5px;

      p {
        display: none;
      }
    }
  }
}
</style>
