<template>
  <div class="addMain">
    <!-- 左侧 -->
    <div class="leftMain">
      <p class="titleList">组件列表</p>
      <div class="searchStyle"><Input search placeholder="搜索组件" v-model="searchText" @on-search="searchMethod" @keyup.enter.native="searchMethod" /></div>
      <ul class="leftMainUl">
        <li v-for="(item, i) in leftList" :key="i" :name="item.name" :class="['typeName', { typeNameActive: item.check }]" @click="select(item)">
          <span class="zjList">{{ item.zjflName }}</span>
          <!-- <span class="num">{{ item.total }}</span> -->
          <Badge :count="item.total" class-name="num"></Badge>
        </li>
      </ul>
    </div>
    <!-- 右侧 -->
    <div class="rightMain">
      <p class="titleList">
        {{ titleObj.title }}（<span class="titleObjNum">{{ titleObj.num }}</span
        >）
      </p>
      <ul class="rightMainUl" v-if="rightList && rightList.length > 0">
        <li v-for="(item, i) in rightList" :key="i + 'A'" :class="['contentDiv', { contentActive: item.check }]" @click="selectDiv(item)">
          <div class="leftLi"><img :src="item.slt" class="imgIcon" /></div>
          <!-- <Icon type="md-checkmark-circle" v-if="item.check"  color="#348fe4" /> -->
          <Checkbox v-model="item.check" class="selectIcon"><span></span></Checkbox>
          <div class="rightLi">
            <p class="rightTitle">{{ item.zwmc }}</p>
            <p class="rightContent">{{ item.zjsm }}</p>
          </div>
        </li>
      </ul>
      <div class="tips" v-else>
        <img src="@/assets/images/no-people.png" alt="" />
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  props: ['mhId', 'layoutData'],
  data() {
    return {
      titleObj: {
        title: '全部',
        num: '0'
      },
      searchText: '',
      rightAllList: [],
      activeName: 1,
      selectList: [],
      leftList: [],
      rightList: []
    }
  },
  methods: {
    ...mapActions(['authPostRequest', 'postRequest', 'authGetRequest']),
    select(item) {
      this.$set(this.titleObj, 'title', item.zjflName)
      this.$set(this.titleObj, 'num', item.total)
      this.rightList = item.records
      if (this.rightList && this.rightList.length > 0) {
        this.rightList.forEach(item => {
          if (this.layoutData && this.layoutData.length > 0) {
            this.layoutData.forEach(val => {
              if (val.zjmc == item.name) {
                this.$set(item, 'check', true)
              }
            })
          }
        })
      }
      this.leftList.forEach(val => {
        this.$set(val, 'check', false)
      })
      this.$set(item, 'check', true)
    },
    // 对象数组去重方法
    unique(arr) {
      const res = new Map()
      return arr.filter(arr => !res.has(arr.id) && res.set(arr.id, 1))
    },
    selectDiv(item) {
      if (item.check) {
        this.$set(item, 'check', false)
        this.selectList.forEach((val, i) => {
          if (val.zjmc == item.name) {
            this.selectList.splice(i, 1)
          }
        })
      } else {
        this.$set(item, 'check', true)
        this.$set(item, 'zjmc', item.name)
        this.selectList.push(item)
      }

      this.selectList = this.unique(this.selectList)
    },
    searchMethod() {
      //字符串方法indexOf
      var arr = []
      this.rightAllList.forEach(item => {
        if (item.zwmc.indexOf(this.searchText) >= 0) {
          arr.push(item)
        }
      })
      this.rightList = arr
      this.$set(this.titleObj, 'title', this.searchText)
      this.$set(this.titleObj, 'num', this.rightList.length)
    },
    getInit() {
      let params = {
        mhId: this.mhId
      }
      let url =''
      if(this.mhId){
        url= this.$path.icp_mhpz_all_list
      }else{
        url= this.$path.icp_mhpz_all_zjlist
      }
      this.authGetRequest({ url: `${url}`, params: params }).then(res => {
        if (res.success) {
          this.selectList = []
          this.leftList = res.data
          if (this.leftList && this.leftList.length > 0) {
            this.leftList.forEach((val, i) => {
              val.name = Number(i + 1)
              if (i == 0) {
                val.check = true
              } else {
                val.check = false
              }
              if (val.zjflName == '全部') {
                this.rightAllList = val.records
              }
            })
            this.$set(this.titleObj, 'title', this.leftList[0].zjflName)
            this.$set(this.titleObj, 'num', this.leftList[0].total)
            this.rightList = this.leftList[0].records || []
            if (this.rightList && this.rightList.length > 0) {
              this.rightList.forEach(item => {
                if (this.layoutData && this.layoutData.length > 0) {
                  this.layoutData.forEach(val => {
                    if (val.zjmc == item.name) {
                      this.$set(item, 'check', true)
                      this.$set(item, 'zjmc', item.name)
                      this.selectList.push(item)
                    }
                  })
                }
              })
            }
          }
        }
      })
    }
  },
  mounted() {}
}
</script>
<style scoped lang="less">
.titleObjNum {
  font-weight: bold;
  color: #2b5fda;
}
.titleList {
  margin-left: -2px !important ;
  font-size: 16px;
  font-family: Microsoft YaHei Bold, Microsoft YaHei Bold-Bold;
  font-weight: 700;
  text-align: left;
  color: #3d4966;
  line-height: 40px;
  padding-left: 15px;
  box-sizing: content-box;
}
.titleList:last-of-type {
  border-bottom: 1px solid #dcdee2;
}

.searchStyle {
  background: #f5faff;
  // margin-left: -1px;
  // box-shadow: 0px 1px 0px 0px #cee0f0 inset;
  padding: 5px 20px;
}
.iconT {
  display: flex;
  align-items: center;
}
.leftMainUl {
  // height: 385px;
  margin-top: 10px;
  border-right: 1px solid #cee0f0;
}
.addMain {
  border: 1px solid #cee0f0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  line-height: 42px;
}

.typeName {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: 48px;
  line-height: 48px;
}
.typeName {
  align-items: center;
  align-self: center;
  border-left: 6px solid transparent;
  padding: 0 16px;
}
.typeName:hover {
  background: #ebf1ff;
  border-left: 6px solid #2b5fda;
  color: #2b5fda !important;
  .zjList {
    color: #2b5fda !important;
  }
  /deep/ .ivu-badge-count {
    color: #2b5fda;
    background: #fff;
  }
}
.typeNameActive {
  background: #ebf1ff;
  border-left: 6px solid #2b5fda;
  color: #2b5fda !important;
}
.typeNameActive .zjList {
  color: #2b5fda !important;
}
.typeNameActive /deep/ .ivu-badge-count {
  color: #2b5fda;
  background: #fff;
}
.zjList {
  font-size: 16px;
  font-family: Microsoft YaHei Regular, Microsoft YaHei Regular-Regular;
  font-weight: 400;
  text-align: justifyLeft;
  // color: #2b5fda;
  color: #2b3646;
  line-height: 48px;
}
.contentDiv {
  position: relative;
  border-radius: 4px;
  box-shadow: 0px 4px 6px 0px rgba(207, 213, 230, 0.5);
  padding: 16px;
  width: 98%;
  display: flex;
  border-radius: 2px;
  margin-bottom: 10px;
  border: 1px solid transparent;
  background: #fff;
}
.contentActive {
  background-color: rgba(52, 143, 228, 0.1) !important;
  border: 1px solid #348fe4 !important;
}
.selectIcon {
  position: absolute;
  right: 8px;
  top: 8px;
}
.contentDiv:hover {
  border: 1px solid #348fe4;
}
.rightMainUl {
  padding: 16px;
  height: 550px;
  overflow-y: auto;
  background: #f7f8fc;
}
.leftMain {
  width: 30%;
  height: 590px;
  border-right: 1px solid #cee0f0;
}
.rightMain {
  flex: 1;
}
.rightTitle {
  font-size: 16px;
  font-family: Microsoft YaHei Bold, Microsoft YaHei Bold-Bold;
  font-weight: 700;
  text-align: left;
  color: #2b3646;
  line-height: 34px;
}
.rightContent {
  font-size: 16px;
  font-family: Microsoft YaHei Regular, Microsoft YaHei Regular-Regular;
  font-weight: 400;
  text-align: left;
  color: #7a8699;
  line-height: 24px;
}
.rightNum {
  color: #888;
  margin-bottom: 0;
}
/deep/ .ivu-menu {
  width: 100% !important;
  padding: 10px 0;
}
/deep/ .ivu-badge-count {
  font-weight: 400;
  color: #7a8699;
  background: #ebf1ff;
  border: 1px solid #ebf1ff !important;
  box-shadow: 0 0 0 1px transparent;
}

/deep/ .ivu-menu-item-active .num {
  background-color: rgba(52, 143, 228, 0.2);
  color: #348fe4;
}
.leftLi {
  width: 156px;
  height: 88px;
  margin-right: 20px;
  background: #fafafa;
  border: 1px solid #cee0f0;
}
.imgIcon {
  width: 100%;
  height: 100%;
}
.addIconCom {
  width: 20px;
  height: 20px;
  display: inline-block;
  margin: 0 10px 0 -20px;
}
</style>
