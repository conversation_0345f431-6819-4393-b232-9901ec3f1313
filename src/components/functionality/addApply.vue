<template>
  <div class="icp-common-form icp-bottom-all content ">
    <!-- 全部应用 -->
    <ul class="appUl">
      <template v-for="(item, i) in appTypeList">
        <li :key="i" class="appLi" v-if="item.showType">
          <div class="leftTitle">
            <span>{{ item.typeName }}</span
            >(<span class="numApp">{{ item.typeList.length || 0 }}</span
            >)
          </div>
          <div class="rightTitle">
            <!-- 全选 -->
            <div class="iconDiv" @click="selectAll(i)">
              <p class="iconImg addApp"><Icon :type="item.selectText ? 'md-checkmark' : 'md-remove'" :color="'#4C91FF'" :size="32" class="addIconA" /></p>
              <p class="appName">{{ item.selectText ? '全选' : '取消全选' }}</p>
            </div>

            <div v-for="(val, index) in item.typeList" :key="index + 'A'" :class="[val.noDisabled?'':'noDisabled','iconDiv']" @click="selectCur(val, i)">
              <p :class="['iconImg','animationIcon']">
                <img :src="val.yytb" class="tbIcon" alt="" />
                <Icon v-if="val.check" type="ios-checkmark-circle" class="selectG" :color="'#4C91FF'" :size="20" /></p>
              <p class="appName">{{ val.yymc }}</p>
            </div>
          </div>
        </li>
      </template>
    </ul>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  props: ['myApp', 'appTypeListModel'],
  data() {
    return {
      appTypeList: [],
      xzApp: []
    }
  },
  watch:{
    myApp:{
      handler(n,o){
        this.xzApp=n
      },
      deep:true,
      immediate:true

    }
  },
  mounted() {
    // this.getData()
  },
  created() {},
  methods: {
    ...mapActions(['authPostRequest', 'postRequest', 'authGetRequest']),
    getData() {
       let params={
          systemId:serverConfig.APP_ID,
      }
      this.authGetRequest({ url: `${this.$path.acp_wdyy_getAllApply}`, params: params}).then(res => {
        if (res.success) {
          // let markArr = localStorage.getItem('appComp') ? JSON.parse(localStorage.getItem('appComp')) : []
          // let appTemp= res.data &&  res.data.length>0?JSON.parse(JSON.stringify(res.data)):[]
          let appTemp = []
          // markArr.forEach(mark => {
            res.data.forEach(ele => {
              if (ele.typeList && ele.typeList.length > 0 ) {
                this.$set(ele,'typeList',ele.typeList.filter(eleApp => eleApp.yylx != 1))
                if(ele.typeList && ele.typeList.length>0){
                  ele.showType=true
                }
                ele.selectText = true
                appTemp.push(ele)
              }
            });
          // });

          // appTemp.forEach((ele,index) => {
          //   this.$set(ele,'typeList',ele.typeList.filter(eleApp => eleApp.yylx != 1))
          // })
          this.appTypeList = appTemp
          this.appTypeList.forEach((item, i) => {
            if(item.typeList && item.typeList.length>0){
              item.showType=true
            }
            item.selectText = true
            item.typeList.forEach(val => {
              val.imgAppUrl=val.yytb  //require(`../../../public/image/${val.yytb}`)
              if(val.ljdz && val.ljdz!='/'){
                    val.noDisabled=true
                  }else{
                    val.noDisabled=false
              }
              this.xzApp.forEach(myAppItem => {
                if (myAppItem.id == val.id) {
                  this.$set(val, 'check', true)
                  this.selectCur(val,i,true) 
                  this.$forceUpdate()
                }
                
              })
            })
          })
        }
      })
    },
    selectAll(i) {
            this.appTypeList[i].selectText = !this.appTypeList[i].selectText
      if (this.appTypeList[i].selectText) {
        this.appTypeList[i].typeList.forEach(val => {
          this.$set(val, 'check', false)
        })
      } else {
        this.appTypeList[i].typeList.forEach(val => {
          this.$set(val, 'check', true)
        })
      }

      this.$forceUpdate()
    },
    selectCur(val, i,tag) {
        if(!tag){
           this.$set(val, 'check', !val.check)
        }
      
      this.appTypeList[i].selectText = this.appTypeList[i].typeList.some(val => {
        return val.check != true
      })
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="less" scoped>
@import url('./addApply.less');
.icp-common-content {
  width: 98% !important;
  padding-bottom: 0 !important;
}
/deep/ .icp-base-footer {
  position: absolute;
  padding: 10px 0;
  text-align: right;
  justify-content: space-between;
}
.content {
  min-width: 1024px;
  max-width: 1024px;
  height: 600px !important;
}
.iconDiv {
  width: 105px !important;
  position: relative;
}
.selectG {
  position: absolute;
  right: -20px;

}

</style>
