<template>
  <div class="zoom-bar-cls">
    <span class="btn-cls" :class="{'disabled-cls': zoomSize <= 0.2}" @click="zoomDFun()" title="缩小">
      <Icon type="md-remove" />
    </span>
    <div class="slider-outter">
      <Slider  v-model="curZoom" :min="1" :max="100" @on-change="zoomChange"></Slider>
    </div>
    <span class="btn-cls" :class="{'disabled-cls': zoomSize >= 2}" @click="zoomAFun()" title="放大">
      <Icon type="md-add" />
    </span>
    <span class="btn-cls ml-10" @click="zoomFit()" title="自适应">
      <img :src="restore" />
    </span>
  </div>
</template>

<script>
import restore from "./images/restore.png";
export default {
  props: {
    graph: {}
  },
  data() {
    return {
      zoom: 0.5, // 设置50作为初始面板值【0，1】
      curZoom: 50, // 由于那个滑块值，是整数。【1-100】因此需要手动除以100做转换
      zoomSize: 1,
      restore,
    };
  },
  mounted() {
    this.$nextTick(() => {
      if (this.graph) {
        // 获取当前的视图zoom值，作为初始化值。后续的放缩，都基于该比例进行计算
        let curZoom = this.graph.zoom();
        this.zoomSize = curZoom.toFixed(2);
      }
    });
  },
  methods: {
    // 放缩事件
    zoomChange() {
      this.zoomSize = this.curZoom / (this.zoom * 100);
      this.graph.zoomTo(this.zoomSize);
    },
    // 缩小
    zoomDFun() {
      if (this.zoomSize < 0.2) {
        return false;
      }
      this.zoomSize = (Number(this.zoomSize) - 0.1).toFixed(2);
      this.curZoom = this.zoomSize * 50;
      this.graph.zoomTo(this.zoomSize);
      return true;
    },
    // 放大
    zoomAFun() {
      if (this.zoomSize >= 2) {
        return false;
      }
      this.zoomSize = (Number(this.zoomSize) + 0.1).toFixed(2);
      this.curZoom = Number(this.zoomSize * 50);
      this.graph.zoomTo(this.zoomSize);
      return true;
    },
    zoomFit() {
      this.curZoom = 50;
      this.zoom = 0.5; // 设置50作为初始面板值【0，1】
      this.zoomSize = 1;
      if (this.graph) {
        this.graph.zoomToFit({
          padding: { left: 20, top: 20, right: 20, bottom: 88 },
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.zoom-bar-cls {
  position: absolute;
  bottom: 20px;
  z-index: 100;
  left: 0;
  right: 0;
  margin: auto;
  width: 380px;
  height: 48px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 2px 6px 1px rgba(0, 0, 0, 0.1);
  border-radius: 4px 4px 4px 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  .btn-cls {
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    i {
      font-size: 24px;
      color: #415060;
    }
  }
  .disabled-cls {
    i {
      font-size: 24px;
      color: #ccc;
      cursor: not-allowed;
    }
  }
  .slider-outter {
    width: 212px;
    height: 100%;
    margin: 0px 16px;
    /deep/.ui-slider {
      height: 100%;
    }
  }
  /deep/.right-value {
    display: none;
  }
}
</style>
