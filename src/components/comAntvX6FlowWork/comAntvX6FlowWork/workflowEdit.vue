<template>
  <div class="workflow-edit">
    <div class="preset-nodes">
      <img class="start-end" src="./images/start.svg" alt="" @dragstart="handleDragStart($event, 'start-node')">
      <img class="start-end" src="./images/end.svg" alt="" @dragstart="handleDragStart($event, 'end-node')">
      <div class="card-node" @dragstart="handleDragStart($event, 'card-node')" draggable="true"></div>
      <div class="card-node audit" @dragstart="handleDragStart($event, 'audit-node')" draggable="true"></div>
    </div>
    <div class="chart-container">
      <div
        :id="flowId"
        @drop="handleDrop"
        @dragover="handleDragOver"
        class="flow-chart"
      ></div>
      <div class="tool-box">
        <Button  @click="getWorkflowData">重置</Button>
        <Button type="primary" @click="clearGraph">清空</Button>
        <Button type="primary" :loading="submitLoading" @click="saveWorkflow">保存</Button>
        <Button type="primary" @click="exportJson">导出</Button>
      </div>
      <cell-form ref="cellForm"></cell-form>
    </div>
  </div>
</template>

<script>
// import { getWorkflowManageById, getWorkflowManageByCode, saveWorkflowManage } from "@/axios/wfesBaseService";
import { initGraph, dataToNode, nodeToData, dataToLine, lineToData } from "./graph";
import { bindEvents } from "./graphEdit";
import { downloadData } from "@/util";
import cellForm from "./components/cellForm";

export default {
  name: "comWorkflowEdit",
  components: { cellForm },
  props: {
    id: {
      type: String,
      default: "",
    },
    workflowCode: {
      type: String,
      default: "",
    },
    renderJson: {
      type: Object,
      default() {
        return {};
      }
    },
  },
  data() {
    return {
      flowId: `flow-${Math.random().toString().slice(-3)}`,
      graph: null,
      dragObj: null,
      submitLoading: false,
    };
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        if (val) {
          // this.getWorkflowData(getWorkflowManageById({ id: val }));
        } else {
          this.graph && this.graph.clearCells();
        }
      },
    },
    workflowCode: {
      immediate: true,
      handler(val) {
        if (val) {
          // this.getWorkflowData(getWorkflowManageByCode({ workflowCode: val }));
        } else {
          this.graph && this.graph.clearCells();
        }
      },
    },
    renderJson: {
      immediate: true,
      handler(val) {
        if (val && JSON.stringify(val) !== "{}") {
          this.$nextTick(() => {
            this.drawGraph(val);
          });
        } else {
          this.graph && this.graph.clearCells();
        }
      },
    },
  },
  methods: {
    exportJson() {
      if (!this.graph) return;
      const nodeList = this.graph.getNodes().map((node) => nodeToData(node, { addId: true }));
      const lineList = this.graph.getEdges().map(lineToData);
      const graphData = { nodeList, lineList };
      const blob = new Blob([JSON.stringify(graphData, null, 2)], { type: "application/json" });
      downloadData(blob, "graph.json");
    },
    clearGraph() {
      if (this.graph) {
        this.graph.clearCells();
        this.$refs.cellForm.closeModal();
      }
    },
    refreshGraph() {
      if (this.graph) return this.clearGraph();
      this.graph = initGraph(this.flowId, true);
      bindEvents(this.graph, this.flowId);
      this.graph.on("cell:contextmenu", ({ e, cell }) => {
        e.preventDefault();
        this.$refs.cellForm.openModal(e, cell, this.flowId);
      });
    },
    async saveWorkflow() {
      console.log(this.graph,'this.graph')
      try {
        this.submitLoading = true;
        const nodeList = this.graph.getNodes().map(nodeToData);
        const lineList = this.graph.getEdges().map(lineToData);
        const { returnCode } = await saveWorkflowManage({
          id: this.id,
          nodeList,
          lineList,
        }) || {};
        if (returnCode === 0) {
          this.$Message.success("保存成功");
        }
      } catch (err) {
        this.$Message.error(`保存流程失败：${err.message || err}`);
      } finally {
        this.submitLoading = false;
      }
    },
    async getWorkflowData(request) {
      try {
        const { returnCode, data } = await request || {};
        if (returnCode === 0) {
          this.drawGraph(data);
        }
      } catch (err) {
        this.$Message.error(`获取流程详情失败：${err.message || err}`);
      }
    },
    drawGraph({ nodeList, lineList }) {
      this.refreshGraph();
      this.graph.addNodes(nodeList.map((item) => dataToNode(item)));
      this.graph.addEdges(lineList.map((item) => dataToLine(item)));
      this.graph.zoomToFit({
        padding: { left: 20, top: 20, right: 20, bottom: 80 }
      });
    },
    handleDragStart(e, nodeType) {
      e.dataTransfer.dropEffect = "move";
      this.dragObj = {
        nodeType,
        offsetX: e.offsetX,
        offsetY: e.offsetY,
      };
    },
    handleDrop(e) {
      e.preventDefault();
      if (!this.dragObj) return;
      const coords = this.graph.clientToLocal(e.clientX - this.dragObj.offsetX, e.clientY - this.dragObj.offsetY);
      const node = dataToNode({
        nodeType: this.dragObj.nodeType,
        nodeX: coords.x,
        nodeY: coords.y,
        nodeStatus: 1,
        nodeLevel: this.dragObj.nodeType === "start-node" ? 0 :
          this.dragObj.nodeType === "end-node" ? 9 : 1, // 节点级别
        endNode: this.dragObj.nodeType === "end-node" ? 1 : 0, // 是否默认节点
      });
      this.graph.addNode(node);
      this.dragObj = null;
    },
    handleDragOver(e) {
      e.preventDefault();
      e.dataTransfer.dropEffect = "move";
    },
  },
  mounted() {
    this.refreshGraph();
    const flowContainer = document.getElementById(this.flowId);
    flowContainer && flowContainer.addEventListener("mousedown", () => {
      this.$refs.cellForm.closeModal();
    });
  },
  beforeDestroy() {
    this.graph.dispose();
    this.graph.clearKeys();
    this.clearGraph();
    this.graph = null;
    const flowContainer = document.getElementById(this.flowId);
    flowContainer && flowContainer.removeEventListener("mousedown");
  },
};
</script>

<style lang="less" scoped>
.workflow-edit {
  width: 100%;
  height: 100%;
  padding-bottom: 16px;
  display: flex;
  .preset-nodes {
    flex: none;
    width: 220px;
    padding: 0 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-right: 1px solid #dfe3e8;
    overflow-y: auto;
    .start-end {
      flex: none;
      width: 58px;
      height: 58px;
      margin-top: 20px;
      border: 1px solid #ACCDEE;
      border-radius: 50%;
      box-shadow: 0px 2px 6px 0px rgba(0,34,84,0.12);
      cursor: move;
    }
    .card-node {
      flex: none;
      width: 185px;
      height: 100px;
      margin-top: 20px;
      border: 1px solid #ACCDEE;
      border-radius: 8px;
      box-shadow: 0px 4px 4px 0px rgba(70,70,70,0.1);
      position: relative;
      cursor: move;
      &::before {
        content: url(./images/avatar.svg);
        position: absolute;
        left: 20px;
        top: 34px;
      }
      &::after {
        content: url(./images/corner.svg);
        position: absolute;
        right: 1px;
        top: 1px;
      }
      &.audit {
        border: 1px solid rgba(255,152,10,0.6);
        box-shadow: 0px 4px 4px 0px rgba(255,152,10,0.1);
        &::before {
          content: url(./images/avatar_audit.svg);
        }
        &::after {
          content: url(./images/corner_audit.svg);
        }
      }
    }
  }
  .chart-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    .flow-chart {
      width: 100%;
      height: 100%;
    }
    .tool-box {
      position: absolute;
      left: 16px;
      right: 16px;
      bottom: 30px;
      text-align: center;
      z-index: 1;
      button{
          margin-right: 10px;
      }
    }
    .test {
      display: none;
      position: absolute;
      z-index: 1;
    }
  }
}
</style>
<style lang="less">
@import "./animation.less";
.x6-widget-selection-inner {
  border: 1px solid #239edd;
}
.x6-widget-selection-box {
  opacity: 0;
}
</style>
