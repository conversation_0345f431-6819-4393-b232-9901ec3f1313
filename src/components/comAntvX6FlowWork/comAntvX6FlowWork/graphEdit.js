import { Selection } from "@antv/x6-plugin-selection";
import { Snapline } from "@antv/x6-plugin-snapline";
import { Keyboard } from "@antv/x6-plugin-keyboard";
import { Clipboard } from "@antv/x6-plugin-clipboard";
import { History } from "@antv/x6-plugin-history";

export function bindEvents(graph, flowId) {
  graph
    .use(new Selection({
      modifiers: "ctrl",
      rubberband: true,
      showNodeSelectionBox: true,
    }))
    .use(new Snapline())
    .use(new Keyboard())
    .use(new Clipboard())
    .use(new History());
  graph.bindKey(["meta+c", "ctrl+c"], () => {
    const cells = graph.getSelectedCells();
    if (cells.length) {
      graph.copy(cells);
    }
    return false;
  });
  graph.bindKey(["meta+x", "ctrl+x"], () => {
    const cells = graph.getSelectedCells();
    if (cells.length) {
      graph.cut(cells);
    }
    return false;
  });
  graph.bindKey(["meta+v", "ctrl+v"], () => {
    if (!graph.isClipboardEmpty()) {
      const cells = graph.paste({ offset: 32 });
      graph.cleanSelection();
      graph.select(cells);
    }
    return false;
  });
  graph.bindKey(["meta+z", "ctrl+z"], () => {
    if (graph.canUndo()) {
      graph.undo();
    }
    return false;
  });
  graph.bindKey(["meta+shift+z", "ctrl+shift+z"], () => {
    if (graph.canRedo()) {
      graph.redo();
    }
    return false;
  });
  graph.bindKey(["meta+a", "ctrl+a"], (e) => {
    e.preventDefault();
    const nodes = graph.getNodes();
    if (nodes) {
      graph.select(nodes);
    }
  });
  graph.bindKey("backspace", () => {
    const cells = graph.getSelectedCells();
    if (cells.length) {
      graph.removeCells(cells);
    }
  });
  graph.on("cell:mouseenter", ({ cell }) => {
    // 连接桩显示/隐藏
    const container = document.getElementById(flowId);
    const ports = container.querySelectorAll(".x6-port-body");
    ports.forEach((port) => {
      port.style.visibility = "visible";
    });
    if (cell.isNode()) {
      cell.addTools([
        {
          name: "button-remove",
          args: {
            x: "100%",
            y: 0,
            offset: { x: -20, y: 20 },
            visibility: "hidden"
          },
        },
      ]);
    } else {
      cell.addTools([
        {
          name: "button-remove",
          args: { distance: -30 },
        },
        {
          name: "source-arrowhead",
          args: {
            tagName: "circle",
            attrs: {
              r: 7,
              stroke: 0,
            },
          },
        },
        {
          name: "target-arrowhead",
          args: {
            attrs: {
              width: 12,
              height: 8,
              stroke: 0,
            },
          },
        },
      ]);
    }
  });
  graph.on("cell:mouseleave", ({ cell }) => {
    // 连接桩显示/隐藏
    const container = document.getElementById(flowId);
    const ports = container.querySelectorAll(".x6-port-body");
    ports.forEach((port) => {
      port.style.visibility = "hidden";
    });
    const { items: tools } = cell.getTools() || {};
    if (!tools) return;
    tools.forEach((tool) => {
      cell.removeTool(tool.name);
    });
  });
}
