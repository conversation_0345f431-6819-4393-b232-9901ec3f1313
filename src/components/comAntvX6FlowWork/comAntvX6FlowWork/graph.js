import { Graph, Shape } from "@antv/x6";

import start from "./images/start.svg";
import end from "./images/end.svg";
import corner from "./images/corner.svg";
import cornerAudit from "./images/corner_audit.svg";
import avatarCard from "./images/avatar.svg";
import avatarDisabledCard from "./images/avatar_disabled.svg";
import avatarAduit from "./images/avatar_audit.svg";
import rTopImageDisabled from "./images/r_top_image_disabled.svg";

let nodeStrokeColor = "#5FADFB"; // 节点边框颜色
let nodeStrokeAuditColor = "#FF980A"; // 节点边框颜色
let nodeStrokeDisabledColor = "#5F709A"; // 禁用节点边框颜色

function registerNode() {
  const portAttrs = { // 定义连接柱的样式
    circle: {
      r: 6, // 半径
      magnet: true,
      stroke: nodeStrokeColor,
      strokeWidth: 2,
      fill: "#fff",
      visibility: "hidden",
    }
  };
  const portsConfig = {
    groups: {
      top: {
        position: "top", // 定义连接柱的位置，如果不配置，将显示为默认样式
        label: { position: "top" },
        attrs: portAttrs,
      },
      bottom: {
        position: "bottom",
        label: { position: "bottom" },
        attrs: portAttrs,
      },
      right: {
        position: "right",
        label: { position: "right" },
        attrs: portAttrs,
      },
      left: {
        position: "left",
        label: { position: "left" },
        attrs: portAttrs,
      }
    },
    items: [
      { id: "top", group: "top" },
      { id: "bottom", group: "bottom" },
      { id: "left", group: "left" },
      { id: "right", group: "right" }
    ]
  };
  const startEndConfig = ({ xlinkHref }) => {
    return {
      width: 58,
      height: 58,
      ports: portsConfig,
      markup: [
        { tagName: "rect", selector: "body" },
        { tagName: "image", selector: "avatar" }
      ],
      attrs: {
        // 边框
        body: {
          refWidth: "100%",
          refHeight: "100%",
          fill: "#fff",
          stroke: "#ACCDEE",
          strokeWidth: 1,
          rx: 60,
          ry: 60,
          filter: {
            name: "dropShadow",
            args: {
              dx: 0,
              dy: 2,
              blur: 6,
              color: "rgba(0,34,84,0.12)",
            },
          },
        },
        // 图标
        image: {
          xlinkHref,
          width: 58,
          height: 58,
        }
      },
    };
  };
  Graph.registerNode("start-node", startEndConfig({ xlinkHref: start }), true);
  Graph.registerNode("end-node", startEndConfig({ xlinkHref: end }), true);
  const nodeConfig = ({ stroke, filter, avatar, color, rightTopImage }) => {
    return {
      width: 185,
      height: 100,
      ports: portsConfig,
      markup: [
        { tagName: "rect", selector: "body" },
        { tagName: "image", selector: "avatar" },
        { tagName: "image", selector: "rightTopImage" },
        { tagName: "text", selector: "name" },
        { tagName: "rect", selector: "tipBg" },
        { tagName: "text", selector: "tipNum" },
      ],
      attrs: {
        // 边框
        body: {
          refWidth: "100%",
          refHeight: "100%",
          fill: "#fff",
          stroke,
          strokeWidth: 1,
          rx: 10,
          ry: 10,
          pointerEvents: "visiblePainted",
          filter,
          boxShadow: "10px 10px 5px 1px rgba(0, 0, 0, 0.3)",
          cursor: "pointer",
        },
        // 左侧图标
        avatar: {
          xlinkHref: avatar,
          width: 30,
          height: 30,
          refX: 20,
          refY: 34,
          class: "node-avatar",
          style: {
            "--size": 30,
            "--refX": 20,
            "--refY": 34,
          },
        },
        // 流程名称
        // 矩形 x,y轴坐标位于节点右下角
        name: {
          refX: 40,
          refX2: 15,
          refY: "48%",
          fontFamily: "Source Han Sans CN, Source Han Sans CN",
          yAlign: "middle",
          fill: color, // 字体颜色
          fontSize: 20,
          fontWeight: "400",
          textAnchor: "start",
          cursor: "pointer",
          textWrap: {
            width: "68%",
            height: "80%",
            ellipsis: true,
          },
        },
        // 右上角背景图片
        rightTopImage: {
          xlinkHref: rightTopImage,
          width: 78,
          height: 66,
          refX: 108,
          refY: 1,
          cursor: "pointer",
        },
        // 未读信息提示,tipBg背景效果，tipNum具体数字
        tipBg: {
          fill: "rgb(254 86 78)",
          opacity: 0.9,
          visibility: "hidden", // hidden、visible
          fontSize: 18,
          width: 40,
          height: 40,
          display: "block",
          rx: 40,
          ry: 40,
          refX: "100%",
          xAlign: "middle",
          yAlign: "middle",
          cursor: "pointer",
        },
        tipNum: {
          fill: "#fff", // 字体颜色
          fontSize: 20,
          visibility: "hidden", // hidden、visible
          fontWeight: "600",
          refX: "100%",
          xAlign: "middle",
          yAlign: "middle",
        }
      },
    };
  };
  Graph.registerNode("card-node", nodeConfig({
    stroke: nodeStrokeColor,
    filter: {
      name: "dropShadow",
      args: {
        dx: 0,
        dy: 4,
        blur: 4,
        color: "rgba(70,70,70,0.1)",
      },
    },
    color: "#00244A", // 字体颜色
    rightTopImage: corner,
  }), true);
  Graph.registerNode("audit-node", nodeConfig({
    stroke: nodeStrokeAuditColor,
    filter: {
      name: "dropShadow",
      args: {
        dx: 0,
        dy: 4,
        blur: 4,
        color: "rgba(255,152,10,0.1)",
      },
    },
    color: "#ff7700", // 字体颜色
    rightTopImage: cornerAudit,
  }), true);
}

function registerEdge() {
  Shape.Edge.config({
    attrs: {
      line: {
        stroke: nodeStrokeColor,
        strokeWidth: 2,
        targetMarker: {
          name: "block",
          width: 12,
          height: 8
        },
        strokeDasharray: 0,
        class: "ant-line",
      },
    },
    label: {
      attrs: {
        label: {
          fontWeight: "500",
          fill: nodeStrokeColor,
          fontSize: 16,
        },
      },
    },
    data: {
      lineStatus: 1,
      color: nodeStrokeColor,
    },
    zIndex: 1,
  });
}

// 获取流经节点/线的所有连接
function getAllConnectedEdges(graph, cell, direction = "all", visitedCells = new Set()) {
  const edges = [];
  if (visitedCells.has(cell.id)) return [];
  if (cell.isNode()) {
    if (direction === "all") {
      visitedCells.add(`${cell.id}-up`);
      visitedCells.add(`${cell.id}-down`);
    } else {
      visitedCells.add(`${cell.id}-${direction}`);
    }
    const node = cell;
    const data = node.getData() || {};
    if (!data.nodeStatus) return [];
    let connected;
    if (direction === "all") {
      connected = graph.getConnectedEdges(node);
    } else if (direction === "up") {
      connected = graph.getIncomingEdges(node);
    } else if (direction === "down") {
      connected = graph.getOutgoingEdges(node);
    }
    connected && connected.forEach((edge) => {
      const edgeData = edge.getData() || {};
      if (!edgeData.lineStatus) return [];
      edges.push(...getAllConnectedEdges(graph, edge, direction, visitedCells));
    });
  } else {
    visitedCells.add(cell.id);
    const edge = cell;
    const data = edge.getData() || {};
    if (!data.lineStatus) return [];
    const sourceNode = edge.getSourceCell();
    if (sourceNode && direction !== "down") {
      const sourceData = sourceNode.getData() || {};
      edges.push(edge);
      if (sourceData.nodeStatus) {
        edges.push(...getAllConnectedEdges(graph, sourceNode, "up", visitedCells));
      }
    }
    const targetNode = edge.getTargetCell();
    if (targetNode && direction !== "up") {
      const targetData = targetNode.getData() || {};
      edges.push(edge);
      if (targetData.nodeStatus) {
        edges.push(...getAllConnectedEdges(graph, targetNode, "down", visitedCells));
      }
    }
  }
  return edges;
}

export function initGraph(flowId, editable = false, options = {}) {
  registerNode();
  registerEdge();
  const graph = new Graph({
    container: document.getElementById(flowId),
    autoResize: true,
    background: {
      color: "#F7F9FC",
    },
    grid: {
      visible: true,
      type: "dot",
      size: 20,
      args: {
        color: "#E6ECF2", // 网点颜色
        thickness: 4, // 网点大小
      },
    },
    // mousewheel: true, // 画布放缩
    mousewheel: { // 画布放缩
      enabled: true,
      zoomAtMousePosition: false, // 是否将鼠标位置作为中心缩放
      guard: (event) => { // 判断一个滚轮事件是否应该被处理
        try {
          // 此处主要是为了和实战平台代码保持一致。实战平台代码，多了一个放大缩小的工具条
          if (event.deltaY > 0) { // 向前滚动（缩小）
            if (options.graphToolbar) {
              options.graphToolbar.zoomDFun();
            } else {
              return true;
            }
          } else if (event.deltaY < 0) { // 向后滚动（放大）
            if (options.graphToolbar) {
              options.graphToolbar.zoomAFun();
            } else {
              return true;
            }
          }
        } catch (e) {
          return false;
        }
      },
    },
    panning: true,
    scaling: {
      min: 0.05, // 默认值为 0.01
      max: 12, // 默认值为 16
    },
    interacting: {
      nodeMovable: editable, // 节点是否可以被移动
      edgeMovable: editable, // 边是否可以被移动。
    },
    // 连线选项,配置 connecting 可以实现丰富的连线交互
    connecting: {
      router: { // 路径样式
        name: "manhattan",
        args: {
          padding: 40,
        },
      },
      allowEdge: false, // 是否允许边链接到另一个边
      allowNode: false,
      connector: {
        name: "rounded",
        args: {
          radius: 8
        }
      },
      anchor: "center", // 被连接的节点的锚点
      connectionPoint: "anchor", // 指定连接点
      allowBlank: false,
      snap: { // 吸附
        radius: 20
      },
      // 后端说前端暂时不用限制线的连接
      // validateConnection({ sourceCell, targetCell }) {
      //   const sourceData = sourceCell.getData();
      //   const targetData = targetCell.getData();
      //   if (!sourceData || !targetData) return true;
      //   return sourceData.nodeLevel <= targetData.nodeLevel;
      // },
      highlight: true,
    },
    // 可以连线的连接桩高亮
    highlighting: {
      magnetAvailable: {
        name: "stroke",
        args: {
          padding: 6,
          attrs: {
            stroke: "#52C41A",
          },
        },
      },
    },
  });
  graph.on("cell:mouseenter", ({ cell }) => {
    const connectedEdges = getAllConnectedEdges(graph, cell);
    connectedEdges.forEach((edge) => {
      const data = edge.getData();
      if (!data.lineStatus) return;
      edge.setAttrByPath("line/strokeDasharray", 5);
    });
    graph.once("cell:mouseleave", ({ cell: leaveCell }) => {
      if (leaveCell === cell) {
        connectedEdges.forEach((edge) => {
          edge.setAttrByPath("line/strokeDasharray", 0);
        });
      }
    });
  });
  return graph;
}

function toBase64(str) {
  // 将字符串转换为 Uint8Array
  const encoder = new TextEncoder();
  const uint8Array = encoder.encode(str);

  // 将 Uint8Array 转换为二进制字符串
  const binaryString = Array.from(uint8Array, byte => String.fromCharCode(byte)).join("");

  // 使用 btoa 进行 Base64 编码
  return btoa(binaryString);
}

function fromBase64(base64String) {
  // 使用 atob 将 Base64 编码的字符串解码为二进制字符串
  const binaryString = atob(base64String);

  // 将二进制字符串转换为 Uint8Array
  const uint8Array = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    uint8Array[i] = binaryString.charCodeAt(i);
  }

  // 使用 TextDecoder 将 Uint8Array 解码为原始字符串
  const decoder = new TextDecoder();
  return decoder.decode(uint8Array);
}

function replaceBase64Text(base64String = "", reg = "", replaceStr = "") {
  if (!base64String) return "";
  const [pre, str] = base64String.split(",");
  if (!pre.endsWith("base64") || !str) return base64String;
  const text = fromBase64(str).replace(reg, replaceStr);
  return `${pre},${toBase64(text)}`;
}

export function dataToNode(item, { currentNodeId = "", disabledNodes = {} } = {}) {
  let attrs = {};
  if (["card-node", "audit-node"].includes(item.nodeType)) {
    const avatar = item.nodeType === "audit-node" ? avatarAduit : avatarCard;
    attrs = {
      body: {
        cursor: "pointer",
        stroke: item.nodeType === "card-node" ? nodeStrokeColor : nodeStrokeAuditColor,
        fill: currentNodeId === item.id ? (item.nodeType === "card-node" ? "rgba(35, 144, 255, .8)" : "rgba(255, 119, 0, .8)") : (item.nodeStatus ? "#FFFFFF" : "#F7F7F7"),
      },
      avatar: {
        xlinkHref: currentNodeId === item.id ? replaceBase64Text(item.image || avatar, /(#2390FF|#FF7700)/g, "#FFFFFF") : item.image || avatar,
      },
      name: {
        cursor: "pointer",
        text: item.nodeName || "",
        fill: currentNodeId === item.id ? "#FFFFFF" : undefined,
      },
      tipBg: {
        cursor: "pointer",
      },
      rightTopImage: {
        cursor: "pointer",
        xlinkHref: item.nodeType === "card-node" ? corner : cornerAudit
      },
    };
    // 灰色，禁用 或者 不可点击
    if (item.nodeStatus === 0 || disabledNodes[item.nodeCode]) {
      attrs.body.stroke = "#A2ACC6";
      attrs.name.fill = nodeStrokeDisabledColor;
      attrs.avatar = {
        xlinkHref: replaceBase64Text(item.image || avatarDisabledCard, /(#2390FF|#FF7700)/g, nodeStrokeDisabledColor),
      };
      attrs.rightTopImage.xlinkHref = rTopImageDisabled;
      attrs.rightTopImage.cursor = "default";
      attrs.name.cursor = "default";
      attrs.body.cursor = "default";
      attrs.tipBg.cursor = "default";
    }
  }
  return {
    id: item.id,
    shape: item.nodeType || "card-node",
    position: {
      x: +item.nodeX,
      y: +item.nodeY,
    },
    data: item,
    attrs,
  };
}

export function nodeToData(node, { addId } = {}) {
  const { x: nodeX, y: nodeY } = node.getPosition();
  const image = node.getAttrByPath("avatar/xlinkHref") || "";
  // 新增节点时id是X6生成的36位超出后端限制导致新增报错，不能加id，
  // 导出json需要加上id
  return {
    id: addId ? node.id : undefined,
    ...node.getData(),
    nodeType: node.shape,
    nodeX,
    nodeY,
    image: image.includes("base64") ? image : "", // 图片路径不上传
    nodeName: node.getAttrByPath("name/text"),
  };
}

export function dataToLine(item) {
  return {
    id: item.id,
    source: { cell: item.nodeId, port: item.sourcePort || "right" },
    target: { cell: item.nextNodeId, port: item.targetPort || "right" },
    data: item,
    attrs: {
      line: {
        stroke: item.lineStatus ? item.color || nodeStrokeColor : "#C0C4CC",
      },
    },
    label: {
      attrs: {
        label: {
          strokeWidth: 2,
          text: item.lineStatus ? item.lineName || "" : "X",
          fill: item.lineStatus ? item.color || nodeStrokeColor : "#F74037",
          fontWeight: 500,
          fontSize: 16
        },
      },
    },
  };
}

export function lineToData(line) {
  const { cell: nodeId, port: sourcePort } = line.getSource();
  const { cell: nextNodeId, port: targetPort } = line.getTarget();
  const labels = line.getLabels();
  const lineData = line.getData();
  return {
    ...lineData,
    nodeId, // 来源点
    sourcePort,
    nextNodeId, // 目标点
    targetPort,
    lineName: lineData.lineStatus ? labels[0].attrs.label.text : lineData.lineName,
    color: lineData.lineStatus ? line.getAttrByPath("line/stroke") : lineData.color,
  };
}
