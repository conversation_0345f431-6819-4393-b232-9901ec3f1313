// 节点图标动画
.x6-node:hover {
  .node-avatar {
    animation: pulse 1.5s infinite;
  }
}

// @keyframes pulse {
//   0% {
//     transform: matrix(1, 0, 0, 1, var(--refX), var(--refY));
//   }

//   50% {
//     transform: matrix(1, 0, 0, 1, calc(var(--refX) - 2), calc(var(--refY) - 2)) scale(1.2);
//   }

//   100% {
//     transform: matrix(1, 0, 0, 1, var(--refX), var(--refY));
//   }
// }

// 线条动画
.ant-line {
  animation: ant-line 30s infinite linear;
}

@keyframes ant-line {
  to {
    stroke-dashoffset: -1000;
  }
}
