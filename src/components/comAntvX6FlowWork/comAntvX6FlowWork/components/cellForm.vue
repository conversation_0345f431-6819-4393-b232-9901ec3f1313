<template>
  <div ref="cellForm" class="cell-form" :style="modalStyle" v-show="showModal">
    <div class="form-header">{{ formType === "node" ? "节点" : "线条" }}数据编辑</div>
    <Icon  type="ios-close" class="form-close" @click="closeModal"></Icon >
    <Form ref="form" :model="formData" :rules="rules" :col="1" :label-width="90" type="fit" class="form-content">
      <template v-if="formData.nodeType === 'start-node' || formData.nodeType === 'end-node'">
        <FormItem  label="业务状态" prop="businessStatus">
          <Input v-model="formData.businessStatus"></Input>
        </FormItem >
      </template>
      <template v-else-if="formType === 'node'">
        <FormItem  label="节点图标" prop="image">
          <div class="camera-select" @click="$refs.fileInput.click()">
            <img v-if="formData.image" :src="formData.image"></img>
            <Icon  type="ios-camera-outline" v-else class="camera-icon"></Icon >
          </div>
          <input ref="fileInput" type="file" @change="onUpload" v-show="false" accept="image/*" />
        </FormItem >
        <FormItem  label="节点名称" prop="nodeName">
          <Input v-model="formData.nodeName"></Input>
        </FormItem >
        <FormItem  label="节点编码" prop="nodeCode">
          <Input v-model="formData.nodeCode"></Input>
        </FormItem >
        <FormItem  label="是否启用" prop="nodeStatus">
          <RadioGroup  v-model="formData.nodeStatus">
            <Radio  :value="1">启用</Radio >
            <Radio  :value="0">禁用</Radio >
          </RadioGroup >
        </FormItem >
        <FormItem  label="业务状态" prop="businessStatus">
          <Input v-model="formData.businessStatus"></Input>
        </FormItem >
      </template>
      <template v-else>
        <FormItem  label="线条名称" prop="lineName">
          <Input v-model="formData.lineName"></Input>
        </FormItem >
        <FormItem  label="线条颜色" prop="color">
          <ColorPicker  v-model="formData.color" :colors="['#5FADFB', '#00C170', '#FA4242']" transfer></ColorPicker>
        </FormItem >
        <FormItem  label="是否启用" prop="lineStatus">
          <RadioGroup  v-model="formData.lineStatus">
            <Radio  :value="1">启用</Radio >
            <Radio  :value="0">禁用</Radio >
          </RadioGroup >
        </FormItem >
        <FormItem  label="业务状态" prop="businessStatus">
          <Input v-model="formData.businessStatus"></Input>
        </FormItem >
        <FormItem  label="流转规则" prop="flowSpel">
          <Input v-model="formData.flowSpel" type="textarea"></Input>
        </FormItem >
      </template>
    </Form>
    <div class="form-footer">
      <Button  @click="closeModal">取消</Button>
      <Button type="primary" wait @click="onConfirm">确定</Button>
    </div>
  </div>
</template>

<script>

import { dataToLine, dataToNode } from "../graph";

export default {
  name: "cellForm",
  data() {
    return {
      showModal: false,
      modalStyle: {},
      formType: "node",
      formData: {},
      cell: null,
      rules: {
        // 节点
        nodeName: { required: true, message: "请输入节点名称", trigger: "change" },
        nodeCode: { required: true, message: "请输入节点编码", trigger: "change" },
        nodeStatus: { required: true, message: "请选择是否启用", trigger: "change", type: "number" },
        // 线条
        lineStatus: { required: true, message: "请选择是否启用", trigger: "change", type: "number" },
      },
    };
  },
  methods: {
    onUpload() {
      try {
        let file = this.$refs.fileInput.files[0];
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onerror = (error) => {
          throw (error);
        };
        reader.onload = () => {
          const imgUrl = reader.result;
          this.$set(this.formData, "image", imgUrl);
        };
      } catch (err) {
        this.$Message.error(`上传图片失败：${err.message || err}`);
      } finally {
        this.$refs.fileInput.value = null;
      }
    },
    async onConfirm() {
      let formValidate = await this.$refs.form.validate();
      if (!formValidate) return this.$invalidMessage();
      if (this.formType === "node") {
        const { attrs } = dataToNode(this.formData);
        this.cell.setAttrs(attrs);
        this.cell.setData(this.formData);
      } else {
        const { attrs, label } = dataToLine(this.formData);
        this.cell.setAttrs(attrs);
        this.cell.setLabels([label]);
        this.cell.setData(this.formData);
      }
      this.closeModal();
    },
    openModal(e, cell, flowId) {
      this.showModal = true;
      this.formType = cell.isNode() ? "node" : "line";
      this.formData = cell.getData() || {};
      this.cell = cell;
      this.$nextTick(() => {
        const x = e.offsetX;
        const y = e.offsetY;
        const flowContainer = document.getElementById(flowId);
        const cellForm = this.$refs.cellForm;
        const offsetLeft = x + cellForm.offsetWidth + 20 > flowContainer.offsetWidth ?
          x - cellForm.offsetWidth - 20 : x + 20;
        const offsetTop = y + cellForm.offsetHeight - 20 > flowContainer.offsetHeight ?
          flowContainer.offsetHeight - cellForm.offsetHeight : y - 20;
        this.modalStyle = {
          left: `${offsetLeft}px`,
          top: `${offsetTop}px`,
        };
      });
    },
    closeModal() {
      this.showModal = false;
      this.formData = {};
      this.modalStyle = {};
    },
  },
};
</script>

<style lang="less" scoped>
@text-color: #00244a;
.cell-form {
  position: absolute;
  background: #fff;
  border: 1px solid #eeeeee;
  box-shadow: 0px 2px 6px 0px rgba(0, 34, 84, 0.12);
  border-radius: 4px;
  z-index: 999;
  width: 350px;
  display: flex;
  flex-direction: column;
  .form-header {
    flex: none;
    line-height: 40px;
    font-weight: bold;
    font-size: 16px;
    color: @text-color;
    border-bottom: 1px solid #E4EAF0;
    padding: 0 10px;
  }
  .form-close {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
  }
  .form-content {
    flex: 1;
    padding: 16px 10px;
    overflow-y: auto;
  }
  .form-footer {
    flex: none;
    text-align: center;
    line-height: 60px;
    border-top: 1px solid #E4EAF0;
  }
}
.camera-select {
  width: 60px;
  height: 60px;
  background: #F5F7FA;
  overflow: hidden;
  cursor: pointer;
  .camera-icon {
    width: 100%;
    height: 100%;
    font-size: 26px;
    border-radius: 6px;
    border: 1px dashed #C4CED8;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
