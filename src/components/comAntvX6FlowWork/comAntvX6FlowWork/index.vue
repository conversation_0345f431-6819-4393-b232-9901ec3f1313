<template>
  <div class="vi-workflow" :style="{ height }">
    <div class="vi-workflow-container">
      <ui-tabs v-model="curTab" height="100%" @on-click="changeTab">
        <ui-tab-pane name="form" label="业务表单">
          <div class="vi-workflow-content">
            <slot></slot>
          </div>
        </ui-tab-pane>
        <ui-tab-pane name="record" label="审批记录">
          <ui-timeline class="vi-workflow-content">
            <ui-timeline-item v-for="(item, index) in historyList" :key="index">
              <p class="timeline-title">
                <span>{{ item.createTime }}</span>
                <span>{{ item.createUserName }}</span>
                <span>{{ item.nodeName }}</span>
              </p>
              <p class="timeline-content" v-if="typeof item.auditResult === 'number'">
                <span
                  :class="[item.auditResult === 1 ? 'com-normal-text' : 'com-danger-text']"
                >{{ item.auditResult === 1 ? "同意" : "不同意" }}：</span>
                <span>{{ item.auditOpinion || "-" }}</span>
              </p>
            </ui-timeline-item>
          </ui-timeline>
        </ui-tab-pane>
        <ui-tab-pane name="flow" label="流程图" class="mt-20">
          <div :id="flowId" class="w100 h100"></div>
        </ui-tab-pane>
      </ui-tabs>
    </div>
    <div class="vi-workflow-form" v-if="$slots.form">
      <slot name="form"></slot>
    </div>
    <div class="vi-workflow-submit" v-if="$slots.submit">
      <slot name="submit"></slot>
    </div>
  </div>
</template>

<script>
// import { getWorkflow } from "@/axios/wfesBaseService";
import { initGraph, dataToNode, dataToLine } from "./graph";

export default {
  name: "viWorkflow",
  props: {
    workflowCode: {
      type: String,
      default: "",
    },
    businessId: {
      type: [String, Number],
      default: "",
    },
    height: {
      type: String,
      default: "100%",
    },
  },
  data() {
    return {
      curTab: "form",
      flowId: `flow-${Math.random().toString().slice(-3)}`,
      historyList: [],
      graph: null,
    };
  },
  methods: {
    initData() {
      this.$nextTick(() => {
        this.curTab = this.$options.data.call(this).curTab;
        this.getWorkflowData();
      });
    },
    async getWorkflowData() {
      try {
        const { returnCode, data } = {}
        // await getWorkflow({
        //   workflowCode: this.workflowCode,
        //   businessId: this.businessId,
        //   prisonId: this.$store.state.userInfo.prisonId,
        // }) || {};
        if (returnCode === 0) {
          this.historyList = data.historyList;
          this.drawGraph(data);
        }
      } catch (err) {
        this.$Message.error(`获取流程数据失败：${err.message || err}`);
      }
    },
    drawGraph({ currentNodeId, nodeList, lineList }) {
      if (!this.graph) {
        this.graph = initGraph(this.flowId);
      } else {
        this.graph.clearCells();
      }
      this.graph.addNodes(nodeList.map((item) => dataToNode(item, currentNodeId)));
      this.graph.addEdges(lineList.map((item) => dataToLine(item)));
      this.graph.zoomToFit({ padding: 20 });
    },
    changeTab(val) {
      if (val == "flow") {
        this.graph.zoomToFit({ padding: 20 });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.vi-workflow {
  display: flex;
  flex-direction: column;
  &-form {
    flex: none;
    padding: 20px;
    border-top: 1px solid #E4EAF0;
  }
  &-submit {
    flex: none;
  }
  &-container {
    flex: 1;
    padding: 10px 20px 20px;
    overflow: hidden;
  }
  &-content {
    height: calc(100% - 20px);
    margin-top: 20px;
    overflow: auto;
  }
}
.timeline-title {
  font-size: 16px;
  span {
    display: inline-block;
    margin-right: 30px;
  }
}
.timeline-content {
  padding: 8px 0;
  font-size: 16px;
  line-height: 25px;
}
</style>
<style lang="less">
@import "./animation.less";
</style>

