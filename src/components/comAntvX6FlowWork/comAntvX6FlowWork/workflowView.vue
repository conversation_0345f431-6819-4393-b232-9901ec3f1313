<template>
  <div   :style="{width, height}">
    <div :id="flowId" :style="{width, height}"></div>
    <graphToolbar ref="graphToolbar" :graph="graph"></graphToolbar>
  </div>
</template>

<script>
// import { loading } from "material";
// import { getWorkflowManageById, getWorkflowManageByCode } from "@/axios/wfesBaseService";
import { initGraph, dataToNode, dataToLine } from "./graph";
import graphToolbar from "./graphToolbar.vue";
export default {
  name: "comWorkflowView",
  // directives: {loading},
  components: {
    graphToolbar
  },
  props: {
    id: {
      type: String,
      default: "",
    },
    workflowCode: {
      type: String,
      default: "",
    },
    renderJson: {
      type: Object,
      default() {
        return {};
      }
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "100%"
    },
    countInfo: {
      type: Object,
      default() {
        return {};
      }
    },
    // 不可点击的节点
    disabledNodes: {
      type: Object,
      default() {
        return {};
      }
    },
  },
  data() {
    return {
      flowId: `flow-${Math.random().toString().slice(-3)}`,
      graph: null,
      loading: false,
    };
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        if (val) {
          // this.getWorkflowData(getWorkflowManageById({ id: val }));
        } else {
          this.graph && this.graph.clearCells();
        }
      },
    },
    workflowCode: {
      immediate: true,
      handler(val) {
        if (val) {
          // this.getWorkflowData(getWorkflowManageByCode({ workflowCode: val }));
        } else {
          this.graph && this.graph.clearCells();
        }
      },
    },
    renderJson: {
      immediate: true,
      handler(val) {
        if (val && JSON.stringify(val) !== "{}") {
          this.$nextTick(() => {
            this.drawGraph(val);
          });
        } else {
          this.graph && this.graph.clearCells();
        }
      },
    },
    countInfo: { // [{nodeCode：num}]
      immediate: true,
      handler() {
        this.renderNodeTagNum();
      },
    },
  },
  methods: {
    // 添加、修改节点右上角通知内容
    renderNodeTagNum() {
      if (!this.graph) return;
      const nodes = this.graph && this.graph.getNodes();
      nodes.forEach(node => {
        const { nodeCode } = node.getData() || {};
        if (nodeCode) {
          let textToDoNum = this.countInfo[nodeCode];  // 获取节点的待处理数
          if (textToDoNum > 0) {
            node.setAttrs({
              tipBg: {
                visibility: "visible"
              },
              tipNum: {
                visibility: "visible",
                text: textToDoNum,
                fontSize: 22 - textToDoNum.toString().length * 2,
              },
            });
          } else {
            node.setAttrs({
              tipBg: {
                visibility: "hidden"
              },
              tipNum: {
                visibility: "hidden",
                text: 0,
              },
            });
          }
        }
      });
    },
    async getWorkflowData(request) {
      try {
        this.loading = true;
        const { returnCode, data } = await request || {};
        if (returnCode === 0) {
          this.drawGraph(data);
        }
      } catch (err) {
        this.$Message.error(`获取流程数据失败：${err.message || err}`);
      } finally {
        this.loading = false;
      }
    },
    drawGraph({ nodeList, lineList }) {
      this.refreshGraph();
      this.graph.addNodes(nodeList.map((item) => dataToNode(item, { disabledNodes: this.disabledNodes })));
      this.graph.addEdges(lineList.map((item) => dataToLine(item)));
      this.graph.zoomToFit({
        padding: { left: 20, top: 20, right: 20, bottom: 88 },
      });
      this.renderNodeTagNum();
    },
    refreshGraph() {
      let graphToolbar = this.$refs.graphToolbar;
      if (this.graph) return this.graph.clearCells();
      this.graph = initGraph(this.flowId, false, {
        graphToolbar
      });
      // 节点点击事件
      this.graph.on("node:click", ({ node }) => {
        const data = node.getData();
        if (this.disabledNodes[data.nodeCode]) return;
        this.$emit("node-click", data);
      });
    },
  },
};
</script>

<style lang="less">
@import "./animation.less";
</style>
