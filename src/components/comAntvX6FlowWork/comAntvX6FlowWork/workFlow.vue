<template>
    <div>
      <Tabs value="workflowEdit">
        <TabPane :label="item.name" :name="item.comView"  :key="index" v-for="(item,index) in tabData">
                <component :is="item.comView"  />
        </TabPane>
      </Tabs>
    </div>
</template>
<script>
import workflowEdit from './workflowEdit.vue';
import workflowView from './workflowView.vue';
export default {
    components: {
        workflowEdit, workflowView
    },
    data() {
        return {
            tabData: [
                { name: '编辑流程图', comView: 'workflowEdit' },
                { name: '查看流程图', comView: 'workflowView' },
            ]
        }
    }
}
</script>