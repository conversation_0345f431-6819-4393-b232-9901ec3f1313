<template>
  <div class="case-personnel-selector">
    <!-- 办案人员选择区域 -->
    <div class="personnel-header">
      <div class="section-title">
        <h4>{{ title }}</h4>
        <div class="action-buttons" v-if="mode === 'edit'">
          <Button
            v-if="selectedPersonnel"
            @click="refreshPersonnelInfo"
            type="primary"
            size="small"
            class="refresh-btn"
            :loading="loading"
          >
            <Icon type="ios-refresh" v-if="!loading" />
            刷新
          </Button>
          <Button
            v-if="selectedPersonnel"
            @click="clearSelection"
            type="warning"
            size="small"
            class="reselect-btn"
          >
            重新输入
          </Button>
        </div>
      </div>

      <!-- 未选择人员时的输入区域 - 仅编辑模式显示 -->
      <div class="personnel-input-area" v-if="!selectedPersonnel && mode === 'edit' && !personnelCode">
        <div class="input-container">
          <Input
            v-model="inputPersonnelCode"
            :placeholder="placeholder"
            @on-enter="handleManualInput"
            @on-blur="handleManualInput"
            class="personnel-input"
          />
          <Button @click="handleManualInput" type="primary" class="search-btn" :loading="loading">
            <Icon type="ios-search" v-if="!loading" />
            {{ loading ? '查询中...' : '查询' }}
          </Button>
        </div>

        <!-- 加载状态提示 -->
        <div class="loading-tip" v-if="loading">
          <Spin size="small" />
          <span>正在查询办案人员信息...</span>
        </div>
      </div>

      <!-- 自动模式的状态显示 -->
      <div class="auto-mode-status" v-if="personnelCode">
        <div class="status-container">
          <Input
            :value="selectedPersonnel ? `${selectedPersonnel[fieldMapping.name]} - ${selectedPersonnel[fieldMapping.unit]}` : ''"
            :placeholder="personnelCode ? '正在获取办案人员信息...' : placeholder"
            readonly
            :disabled="!personnelCode"
          >
            <Icon
              v-if="loading"
              type="ios-loading"
              slot="suffix"
              class="loading-icon"
            />
            <Icon
              v-else-if="selectedPersonnel"
              type="ios-checkmark-circle"
              slot="suffix"
              color="#19be6b"
            />
          </Input>
        </div>
      </div>

      <!-- 已选择人员信息展示 -->
      <div class="personnel-info" v-if="selectedPersonnel">
        <!-- 统一宽度的卡片容器 -->
        <div class="unified-cards-container">
          <!-- 办案人员信息卡片 -->
          <div class="personnel-card">
            <!-- 头像区域 -->
            <div class="avatar-section">
              <div class="avatar-container">
                <div class="personnel-avatar-placeholder">
                  <Icon type="ios-person" size="48" />
                </div>
                <div class="personnel-name">{{ formatValue(selectedPersonnel[fieldMapping.name]) }}</div>
              </div>
            </div>

            <!-- 信息区域 -->
            <div class="info-section">
              <!-- 办案单位 -->
              <div class="info-item highlight-item">
                <span class="info-label">办案单位</span>
                <span class="info-value highlight-value">{{ formatValue(selectedPersonnel[fieldMapping.unit]) }}</span>
              </div>

              <!-- 基本信息 -->
              <div class="info-item">
                <span class="info-label">性别</span>
                <span class="info-value">{{ formatValue(selectedPersonnel[fieldMapping.gender]) }}</span>
              </div>

              <div class="info-item">
                <span class="info-label">证件类型</span>
                <span class="info-value">{{ formatValue(selectedPersonnel[fieldMapping.idType]) }}</span>
              </div>

              <div class="info-item">
                <span class="info-label">证件号码</span>
                <span class="info-value">{{ formatValue(selectedPersonnel[fieldMapping.idNumber]) }}</span>
              </div>

              <div class="info-item">
                <span class="info-label">联系方式</span>
                <span class="info-value">{{ formatValue(selectedPersonnel[fieldMapping.contact]) }}</span>
              </div>

              <div class="info-item">
                <span class="info-label">单位类型</span>
                <span class="info-value">{{ formatValue(selectedPersonnel[fieldMapping.unitType]) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 扫码提示 - 仅编辑模式显示 -->
    <div class="scan-tip" v-if="showScanTip && enableScan && mode === 'edit'">
      <Icon type="ios-barcode" size="16" />
      <span v-if="scanPrefix">
        支持扫码枪扫描条形码自动识别办案人员
        <Tag size="small" color="blue">格式: {{ scanPrefix }}人员编码</Tag>
      </span>
      <span v-else>支持扫码枪扫描条形码自动识别办案人员</span>
      <span class="scan-status" v-if="isScanning">
        <Icon type="ios-radio-button-on" color="#19be6b" />
        扫码中...
      </span>
    </div>
  </div>
</template>

<script>
import {normalizeObject}   from '@/libs/util'

export default {
  name: 'casePersonnelSelector',
  props: {
    // v-model 绑定的人员编码
    value: {
      type: String,
      default: ''
    },
    // 外部传入的被监管人员编码（用于自动加载关联的办案人员信息）
    personnelCode: {
      type: String,
      default: ''
    },
    // 目标表单对象（直接传入要填充的表单项）
    targetForm: {
      type: Object,
      default: () => ({})
    },
    // 组件模式：edit-编辑模式（默认），detail-详情模式
    mode: {
      type: String,
      default: 'edit',
      validator: function (value) {
        return ['edit', 'detail'].indexOf(value) !== -1
      }
    },
    // 组件标题
    title: {
      type: String,
      default: '办案人员'
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: '请输入办案人员编码'
    },
    // 是否启用扫码功能
    enableScan: {
      type: Boolean,
      default: true
    },
    // 是否显示扫码提示
    showScanTip: {
      type: Boolean,
      default: true
    },
    // 扫码标识符，用于区分扫码和手动输入（可选，设为空字符串则不使用标识符）
    scanPrefix: {
      type: String,
      default: 'case:'
    },
    // 扫码时间阈值，单位毫秒（用于区分扫码枪快速输入和人工慢速输入）
    timeThreshold: {
      type: Number,
      default: 50
    },
    // 字段映射配置，用于将API返回字段映射到组件显示字段
    fieldMapping: {
      type: Object,
      default: () => ({
        name: 'bar',        // 办案人姓名
        unit: 'badw',       // 办案单位
        gender: 'barxb',    // 办案人性别
        idType: 'barzjlx',  // 办案人证件类型
        idNumber: 'barzjhm', // 办案人证件号码
        contact: 'barlxff',  // 办案人联系方式
        unitType: 'badwlx'   // 办案单位类型
      })
    }
  },
  data() {
    return {
      selectedPersonnel: null,
      inputPersonnelCode: '',
      scanCode: '', // 扫码缓存
      lastKeyTime: null, // 上一次按键时间
      isScanning: false,
      loading: false, // 加载状态
      componentId: null // 组件实例ID，用于避免多组件扫码冲突
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && newVal !== this.getCurrentPersonnelCode()) {
          this.loadPersonnelInfo(newVal)
        } else if (!newVal) {
          this.selectedPersonnel = null
          this.inputPersonnelCode = ''
        }
      },
      immediate: true
    },
    // 监听外部传入的人员编码变化，自动加载办案人员信息
    personnelCode: {
      handler(newCode, oldCode) {
        if (newCode && newCode !== oldCode) {
          console.log('检测到人员编码变化，开始加载办案人员信息:', newCode)
          this.loadCasePersonnelByPersonnelCode(newCode)
        } else if (!newCode) {
          this.clearTargetForm()
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 只有编辑模式才启用扫码功能
    if (this.enableScan && this.mode === 'edit') {
      this.initScanListener()
    }
  },
  beforeDestroy() {
    if (this.scanTimeout) {
      clearTimeout(this.scanTimeout)
    }
    this.removeScanListener()
  },
  methods: {
    /**
     * 处理手动输入
     */
    handleManualInput() {
      const code = this.inputPersonnelCode.trim()

      const validation = this.validatePersonnelCode(code)
      if (!validation.valid) {
        this.$Message.warning(validation.message)
        return
      }

      this.$emit('input', code)
    },

    /**
     * 清空选择
     */
    clearSelection() {
      this.selectedPersonnel = null
      this.inputPersonnelCode = ''
      this.loading = false

      this.$emit('input', '')
      this.$emit('change', null, '')

      this.$Message.info('已清空办案人员信息')
    },

    /**
     * 获取当前人员编码
     */
    getCurrentPersonnelCode() {
      return this.inputPersonnelCode || ''
    },

    /**
     * 加载办案人员信息
     */
    async loadPersonnelInfo(jgrybm) {
      if (!jgrybm) return

      this.loading = true

      try {
        const resp = await this.$store.dispatch('authGetRequest', {
          url: '/acp/db/detainRegKss/getCasePersonnelByjgrybm',
          params: { jgrybm }
        })

        if (resp.code === 0 && resp.data) {
          this.selectedPersonnel = normalizeObject(resp.data)
          this.inputPersonnelCode = jgrybm

          this.$emit('change', this.selectedPersonnel, jgrybm)
          this.$Message.success(`办案人员信息获取成功: ${this.selectedPersonnel[this.fieldMapping.name] || '未知'}`)
        } else {
          const errorMsg = resp.msg || '获取办案人员信息失败'
          this.$Message.warning(errorMsg)

          this.selectedPersonnel = null
          this.$emit('change', null, '')
        }
      } catch (error) {
        this.$Message.error('获取办案人员信息失败，请检查网络连接')
        this.selectedPersonnel = null
        this.$emit('change', null, '')
      } finally {
        this.loading = false
      }
    },

    /**
     * 根据被监管人员编码加载关联的办案人员信息
     */
    async loadCasePersonnelByPersonnelCode(personnelCode) {
      if (!personnelCode) return

      this.loading = true

      try {
        // 调用API获取与该被监管人员关联的办案人员信息
        const response = await this.$store.dispatch('authGetRequest', {
          url: '/acp-com/acp/case/personnel/getByPersonnelCode',
          params: {
            personnelCode: personnelCode
          }
        })

        if (response.code === 0 && response.data) {
          this.selectedPersonnel = normalizeObject(response.data)

          // 自动填充目标表单
          this.fillTargetForm(response.data)

          this.$Message.success('办案人员信息已自动填充')
        } else {
          console.log('未找到关联的办案人员信息:', response.msg || '无数据')
          this.clearTargetForm()
        }
      } catch (error) {
        console.error('加载办案人员信息失败:', error)
        this.$Message.error('加载办案人员信息失败')
        this.clearTargetForm()
      } finally {
        this.loading = false
      }
    },

    /**
     * 初始化扫码监听
     */
    initScanListener() {
      // 只有编辑模式才启用扫码功能
      if (this.enableScan && this.mode === 'edit') {
        // 使用组件实例ID作为标识，避免多个组件间的扫码冲突
        this.componentId = 'case-personnel-selector-' + Math.random().toString(36).substring(2, 9)
        document.addEventListener('keydown', this.handleGlobalKeyDown)
      }
    },

    /**
     * 移除扫码监听
     */
    removeScanListener() {
      document.removeEventListener('keydown', this.handleGlobalKeyDown)
    },

    /**
     * 处理全局键盘事件 - 基于时间间隔的智能扫码识别
     */
    handleGlobalKeyDown(event) {
      if (!this.enableScan || this.mode !== 'edit') return

      const currentTime = new Date().getTime()

      // 如果是第一次按键或者两次按键间隔超过阈值，则重置扫码缓存
      if (this.lastKeyTime !== null && currentTime - this.lastKeyTime > this.timeThreshold) {
        this.scanCode = ''
      }

      // 排除功能键，只处理可见字符
      if (event.key.length === 1) {
        this.scanCode += event.key
      }

      // 如果按键是 Enter，并且扫码缓存不为空，则认为是扫码结束
      if (event.key === 'Enter' && this.scanCode) {
        console.log('识别到扫码枪输入:', this.scanCode)

        // 处理扫码结果
        this.processScanCode(this.scanCode)

        // 清空扫码缓存，为下一次扫码做准备
        this.scanCode = ''

        // 阻止Enter键的默认行为，比如触发表单提交
        event.preventDefault()
      }

      // 更新上一次按键时间
      this.lastKeyTime = currentTime
    },

    /**
     * 处理扫码结果 - 基于时间间隔的智能识别
     */
    processScanCode(scannedCode) {
      if (!scannedCode || scannedCode.trim() === '') return

      const code = scannedCode.trim()

      if (this.scanPrefix && !code.startsWith(this.scanPrefix)) {
        return
      }

      let personnelCode = code
      if (this.scanPrefix) {
        personnelCode = code.replace(this.scanPrefix, '').trim()
      }

      if (personnelCode && personnelCode.length > 0) {
        const validation = this.validatePersonnelCode(personnelCode)
        if (!validation.valid) {
          this.$Message.warning(`扫码内容格式错误: ${validation.message}`)
          return
        }

        this.$Message.success('扫码成功，正在获取办案人员信息...')
        this.inputPersonnelCode = personnelCode
        this.$emit('input', personnelCode)
      } else {
        this.$Message.warning('扫码内容格式错误，请重新扫码')
      }
    },

    /**
     * 格式化显示值，处理 null 字符串
     */
    formatValue(value) {
      if (!value || value === 'null' || value === null || value === undefined) {
        return '-'
      }
      return value
    },

    /**
     * 获取当前人员编码
     */
    getCurrentPersonnelCode() {
      return this.inputPersonnelCode || this.value || ''
    },

    /**
     * 获取当前人员信息
     */
    getCurrentPersonnelInfo() {
      return this.selectedPersonnel
    },

    /**
     * 验证人员编码格式
     */
    validatePersonnelCode(code) {
      if (!code || typeof code !== 'string') {
        return { valid: false, message: '人员编码不能为空' }
      }

      const trimmedCode = code.trim()
      if (trimmedCode.length < 3) {
        return { valid: false, message: '人员编码长度不能少于3位' }
      }

      if (trimmedCode.length > 20) {
        return { valid: false, message: '人员编码长度不能超过20位' }
      }

      // 可以添加更多的格式验证规则
      const codePattern = /^[A-Za-z0-9_-]+$/
      if (!codePattern.test(trimmedCode)) {
        return { valid: false, message: '人员编码只能包含字母、数字、下划线和横线' }
      }

      return { valid: true, message: '格式正确' }
    },

    /**
     * 刷新当前人员信息
     */
    refreshPersonnelInfo() {
      const code = this.getCurrentPersonnelCode()
      if (code) {
        this.loadPersonnelInfo(code)
      } else {
        this.$Message.warning('没有可刷新的人员信息')
      }
    },

    /**
     * 填充目标表单
     */
    fillTargetForm(apiData) {
      if (!this.targetForm || !this.fieldMapping) return

      console.log('开始填充表单数据:', apiData, this.fieldMapping)

      // 根据字段映射填充数据
      Object.keys(this.fieldMapping).forEach(formField => {
        const apiField = this.fieldMapping[formField]
        if (apiData[apiField] !== undefined && apiData[apiField] !== null && apiData[apiField] !== '') {
          this.$set(this.targetForm, formField, apiData[apiField])
          console.log(`映射字段 ${formField} = ${apiData[apiField]}`)
        }
      })

      // 特殊处理：办案单位类型相关
      if (apiData.badwlx) {
        this.$set(this.targetForm, 'disFale', true)
        this.setUnitDicName(apiData.badwlx)
      }

      // 特殊处理：照片
      if (apiData.zpUrl) {
        this.$set(this.targetForm, 'defaultList', [{ url: apiData.zpUrl, name: '' }])
      }

      console.log('表单填充完成:', this.targetForm)
    },

    /**
     * 设置办案单位字典名称
     */
    setUnitDicName(badwlx) {
      const dicNameMap = {
        '1': 'ZD_BADW_GAJG',  // 公安机关
        '2': 'ZD_BADW_JCY',   // 检察院
        '3': 'ZD_BADW_FY',    // 法院
        '4': 'ZD_BADW_AQJG',  // 安全机关
        '5': ''
      }
      this.$set(this.targetForm, 'DisDicName', dicNameMap[badwlx] || '')
    },

    /**
     * 清空目标表单
     */
    clearTargetForm() {
      if (!this.targetForm || !this.fieldMapping) return

      Object.keys(this.fieldMapping).forEach(formField => {
        this.$set(this.targetForm, formField, '')
      })

      this.$set(this.targetForm, 'disFale', false)
      this.$set(this.targetForm, 'DisDicName', '')
      this.$set(this.targetForm, 'defaultList', [])
    }
  }
}
</script>

<style lang="less" scoped>
@import url('./index.less');
</style>
