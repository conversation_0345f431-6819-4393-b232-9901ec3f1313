<template>
  <div class="inquiry-test-page">
    <div class="page-header">
      <h1>提讯登记 - 扫码复用办案人员测试</h1>
      <p>模拟真实提讯页面，测试扫码后自动复用办案人员信息的功能</p>
      <div class="page-info">
        <Tag color="success">页面已加载</Tag>
        <Tag color="blue">Vue组件正常</Tag>
        <Tag :color="showDebugPanel ? 'orange' : 'default'">调试面板: {{ showDebugPanel ? '显示' : '隐藏' }}</Tag>
      </div>
    </div>

    <!-- 测试配置 -->
    <div class="test-config" v-if="showDebugPanel">
      <h2>测试配置</h2>
      <Form :model="testConfig" :label-width="120" class="config-form">
        <Row :gutter="16">
          <Col span="8">
            <FormItem label="启用扫码联动">
              <Switch v-model="testConfig.enableLinkage" />
              <span style="margin-left: 8px;">{{ testConfig.enableLinkage ? '已启用' : '已禁用' }}</span>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="联动延迟">
              <InputNumber
                v-model="testConfig.linkageDelay"
                :min="0"
                :max="2000"
                :step="100"
                style="width: 100%"
              />
              <span style="font-size: 12px; color: #999;">毫秒</span>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="显示调试">
              <Switch v-model="showDebugPanel" />
              <span style="margin-left: 8px;">{{ showDebugPanel ? '显示' : '隐藏' }}</span>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>

    <!-- 状态面板 -->
    <div class="status-panel" v-if="showDebugPanel">
      <h2>当前状态</h2>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">被监管人员:</span>
          <span class="value">{{ selectedPersonnel ? selectedPersonnel.xm : '未选择' }}</span>
        </div>
        <div class="status-item">
          <span class="label">办案人员:</span>
          <span class="value">{{ formData.casePersonnel.bar || '未填写' }}</span>
        </div>
        <div class="status-item">
          <span class="label">数据来源:</span>
          <span class="value">{{ isLinkedData ? '扫码复用' : '手动填写' }}</span>
        </div>
        <div class="status-item">
          <span class="label">联动状态:</span>
          <span class="value">{{ testConfig.enableLinkage ? '已启用' : '已禁用' }}</span>
        </div>
      </div>

      <!-- 快速测试按钮 -->
      <div class="quick-test-buttons">
        <Button @click="testFillCasePersonnel" type="primary" size="small">
          测试填充
        </Button>
        <Button @click="testClearCasePersonnel" type="warning" size="small">
          清空数据
        </Button>
        <Button @click="scrollToBottom" type="info" size="small">
          滚动测试
        </Button>
      </div>
    </div>

    <!-- 提讯登记表单 -->
    <div class="inquiry-form">
      <h2>提讯登记表单</h2>
      
      <Form :model="formData" :label-width="120" class="main-form">
        <!-- 被监管人员信息 -->
        <div class="form-section">
          <h3>被监管人员信息</h3>
          <FormItem label="被监管人员">
            <personnel-selector
              v-model="formData.jgrybm"
              title="被监管人员"
              placeholder="点击选择在押人员或扫码识别"
              :show-case-info="true"
              :enable-scan="true"
              :show-scan-tip="true"
              @change="handlePersonnelChange"
            />
          </FormItem>
        </div>

        <!-- 提讯基本信息 -->
        <div class="form-section">
          <h3>提讯基本信息</h3>
          <Row :gutter="16">
            <Col span="12">
              <FormItem label="提讯时间">
                <DatePicker
                  v-model="formData.inquiryTime"
                  type="datetime"
                  placeholder="请选择提讯时间"
                  style="width: 100%"
                />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="提讯地点">
                <Input v-model="formData.inquiryLocation" placeholder="请输入提讯地点" />
              </FormItem>
            </Col>
          </Row>
          <Row :gutter="16">
            <Col span="12">
              <FormItem label="提讯原因">
                <Select v-model="formData.inquiryReason" placeholder="请选择提讯原因">
                  <Option value="case_investigation">案件侦查</Option>
                  <Option value="evidence_collection">证据收集</Option>
                  <Option value="case_verification">案情核实</Option>
                  <Option value="other">其他</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="预计时长">
                <InputNumber 
                  v-model="formData.estimatedDuration" 
                  :min="1" 
                  :max="480" 
                  placeholder="分钟"
                  style="width: 100%"
                />
              </FormItem>
            </Col>
          </Row>
        </div>

        <!-- 办案人员信息区域 - 模拟真实提讯页面结构 -->
        <div class="form-section case-personnel-area">
          <h3>
            办案人员信息
            <Tag color="red">重要区域</Tag>
            <span v-if="testConfig.enableLinkage" class="linkage-status">
              <Icon type="ios-link" style="color: #19be6b;" />
              扫码联动已启用
            </span>
          </h3>

          <!-- 测试提示 -->
          <Alert show-icon type="info" style="margin-bottom: 16px;">
            <template slot="desc">
              这是办案人员信息区域，如果你能看到这个提示，说明页面渲染正常。
              当前数据来源：{{ isLinkedData ? '扫码复用' : '手动填写' }}
            </template>
          </Alert>
          
          <!-- 办案人员表单 - 模拟提讯页面的结构 -->
          <div class="case-personnel-form">
            <Row :gutter="16">
              <Col span="12">
                <FormItem label="办案人员姓名" required>
                  <Input 
                    v-model="formData.casePersonnel.bar" 
                    placeholder="请输入办案人员姓名"
                    :readonly="isLinkedData"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="办案单位类型">
                  <Select 
                    v-model="formData.casePersonnel.badwlx" 
                    placeholder="请选择办案单位类型"
                    :disabled="isLinkedData"
                  >
                    <Option value="1">公安机关</Option>
                    <Option value="2">检察机关</Option>
                    <Option value="3">审判机关</Option>
                    <Option value="4">其他</Option>
                  </Select>
                </FormItem>
              </Col>
            </Row>
            
            <Row :gutter="16">
              <Col span="12">
                <FormItem label="办案单位" required>
                  <Input 
                    v-model="formData.casePersonnel.badw" 
                    placeholder="请输入办案单位"
                    :readonly="isLinkedData"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="证件类型">
                  <Select 
                    v-model="formData.casePersonnel.barzjlx" 
                    placeholder="请选择证件类型"
                    :disabled="isLinkedData"
                  >
                    <Option value="1">身份证</Option>
                    <Option value="2">警官证</Option>
                    <Option value="3">工作证</Option>
                    <Option value="4">其他</Option>
                  </Select>
                </FormItem>
              </Col>
            </Row>
            
            <Row :gutter="16">
              <Col span="12">
                <FormItem label="证件号码">
                  <Input 
                    v-model="formData.casePersonnel.barzjhm" 
                    placeholder="请输入证件号码"
                    :readonly="isLinkedData"
                  />
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="联系方式">
                  <Input 
                    v-model="formData.casePersonnel.barlxff" 
                    placeholder="请输入联系方式"
                    :readonly="isLinkedData"
                  />
                </FormItem>
              </Col>
            </Row>
            
            <Row :gutter="16">
              <Col span="12">
                <FormItem label="性别">
                  <Select 
                    v-model="formData.casePersonnel.barxb" 
                    placeholder="请选择性别"
                    :disabled="isLinkedData"
                  >
                    <Option value="1">男</Option>
                    <Option value="2">女</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="警号">
                  <Input 
                    v-model="formData.casePersonnel.jh" 
                    placeholder="请输入警号"
                    :readonly="isLinkedData"
                  />
                </FormItem>
              </Col>
            </Row>

            <!-- 操作按钮区域 -->
            <div class="personnel-actions">
              <Button 
                @click="selectPersonnelFromDialog" 
                type="primary" 
                icon="ios-people"
                :disabled="isLinkedData"
              >
                选择人员复用
              </Button>
              <Button 
                @click="scanPersonnelCode" 
                type="success" 
                icon="ios-qr-scanner"
                v-if="testConfig.enableLinkage"
              >
                扫码复用
              </Button>
              <Button 
                @click="clearCasePersonnelData" 
                type="warning" 
                icon="ios-trash"
                v-if="isLinkedData"
              >
                清空复用数据
              </Button>
            </div>

            <!-- 数据来源提示 -->
            <div class="data-source-tip" v-if="isLinkedData">
              <Alert show-icon type="success">
                <template slot="desc">
                  <Icon type="ios-checkmark-circle" />
                  办案人员信息已通过扫码自动复用，如需修改请先清空复用数据
                </template>
              </Alert>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <Button @click="handleSave" type="primary" :loading="saving" size="large">
            保存提讯登记
          </Button>
          <Button @click="handleReset" size="large">
            重置表单
          </Button>
          <Button @click="handlePreview" size="large">
            {{ showPreview ? '隐藏预览' : '预览数据' }}
          </Button>
          <Button @click="showDebugPanel = !showDebugPanel" size="large">
            {{ showDebugPanel ? '隐藏调试' : '显示调试' }}
          </Button>
        </div>
      </Form>
    </div>

    <!-- 数据预览 -->
    <div class="data-preview" v-if="showPreview">
      <h3>表单数据预览</h3>
      <div class="preview-content">
        <pre>{{ JSON.stringify({
          formData,
          selectedPersonnel,
          isLinkedData,
          testConfig
        }, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import personnelSelector from '@/components/personnel-selector/index.vue'

export default {
  name: 'InquiryTestPage',
  components: {
    personnelSelector
  },
  data() {
    return {
      // 测试配置
      testConfig: {
        enableLinkage: true,  // 启用扫码联动
        linkageDelay: 500     // 联动延迟
      },
      
      formData: {
        // 被监管人员信息
        jgrybm: '', // 被监管人员编码
        
        // 提讯基本信息
        inquiryTime: '',
        inquiryLocation: '',
        inquiryReason: '',
        estimatedDuration: null,
        
        // 办案人员信息 - 模拟真实提讯页面结构
        casePersonnel: {
          bar: '',        // 办案人员姓名
          badwlx: '',     // 办案单位类型
          badw: '',       // 办案单位
          barzjlx: '',    // 证件类型
          barzjhm: '',    // 证件号码
          barlxff: '',    // 联系方式
          barxb: '',      // 性别
          jh: ''          // 警号
        }
      },
      
      // 选择的人员信息
      selectedPersonnel: null,
      
      // 标记数据来源
      isLinkedData: false,
      
      // 界面状态
      saving: false,
      showPreview: false,
      showDebugPanel: true,  // 默认显示调试面板
      
      // 联动延迟定时器
      linkageTimer: null
    }
  },

  mounted() {
    this.$nextTick(() => {
      document.body.style.overflow = 'auto'
      document.documentElement.style.overflow = 'auto'
    })
  },

  methods: {
    /**
     * 处理被监管人员选择 - 核心联动逻辑
     */
    handlePersonnelChange(personnelData, code) {
      this.selectedPersonnel = personnelData

      if (personnelData && code) {
        this.$Message.success(`已选择被监管人员: ${personnelData?.xm || '未知'}`)

        if (this.testConfig.enableLinkage) {
          this.autoFillCasePersonnelFromScan(code)
        }
      } else {
        this.clearCasePersonnelData()
      }
    },

    /**
     * 自动复用办案人员信息 - 核心功能
     */
    autoFillCasePersonnelFromScan(personnelCode) {
      if (this.linkageTimer) {
        clearTimeout(this.linkageTimer)
      }

      this.linkageTimer = setTimeout(() => {
        this.loadCasePersonnelInfo(personnelCode)
      }, this.testConfig.linkageDelay)
    },

    /**
     * 加载办案人员信息
     */
    async loadCasePersonnelInfo(personnelCode) {
      try {
        const response = await this.$store.dispatch('authGetRequest', {
          url: '/acp/db/detainRegKss/getCasePersonnelByjgrybm',
          params: { jgrybm: personnelCode }
        })

        if (response.code === 0 && response.data) {
          this.fillCasePersonnelForm(response.data)
          this.isLinkedData = true
          this.$Message.success('扫码复用办案人员信息成功！')
        } else {
          this.$Message.warning('未找到对应的办案人员信息')
        }
      } catch (error) {
        this.$Message.error('加载办案人员信息失败')
      }
    },

    /**
     * 填充办案人员表单
     */
    fillCasePersonnelForm(casePersonnelData) {
      this.formData.casePersonnel = {
        bar: casePersonnelData.bar || '',
        badwlx: casePersonnelData.badwlx || '',
        badw: casePersonnelData.badw || '',
        barzjlx: casePersonnelData.barzjlx || '',
        barzjhm: casePersonnelData.barzjhm || '',
        barlxff: casePersonnelData.barlxff || '',
        barxb: casePersonnelData.barxb || '',
        jh: casePersonnelData.jh || ''
      }
    },

    /**
     * 选择人员复用（模拟s-dialog选择）
     */
    selectPersonnelFromDialog() {
      this.$Message.info('此功能模拟s-dialog选择人员复用，实际开发中需要集成相应的选择组件')

      // 模拟选择结果
      const mockData = {
        bar: '张警官',
        badwlx: '1',
        badw: 'XX市公安局',
        barzjlx: '2',
        barzjhm: 'P123456789',
        barlxff: '13800138000',
        barxb: '1',
        jh: 'J001234'
      }

      this.fillCasePersonnelForm(mockData)
      this.isLinkedData = true
      this.$Message.success('已选择人员并复用信息')
    },

    /**
     * 扫码复用（手动触发扫码）
     */
    scanPersonnelCode() {
      if (!this.formData.jgrybm) {
        this.$Message.warning('请先选择被监管人员')
        return
      }

      this.$Message.info('使用当前被监管人员编码进行扫码复用')
      this.autoFillCasePersonnelFromScan(this.formData.jgrybm)
    },

    /**
     * 清空办案人员复用数据
     */
    clearCasePersonnelData() {
      this.formData.casePersonnel = {
        bar: '',
        badwlx: '',
        badw: '',
        barzjlx: '',
        barzjhm: '',
        barlxff: '',
        barxb: '',
        jh: ''
      }

      this.isLinkedData = false
      this.$Message.info('已清空办案人员复用数据')
    },

    /**
     * 保存表单
     */
    handleSave() {
      // 表单验证
      if (!this.formData.jgrybm) {
        this.$Message.error('请选择被监管人员')
        return
      }
      if (!this.formData.inquiryTime) {
        this.$Message.error('请选择提讯时间')
        return
      }
      if (!this.formData.inquiryLocation) {
        this.$Message.error('请输入提讯地点')
        return
      }
      if (!this.formData.casePersonnel.bar) {
        this.$Message.error('请填写办案人员姓名')
        return
      }
      if (!this.formData.casePersonnel.badw) {
        this.$Message.error('请填写办案单位')
        return
      }

      this.saving = true

      setTimeout(() => {
        this.saving = false
        this.$Message.success('提讯登记保存成功')
      }, 1500)
    },

    /**
     * 重置表单
     */
    handleReset() {
      this.$Modal.confirm({
        title: '确认重置',
        content: '确定要重置提讯表单吗？所有已填写的数据将被清空。',
        onOk: () => {
          this.formData = {
            jgrybm: '',
            inquiryTime: '',
            inquiryLocation: '',
            inquiryReason: '',
            estimatedDuration: null,
            casePersonnel: {
              bar: '',
              badwlx: '',
              badw: '',
              barzjlx: '',
              barzjhm: '',
              barlxff: '',
              barxb: '',
              jh: ''
            }
          }
          this.selectedPersonnel = null
          this.isLinkedData = false
          this.showPreview = false
          this.$Message.info('提讯表单已重置')
        }
      })
    },

    /**
     * 预览数据
     */
    handlePreview() {
      this.showPreview = !this.showPreview
    },

    /**
     * 测试填充办案人员数据
     */
    testFillCasePersonnel() {
      const testData = {
        bar: '测试警官',
        badwlx: '1',
        badw: '测试公安局',
        barzjlx: '2',
        barzjhm: 'P123456789',
        barlxff: '13800138000',
        barxb: '1',
        jh: 'J001234'
      }

      this.fillCasePersonnelForm(testData)
      this.isLinkedData = true
      this.$Message.success('测试数据已填充到办案人员表单')
    },

    /**
     * 测试清空办案人员数据
     */
    testClearCasePersonnel() {
      this.clearCasePersonnelData()
    },

    /**
     * 滚动到页面底部
     */
    scrollToBottom() {
      this.$nextTick(() => {
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: 'smooth'
        })
        this.$Message.info('正在滚动到页面底部')
      })
    }
  },

  beforeDestroy() {
    // 清理定时器
    if (this.linkageTimer) {
      clearTimeout(this.linkageTimer)
    }
  }
}
</script>

<style lang="less" scoped>
.inquiry-test-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: auto;

  .page-header {
    text-align: center;
    margin-bottom: 32px;
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      color: #666;
      margin: 0;
    }

    .page-info {
      margin-top: 12px;
      display: flex;
      gap: 8px;
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  // 测试配置样式
  .test-config {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #2d8cf0;

    h2 {
      font-size: 16px;
      font-weight: 600;
      color: #2d8cf0;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #e8e8e8;
    }

    .config-form {
      .ivu-form-item {
        margin-bottom: 16px;
      }
    }
  }

  // 状态面板样式
  .status-panel {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #ff9500;

    h2 {
      font-size: 16px;
      font-weight: 600;
      color: #ff9500;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #e8e8e8;
    }

    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 12px;

      .status-item {
        display: flex;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 4px;
        border: 1px solid #e9ecef;

        .label {
          color: #666;
          font-weight: 500;
          min-width: 100px;
          margin-right: 10px;
          font-size: 13px;
        }

        .value {
          color: #333;
          font-weight: 500;
          flex: 1;
          font-size: 13px;
        }
      }
    }

    .quick-test-buttons {
      margin-top: 16px;
      padding-top: 12px;
      border-top: 1px solid #e8e8e8;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  // 表单样式
  .inquiry-form {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 24px 0;
      padding-bottom: 12px;
      border-bottom: 2px solid #2d8cf0;
    }

    .form-section {
      margin-bottom: 32px;
      padding: 20px;
      background: #fafafa;
      border-radius: 6px;
      border: 1px solid #e8e8e8;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
        gap: 8px;

        .linkage-status {
          font-size: 12px;
          color: #19be6b;
          font-weight: normal;
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }

      .main-form {
        .ivu-form-item {
          margin-bottom: 16px;
        }
      }
    }

    // 办案人员区域特殊样式
    .case-personnel-area {
      background: #f0f9ff;
      border: 1px solid #91d5ff;

      .case-personnel-form {
        .personnel-actions {
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid #d9d9d9;
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
        }

        .data-source-tip {
          margin-top: 16px;
        }
      }
    }
  }

  // 操作按钮区域
  .form-actions {
    margin-top: 32px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;

    .ivu-btn {
      margin: 0 8px;
    }
  }

  // 数据预览区域
  .data-preview {
    margin-top: 24px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #e8e8e8;
    }

    .preview-content {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 16px;
      overflow-x: auto;
      font-size: 12px;
      line-height: 1.5;
      font-family: 'Courier New', monospace;
    }
  }
}
</style>

<style lang="less" scoped>
/* 全局样式覆盖，确保测试页面可以滚动 */
html, body {
  overflow: auto !important;
  height: auto !important;
  min-height: 100vh !important;
}

#app {
  overflow: auto !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* 确保页面容器可以滚动 */
.ivu-layout {
  overflow: auto !important;
  height: auto !important;
}

.ivu-layout-content {
  overflow: auto !important;
  height: auto !important;
}

/* 强制覆盖主应用的布局样式 */
.main-layout-con {
  height: auto !important;
  overflow: auto !important;
}

.main-content-con {
  overflow: auto !important;
}

.content-wrapper {
  height: auto !important;
  overflow: auto !important;
  min-height: calc(100vh - 200px) !important;
}

.layout-ht {
  height: auto !important;
  overflow: auto !important;
  min-height: calc(100vh - 100px) !important;
}

.layout-ht-bread {
  height: auto !important;
  overflow: auto !important;
  min-height: calc(100vh - 120px) !important;
}

// /* 确保icp-scroll类的元素可以滚动 */
// .icp-scroll {
//   overflow: auto !important;
//   height: auto !important;
// }

/* 确保base-app可以滚动 */
.base-app {
  overflow: auto !important;
  height: auto !important;
}
</style>
