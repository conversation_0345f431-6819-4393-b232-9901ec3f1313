// 办案人员选择组件样式 - 公安风格设计
.case-personnel-selector {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  position: relative;
  display: inline-block;
  max-width: 100%;

  // 标题区域
  .personnel-header {
    margin-bottom: 16px;

    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      h4 {
        color: #d4a574;
        position: relative;
        padding-left: 12px;
        margin: 0;
        font-size: 14px;
        font-weight: 600;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: #d4a574;
          border-radius: 1px;
        }
      }

      .action-buttons {
        margin-left: auto;
        display: flex;
        gap: 8px;
        flex-shrink: 0;

        .refresh-btn,
        .reselect-btn {
          padding: 0 15px;

          .ivu-icon {
            margin-right: 4px;
          }
        }
      }
    }
  }

  // 输入区域样式
  .personnel-input-area {
    border: 2px dashed #bfbfbf;
    border-radius: 4px;

    // 加载状态提示
    .loading-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px;
      margin-top: 12px;
      background: #f8f9fa;
      border-radius: 4px;
      color: #666;
      font-size: 13px;

      .ivu-spin {
        margin-right: 4px;
      }
    }
    background: #fafafa;
    padding: 20px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #d4a574;
      background: #fdf8f0;
    }

    .input-container {
      display: flex;
      gap: 12px;
      align-items: center;

      .personnel-input {
        flex: 1;
        border-radius: 4px;
        
        /deep/ .ivu-input {
          border-color: #d9d9d9;
          transition: all 0.3s ease;

          &:focus {
            border-color: #d4a574;
            box-shadow: 0 0 0 2px rgba(212, 165, 116, 0.2);
          }
        }
      }

      .search-btn {
        background: #d4a574;
        border-color: #d4a574;
        color: #fff;
        padding: 0 20px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background: #c19660;
          border-color: #c19660;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(212, 165, 116, 0.3);
        }

        .ivu-icon {
          margin-right: 4px;
        }
      }
    }
  }

  // 自动模式状态显示
  .auto-mode-status {
    .status-container {
      .ivu-input-wrapper {
        .ivu-input {
          border-color: #d9d9d9;
          background: #fafafa;
          color: #666;

          &:focus {
            border-color: #d4a574;
            box-shadow: 0 0 0 2px rgba(212, 165, 116, 0.2);
          }
        }

        .ivu-input-suffix {
          .loading-icon {
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }

  // 旋转动画
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // 统一宽度的卡片容器
  .unified-cards-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;

    // 使用CSS变量来统一管理卡片宽度
    --card-width: auto;

    // 当容器内容确定后，动态计算最适合的宽度
    min-width: fit-content;

    // 所有卡片都使用统一的宽度
    .personnel-card {
      width: 100%;
      min-width: 370px; // 设置一个合理的最小宽度
      max-width: 100%;
    }
  }

  // 办案人员信息卡片
  .personnel-card {
    display: flex;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    // 头像区域
    .avatar-section {
      flex: 0 0 140px;
      background: linear-gradient(135deg, #fdf8f0 0%, #f5ead6 100%);
      border-right: 1px solid #d9d9d9;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px 12px;

      .avatar-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;

        .personnel-avatar-placeholder {
          width: 100px;
          height: 120px;
          background: #fff;
          border-radius: 4px;
          border: 2px solid #d9d9d9;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          color: #d4a574;
        }

        .personnel-name {
          font-size: 14px;
          font-weight: 600;
          color: #d4a574;
          background: #fff;
          padding: 4px 8px;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          min-width: 70px;
          text-align: center;
        }
      }
    }

    // 信息区域
    .info-section {
      flex: 1;
      padding: 14px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .info-item {
        background: #fafafa;
        border-radius: 4px;
        padding: 8px 12px;
        border: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        transition: background-color 0.2s ease;

        &:hover {
          background: #fdf8f0;
        }

        &.highlight-item {
          background: #fdf8f0;
          border-color: #e6d4b7;

          &:hover {
            background: #fcf4e8;
          }
        }

        .info-label {
          font-size: 12px;
          font-weight: 500;
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .info-value {
          font-size: 12px;
          font-weight: 500;
          color: #333;
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &.highlight-value {
            color: #d4a574;
            font-weight: 600;
            font-size: 13px;
          }
        }
      }
    }
  }

  // 扫码提示区域
  .scan-tip {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 12px;
    padding: 8px 12px;
    background: #fdf8f0;
    border: 1px solid #e6d4b7;
    border-radius: 4px;
    color: #b8860b;
    font-size: 12px;

    .ivu-icon {
      color: #b8860b;
    }

    .scan-status {
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 4px;
      color: #19be6b;
      font-weight: 500;

      .ivu-icon {
        color: #19be6b;
        animation: pulse 1.5s infinite;
      }
    }
  }

  // 扫码状态动画
  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }

  // 移动端响应式样式
  @media (max-width: 768px) {
    .unified-cards-container {
      gap: 12px;

      .personnel-card {
        min-width: auto;
        width: 100%;
      }
    }

    .personnel-card {
      flex-direction: column;

      .avatar-section {
        flex: none;
        padding: 16px;
        border-right: none;
        border-bottom: 1px solid #d9d9d9;

        .avatar-container {
          gap: 10px;

          .personnel-avatar-placeholder {
            width: 90px;
            height: 110px;
          }

          .personnel-name {
            font-size: 14px;
            padding: 4px 8px;
            min-width: 60px;
          }
        }
      }

      .info-section {
        padding: 12px;
        gap: 6px;

        .info-item {
          padding: 6px 8px;

          .info-label {
            width: 70px;
            font-size: 11px;
          }

          .info-value {
            font-size: 11px;

            &.highlight-value {
              font-size: 12px;
            }
          }
        }
      }
    }

    .personnel-input-area {
      padding: 16px;

      .input-container {
        flex-direction: column;
        gap: 8px;

        .personnel-input {
          width: 100%;
        }

        .search-btn {
          width: 100%;
          padding: 8px 20px;
        }
      }
    }

    .scan-tip {
      margin-top: 8px;
      padding: 6px 10px;
      font-size: 11px;
    }
  }
}
