<template>
  <div class="epidemic-response">
    <FormLayout
      ref="formLayout"
      :header-config="headerConfig"
      :show-header="false"
      :bottom-actions="bottomActions"
      :show-bottom-actions="true"
      :form-data="formData"
      :responsive="true"
      :loading="loading"
      @header-action="handleHeaderAction"
      @bottom-action="handleBottomAction"
    >
      <!-- 疫情处置表单 -->
      <template #form>
        <DynamicForm
          ref="businessForm"
          v-model="formData"
          :config="formConfig"
          :mode="formMode"
          :loading="loading"
          @field-change="handleFieldChange"
          @validate="handleValidate"
        />
      </template>
    </FormLayout>
  </div>
</template>

<script>
import {FIELD_TYPES, FORM_MODES} from '@/components/dynamic-form/types'
import {mapActions} from 'vuex'
import FormLayout from '@/components/bsp-layout/layouts/FormLayout.vue'

export default {
  name: 'EpidemicDetailResponse',
  props: {
    pk: {
      type: String,
      default: () => ({})
    }
  },
  components: {
    FormLayout
  },
  data() {
    return {
      loading: false,
      formMode: FORM_MODES.VIEW,

      // 表单数据
      formData: {
        sfcxzjxyq: null,      // 是否出现聚集性疫情
        cxzjxyqrq: null,      // 出现聚集性疫情日期
        zjxyqczqk: '',        // 聚集性疫情处置情况
        bgfs: null,           // 报告方式
        crbbgktbrq: null,     // 传染病报告卡填报日期
        crbbgk: null          // 传染病报告卡
      },

      // 表单头部配置
      headerConfig: {
        title: '疫情处置',
        icon: 'ios-medical',
        iconColor: '#5b8ff9',
        iconSize: 20
      },

      // 底部操作按钮
      bottomActions: [
        {
          name: 'back',
          label: '返回',
          type: 'default',
          icon: 'ios-arrow-back'
        }
      ]
    }
  },

  computed: {
    // 表单配置
    formConfig() {
      return [
        {
          title: '疫情处置',
          columns: 2,
          labelWidth: 180,
          labelColon: true,
          collapsible: false,
          collapsed: false,
          showHeader: false,
          fields: [
            {
              key: 'sfcxzjxyq',
              label: '是否出现聚集性疫情',
              type: FIELD_TYPES.DICTIONARY,
              required: true,
              disabled: true,
              span: 12,
              dicName: 'ZD_TYSFDM',
              props: {
                placeholder: '请选择',
                multiple: false
              }
            },
            {
              key: 'cxzjxyqrq',
              label: '出现聚集性疫情日期',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              disabled: true,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd',
                clearable: true
              }
            },
            {
              key: 'zjxyqczqk',
              label: '聚集性疫情处置情况',
              type: FIELD_TYPES.TEXTAREA,
              disabled: true,
              required: false,
              span: 24,
              props: {
                placeholder: '请输入聚集性疫情处置情况',
                autosize: {minRows: 3, maxRows: 5}
              }
            },
            {
              key: 'bgfs',
              label: '报告方式',
              type: FIELD_TYPES.DICTIONARY,
              disabled: true,
              required: false,
              span: 12,
              dicName: 'ZD_CYB_JHBSCBGFS',
              props: {
                placeholder: '请选择报告方式',
                multiple: false,
                clearable: true
              }
            },
            {
              key: 'crbbgktbrq',
              label: '传染病报告卡填报日期',
              type: FIELD_TYPES.DATE_TIME_PICKER,
              disabled: true,
              required: false,
              span: 12,
              props: {
                placeholder: '请选择日期',
                format: 'yyyy-MM-dd',
                clearable: true
              }
            },
            {
              key: 'crbbgk',
              label: '传染病报告卡',
              type: FIELD_TYPES.FILE_UPLOAD,
              disabled: true,
              required: false,
              span: 24,
              props: {
                serviceMark: serverConfig.OSS_SERVICE_MARK,
                bucketName: serverConfig.bucketName,
                multiple: true,
                isDetail: true,
                defaultList: this.crbbgkFileList  // 使用计算属性
              },
              events: {
                'fileComplete': this.fileCompleteReportCard
              }
            }
          ]
        }
      ]
    },

    // 传染病报告卡文件列表
    crbbgkFileList() {
      if (this.formData.crbbgk) {
        try {
          return typeof this.formData.crbbgk === 'string'
            ? JSON.parse(this.formData.crbbgk)
            : this.formData.crbbgk
        } catch (e) {
          console.warn('解析传染病报告卡文件列表失败:', e)
          return []
        }
      }
      return []
    }
  },


  mounted() {
    this.initializeData()
  },

  methods: {
    ...mapActions(['authPostRequest', 'authGetRequest']),

    // 初始化数据
    initializeData() {
      // 处理路由参数
      const {mode, recordId} = this.$route.query

      if (mode) {
        this.formMode = mode === 'view' ? FORM_MODES.VIEW :
          mode === 'edit' ? FORM_MODES.EDIT :
            FORM_MODES.CREATE
      }

      // 如果有记录ID且不是新增模式，则加载数据
      if (recordId && mode !== 'add') {
        this.loadRecordData(recordId)
      }

      // 兼容旧的pk属性
      if (this.pk && !recordId) {
        this.loadRecordData(this.pk)
      }
    },

    // 加载记录数据
    loadRecordData(recordId) {
      this.loading = true
      this.authGetRequest({
        url: this.$path.epidemic_resp_get,
        params: {id: recordId}
      }).then(res => {
        this.loading = false
        if (res.success && res.data) {
          // 合并数据，保留现有字段
          this.formData = {...this.formData, ...res.data}

          // 处理文件列表数据
          this.processFileData(res.data)

          // 设置表单模式为查看模式（如果是查看详情）
          if (this.$route.query.mode === 'view') {
            this.formMode = FORM_MODES.VIEW
          }

          console.log('疫情处置数据加载成功:', res.data)
        } else {
          this.$Message.error(res.msg || '加载数据失败')
        }
      }).catch(error => {
        this.loading = false
        this.$Message.error('网络异常，请稍后重试')
        console.error('加载数据失败：', error)
      })
    },

    // 处理文件数据
    processFileData(data) {
      // 处理传染病报告卡文件列表
      if (data.crbbgk) {
        try {
          // 如果是字符串，尝试解析为数组
          if (typeof data.crbbgk === 'string') {
            const fileList = JSON.parse(data.crbbgk)
            console.log('解析传染病报告卡文件列表:', fileList)
            // 可以在这里进行额外的文件处理逻辑
          }
        } catch (e) {
          console.warn('解析传染病报告卡文件数据失败:', e)
        }
      }
    },

    // 处理字段变化
    handleFieldChange(key, value, formData) {
      console.log('字段变化:', key, value)
      this.formData = {...this.formData, ...formData}
    },

    // 处理验证
    handleValidate(prop, valid, message) {
      console.log('表单验证:', prop, valid, message)
    },

    // 处理头部操作
    handleHeaderAction(event) {
      console.log('头部操作:', event)
    },

    // 处理底部操作
    handleBottomAction(event) {
      const {action} = event

      switch (action.name) {
        case 'back':
          this.handleBack()
          break
      }
    },


    // 返回
    handleBack() {
      this.$emit('toback')
    },

    // 传染病报告卡文件上传完成处理
    fileCompleteReportCard(files) {
      console.log('传染病报告卡上传完成:', files)
      this.$Message.success('传染病报告卡上传完成')

      // 更新表单数据
      this.formData.crbbgk = JSON.stringify(files)
    }
  }
}
</script>

<style lang="less" scoped>
.epidemic-response {
  height: 100%;

  // 确保表单项标签对齐
  /deep/ .ivu-form-item-label {
    text-align: right;
    padding-right: 12px;
  }
}
</style>
