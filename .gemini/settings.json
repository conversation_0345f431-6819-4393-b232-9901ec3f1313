{"mcpServers": {"jg": {"command": "C:\\Users\\<USER>\\.jdks\\corretto-17.0.13\\bin\\java", "args": ["-Dfile.encoding=UTF-8", "-Dsun.jnu.encoding=UTF-8", "-Dspring.ai.mcp.server.stdio=true", "-Dspring.main.web-application-type=none", "-Dlogging.pattern.console=", "-jar", "C:\\mcp\\mcp-stdio-server-bsp-1.0.0.jar"], "env": {}}, "shrimp-task-manager": {"command": "C:\\Users\\<USER>\\AppData\\Roaming\\nvm\\v24.3.0\\node.exe", "args": ["C:\\dyz\\5code\\mcp-shrimp-task-manager-main\\dist\\index.js"], "env": {"DATA_DIR": "C:\\dyz\\5code\\mcp-shrimp-task-manager-main\\data", "TEMPLATES_USE": "zh", "ENABLE_GUI": "true"}}, "promptx": {"command": "npx", "args": ["-y", "-f", "--registry", "https://registry.npmjs.org", "dpml-prompt@beta", "mcp-server"], "env": {"PROMPTX_WORKSPACE": "C:\\dyz\\5code\\rs-acp-web"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"]}}}