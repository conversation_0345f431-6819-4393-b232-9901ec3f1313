# BSP系统参数工具类使用文档

## 📋 概述

`bsp-system-param.js` 是一个专用于获取BSP系统配置参数值的工具类，支持从BSP系统或其他应用系统中获取配置参数。该工具类提供了简单易用的API接口，支持单个参数获取、批量获取、缓存功能等特性。

## 🌟 功能特性

- ✅ **简单易用**: 提供简洁的API接口
- ✅ **参数验证**: 完善的输入参数验证
- ✅ **错误处理**: 详细的错误信息和处理机制
- ✅ **缓存支持**: 内置5分钟缓存，避免重复请求
- ✅ **批量获取**: 支持一次性获取多个参数
- ✅ **多应用支持**: 支持不同应用系统的参数获取
- ✅ **Promise/async-await**: 现代化的异步处理方式
- ✅ **TypeScript友好**: 完整的JSDoc注释
- ✅ **自动认证**: 自动处理访问令牌，无需手动添加

## 🚀 快速开始

### 安装使用

工具类已经创建在 `src/libs/bsp-system-param.js`，可以直接导入使用：

```javascript
import { getBspSystemParam } from '@/libs/bsp-system-param'

// 获取系统名称
getBspSystemParam('SYSTEM_NAME').then(value => {
  console.log('系统名称:', value)
})
```

### 基本用法

```javascript
// 1. 获取单个参数（默认BSP系统）
const systemName = await getBspSystemParam('SYSTEM_NAME')

// 2. 获取单个参数（指定应用系统）
const version = await getBspSystemParam('VERSION', this.globalAppCode)

// 3. 批量获取参数
const params = await getBspSystemParams(['SYSTEM_NAME', 'VERSION', 'AUTHOR'])

// 4. 使用缓存功能
const cachedValue = await getBspSystemParamWithCache('SYSTEM_NAME')
```

## 📁 文件结构

```
src/libs/
├── bsp-system-param.js          # 主要工具类文件
└── naming-standards.md          # 命名规范文档

src/view/test/
└── bsp-system-param.test.vue    # 测试页面组件

doc/tools/
└── bsp-system-param.md          # 本文档
```

## 🔧 API 接口

### 核心函数

| 函数名 | 描述 | 参数 | 返回值 |
|--------|------|------|--------|
| `getBspSystemParam` | 获取单个系统参数 | `(paramName, systemMark?)` | `Promise<string>` |
| `getBspSystemParams` | 批量获取系统参数 | `(paramNames[], systemMark?)` | `Promise<object>` |
| `getBspSystemParamWithCache` | 带缓存的参数获取 | `(paramName, systemMark?, useCache?)` | `Promise<string>` |
| `clearBspSystemParamCache` | 清除参数缓存 | `(paramName?, systemMark?)` | `void` |



### 参数说明

- **paramName**: 参数名称（必填，字符串类型）
- **systemMark**: 应用标识（可选，默认为'bsp'，可传入`this.globalAppCode`）
- **useCache**: 是否使用缓存（可选，默认为true）

## 🔗 接口信息

### 后端接口
- **接口地址**: `/bsp-com/api/v1/config/param/getParamValue`
- **请求方式**: `GET`
- **请求参数**: 
  - `paramName` (string, 必填): 参数名称
  - `systemMark` (string, 必填): 应用标识
  - `access_token` (string, 必填): 访问令牌（自动添加）

**注意**: `access_token` 由工具类自动添加，无需手动传入。

### 响应格式
```json
{
  "code": 0,
  "data": "参数值",
  "msg": "成功",
  "success": true
}
```

## 🔐 认证机制说明

工具类使用项目的认证机制自动处理访问令牌：

- **自动认证**: 工具类内部使用 `store.dispatch('authGetRequest')` 发送请求
- **Token管理**: 访问令牌 (`access_token`) 由系统自动从 store 中获取并添加到请求中
- **无需手动处理**: 开发者无需关心认证细节，直接调用工具类函数即可
- **权限验证**: 确保当前用户已登录且有权限访问对应的系统参数

## 🌐 全局应用编码说明

在项目的 `src/main.js` 中，已经设置了全局应用编码：

```javascript
Vue.prototype.globalAppCode = serverConfig.APP_CODE;
```

在Vue组件中可以通过 `this.globalAppCode` 获取当前应用的编码。

## 📖 详细用法

### 1. 导入工具类

```javascript
// 导入BSP专用函数
import { getBspSystemParam } from '@/libs/bsp-system-param'

// 导入多个函数
import { 
  getBspSystemParam, 
  getBspSystemParams, 
  getBspSystemParamWithCache 
} from '@/libs/bsp-system-param'

// 导入默认对象
import BspSystemParam from '@/libs/bsp-system-param'
```

### 2. 获取单个系统参数

#### 基本用法（使用默认BSP系统）
```javascript
import { getBspSystemParam } from '@/libs/bsp-system-param'

// Promise 方式
getBspSystemParam('SYSTEM_NAME').then(value => {
  console.log('系统名称:', value)
}).catch(error => {
  console.error('获取参数失败:', error)
})

// async/await 方式
async function loadSystemName() {
  try {
    const systemName = await getBspSystemParam('SYSTEM_NAME')
    console.log('系统名称:', systemName)
  } catch (error) {
    console.error('获取参数失败:', error)
  }
}
```

#### 指定应用系统
```javascript
// 在Vue组件中使用全局应用编码
export default {
  async mounted() {
    try {
      // 使用当前应用的编码获取参数
      const paramValue = await getBspSystemParam('PARAM_NAME', this.globalAppCode)
      console.log('参数值:', paramValue)
    } catch (error) {
      console.error('获取参数失败:', error)
    }
  }
}

// 指定具体的应用编码
const appCode = 'your-app-code'
getBspSystemParam('PARAM_NAME', appCode).then(value => {
  console.log('参数值:', value)
})
```

### 3. 批量获取系统参数

```javascript
import { getBspSystemParams } from '@/libs/bsp-system-param'

// 批量获取多个参数
const paramNames = ['SYSTEM_NAME', 'VERSION', 'AUTHOR', 'DESCRIPTION']

getBspSystemParams(paramNames).then(params => {
  console.log('系统参数:', params)
  // 输出示例:
  // {
  //   SYSTEM_NAME: '监管实战平台',
  //   VERSION: '1.0.0',
  //   AUTHOR: '开发团队',
  //   DESCRIPTION: '北京监管实战平台前端系统'
  // }
})

// 指定应用系统批量获取
getBspSystemParams(paramNames, this.globalAppCode).then(params => {
  console.log('应用参数:', params)
})
```

### 4. 使用缓存功能

```javascript
import { getBspSystemParamWithCache, clearBspSystemParamCache } from '@/libs/bsp-system-param'

// 使用缓存（推荐用法，避免重复请求）
getBspSystemParamWithCache('SYSTEM_NAME').then(value => {
  console.log('系统名称:', value) // 第一次从服务器获取
})

getBspSystemParamWithCache('SYSTEM_NAME').then(value => {
  console.log('系统名称:', value) // 第二次从缓存获取
})

// 强制刷新，不使用缓存
getBspSystemParamWithCache('SYSTEM_NAME', 'bsp', false).then(value => {
  console.log('最新系统名称:', value)
})

// 清除指定参数缓存
clearBspSystemParamCache('SYSTEM_NAME', 'bsp')

// 清除所有缓存
clearBspSystemParamCache()
```

## 💡 使用示例

### 在Vue组件中使用

```vue
<template>
  <div class="system-config">
    <h2>{{ systemName }}</h2>
    <p>版本: {{ version }}</p>
    <p>作者: {{ author }}</p>
    <div v-if="loading">加载中...</div>
    <div v-if="error" class="error">{{ error }}</div>
  </div>
</template>

<script>
import { getBspSystemParam, getBspSystemParams } from '@/libs/bsp-system-param'

export default {
  name: 'SystemConfig',
  data() {
    return {
      systemName: '',
      version: '',
      author: '',
      loading: false,
      error: null
    }
  },
  async mounted() {
    await this.loadSystemConfig()
  },
  methods: {
    async loadSystemConfig() {
      this.loading = true
      this.error = null

      try {
        // 方式1: 单个获取
        this.systemName = await getBspSystemParam('SYSTEM_NAME')
        this.version = await getBspSystemParam('VERSION', this.globalAppCode)

        // 方式2: 批量获取（推荐）
        const params = await getBspSystemParams([
          'SYSTEM_NAME',
          'VERSION',
          'AUTHOR'
        ], this.globalAppCode)

        this.systemName = params.SYSTEM_NAME || '未知系统'
        this.version = params.VERSION || '1.0.0'
        this.author = params.AUTHOR || '开发团队'

      } catch (error) {
        this.error = `加载系统配置失败: ${error.message}`
        console.error('系统配置加载失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.error {
  color: red;
  margin-top: 10px;
}
</style>
```

### 系统初始化配置

```javascript
import { getBspSystemParams } from '@/libs/bsp-system-param'

export async function initBspSystemConfig() {
  try {
    const config = await getBspSystemParams([
      'SYSTEM_NAME',
      'VERSION',
      'DEBUG_MODE',
      'API_TIMEOUT'
    ])

    // 根据配置初始化系统
    if (config.DEBUG_MODE === 'true') {
      console.log('BSP调试模式已启用')
    }

    if (config.API_TIMEOUT) {
      console.log(`BSP API超时时间设置为: ${config.API_TIMEOUT}ms`)
    }

    return config
  } catch (error) {
    console.error('BSP系统初始化失败:', error)
    throw error
  }
}
```

### 动态主题配置

```javascript
import { getBspSystemParams } from '@/libs/bsp-system-param'

export async function loadBspThemeConfig() {
  try {
    const themeParams = await getBspSystemParams([
      'THEME_COLOR',
      'THEME_MODE',
      'LOGO_URL',
      'FAVICON_URL'
    ])

    // 应用BSP主题配置
    if (themeParams.THEME_COLOR) {
      document.documentElement.style.setProperty('--bsp-primary-color', themeParams.THEME_COLOR)
    }

    if (themeParams.THEME_MODE === 'dark') {
      document.body.classList.add('bsp-dark-theme')
    }

    if (themeParams.LOGO_URL) {
      const logoElements = document.querySelectorAll('.bsp-system-logo')
      logoElements.forEach(el => {
        el.src = themeParams.LOGO_URL
      })
    }

    return themeParams
  } catch (error) {
    console.error('BSP主题配置加载失败:', error)
    return null
  }
}
```



## ❌ 错误处理

工具类提供了完善的错误处理机制：

```javascript
import { getBspSystemParam } from '@/libs/bsp-system-param'

getBspSystemParam('PARAM_NAME').catch(error => {
  console.error('错误类型:', error.message)

  // 可能的错误类型:
  // 1. 参数验证错误: "参数名称(paramName)不能为空且必须为字符串类型"
  // 2. 网络请求错误: "获取系统参数失败: 网络请求异常"
  // 3. 业务逻辑错误: "获取系统参数失败，错误代码: 1001"
  // 4. 响应格式错误: "响应数据格式异常"
})
```

## ⚠️ 注意事项

1. **参数名称**: `paramName` 参数必须为非空字符串
2. **应用标识**: `systemMark` 参数可选，默认为 'bsp'
3. **缓存时间**: 默认缓存5分钟，可根据需要调整
4. **网络异常**: 请确保网络连接正常，接口地址可访问
5. **权限验证**: 确保当前用户有权限访问对应的系统参数
6. **函数命名**: 使用 `getBspSystemParam` 等BSP专用函数名

## 🎯 最佳实践

1. **使用缓存**: 对于不经常变化的参数，建议使用 `getBspSystemParamWithCache`
2. **批量获取**: 需要多个参数时，使用 `getBspSystemParams` 而不是多次调用单个函数
3. **错误处理**: 始终添加 try-catch 或 .catch() 处理可能的错误
4. **应用编码**: 在Vue组件中使用 `this.globalAppCode` 获取当前应用编码
5. **参数验证**: 在使用参数值之前，检查是否为空或提供默认值
6. **函数命名**: 使用 `getBspSystemParam` 等BSP专用函数名

## 🧪 测试

### 测试页面

访问测试页面进行功能验证：

```
/test/bsp-system-param
```

测试页面提供以下功能：
- 单个参数获取测试
- 批量参数获取测试
- 缓存功能测试
- 自定义参数测试
- 错误处理验证

### 常用测试参数

```javascript
// 常用的系统参数名称
const commonParams = [
  'SYSTEM_NAME',      // 系统名称
  'VERSION',          // 系统版本
  'AUTHOR',           // 开发作者
  'DESCRIPTION',      // 系统描述
  'CONTACT_EMAIL',    // 联系邮箱
  'DEBUG_MODE',       // 调试模式
  'API_TIMEOUT',      // API超时时间
  'THEME_COLOR',      // 主题颜色
  'LOGO_URL'          // Logo地址
]
```

## 🔄 更新日志

### v1.3.0 (2025-07-02)
- ✨ 更新文件结构说明
- ✨ 移除已删除文件的引用
- ✨ 统一测试页面路径

### v1.2.0 (2025-07-02)
- ✨ 简化API
- ✨ 统一使用BSP专用函数名
- ✨ 优化文档结构

### v1.1.0 (2025-07-02)
- ✨ 重命名为BSP专用工具类
- ✨ 完善文档和示例
- ✨ 统一命名规范

### v1.0.0 (2025-07-02)
- ✨ 初始版本发布
- ✨ 支持单个参数获取
- ✨ 支持批量参数获取
- ✨ 支持参数缓存功能
- ✨ 完善的错误处理机制
- ✨ 详细的使用文档和示例

---

**文档版本**: v1.3.0
**最后更新**: 2025-07-02
**维护团队**: 北京监管项目前端开发团队
