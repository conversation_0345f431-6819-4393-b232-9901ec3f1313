# case-personnel-selector 办案人员选择组件

通用的办案人员选择组件，支持手动输入、扫码枪自动识别和人员信息展示功能。

## 功能特性

- ✅ 手动输入办案人员编码
- ✅ 扫码枪自动识别（键盘监听）
- ✅ 办案人员信息展示（包含照片、基本信息）
- ✅ 输入格式验证
- ✅ 加载状态显示
- ✅ 支持 v-model 双向绑定
- ✅ 支持编辑和详情模式
- ✅ 刷新和清空功能
- ✅ 统一的样式设计
- ✅ 响应式布局

## 使用方式

### 测试页面
办案人员选择组件测试页面： [http://localhost:8080/#/test/business-example](http://localhost:8080/#/test/business-example)

### 基础用法（编辑模式）

```vue
<template>
  <div>
    <case-personnel-selector
      v-model="formData.casePersonnelCode"
      @change="handleCasePersonnelChange"
    />
  </div>
</template>

<script>
import casePersonnelSelector from '@/components/case-personnel-selector'

export default {
  components: {
    casePersonnelSelector
  },
  data() {
    return {
      formData: {
        casePersonnelCode: '', // 办案人员编码
        casePersonnel: {}      // 办案人员信息对象
      }
    }
  },
  methods: {
    handleCasePersonnelChange(personnelData, code) {
      // personnelData: 完整的办案人员信息对象
      // code: 办案人员编码
      this.formData.casePersonnel = personnelData
      this.formData.casePersonnelCode = code
      
      if (personnelData) {
        console.log('选择的办案人员:', personnelData.bar) // 办案人员姓名
        console.log('办案单位:', personnelData.badw)     // 办案单位
      }
    }
  }
}
</script>
```

### 详情模式用法

```vue
<template>
  <case-personnel-selector
    v-model="personnelCode"
    mode="detail"
    title="办案人员信息"
  />
</template>
```

### 高级配置

```vue
<template>
  <case-personnel-selector
    v-model="formData.casePersonnelCode"
    title="主办案人员"
    placeholder="请输入主办案人员编码"
    :enable-scan="true"
    :show-scan-tip="true"
    scan-prefix="case:"
    :field-mapping="customMapping"
    @change="handleChange"
  />
</template>

<script>
export default {
  data() {
    return {
      customMapping: {
        name: 'bar',        // 姓名字段
        unit: 'badw',       // 单位字段
        unitType: 'badwlx', // 单位类型字段
        gender: 'barxb',    // 性别字段
        idType: 'barzjlx',  // 证件类型字段
        idNumber: 'barzjhm', // 证件号码字段
        contact: 'barlxff',  // 联系方式字段
        policeNumber: 'jh'   // 警号字段
      }
    }
  }
}
</script>
```

## Props 配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | '' | 办案人员编码，支持 v-model |
| title | String | '办案人员' | 组件标题 |
| placeholder | String | '请输入办案人员编码' | 输入框占位符 |
| mode | String | 'edit' | 模式：'edit' 编辑模式，'detail' 详情模式 |
| enableScan | Boolean | true | 是否启用扫码功能 |
| showScanTip | Boolean | true | 是否显示扫码提示 |
| scanPrefix | String | 'case:' | 扫码前缀标识符 |
| timeThreshold | Number | 50 | 扫码时间阈值（毫秒） |
| fieldMapping | Object | 默认映射 | 字段映射配置 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | (code: String) | 编码变化时触发，用于 v-model |
| change | (personnelData: Object, code: String) | 办案人员信息变化时触发 |

## Methods 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getCurrentPersonnelCode | - | String | 获取当前办案人员编码 |
| getCurrentPersonnelInfo | - | Object | 获取当前办案人员信息 |
| refreshPersonnelInfo | - | - | 刷新当前办案人员信息 |
| clearSelection | - | - | 清空选择 |
| validatePersonnelCode | (code: String) | Object | 验证编码格式 |

## 字段映射

### 默认字段映射

```javascript
{
  name: 'bar',        // 办案人员姓名
  unit: 'badw',       // 办案单位
  unitType: 'badwlx', // 办案单位类型
  gender: 'barxb',    // 办案人员性别
  idType: 'barzjlx',  // 办案人员证件类型
  idNumber: 'barzjhm', // 办案人员证件号码
  contact: 'barlxff',  // 办案人员联系方式
  policeNumber: 'jh'   // 警号
}
```

### API 数据结构

```javascript
// 接口返回的办案人员数据结构
{
  "bar": "张警官",           // 办案人员姓名
  "badw": "XX市公安局",      // 办案单位
  "badwlx": "1",            // 办案单位类型
  "barxb": "1",             // 办案人员性别
  "barzjlx": "2",           // 办案人员证件类型
  "barzjhm": "P123456789",  // 办案人员证件号码
  "barlxff": "13800138000", // 办案人员联系方式
  "jh": "J001234"           // 警号
}
```

## 扫码功能

### 扫码识别原理
- 监听全局键盘事件
- 通过按键时间间隔判断是否为扫码枪输入
- 支持自定义扫码前缀标识符
- Enter键确认扫码完成

### 扫码格式
```
case:123456  // 带前缀的扫码格式
123456       // 不带前缀的扫码格式
```

### 扫码配置
```vue
<case-personnel-selector
  :enable-scan="true"
  :show-scan-tip="true"
  scan-prefix="case:"
  :time-threshold="50"
/>
```

## 样式定制

### CSS 类名

```less
.case-personnel-selector {
  .personnel-header {}        // 头部区域
  .personnel-input-area {}    // 输入区域
  .loading-tip {}             // 加载提示
  .personnel-info {}          // 人员信息展示区域
  .personnel-photo {}         // 人员照片
  .personnel-details {}       // 人员详细信息
  .scan-tip {}               // 扫码提示
  .action-buttons {}         // 操作按钮区域
}
```

### 自定义样式

```less
// 自定义组件样式
.case-personnel-selector {
  .personnel-input-area {
    border-color: #your-color;
  }
  
  .personnel-info {
    background: #your-background;
  }
}
```

## API 接口

### 获取办案人员信息

**接口地址**: `/acp/db/detainRegKss/getCasePersonnelByjgrybm`

**请求方法**: GET

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| jgrybm | String | 是 | 办案人员编码 |

**响应格式**:
```javascript
{
  "code": 0,
  "success": true,
  "msg": "成功",
  "data": {
    "bar": "办案人员姓名",
    "badw": "办案单位",
    "badwlx": "办案单位类型",
    "barxb": "办案人员性别",
    "barzjlx": "办案人员证件类型",
    "barzjhm": "办案人员证件号码",
    "barlxff": "办案人员联系方式",
    "jh": "警号"
  }
}
```

## 注意事项

1. **扫码功能**: 需要确保扫码枪配置为键盘模式
2. **字段映射**: 根据实际API返回字段调整映射配置
3. **权限控制**: 确保用户有查询办案人员信息的权限
4. **网络异常**: 组件已内置网络异常处理
5. **数据验证**: 组件会自动验证输入的编码格式

## 常见问题

### Q: 扫码功能不工作？
A: 请检查以下几点：
1. 确保扫码枪配置为键盘模式
2. 检查 `enableScan` 属性是否为 true
3. 确认扫码内容格式是否正确
4. 检查浏览器是否有焦点

### Q: 如何自定义字段映射？
A: 通过 `field-mapping` 属性传入自定义映射对象：
```vue
<case-personnel-selector
  :field-mapping="{
    name: 'yourNameField',
    unit: 'yourUnitField'
  }"
/>
```

### Q: 如何处理API权限问题？
A: 组件会自动处理API错误，显示相应的错误提示。确保用户有查询办案人员信息的权限。

### Q: 如何在表单中使用？
A: 组件支持 v-model，可以直接绑定到表单数据：
```vue
<Form :model="formData">
  <FormItem label="办案人员">
    <case-personnel-selector
      v-model="formData.casePersonnelCode"
      @change="handleChange"
    />
  </FormItem>
</Form>
```

## 实际应用示例

### 在提讯登记中使用

```vue
<template>
  <div class="inquiry-form">
    <Form :model="formData" :label-width="120">
      <!-- 被监管人员 -->
      <FormItem label="被监管人员">
        <personnel-selector
          v-model="formData.jgrybm"
          @change="handlePersonnelChange"
        />
      </FormItem>

      <!-- 办案人员 -->
      <FormItem label="办案人员">
        <case-personnel-selector
          v-model="formData.casePersonnelCode"
          title="办案人员"
          @change="handleCasePersonnelChange"
        />
      </FormItem>

      <!-- 其他表单项 -->
      <FormItem label="提讯时间">
        <DatePicker v-model="formData.inquiryTime" type="datetime" />
      </FormItem>
    </Form>
  </div>
</template>

<script>
import personnelSelector from '@/components/personnel-selector'
import casePersonnelSelector from '@/components/case-personnel-selector'

export default {
  components: {
    personnelSelector,
    casePersonnelSelector
  },
  data() {
    return {
      formData: {
        jgrybm: '',              // 被监管人员编码
        casePersonnelCode: '',   // 办案人员编码
        inquiryTime: ''          // 提讯时间
      }
    }
  },
  methods: {
    handlePersonnelChange(personnelData, code) {
      // 处理被监管人员选择
      this.formData.prison = personnelData

      // 可以在这里实现联动逻辑
      if (personnelData && this.enableLinkage) {
        this.autoFillCasePersonnel(code)
      }
    },

    handleCasePersonnelChange(casePersonnelData, code) {
      // 处理办案人员选择
      this.formData.casePersonnel = casePersonnelData
    },

    async autoFillCasePersonnel(personnelCode) {
      // 自动复用办案人员信息的联动逻辑
      this.formData.casePersonnelCode = personnelCode
    }
  }
}
</script>
```

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持手动输入和扫码识别
- 支持办案人员信息展示
- 支持编辑和详情模式
- 支持输入格式验证
- 支持加载状态显示
- 支持刷新和清空功能
