# personnel-selector 人员选择组件

通用的被监管人员选择组件，支持弹框选择和扫码枪自动识别功能。

## 功能特性

- ✅ 弹框选择人员（复用现有 prisonSelect 组件）
- ✅ 扫码枪自动识别（键盘监听）
- ✅ 人员信息展示（包含照片、基本信息、案件信息）
- ✅ 统一的样式设计
- ✅ 响应式布局
- ✅ 支持 v-model 双向绑定
- ✅ 统一宽度设计（所有卡片自动保持一致宽度）
- ✅ 支持编辑和详情模式（详情模式仅展示信息，不支持选择和扫码）

## 使用方式

### 测试页面
人员选择组件测试页面： [http://localhost:8899/#/personnel-selector-test](http://localhost:8899/#/personnel-selector-test)

### 基础用法（编辑模式）

```vue
<template>
  <div>
    <personnel-selector
      v-model="formData.jgrybm"
      @change="handlePersonnelChange"
    />
  </div>
</template>

<script>
import personnelSelector from '@/components/personnel-selector'

export default {
  components: {
    personnelSelector
  },
  data() {
    return {
      formData: {
        jgrybm: '', // 人员编码
        prison: {}  // 人员信息对象
      }
    }
  },
  methods: {
    handlePersonnelChange(personnelData, jgrybm) {
      // personnelData: 完整的人员信息对象
      // jgrybm: 人员编码
      this.formData.prison = personnelData
      console.log('选择的人员:', personnelData)
    }
  }
}
</script>
```

### 详情模式用法

```vue
<template>
  <div>
    <!-- 详情模式：只展示人员信息，不支持选择和扫码 -->
    <personnel-selector
      v-model="personnelCode"
      mode="detail"
      title="人员详情"
      :show-case-info="true"
    />
  </div>
</template>

<script>
import personnelSelector from '@/components/personnel-selector'

export default {
  components: {
    personnelSelector
  },
  data() {
    return {
      personnelCode: 'USER123456' // 直接传入人员编码，组件会自动获取信息
    }
  }
}
</script>
```

### 高级用法（编辑模式）

```vue
<template>
  <div>
    <personnel-selector
      v-model="formData.jgrybm"
      mode="edit"
      title="选择被监管人员"
      placeholder="点击选择或扫码识别人员"
      personnel-type="ZS"
      :show-case-info="true"
      :enable-scan="true"
      :show-scan-tip="true"
      @change="handlePersonnelChange"
    />
  </div>
</template>
```

### 模式对比

```vue
<template>
  <div>
    <!-- 编辑模式：支持选择、扫码、重新选择 -->
    <div class="mode-section">
      <h3>编辑模式</h3>
      <personnel-selector
        v-model="editPersonnelCode"
        mode="edit"
        title="选择被监管人员"
        :show-case-info="true"
        @change="handleEditChange"
      />
    </div>

    <!-- 详情模式：仅展示信息，不支持交互 -->
    <div class="mode-section">
      <h3>详情模式</h3>
      <personnel-selector
        v-model="detailPersonnelCode"
        mode="detail"
        title="人员详情信息"
        :show-case-info="true"
      />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      editPersonnelCode: '',
      detailPersonnelCode: 'USER123456' // 预设人员编码
    }
  },
  methods: {
    handleEditChange(personnelData, jgrybm) {
      console.log('编辑模式选择的人员:', personnelData)
    }
  }
}
</script>

<style>
.mode-section {
  margin-bottom: 32px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}
</style>
```

### 统一宽度功能使用

统一宽度功能是自动启用的，无需额外配置。以下是一些使用场景：

#### 场景1：多个组件并排使用
```vue
<template>
  <div class="personnel-comparison">
    <div class="personnel-item">
      <h3>人员A</h3>
      <personnel-selector v-model="personnelA" />
    </div>
    <div class="personnel-item">
      <h3>人员B</h3>
      <personnel-selector v-model="personnelB" />
    </div>
  </div>
</template>

<style>
.personnel-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}
</style>
```

#### 场景2：测试不同内容长度
```vue
<template>
  <div>
    <!-- 短证件号码 -->
    <personnel-selector
      v-model="shortId"
      title="短证件号码人员"
    />

    <!-- 长证件号码 -->
    <personnel-selector
      v-model="longId"
      title="长证件号码人员"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      shortId: 'SHORT123',      // 短证件号码
      longId: 'VERY_LONG_ID_NUMBER_123456789012345678' // 长证件号码
    }
  }
}
</script>
```

#### 场景3：动态内容测试
```vue
<template>
  <div>
    <personnel-selector v-model="testPersonnel" />

    <div class="test-buttons">
      <Button @click="setShortContent">设置短内容</Button>
      <Button @click="setLongContent">设置长内容</Button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      testPersonnel: ''
    }
  },
  methods: {
    setShortContent() {
      this.testPersonnel = 'SHORT_ID'
    },
    setLongContent() {
      this.testPersonnel = 'EXTREMELY_LONG_PERSONNEL_ID_FOR_TESTING_WIDTH_CONSISTENCY'
    }
  }
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value (v-model) | String | '' | 人员编码，支持双向绑定 |
| mode | String | 'edit' | 组件模式：'edit'-编辑模式（支持选择、扫码），'detail'-详情模式（仅展示） |
| title | String | '被监管人员' | 组件标题 |
| placeholder | String | '点击选择在押人员' | 占位符文本（仅编辑模式有效） |
| personnelType | String | 'ZS' | 人员类型过滤，ZS-在所，ALL-全部 |
| isMultiple | Boolean | false | 是否多选（暂不支持） |
| showCaseInfo | Boolean | true | 是否显示案件信息 |
| enableScan | Boolean | true | 是否启用扫码功能（仅编辑模式有效） |
| showScanTip | Boolean | true | 是否显示扫码提示（仅编辑模式有效） |
| scanPrefix | String | 'bar:' | 扫码标识符前缀，用于区分扫码和手动输入（可选，仅编辑模式有效） |
| timeThreshold | Number | 50 | 扫码时间阈值(ms)，用于区分扫码枪快速输入和人工慢速输入（仅编辑模式有效） |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | (personnelData, jgrybm) | 人员选择变化时触发 |
| input | jgrybm | v-model 双向绑定事件 |

## 模式说明

### 编辑模式 (mode="edit")
- 支持点击选择人员
- 支持扫码枪自动识别
- 显示"重新选择"按钮
- 显示扫码提示信息
- 支持所有交互功能

### 详情模式 (mode="detail")
- 仅展示人员信息
- 不显示选择区域
- 不显示"重新选择"按钮
- 不支持扫码功能
- 不显示扫码提示
- 适用于只读展示场景

## 扫码功能说明（仅编辑模式）

组件在编辑模式下会自动监听键盘输入，当检测到扫码枪输入时：

1. 自动识别条形码中的人员编码
2. 调用人员信息获取接口
3. 展示人员详细信息
4. 触发 change 事件

### 智能扫码识别逻辑

采用基于输入时间间隔和回车键的智能判断方案：

#### 核心原理
- **时间阈值判断**：扫码枪输入间隔通常 < 50ms，人工输入间隔 > 50ms
- **回车键结束**：扫码枪通常在输入完成后自动发送回车键
- **全局监听**：监听全局键盘事件，无需聚焦特定输入框
- **用户无感知**：不影响用户在任何文本框中的正常输入

#### 识别流程
1. 监听全局 `keydown` 事件
2. 计算按键时间间隔，超过阈值则重置扫码缓存
3. 累积快速输入的字符到扫码缓存
4. 检测到回车键且缓存不为空时，判定为扫码完成
5. 验证扫码内容格式（可选的标识符检查）
6. 自动调用人员信息接口

### 智能扫码配置

#### 1. 基础使用（推荐）
```vue
<!-- 使用智能时间判断，无需额外配置 -->
<personnel-selector
  v-model="personnelCode"
  title="被监管人员"
/>
```

#### 2. 自定义配置
```vue
<!-- 自定义时间阈值和标识符 -->
<personnel-selector
  v-model="personnelCode"
  :time-threshold="30"
  scan-prefix="custom:"
  title="被监管人员"
/>
```

#### 3. 禁用标识符检查
```vue
<!-- 不使用标识符，纯粹基于时间判断 -->
<personnel-selector
  v-model="personnelCode"
  scan-prefix=""
  title="被监管人员"
/>
```

#### 4. 条形码配置（可选）
如果使用标识符，在生成条形码时添加前缀：
```
原始人员编码：USER123456
条形码内容：bar:USER123456
```

#### 5. 工作流程
1. 扫码枪快速连续输入字符（间隔 < 50ms）
2. 扫码枪发送回车键表示输入完成
3. 组件检测到快速输入模式，识别为扫码
4. 验证标识符格式（如果启用）
5. 提取人员编码并调用接口
6. 人工慢速输入（间隔 > 50ms）被自动忽略

#### 6. 优势特点
- **用户无感知**：不影响表单中的正常输入
- **兼容性好**：适用于绝大多数模拟键盘的扫码枪
- **纯软件实现**：无需对扫码枪进行特殊配置
- **智能识别**：自动区分扫码和人工输入

## 统一宽度功能

### 功能概述

人员选择组件现在支持统一宽度功能，确保所有卡片（人员卡片、案件卡片、以及未来可能添加的其他卡片）都保持一致的宽度，提升整体视觉效果。

### 技术实现

#### 1. 统一容器设计

使用 `.unified-cards-container` 作为所有卡片的容器：

```html
<div class="unified-cards-container">
  <div class="personnel-card">...</div>
  <div class="case-card">...</div>
  <!-- 未来的 tag-card 等 -->
</div>
```

#### 2. CSS 样式实现

```less
.unified-cards-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  min-width: fit-content;

  // 所有卡片都使用统一的宽度
  .personnel-card,
  .case-card {
    width: 100%;
    min-width: 370px; // 设置合理的最小宽度
    max-width: 100%;
  }
}
```

#### 3. 自适应宽度机制

- **容器宽度**: 使用 `min-width: fit-content` 让容器根据内容自动调整
- **卡片宽度**: 所有卡片使用 `width: 100%` 填满容器
- **最小宽度**: 设置 `min-width: 370px` 确保卡片不会过小
- **响应式**: 移动端自动调整为 `min-width: auto`

### 主要特性

#### ✅ 统一宽度
- 所有卡片自动保持相同宽度
- 无论内容长短，宽度都保持一致
- 整体视觉效果更加美观

#### ✅ 自适应调整
- 根据最长内容自动调整容器宽度
- 所有卡片同步调整到相同宽度
- 避免宽度不一致的视觉问题

#### ✅ 响应式设计
- 桌面端保持统一宽度
- 移动端自动适应屏幕宽度
- 不同屏幕尺寸都有良好表现

#### ✅ 扩展性
- 支持未来添加更多卡片类型
- 新卡片自动继承统一宽度特性
- 无需额外配置

### 测试验证

#### 测试场景
- ✅ 短证件号码 vs 长证件号码
- ✅ 短监室名称 vs 长监室名称
- ✅ 简单案件信息 vs 复杂案件信息
- ✅ 显示案件信息 vs 不显示案件信息
- ✅ 桌面端 vs 移动端响应式

#### 测试页面
- 基础测试：`/src/components/personnel-selector/test.vue`
- 宽度演示：`/src/components/personnel-selector/width-demo.vue`

### 兼容性

- ✅ 向后兼容：不影响现有功能
- ✅ 样式兼容：不破坏现有样式
- ✅ 功能兼容：所有原有功能正常工作
- ✅ 响应式兼容：移动端表现良好

## 样式说明

组件使用独立的样式文件 `index.less`，样式参考提讯模块：

- 选择区域样式与提讯模块保持一致
- 人员信息展示布局一致
- 支持响应式设计
- 统一的颜色和字体规范
- **新增统一宽度容器样式**

## 依赖组件

- `prisonSelect`: 人员选择弹窗组件（来自 sd-prison-select）
- `iView`: UI 组件库

## API 接口

组件依赖以下接口：

- `/acp-com/base/pm/prisoner/getPrisonerSelectCompomenOne`: 获取单个人员详细信息

## 注意事项

1. 确保项目中已安装 `sd-prison-select` 组件
2. 扫码功能需要扫码枪支持键盘输入模式（仅编辑模式）
3. 人员编码格式需要与后端接口保持一致
4. 组件会自动处理 null 值显示为 '-'
5. 组件名称采用驼峰命名：`personnelSelector`
6. 详情模式下传入的人员编码必须有效，组件会自动获取人员信息
7. 详情模式适用于展示已知人员信息的场景，如详情页、审批流程等

## 文件结构

```
src/components/personnel-selector/
├── index.vue                # 主组件文件（支持编辑和详情模式）
├── index.less               # 样式文件（包含统一宽度样式）
├── test.vue                 # 测试页面（包含详情模式测试）
├── detail-mode-test.vue     # 详情模式专项测试页面
├── detail-mode-example.vue  # 详情模式使用示例页面
├── width-demo.vue           # 宽度演示页面
├── form-test.vue            # 表单冲突测试页面
├── scan-prefix-example.vue  # 扫码标识符使用示例
├── simple-test.vue          # 简单功能测试页面
├── example.vue              # 基础使用示例
└── BUGFIX.md                # Bug修复说明文档
```

## 更新日志

### v1.3.0 - 详情模式支持
- ✅ **新增详情模式**：支持 mode 属性，可设置为 'edit' 或 'detail'
- ✅ **详情模式特性**：仅展示人员信息，不支持选择和扫码功能
- ✅ **智能交互控制**：详情模式下自动隐藏重新选择按钮和扫码提示
- ✅ **使用场景扩展**：适用于详情页、审批流程等只读展示场景
- ✅ **向后兼容**：默认为编辑模式，不影响现有功能
- 📚 **文档完善**：新增详情模式使用说明和对比示例

### v1.0.0
- 初始版本
- 支持弹框选择和扫码识别
- 完整的人员信息展示
- 响应式设计
- 参考提讯模块样式规范

### v1.1.0 - 统一宽度功能
- ✅ **新增统一宽度功能**：所有卡片自动保持一致宽度
- ✅ **自适应宽度机制**：根据最长内容自动调整容器宽度
- ✅ **响应式优化**：桌面端统一宽度，移动端自适应屏幕
- ✅ **扩展性支持**：为未来的 tag-card 等新卡片预留扩展能力
- ✅ **测试页面完善**：添加统一宽度测试场景和演示页面
- ✅ **向后兼容**：不影响现有功能和样式
- 🔧 **技术实现**：使用 `.unified-cards-container` 统一管理卡片宽度
- 📚 **文档更新**：完善统一宽度功能说明和使用指南

#### 统一宽度功能详情
- **核心特性**：personnel-card、case-card 等所有卡片宽度完全统一
- **自动调整**：证件号码长短不同时，所有卡片宽度自动保持一致
- **视觉优化**：整体布局更加美观和专业
- **技术架构**：使用 CSS Flexbox + fit-content 实现智能宽度计算
- **最小宽度**：设置 420px 最小宽度确保内容显示完整
- **移动适配**：小屏幕设备自动调整为 auto 宽度

### v1.1.1 - 扫码功能优化
- 🐛 **修复表单冲突问题**：解决扫码功能干扰表单输入的bug
- ✅ **智能输入检测**：自动识别扫码枪输入和手动输入
- ✅ **表单兼容模式**：新增 enableGlobalKeyListener 属性
- ✅ **输入源过滤**：自动跳过表单元素的键盘事件
- ✅ **扫码内容验证**：多重检查确保扫码内容有效性
- 🧪 **测试页面**：添加表单冲突测试页面 form-test.vue
- 📚 **文档完善**：更新扫码功能说明和使用指南

### v1.1.2 - 扫码标识符方案
- 🎯 **扫码标识符机制**：采用标识符彻底解决表单冲突问题
- ✅ **新增 scanSuffix 属性**：默认标识符 `:RS2025`，支持自定义
- ✅ **完美表单兼容**：只有包含标识符的输入才被识别为扫码
- ✅ **简化逻辑**：移除复杂的检测机制，使用更优雅的解决方案
- 🔧 **条形码格式**：`人员编码 + 标识符`，如 `USER123456:RS2025`
- 🧪 **测试优化**：更新测试页面，添加标识符测试功能
- 📚 **文档更新**：完善扫码标识符使用说明

### v1.2.0 - 智能扫码方案
- 🚀 **智能时间判断**：基于输入时间间隔和回车键的智能识别
- ✅ **用户无感知**：不影响用户在任何文本框中的正常输入
- ✅ **纯软件实现**：无需对扫码枪进行特殊配置
- ✅ **兼容性极佳**：适用于绝大多数模拟键盘的扫码枪
- 🔧 **时间阈值**：默认50ms，扫码枪快速输入 vs 人工慢速输入
- 🎯 **回车键结束**：扫码枪自动发送回车键表示输入完成
- 🧪 **智能测试器**：新增智能扫码测试页面，可模拟不同输入模式
- 📚 **文档完善**：详细说明智能扫码原理和配置方法

### v1.2.1 - 条形码格式优化
- 🔧 **标识符调整**：将 scanSuffix 改为 scanPrefix，使用前缀标识符
- ✅ **新格式**：条形码格式从 `人员编码:RS2025` 改为 `bar:人员编码`
- 🎯 **更直观**：前缀格式更符合条形码扫描的直觉习惯
- 📚 **文档同步**：更新所有相关文档和示例代码

#### 智能扫码方案详情
- **核心优势**：完全解决表单输入冲突，用户体验最佳
- **技术原理**：监听全局键盘事件，通过时间间隔智能判断输入类型
- **阈值机制**：扫码枪输入间隔 < 50ms，人工输入间隔 > 50ms
- **标识符可选**：支持额外的标识符验证，提供双重保障
- **参数可调**：时间阈值可自定义，适应不同硬件环境

### v1.2.0
- 增加人员标签卡片
- 扩展被监管人员的管教信息（主协管民警）
- 增加人员风险标识
- 增加人员总台跳转
- 增加一人一档跳转
- 每个信息卡片支持收缩
- 支持编辑和详情模式