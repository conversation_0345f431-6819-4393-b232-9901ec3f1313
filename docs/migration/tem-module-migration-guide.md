# TEM模块迁移技术文档

## 📋 迁移概述

本文档记录了rs-acp-web项目的最新迁移变更，包括TEM模块集成、视频通话功能、表单组件扩展等重要功能的合并过程。

## 🎯 迁移策略

- **基础原则**: 保持主项目架构稳定，模块化集成新功能
- **合并方式**: 采用模块化路径和路由管理，避免冲突
- **兼容性**: 确保现有IHC模块功能不受影响

## 📊 变更文件清单

### 🆕 新增文件（已暂存）

#### 视频播放器模块
| 文件路径 | 文件类型 | 功能描述 |
|---------|---------|---------|
| `public/adapter-7.4.0.min.js` | 第三方库 | WebRTC适配器 |
| `public/srs.sdk.js` | SDK | SRS流媒体SDK |
| `src/components/VideoPlayer/index.vue` | Vue组件 | 视频播放器组件 |

#### 表单组件扩展
| 文件路径 | 文件类型 | 功能描述 |
|---------|---------|---------|
| `src/components/bl-form/TreeSelectOption.vue` | Vue组件 | 树形选择器选项 |
| `src/components/bl-form/addTem.vue` | Vue组件 | 添加模板组件 |
| `src/components/bl-form/addTemContent.vue` | Vue组件 | 模板内容组件 |
| `src/components/bl-form/el-select-tree.vue` | Vue组件 | Element树形选择器 |
| `src/components/bl-form/jqdt.vue` | Vue组件 | 监区动态组件 |
| `src/components/bl-form/step.vue` | Vue组件 | 步骤组件 |
| `src/components/bl-form/thjy.vue` | Vue组件 | 谈话记要组件 |
| `src/components/bl-form/thmbyy.vue` | Vue组件 | 谈话目标原因组件 |

#### TEM模块配置
| 文件路径 | 文件类型 | 功能描述 |
|---------|---------|---------|
| `src/path/tem/base.js` | 配置文件 | TEM模块基础路径配置 |
| `src/path/tem/bsp-setting.js` | 配置文件 | BSP设置路径配置 |
| `src/path/tem/bsp-talk.js` | 配置文件 | BSP通话路径配置 |
| `src/path/tem/bsp-uac.js` | 配置文件 | BSP用户访问控制路径 |
| `src/path/tem/path.js` | 配置文件 | TEM模块路径汇总 |

#### 路由配置
| 文件路径 | 文件类型 | 功能描述 |
|---------|---------|---------|
| `src/router/tem/router_talk.js` | 路由配置 | 通话相关路由 |
| `src/router/tem/routers.js` | 路由配置 | TEM模块路由汇总 |

#### 业务功能模块
| 文件路径 | 文件类型 | 功能描述 |
|---------|---------|---------|
| `src/view/groupTalk/detail.vue` | Vue页面 | 群组通话详情页 |
| `src/view/groupTalk/index.vue` | Vue页面 | 群组通话主页 |
| `src/view/groupTalk/jls.vue` | Vue页面 | 通话记录页 |
| `src/view/groupTalk/tempModal.vue` | Vue组件 | 临时模态框 |
| `src/view/groupTalk/ywtz.vue` | Vue页面 | 业务通知页 |
| `src/view/intalk/business.vue` | Vue页面 | 内部通话业务页 |
| `src/view/intalk/index.vue` | Vue页面 | 内部通话主页 |
| `src/view/jcpz/index.vue` | Vue页面 | 基础配置主页 |
| `src/view/jcpz/thrybq.vue` | Vue页面 | 通话人员标签配置 |
| `src/view/jcpz/thtwzs.vue` | Vue页面 | 通话图文展示配置 |
| `src/view/jcpz/zdpz.vue` | Vue页面 | 自动配置页 |

#### 内部通话组件
| 文件路径 | 文件类型 | 功能描述 |
|---------|---------|---------|
| `src/view/intalk/component/PersonInfoCard.vue` | Vue组件 | 人员信息卡片 |
| `src/view/intalk/component/TalkRecordCard.vue` | Vue组件 | 通话记录卡片 |
| `src/view/intalk/component/intalk-detail.vue` | Vue组件 | 内部通话详情 |
| `src/view/intalk/component/ryxx.vue` | Vue组件 | 人员信息组件 |
| `src/view/intalk/component/talkDetailOptimized.vue` | Vue组件 | 优化的通话详情 |
| `src/view/intalk/component/talkLedgerDetails..vue` | Vue组件 | 通话台账详情 |
| `src/view/intalk/component/yryd.vue` | Vue组件 | 语音阅读组件 |

#### 工具和资源文件
| 文件路径 | 文件类型 | 功能描述 |
|---------|---------|---------|
| `src/util/package.json` | 配置文件 | 工具模块依赖配置 |
| `src/util/sip.js` | 工具文件 | SIP通信工具 |
| `src/util/validate.js` | 工具文件 | 验证工具函数 |
| `src/components/index.js` | 配置文件 | 组件导出配置 |

#### 图片资源
| 文件路径 | 文件类型 | 功能描述 |
|---------|---------|---------|
| `src/assets/icon/fanhui.svg` | 图标 | 返回图标 |
| `src/assets/images/doing bf.png` | 图片 | 进行中背景图 |
| `src/assets/images/doing01.png` | 图片 | 进行中状态图 |
| `src/assets/images/fangda.png` | 图片 | 放大图标 |
| `src/assets/images/jh.png` | 图片 | 监狱图标 |
| `src/assets/images/left.svg` | 图标 | 左箭头图标 |
| `src/assets/images/logoJh.jpg` | 图片 | 监狱Logo JPG |
| `src/assets/images/logoJh.png` | 图片 | 监狱Logo PNG |
| `src/assets/images/no-people.png` | 图片 | 无人员图标 |
| `src/assets/images/right (5).png` | 图片 | 右箭头图标5 |
| `src/assets/images/right (7).png` | 图片 | 右箭头图标7 |
| `src/assets/images/suoxiao.png` | 图片 | 缩小图标 |
| `src/assets/images/xiangzuoshuangjiantou_1.svg` | 图标 | 向左双箭头图标 |
| `src/assets/images/yuanxingxiangzuoshuangjiantou.svg` | 图标 | 圆形向左双箭头 |

### 🔄 修改文件（未暂存）

| 文件路径 | 修改类型 | 预期变更内容 |
|---------|---------|-------------|
| `package.json` | 依赖更新 | 新增TEM模块相关依赖 |
| `src/main.js` | 应用配置 | 集成TEM模块组件和插件 |
| `src/path/path.js` | 路径配置 | 添加TEM模块路径映射 |
| `src/path/tem/path.js` | 路径配置 | TEM模块路径配置更新 |
| `src/router/index.js` | 路由配置 | 集成TEM模块路由 |
| `src/router/tem/routers.js` | 路由配置 | TEM路由配置更新 |
| `src/util/index.js` | 工具函数 | 新增工具函数支持 |
| `src/view/groupTalk/detail.vue` | 页面组件 | 群组通话详情页优化 |

## 🔧 详细迁移过程

### 1. TEM模块集成

#### 1.1 路径配置集成
- **位置**: `src/path/tem/`
- **策略**: 采用模块化路径管理，避免与现有IHC模块冲突
- **配置文件**:
  - `base.js`: 基础路径定义
  - `bsp-*.js`: 各业务模块路径配置
  - `path.js`: 路径汇总导出

#### 1.2 路由配置集成
- **位置**: `src/router/tem/`
- **策略**: 独立路由模块，通过主路由文件统一管理
- **配置文件**:
  - `router_talk.js`: 通话相关路由
  - `routers.js`: TEM模块路由汇总

### 2. 视频通话功能集成

#### 2.1 核心组件
- **VideoPlayer组件**: 统一的视频播放器组件
- **SRS SDK**: 流媒体处理SDK集成
- **WebRTC适配器**: 跨浏览器兼容性支持

#### 2.2 业务模块
- **群组通话**: 支持多人视频通话功能
- **内部通话**: 内部人员通信功能
- **通话记录**: 完整的通话历史管理

### 3. 表单组件扩展

#### 3.1 bl-form组件系列
- **树形选择器**: 支持层级数据选择
- **步骤组件**: 流程化表单支持
- **模板组件**: 动态表单模板功能
- **专业组件**: 监区动态、谈话记要等业务组件

## 🏗️ 架构设计

### 模块化架构
```
src/
├── path/
│   ├── ihc/          # IHC模块路径配置
│   ├── tem/          # TEM模块路径配置
│   └── path.js       # 统一路径管理
├── router/
│   ├── ihc/          # IHC模块路由
│   ├── tem/          # TEM模块路由
│   └── index.js      # 统一路由管理
├── components/
│   ├── bl-form/      # 表单组件库
│   ├── VideoPlayer/  # 视频播放器
│   └── ...
└── view/
    ├── groupTalk/    # 群组通话
    ├── intalk/       # 内部通话
    ├── jcpz/         # 基础配置
    └── ...
```

### 配置管理策略
1. **路径配置**: 模块化管理，避免冲突
2. **路由配置**: 独立模块，统一注册
3. **组件注册**: 全局注册关键组件
4. **依赖管理**: 按需加载，优化性能

## ✅ 迁移验证清单

### 功能完整性
- [ ] TEM模块路径配置正确
- [ ] TEM模块路由注册成功
- [ ] 视频通话功能正常
- [ ] 表单组件库可用
- [ ] 图片资源加载正常

### 兼容性保证
- [ ] 现有IHC模块功能不受影响
- [ ] 主项目原有功能保持稳定
- [ ] 新旧组件共存无冲突

### 性能优化
- [ ] 按需加载配置正确
- [ ] 静态资源优化
- [ ] 路由懒加载实现

## 🚀 部署注意事项

### 环境配置
1. **代理配置**: vue.config.js中已配置tem-com代理
2. **依赖安装**: 确保package.json中的新依赖已安装
3. **构建配置**: 检查webpack配置是否支持新模块

### 测试建议
1. **功能测试**: 逐一验证新增功能模块
2. **兼容性测试**: 确保现有功能不受影响
3. **性能测试**: 验证加载性能和运行效率

## 🔍 关键技术决策

### 1. 模块化架构选择
- **决策**: 采用独立模块目录结构（ihc/, tem/）
- **原因**: 避免不同项目间的路径和路由冲突
- **优势**: 便于维护、扩展和版本管理

### 2. 路径配置策略
- **决策**: 每个模块独立管理路径配置
- **实现**: base.js定义基础路径，各业务模块独立配置
- **好处**: 模块间解耦，便于独立开发和测试

### 3. 组件注册方式
- **决策**: 关键组件全局注册，业务组件按需引入
- **考虑**: 平衡性能和开发便利性
- **实现**: components/index.js统一管理组件导出

## 📊 迁移效果量化

### 代码量统计
- **新增文件**: 52个
- **修改文件**: 8个
- **新增代码行数**: 预估3000+行
- **涉及模块**: 视频通话、表单组件、配置管理、工具函数

### 功能能力提升
| 功能类别 | 迁移前 | 迁移后 | 提升描述 |
|---------|--------|--------|---------|
| 通话功能 | 基础通话 | 群组+内部通话 | 支持多人视频通话 |
| 表单组件 | 标准组件 | bl-form组件库 | 专业表单组件库 |
| 视频播放 | 基础播放 | VideoPlayer组件 | 统一视频播放器 |
| 配置管理 | 单一配置 | 模块化配置 | TEM+IHC双模块支持 |
| 工具函数 | 基础工具 | 扩展工具库 | SIP通信、验证等 |

### 技术栈更新
- **新增SDK**: SRS流媒体SDK
- **新增适配器**: WebRTC适配器
- **新增通信**: SIP协议支持
- **新增组件**: Element树形选择器等

## 🛠️ 具体操作指南

### 提交变更操作
```bash
# 1. 查看当前状态
git status

# 2. 添加修改的文件到暂存区
git add src/main.js
git add src/path/path.js
git add src/path/tem/path.js
git add src/router/index.js
git add src/router/tem/routers.js
git add src/util/index.js
git add src/view/groupTalk/detail.vue
git add package.json

# 3. 提交所有变更
git commit -m "feat: 集成TEM模块和视频通话功能

- 新增TEM模块路径和路由配置
- 集成视频播放器和通话功能
- 扩展bl-form表单组件库
- 添加SIP通信和验证工具
- 优化模块化架构设计"

# 4. 推送到远程仓库
git push origin dev_0512
```

### 环境配置检查
```bash
# 1. 检查Node.js版本
node --version

# 2. 安装新依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 构建生产版本
npm run build
```

### 功能测试步骤
1. **视频通话测试**
   - 访问群组通话页面
   - 测试视频播放器功能
   - 验证通话记录功能

2. **表单组件测试**
   - 测试树形选择器
   - 验证步骤组件
   - 检查模板组件功能

3. **配置管理测试**
   - 验证TEM模块路径配置
   - 检查路由跳转功能
   - 测试基础配置页面

## 📋 问题排查指南

### 常见问题及解决方案

#### 1. 路径配置冲突
**问题**: TEM模块路径与现有路径冲突
**解决**: 检查base.js中的路径定义，确保唯一性
```javascript
// src/path/tem/base.js
export const bspTalkRoot = '/tem-com'  // 确保路径唯一
```

#### 2. 组件注册失败
**问题**: 新组件无法正常使用
**解决**: 检查components/index.js中的组件导出
```javascript
// src/components/index.js
export { default as VideoPlayer } from './VideoPlayer'
```

#### 3. 路由跳转异常
**问题**: TEM模块页面无法访问
**解决**: 检查router/index.js中的路由注册
```javascript
// src/router/index.js
import temRouters from './tem/routers'
// 确保temRouters已正确导入和注册
```

#### 4. 依赖加载错误
**问题**: 新增依赖无法加载
**解决**: 检查package.json并重新安装
```bash
rm -rf node_modules
npm install
```

## 📝 后续工作

### 待完成任务
1. **代码提交**: 提交未暂存的修改文件
2. **测试完善**: 编写单元测试和集成测试
3. **文档更新**: 更新API文档和用户手册
4. **性能优化**: 代码分割和懒加载优化
5. **安全检查**: 代码安全审计和漏洞扫描

### 维护建议
1. **定期更新**: 保持依赖版本最新
2. **监控告警**: 设置性能和错误监控
3. **用户反馈**: 建立反馈收集机制
4. **持续集成**: 完善CI/CD流程
5. **代码审查**: 建立代码审查机制

### 扩展计划
1. **功能扩展**: 根据业务需求扩展新功能
2. **性能优化**: 持续优化加载和运行性能
3. **用户体验**: 改进界面和交互体验
4. **技术升级**: 跟进Vue.js和相关技术栈更新

---

**文档版本**: v1.0
**创建时间**: 2025-06-28
**最后更新**: 2025-07-02
**维护人员**: 北京监管项目前端开发团队
**审核状态**: 待审核
