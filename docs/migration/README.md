# 迁移文档目录

本目录包含rs-acp-web项目的迁移相关文档，记录了项目模块集成、功能扩展等重要变更过程。

## 📁 目录结构

```
docs/migration/
├── README.md                           # 本说明文档
├── tem-module-migration-checklist.md  # TEM模块迁移执行清单
└── tem-module-migration-guide.md      # TEM模块迁移技术文档
```

## 📋 文档说明

### tem-module-migration-checklist.md
**TEM模块迁移执行清单**
- 迁移目标和策略
- 详细执行步骤
- 风险点检查清单
- 问题排查指南
- 完成度追踪

### tem-module-migration-guide.md
**TEM模块迁移技术文档**
- 迁移概述和策略
- 详细变更文件清单
- 架构设计说明
- 技术决策记录
- 操作指南和测试步骤

## 🎯 使用指南

### 对于开发人员
1. 首先阅读 `tem-module-migration-guide.md` 了解技术细节
2. 参考 `tem-module-migration-checklist.md` 执行迁移步骤
3. 按照清单进行功能验证和测试

### 对于项目管理人员
1. 查看 `tem-module-migration-checklist.md` 了解项目进度
2. 关注风险点检查和完成度追踪
3. 协调相关团队完成迁移任务

### 对于测试人员
1. 参考迁移文档了解新增功能
2. 按照验证清单进行功能测试
3. 重点关注兼容性和性能测试

## 📝 文档维护

### 更新原则
- 迁移过程中的重要变更需及时更新文档
- 遇到新问题时补充到问题排查指南
- 完成迁移后更新完成度状态

### 版本管理
- 文档版本号与项目版本保持一致
- 重大变更时创建新版本文档
- 保留历史版本以供参考

---

**目录创建时间**: 2025-07-03  
**维护团队**: 北京监管项目前端开发团队  
