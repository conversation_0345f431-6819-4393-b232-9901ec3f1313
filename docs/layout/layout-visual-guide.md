# 布局样式系统可视化使用指南

## 📋 系统概述

基于变量驱动的布局样式系统，提供9种常用布局类型，完全替代原有的 `Inquiry-wrap`。

## 🎯 布局类型图解

### 1. 管理页面布局 (bsp-management-layout)

```
┌─────────────────────────────────────────────────────────────┐
│                    管理页面布局                              │
├─────────────────┬───────────────────────────────────────────┤
│                 │                                           │
│   查询条件区域   │              数据表格区域                  │
│                 │                                           │
│  ┌───────────┐  │  ┌─────────────────────────────────────┐  │
│  │ 姓名输入框 │  │  │          表格标题栏                  │  │
│  └───────────┘  │  ├─────────────────────────────────────┤  │
│  ┌───────────┐  │  │ 姓名 │ 年龄 │ 监区 │ 状态 │ 操作    │  │
│  │ 状态选择  │  │  ├─────────────────────────────────────┤  │
│  └───────────┘  │  │ 张三 │ 35  │ 一区 │ 健康 │ 编辑    │  │
│  ┌───────────┐  │  │ 李四 │ 42  │ 二区 │ 患病 │ 编辑    │  │
│  │ 查询按钮  │  │  │ 王五 │ 28  │ 一区 │ 健康 │ 编辑    │  │
│  └───────────┘  │  └─────────────────────────────────────┘  │
│                 │                                           │
│     340px       │                 自适应                     │
└─────────────────┴───────────────────────────────────────────┘
```

**使用代码：**
```vue
<div class="bsp-management-layout">
  <div class="bsp-management-layout-left">查询条件</div>
  <div class="bsp-management-layout-right">数据表格</div>
</div>
```

### 2. 人员选择布局 (bsp-selection-layout)

```
┌─────────────────────────────────────────────────────────────┐
│                    人员选择布局                              │
├─────────────────────┬───────────────────────────────────────┤
│                     │                                       │
│    可选人员列表      │           已选人员列表                 │
│                     │                                       │
│  ┌───────────────┐  │  ┌─────────────────────────────────┐  │
│  │   搜索框      │  │  │      已选人员 (3)               │  │
│  └───────────────┘  │  ├─────────────────────────────────┤  │
│  ┌───────────────┐  │  │ ☑ 张三    [一监区]        ✕   │  │
│  │ ☐ 张三        │  │  │ ☑ 李四    [二监区]        ✕   │  │
│  │ ☐ 李四        │  │  │ ☑ 王五    [一监区]        ✕   │  │
│  │ ☐ 王五        │  │  └─────────────────────────────────┘  │
│  │ ☐ 赵六        │  │  ┌─────────────────────────────────┐  │
│  │ ☐ 孙七        │  │  │ [清空全部] [确认选择(3)]        │  │
│  └───────────────┘  │  └─────────────────────────────────┘  │
│                     │                                       │
│       400px         │                自适应                  │
└─────────────────────┴───────────────────────────────────────┘
```

**使用代码：**
```vue
<div class="bsp-selection-layout">
  <div class="bsp-selection-layout-left">可选项列表</div>
  <div class="bsp-selection-layout-right">已选项列表</div>
</div>
```

### 3. 详情页面布局 (bsp-detail-layout)

```
┌─────────────────────────────────────────────────────────────┐
│                      详情页面布局                            │
├─────────────────────────────────────────────────────────────┤
│  病情详情 - 张三                    [编辑] [删除] [打印]     │ ← 头部 (60px)
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                   基本信息                          │    │
│  │  姓名：张三    年龄：35岁    监区：一监区            │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                   病情信息                          │    │ ← 内容区域
│  │  疾病：高血压  等级：重病号                         │    │   (自适应)
│  │  描述：患者血压持续偏高，需要定期监测...            │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                   治疗记录                          │    │
│  │  [数据表格显示治疗历史记录]                         │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                [返回列表] [保存修改]                         │ ← 底部 (60px)
└─────────────────────────────────────────────────────────────┘
```

**使用代码：**
```vue
<div class="bsp-detail-layout">
  <div class="bsp-detail-layout-header">标题和操作按钮</div>
  <div class="bsp-detail-layout-content">详情内容</div>
  <div class="bsp-detail-layout-footer">操作按钮</div>
</div>
```

### 4. 搜索页面布局 (bsp-search-layout)

```
┌─────────────────────────────────────────────────────────────┐
│                      搜索页面布局                            │
├─────────────────────────────────────────────────────────────┤
│  [搜索框: 请输入关键词] [状态▼] [监区▼] [搜索] [重置]        │ ← 搜索头部
├─────────────────────┬───────────────────────────────────────┤
│                     │                                       │
│      筛选条件        │            搜索结果                    │
│                     │                                       │
│  ┌───────────────┐  │  找到 25 条结果，关键词："张"          │
│  │    监区       │  │  ┌─────────────────────────────────┐  │
│  │ ☐ 一监区      │  │  │ [头像] 张三  P001  一监区  35岁  │  │
│  │ ☐ 二监区      │  │  │        入所时间：2023-01-15     │  │
│  │ ☐ 三监区      │  │  │                    [查看] [编辑] │  │
│  └───────────────┘  │  └─────────────────────────────────┘  │
│  ┌───────────────┐  │  ┌─────────────────────────────────┐  │
│  │    年龄段     │  │  │ [头像] 张四  P015  二监区  42岁  │  │
│  │ ☐ 18-30      │  │  │        入所时间：2023-02-20     │  │
│  │ ☐ 31-45      │  │  │                    [查看] [编辑] │  │
│  │ ☐ 46-60      │  │  └─────────────────────────────────┘  │
│  └───────────────┘  │  ┌─────────────────────────────────┐  │
│                     │  │         [分页导航]              │  │
│       340px         │  └─────────────────────────────────┘  │
└─────────────────────┴───────────────────────────────────────┘
```

**使用代码：**
```vue
<div class="bsp-search-layout">
  <div class="bsp-search-layout-header">搜索表单</div>
  <div class="bsp-search-layout-content">
    <div class="bsp-search-layout-content-left">筛选条件</div>
    <div class="bsp-search-layout-content-right">搜索结果</div>
  </div>
</div>
```

### 5. 工作台布局 (bsp-dashboard-layout)

```
┌─────────────────────────────────────────────────────────────┐
│                      工作台布局                              │
├─────────────┬───────────────────────────────────────────────┤
│             │                                               │
│   侧边导航   │                 顶部工具栏                     │
│             │                                               │
│ ┌─────────┐ ├───────────────────────────────────────────────┤
│ │ 统计概览 │ │                                               │
│ └─────────┘ │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌──────┐ │
│ ┌─────────┐ │  │ 今日新增 │ │ 总人数  │ │ 重病号  │ │ 出所 │ │
│ │ 数据分析 │ │  │   15   │ │  1,234 │ │   23   │ │  5  │ │
│ └─────────┘ │  └─────────┘ └─────────┘ └─────────┘ └──────┘ │
│ ┌─────────┐ │                                               │
│ │ 系统设置 │ │  ┌─────────────────────────────────────────┐ │
│ └─────────┘ │  │              图表区域                    │ │
│             │  │         [各种统计图表]                   │ │
│             │  └─────────────────────────────────────────┘ │
│    280px    │                   自适应                      │
└─────────────┴───────────────────────────────────────────────┘
```

**使用代码：**
```vue
<div class="bsp-dashboard-layout">
  <div class="bsp-dashboard-layout-sidebar">侧边导航</div>
  <div class="bsp-dashboard-layout-header">顶部工具栏</div>
  <div class="bsp-dashboard-layout-content">主要内容</div>
</div>
```

### 6. 卡片网格布局 (bsp-card-grid-layout)

```
┌─────────────────────────────────────────────────────────────┐
│                    卡片网格布局                              │
├─────────────────────────────────────────────────────────────┤
│  人员列表                           [状态筛选▼] [监区筛选▼]  │ ← 头部
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌────────┐ │
│  │ [头像]      │ │ [头像]      │ │ [头像]      │ │ [头像] │ │
│  │ 张三        │ │ 李四        │ │ 王五        │ │ 赵六   │ │
│  │ 一监区      │ │ 二监区      │ │ 一监区      │ │ 三监区 │ │
│  │ 35岁 健康   │ │ 42岁 患病   │ │ 28岁 健康   │ │ 50岁   │ │
│  │ [查看详情]  │ │ [查看详情]  │ │ [查看详情]  │ │ [查看] │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └────────┘ │
│                                                             │ ← 内容区域
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌────────┐ │   (自适应网格)
│  │ [头像]      │ │ [头像]      │ │ [头像]      │ │ [头像] │ │
│  │ 孙七        │ │ 周八        │ │ 吴九        │ │ 郑十   │ │
│  │ 二监区      │ │ 一监区      │ │ 三监区      │ │ 二监区 │ │
│  │ 33岁 重病号 │ │ 45岁 健康   │ │ 39岁 患病   │ │ 31岁   │ │
│  │ [查看详情]  │ │ [查看详情]  │ │ [查看详情]  │ │ [查看] │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**使用代码：**
```vue
<div class="bsp-card-grid-layout">
  <div class="bsp-card-grid-layout-header">
    <div class="bsp-card-grid-layout-header-title">标题</div>
    <div class="bsp-card-grid-layout-header-filters">筛选条件</div>
  </div>
  <div class="bsp-card-grid-layout-content">
    <div class="bsp-card-grid-layout-content-grid">卡片网格</div>
  </div>
</div>
```

### 7. 分步表单布局 (bsp-wizard-layout)

```
┌─────────────────────────────────────────────────────────────┐
│                    分步表单布局                              │
├─────────────────────────────────────────────────────────────┤
│  ● 基本信息 ——————— ○ 详细信息 ——————— ○ 确认提交           │ ← 步骤条
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                        当前步骤内容                          │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │                   基本信息表单                      │    │
│  │                                                     │    │
│  │  姓名：[_______________]  年龄：[_____]             │    │
│  │                                                     │    │ ← 内容区域
│  │  性别：○ 男  ○ 女        监区：[选择监区▼]         │    │   (自适应)
│  │                                                     │    │
│  │  身份证号：[_________________________]              │    │
│  │                                                     │    │
│  │  联系电话：[_________________________]              │    │
│  │                                                     │    │
│  └─────────────────────────────────────────────────────┘    │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                    [上一步]        [下一步]                  │ ← 操作按钮
└─────────────────────────────────────────────────────────────┘
```

**使用代码：**
```vue
<div class="bsp-wizard-layout">
  <div class="bsp-wizard-layout-steps">步骤条</div>
  <div class="bsp-wizard-layout-content">表单内容</div>
  <div class="bsp-wizard-layout-actions">操作按钮</div>
</div>
```

### 8. 主从表布局 (bsp-master-detail-layout)

```
┌─────────────────────────────────────────────────────────────┐
│                    主从表布局                                │
├─────────────────────────────────────────────────────────────┤
│                        主表 (40%)                           │
│  ┌─────────────────────────────────────────────────────┐    │
│  │ 姓名 │ 年龄 │ 监区   │ 状态   │ 入所时间    │ 操作  │    │
│  ├─────────────────────────────────────────────────────┤    │
│  │ 张三 │ 35  │ 一监区 │ 健康   │ 2023-01-15 │ 查看  │ ←  │
│  │ 李四 │ 42  │ 二监区 │ 患病   │ 2023-02-20 │ 查看  │    │
│  │ 王五 │ 28  │ 一监区 │ 健康   │ 2023-03-10 │ 查看  │    │
│  └─────────────────────────────────────────────────────┘    │
├─────────────────────────────────────────────────────────────┤
│                        从表 (60%)                           │
│  ┌─ 张三的详细信息 ─┬─ 病历记录 ─┬─ 家属信息 ─┐              │
│  │                  │            │            │              │
│  │  基本信息：       │ 2024-01-15 │ 姓名：张XX │              │
│  │  姓名：张三       │ 体检正常   │ 关系：父亲 │              │
│  │  年龄：35岁       │            │ 电话：139XX│              │
│  │  监区：一监区     │ 2024-01-10 │            │              │
│  │  状态：健康       │ 感冒治疗   │ 姓名：李XX │              │
│  │                  │            │ 关系：配偶 │              │
│  └──────────────────┴────────────┴────────────┘              │
└─────────────────────────────────────────────────────────────┘
```

**使用代码：**
```vue
<div class="bsp-master-detail-layout">
  <div class="bsp-master-detail-layout-master">主表</div>
  <div class="bsp-master-detail-layout-detail">从表</div>
</div>
```

### 9. 聊天界面布局 (bsp-chat-layout)

```
┌─────────────────────────────────────────────────────────────┐
│                      聊天界面布局                            │
├─────────────────┬───────────────────────────────────────────┤
│                 │                                           │
│   会话列表       │              聊天主界面                    │
│                 │                                           │
│ ┌─────────────┐ │ ┌─────────────────────────────────────┐   │
│ │ [搜索框]    │ │ │ 与 张三 的对话              [设置] │   │ ← 头部
│ └─────────────┘ │ └─────────────────────────────────────┘   │
│ ┌─────────────┐ │ ┌─────────────────────────────────────┐   │
│ │ ● 张三      │ │ │                                     │   │
│ │   你好...   │ │ │  张三: 医生，我最近感觉不舒服        │   │
│ └─────────────┘ │ │                              10:30  │   │
│ ┌─────────────┐ │ │                                     │   │
│ │ ○ 李四      │ │ │      医生: 哪里不舒服？具体说说      │   │ ← 消息内容
│ │   需要...   │ │ │                              10:31  │   │   (自适应)
│ └─────────────┘ │ │                                     │   │
│ ┌─────────────┐ │ │  张三: 头痛，还有点发烧              │   │
│ │ ○ 王五      │ │ │                              10:32  │   │
│ │   药品...   │ │ │                                     │   │
│ └─────────────┘ │ │      医生: 我安排护士给你测体温      │   │
│                 │ │                              10:33  │   │
│                 │ └─────────────────────────────────────┘   │
│                 │ ┌─────────────────────────────────────┐   │
│      300px      │ │ [输入消息...] [表情] [文件] [发送]  │   │ ← 输入区
│                 │ └─────────────────────────────────────┘   │
└─────────────────┴───────────────────────────────────────────┘
```

**使用代码：**
```vue
<div class="bsp-chat-layout">
  <div class="bsp-chat-layout-sidebar">
    <div class="bsp-chat-layout-sidebar-header">搜索</div>
    <div class="bsp-chat-layout-sidebar-list">会话列表</div>
  </div>
  <div class="bsp-chat-layout-main">
    <div class="bsp-chat-layout-main-header">会话标题</div>
    <div class="bsp-chat-layout-main-content">消息内容</div>
    <div class="bsp-chat-layout-main-input">输入框</div>
  </div>
</div>
```

## 📏 关键尺寸说明

### 固定尺寸
- **管理布局左侧**: 340px
- **选择布局左侧**: 400px
- **工作台侧边栏**: 280px
- **聊天侧边栏**: 300px
- **详情页头部/底部**: 60px
- **主从表主表**: 40% 高度

### 响应式断点
- **小屏**: 768px 以下自动变为垂直布局
- **卡片网格**: 最小卡片宽度 300px，自动响应式排列

## 🎨 样式变量

所有布局使用统一的设计变量：

```less
// 尺寸变量
@layout-left-width-default: 340px;
@selection-left-width: 400px;
@dashboard-sidebar-width: 280px;

// 颜色变量
@border-color-base: #dcdee2;
@background-color-base: #ffffff;
@background-color-light: #f5faff;

// 间距变量
@padding-sm: 8px;
@padding-md: 16px;
@padding-lg: 24px;
```

## 🔄 迁移指南

### 从 Inquiry-wrap 迁移到 bsp-management-layout

```vue
<!-- 旧代码 -->
<div class="Inquiry-wrap">
  <div class="Inquiry-wrap-left">查询条件</div>
  <div class="Inquiry-wrap-right">数据表格</div>
</div>

<!-- 新代码 -->
<div class="bsp-management-layout">
  <div class="bsp-management-layout-left">查询条件</div>
  <div class="bsp-management-layout-right">数据表格</div>
</div>
```

## 💡 选择建议

| 页面功能 | 推荐布局 | 原因 |
|---------|----------|------|
| 数据管理列表 | `bsp-management-layout` | 左侧查询，右侧表格 |
| 人员/设备选择 | `bsp-selection-layout` | 双栏选择交互 |
| 记录详情查看 | `bsp-detail-layout` | 标准详情页结构 |
| 搜索功能页面 | `bsp-search-layout` | 顶部搜索+筛选结果 |
| 数据统计看板 | `bsp-dashboard-layout` | 网格化展示 |
| 人员卡片展示 | `bsp-card-grid-layout` | 响应式卡片网格 |
| 多步骤操作 | `bsp-wizard-layout` | 引导式操作流程 |
| 主从数据关系 | `bsp-master-detail-layout` | 一对多数据展示 |
| 在线沟通 | `bsp-chat-layout` | 聊天交互界面 |

## 🚀 快速开始

1. **选择合适的布局类型**：根据页面功能从上表选择
2. **复制对应的HTML结构**：从本文档复制基础代码
3. **添加具体内容**：在对应区域填入业务内容
4. **自定义样式**：如需要，在组件内添加scoped样式

## 📋 注意事项

- 所有布局都支持响应式设计
- 使用变量系统便于主题定制
- 遵循BEM命名规范
- 优先使用全局布局类，避免重复开发
- 在小屏设备上会自动适配为垂直布局
