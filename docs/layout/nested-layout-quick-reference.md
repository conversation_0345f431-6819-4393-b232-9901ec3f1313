# 嵌套布局快速参考指南

## 🎯 嵌套布局类型速查

### 1. 基础嵌套：外层左右 + 左侧上下

```html
<div class="bsp-nested-layout">
  <div class="bsp-nested-layout-left">
    <div class="bsp-nested-layout-left-top">搜索条件</div>
    <div class="bsp-nested-layout-left-bottom">树形菜单</div>
  </div>
  <div class="bsp-nested-layout-right">
    <div class="bsp-nested-layout-right-header">工具栏</div>
    <div class="bsp-nested-layout-right-content">主要内容</div>
  </div>
</div>
```

**适用场景**：病情管理、人员管理、设备管理等常规管理页面

### 2. 复杂嵌套：三层嵌套布局

```html
<div class="bsp-complex-nested-layout">
  <div class="bsp-complex-nested-layout-left">
    <div class="bsp-complex-nested-layout-left-search">高级搜索</div>
    <div class="bsp-complex-nested-layout-left-tree">分类树</div>
  </div>
  <div class="bsp-complex-nested-layout-right">
    <div class="bsp-complex-nested-layout-right-toolbar">统计工具栏</div>
    <div class="bsp-complex-nested-layout-right-main">主数据表格</div>
    <div class="bsp-complex-nested-layout-right-detail">详情面板</div>
  </div>
</div>
```

**适用场景**：复杂数据管理系统、监控平台、分析系统

## 📋 iView组件使用规范

### 表单组件

```vue
<!-- 标准表单结构 -->
<Form ref="formRef" :model="formData" :label-width="80">
  <FormItem label="姓名" prop="name">
    <Input v-model="formData.name" placeholder="请输入姓名" />
  </FormItem>
  
  <FormItem label="监区" prop="area">
    <Select v-model="formData.area" placeholder="请选择监区">
      <Option value="1">一监区</Option>
      <Option value="2">二监区</Option>
    </Select>
  </FormItem>
  
  <FormItem label="状态" prop="status">
    <CheckboxGroup v-model="formData.statusList">
      <Checkbox label="healthy">健康</Checkbox>
      <Checkbox label="sick">患病</Checkbox>
    </CheckboxGroup>
  </FormItem>
  
  <FormItem>
    <Button type="primary" @click="handleSubmit">提交</Button>
    <Button @click="handleReset">重置</Button>
  </FormItem>
</Form>
```

### 表格组件

```vue
<!-- 标准表格结构 -->
<Table 
  :columns="columns"
  :data="tableData"
  :loading="loading"
  stripe
  border
  @on-selection-change="handleSelection">
</Table>

<!-- 表格列定义 -->
columns: [
  { type: 'selection', width: 60 },
  { type: 'index', width: 60, title: '序号' },
  { title: '姓名', key: 'name', width: 100 },
  {
    title: '状态',
    key: 'status',
    render: (h, params) => {
      return h('Tag', {
        props: { color: 'blue' }
      }, params.row.status);
    }
  }
]
```

### 树形组件

```vue
<!-- 树形菜单 -->
<Tree 
  :data="treeData"
  @on-select-change="handleTreeSelect"
  show-checkbox>
</Tree>

<!-- 树形数据结构 -->
treeData: [
  {
    title: '一监区',
    value: '1',
    expand: true,
    children: [
      { title: '101室', value: '1-101' },
      { title: '102室', value: '1-102' }
    ]
  }
]
```

## 🎨 样式定制指南

### 区域间距调整

```less
// 调整左侧区域宽度
.bsp-nested-layout-left {
  width: 380px; // 默认340px
}

// 调整上下区域高度比例
.bsp-nested-layout-left-top {
  height: 250px; // 固定高度
}

.bsp-nested-layout-left-bottom {
  flex: 1; // 自适应剩余高度
}
```

### 内容区域样式

```less
// 搜索区域样式
.search-panel {
  padding: 16px;
  background: #f5faff;
  border-radius: 6px;
  
  h4 {
    margin-bottom: 16px;
    color: #333;
    border-bottom: 2px solid #2b5fd9;
    padding-bottom: 8px;
  }
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fff;
  border-bottom: 1px solid #dcdee2;
}
```

## 🔧 常用功能实现

### 搜索功能

```javascript
// 搜索处理
handleSearch() {
  this.loading = true;
  
  const params = {
    ...this.searchForm,
    page: this.currentPage,
    pageSize: this.pageSize
  };
  
  this.loadData(params).finally(() => {
    this.loading = false;
  });
}

// 重置搜索
handleReset() {
  this.$refs.searchForm.resetFields();
  this.handleSearch();
}
```

### 树形选择联动

```javascript
// 树形节点选择
handleTreeSelect(selectedNodes) {
  // 获取选中的节点值
  const selectedValues = selectedNodes.map(node => node.value);
  
  // 更新搜索条件
  this.searchForm.areas = selectedValues;
  
  // 自动触发搜索
  this.handleSearch();
}
```

### 表格行选择

```javascript
// 表格选择变化
handleSelectionChange(selection) {
  this.selectedRows = selection;
}

// 当前行变化（用于详情面板）
handleCurrentRowChange(currentRow) {
  this.currentRowData = currentRow;
  if (currentRow) {
    this.loadDetailData(currentRow.id);
  }
}
```

### 批量操作

```javascript
// 批量删除
handleBatchDelete() {
  if (this.selectedRows.length === 0) {
    this.$Message.warning('请先选择要删除的记录');
    return;
  }
  
  this.$Modal.confirm({
    title: '批量删除确认',
    content: `确定要删除选中的 ${this.selectedRows.length} 条记录吗？`,
    onOk: () => {
      // 执行批量删除
      this.performBatchDelete();
    }
  });
}
```

## 📱 响应式适配

### 移动端适配

```less
@media (max-width: 768px) {
  .bsp-nested-layout {
    flex-direction: column;
    
    &-left {
      width: 100%;
      height: 300px;
      border-right: none;
      border-bottom: 1px solid #dcdee2;
    }
    
    &-right {
      flex: 1;
    }
  }
}
```

### 平板适配

```less
@media (max-width: 992px) {
  .bsp-complex-nested-layout-right-toolbar {
    flex-direction: column;
    gap: 16px;
    
    .stats-summary {
      width: 100%;
      justify-content: space-around;
    }
  }
}
```

## 💡 最佳实践

### 1. 区域职责划分

- **左上**：搜索条件、筛选器
- **左下**：分类树、快速筛选、统计信息
- **右上**：工具栏、统计概览、批量操作
- **右中**：主要数据表格
- **右下**：详情面板、操作历史

### 2. 交互设计原则

- **就近原则**：相关功能放在相邻区域
- **联动原则**：区域间数据要有联动关系
- **反馈原则**：操作要有明确的视觉反馈
- **一致性**：同类操作保持一致的交互方式

### 3. 性能优化

```javascript
// 使用防抖优化搜索
import { debounce } from 'lodash';

methods: {
  handleSearch: debounce(function() {
    this.loadData();
  }, 300),
  
  // 虚拟滚动优化大数据表格
  handleVirtualScroll() {
    // 实现虚拟滚动逻辑
  }
}
```

### 4. 数据管理

```javascript
// 使用Vuex管理复杂状态
computed: {
  ...mapState(['searchConditions', 'tableData', 'selectedRows']),
  ...mapGetters(['filteredData', 'statisticsData'])
},

methods: {
  ...mapActions(['loadTableData', 'updateSearchConditions'])
}
```

## 🚀 快速开始模板

```vue
<template>
  <div class="bsp-nested-layout">
    <div class="bsp-nested-layout-left">
      <div class="bsp-nested-layout-left-top">
        <!-- 搜索表单 -->
      </div>
      <div class="bsp-nested-layout-left-bottom">
        <!-- 树形菜单 -->
      </div>
    </div>
    <div class="bsp-nested-layout-right">
      <div class="bsp-nested-layout-right-header">
        <!-- 工具栏 -->
      </div>
      <div class="bsp-nested-layout-right-content">
        <!-- 数据表格 -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NestedLayoutTemplate',
  data() {
    return {
      searchForm: {},
      tableData: [],
      loading: false
    }
  },
  methods: {
    handleSearch() {},
    handleReset() {},
    loadData() {}
  }
}
</script>
```

复制此模板，根据具体需求填充内容即可快速构建嵌套布局页面。
