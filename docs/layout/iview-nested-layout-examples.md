# iView组件复杂嵌套布局样例

## 🎯 布局结构图解

### 样例1：嵌套布局 - 外层左右，左侧上下分栏

```
┌─────────────────────────────────────────────────────────────┐
│                    嵌套布局示例                              │
├─────────────────────┬───────────────────────────────────────┤
│                     │                                       │
│    左上：搜索条件    │              右侧：主要内容区域        │
│                     │                                       │
│ ┌─────────────────┐ │ ┌─────────────────────────────────┐   │
│ │ 查询表单        │ │ │          工具栏                 │   │
│ │ [姓名] [监区]   │ │ │ [新增] [导出] [刷新]            │   │
│ │ [状态] [查询]   │ │ └─────────────────────────────────┘   │
│ └─────────────────┘ │ ┌─────────────────────────────────┐   │
├─────────────────────┤ │                                 │   │
│                     │ │          数据表格               │   │
│   左下：树形菜单     │ │                                 │   │
│                     │ │ 姓名│年龄│监区│状态│操作        │   │
│ ┌─────────────────┐ │ │ ────┼───┼───┼───┼────        │   │
│ │ ▼ 一监区        │ │ │ 张三│35 │一区│健康│编辑        │   │
│ │   ├ 101室      │ │ │ 李四│42 │二区│患病│编辑        │   │
│ │   └ 102室      │ │ │ 王五│28 │一区│健康│编辑        │   │
│ │ ▼ 二监区        │ │ │                                 │   │
│ │   ├ 201室      │ │ └─────────────────────────────────┘   │
│ │   └ 202室      │ │                                       │
│ └─────────────────┘ │                                       │
│                     │                                       │
│       340px         │                自适应                  │
└─────────────────────┴───────────────────────────────────────┘
```

### 样例2：复杂嵌套布局 - 三层嵌套

```
┌─────────────────────────────────────────────────────────────┐
│                  复杂嵌套布局示例                            │
├─────────────────────┬───────────────────────────────────────┤
│                     │                                       │
│   左上：高级搜索     │         右上：统计工具栏               │
│                     │                                       │
│ ┌─────────────────┐ │ ┌─────────────────────────────────┐   │
│ │ 多条件搜索表单   │ │ │ 总数:1234 │ 今日:15 │ [导出]   │   │
│ │ 时间范围选择    │ │ └─────────────────────────────────┘   │
│ └─────────────────┘ ├───────────────────────────────────────┤
├─────────────────────┤ │                                       │
│                     │ │         右中：主数据表格               │
│   左下：分类树       │ │                                       │
│                     │ │ [表格数据展示区域]                    │
│ ┌─────────────────┐ │ │                                       │
│ │ ▼ 人员分类      │ │ │                                       │
│ │   ├ 在押人员    │ │ │                                       │
│ │   ├ 重病号      │ │ │                                       │
│ │   └ 出所人员    │ │ │                                       │
│ │ ▼ 监区分类      │ │ │                                       │
│ │   ├ 一监区      │ │ │                                       │
│ │   └ 二监区      │ │ │                                       │
│ └─────────────────┘ ├───────────────────────────────────────┤
│                     │ │                                       │
│                     │ │      右下：详情/操作面板               │
│                     │ │                                       │
│                     │ │ [选中记录的详细信息和操作按钮]         │
│                     │ │                                       │
│       400px         │ └───────────────────────────────────────┘
└─────────────────────┴───────────────────────────────────────┘
```

## 🚀 完整代码样例

### 样例1：病情管理嵌套布局页面

```vue
<template>
  <div class="bsp-nested-layout">
    <!-- 左侧区域：搜索条件 + 树形菜单 -->
    <div class="bsp-nested-layout-left">
      <!-- 左上：搜索条件 -->
      <div class="bsp-nested-layout-left-top">
        <h4>查询条件</h4>
        <Form 
          ref="searchForm" 
          :model="searchForm" 
          :label-width="80"
          @submit.native.prevent>
          
          <FormItem label="姓名">
            <Input 
              v-model="searchForm.name" 
              placeholder="请输入姓名"
              clearable
              @on-enter="handleSearch" />
          </FormItem>
          
          <FormItem label="监区">
            <Select 
              v-model="searchForm.area" 
              placeholder="请选择监区"
              clearable>
              <Option value="">全部监区</Option>
              <Option value="1">一监区</Option>
              <Option value="2">二监区</Option>
              <Option value="3">三监区</Option>
            </Select>
          </FormItem>
          
          <FormItem label="健康状态">
            <Select 
              v-model="searchForm.healthStatus" 
              placeholder="请选择状态"
              clearable>
              <Option value="">全部状态</Option>
              <Option value="healthy">健康</Option>
              <Option value="sick">患病</Option>
              <Option value="serious">重病号</Option>
            </Select>
          </FormItem>
          
          <FormItem label="入所时间">
            <DatePicker
              v-model="searchForm.dateRange"
              type="daterange"
              placement="bottom-start"
              placeholder="选择日期范围"
              style="width: 100%">
            </DatePicker>
          </FormItem>
          
          <FormItem>
            <Button 
              type="primary" 
              @click="handleSearch"
              :loading="searchLoading"
              long>
              <Icon type="ios-search" />
              查询
            </Button>
          </FormItem>
          
          <FormItem>
            <Button 
              @click="handleReset"
              long>
              <Icon type="ios-refresh" />
              重置
            </Button>
          </FormItem>
        </Form>
      </div>
      
      <!-- 左下：监区树形菜单 -->
      <div class="bsp-nested-layout-left-bottom">
        <h4>监区分布</h4>
        <Tree 
          :data="areaTreeData"
          @on-select-change="handleAreaSelect"
          show-checkbox
          multiple>
        </Tree>
        
        <Divider />
        
        <h4>快速筛选</h4>
        <div class="quick-filters">
          <Tag 
            v-for="filter in quickFilters"
            :key="filter.key"
            :color="filter.active ? 'blue' : 'default'"
            checkable
            :checked="filter.active"
            @on-change="handleQuickFilter(filter)">
            {{ filter.label }}
          </Tag>
        </div>
      </div>
    </div>
    
    <!-- 右侧区域：主要内容 -->
    <div class="bsp-nested-layout-right">
      <!-- 右侧头部：工具栏 -->
      <div class="bsp-nested-layout-right-header">
        <div class="toolbar-left">
          <h3>病情管理列表</h3>
          <Badge :count="totalCount" class="count-badge">
            <span>总记录数</span>
          </Badge>
        </div>
        
        <div class="toolbar-right">
          <ButtonGroup>
            <Button 
              type="primary" 
              icon="ios-add"
              @click="handleAdd">
              新增病情
            </Button>
            <Button 
              icon="ios-download-outline"
              @click="handleExport">
              导出数据
            </Button>
            <Button 
              icon="ios-refresh"
              @click="handleRefresh">
              刷新
            </Button>
          </ButtonGroup>
        </div>
      </div>
      
      <!-- 右侧内容：数据表格 -->
      <div class="bsp-nested-layout-right-content">
        <Table 
          ref="dataTable"
          :columns="tableColumns"
          :data="tableData"
          :loading="tableLoading"
          stripe
          border
          size="small"
          @on-selection-change="handleSelectionChange"
          @on-row-click="handleRowClick">
        </Table>
        
        <div class="table-footer">
          <div class="selection-info">
            <span v-if="selectedRows.length > 0">
              已选择 {{ selectedRows.length }} 条记录
              <Button 
                type="text" 
                size="small"
                @click="handleBatchDelete">
                批量删除
              </Button>
            </span>
          </div>
          
          <Page 
            :total="totalCount"
            :current="currentPage"
            :page-size="pageSize"
            show-sizer
            show-elevator
            show-total
            @on-change="handlePageChange"
            @on-page-size-change="handlePageSizeChange">
          </Page>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NestedLayoutExample',
  data() {
    return {
      // 搜索表单数据
      searchForm: {
        name: '',
        area: '',
        healthStatus: '',
        dateRange: []
      },
      
      // 加载状态
      searchLoading: false,
      tableLoading: false,
      
      // 分页数据
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      
      // 表格数据
      tableData: [],
      selectedRows: [],
      
      // 监区树形数据
      areaTreeData: [
        {
          title: '一监区',
          value: '1',
          expand: true,
          children: [
            { title: '101室', value: '1-101' },
            { title: '102室', value: '1-102' },
            { title: '103室', value: '1-103' }
          ]
        },
        {
          title: '二监区',
          value: '2',
          expand: true,
          children: [
            { title: '201室', value: '2-201' },
            { title: '202室', value: '2-202' },
            { title: '203室', value: '2-203' }
          ]
        },
        {
          title: '三监区',
          value: '3',
          expand: false,
          children: [
            { title: '301室', value: '3-301' },
            { title: '302室', value: '3-302' }
          ]
        }
      ],
      
      // 快速筛选
      quickFilters: [
        { key: 'today', label: '今日新增', active: false },
        { key: 'serious', label: '重病号', active: false },
        { key: 'medicine', label: '需要用药', active: false },
        { key: 'review', label: '待复查', active: false }
      ],
      
      // 表格列定义
      tableColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '序号',
          type: 'index',
          width: 80,
          align: 'center'
        },
        {
          title: '姓名',
          key: 'name',
          width: 100,
          render: (h, params) => {
            return h('div', [
              h('strong', params.row.name),
              h('br'),
              h('small', { style: { color: '#999' } }, params.row.code)
            ]);
          }
        },
        {
          title: '年龄',
          key: 'age',
          width: 80,
          align: 'center'
        },
        {
          title: '监区',
          key: 'area',
          width: 100,
          align: 'center'
        },
        {
          title: '健康状态',
          key: 'healthStatus',
          width: 120,
          align: 'center',
          render: (h, params) => {
            const statusMap = {
              'healthy': { color: 'success', text: '健康' },
              'sick': { color: 'warning', text: '患病' },
              'serious': { color: 'error', text: '重病号' }
            };
            const status = statusMap[params.row.healthStatus] || { color: 'default', text: '未知' };
            return h('Tag', {
              props: { color: status.color }
            }, status.text);
          }
        },
        {
          title: '疾病名称',
          key: 'diseaseName',
          width: 150,
          ellipsis: true,
          tooltip: true
        },
        {
          title: '入所时间',
          key: 'entryDate',
          width: 120,
          align: 'center'
        },
        {
          title: '最后检查',
          key: 'lastCheckDate',
          width: 120,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: { type: 'primary', size: 'small' },
                style: { marginRight: '5px' },
                on: { click: () => this.handleView(params.row) }
              }, '查看'),
              h('Button', {
                props: { type: 'info', size: 'small' },
                style: { marginRight: '5px' },
                on: { click: () => this.handleEdit(params.row) }
              }, '编辑'),
              h('Button', {
                props: { type: 'error', size: 'small' },
                on: { click: () => this.handleDelete(params.row) }
              }, '删除')
            ]);
          }
        }
      ]
    }
  },
  
  mounted() {
    this.loadTableData();
  },
  
  methods: {
    // 加载表格数据
    async loadTableData() {
      this.tableLoading = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟数据
        this.tableData = [
          {
            id: 1,
            name: '张三',
            code: 'P001',
            age: 35,
            area: '一监区',
            healthStatus: 'healthy',
            diseaseName: '',
            entryDate: '2023-01-15',
            lastCheckDate: '2024-01-10'
          },
          {
            id: 2,
            name: '李四',
            code: 'P002',
            age: 42,
            area: '二监区',
            healthStatus: 'sick',
            diseaseName: '高血压',
            entryDate: '2023-02-20',
            lastCheckDate: '2024-01-08'
          },
          {
            id: 3,
            name: '王五',
            code: 'P003',
            age: 28,
            area: '一监区',
            healthStatus: 'serious',
            diseaseName: '糖尿病',
            entryDate: '2023-03-10',
            lastCheckDate: '2024-01-05'
          }
        ];
        
        this.totalCount = 156; // 模拟总数
        
      } catch (error) {
        this.$Message.error('加载数据失败');
      } finally {
        this.tableLoading = false;
      }
    },
    
    // 搜索处理
    handleSearch() {
      this.searchLoading = true;
      this.currentPage = 1;
      
      // 构建搜索参数
      const params = {
        ...this.searchForm,
        page: this.currentPage,
        pageSize: this.pageSize
      };
      
      console.log('搜索参数:', params);
      
      // 重新加载数据
      this.loadTableData().finally(() => {
        this.searchLoading = false;
      });
    },
    
    // 重置搜索
    handleReset() {
      this.$refs.searchForm.resetFields();
      this.searchForm = {
        name: '',
        area: '',
        healthStatus: '',
        dateRange: []
      };
      this.handleSearch();
    },
    
    // 监区选择处理
    handleAreaSelect(selectedNodes) {
      console.log('选中的监区:', selectedNodes);
      // 可以根据选中的监区自动筛选数据
    },
    
    // 快速筛选处理
    handleQuickFilter(filter) {
      filter.active = !filter.active;
      console.log('快速筛选:', filter);
      // 根据筛选条件重新加载数据
    },
    
    // 新增
    handleAdd() {
      this.$router.push('/sick/add');
    },
    
    // 导出
    handleExport() {
      this.$Modal.confirm({
        title: '确认导出',
        content: '是否导出当前查询结果？',
        onOk: () => {
          this.$Message.success('导出成功');
        }
      });
    },
    
    // 刷新
    handleRefresh() {
      this.loadTableData();
    },
    
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    
    // 行点击
    handleRowClick(row, index) {
      console.log('点击行:', row);
    },
    
    // 查看详情
    handleView(row) {
      this.$router.push(`/sick/detail/${row.id}`);
    },
    
    // 编辑
    handleEdit(row) {
      this.$router.push(`/sick/edit/${row.id}`);
    },
    
    // 删除
    handleDelete(row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除【${row.name}】的病情记录吗？`,
        onOk: () => {
          this.$Message.success('删除成功');
          this.loadTableData();
        }
      });
    },
    
    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$Message.warning('请先选择要删除的记录');
        return;
      }
      
      this.$Modal.confirm({
        title: '批量删除确认',
        content: `确定要删除选中的 ${this.selectedRows.length} 条记录吗？`,
        onOk: () => {
          this.$Message.success('批量删除成功');
          this.selectedRows = [];
          this.loadTableData();
        }
      });
    },
    
    // 分页变化
    handlePageChange(page) {
      this.currentPage = page;
      this.loadTableData();
    },
    
    // 页面大小变化
    handlePageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.currentPage = 1;
      this.loadTableData();
    }
  }
}
</script>

<style lang="less" scoped>
// 左上搜索区域样式
.bsp-nested-layout-left-top {
  h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 2px solid #2b5fd9;
    padding-bottom: 8px;
  }
  
  .ivu-form-item {
    margin-bottom: 16px;
  }
}

// 左下树形菜单区域样式
.bsp-nested-layout-left-bottom {
  h4 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 14px;
    font-weight: 500;
  }
  
  .quick-filters {
    .ivu-tag {
      margin: 4px 4px 4px 0;
      cursor: pointer;
    }
  }
}

// 右侧工具栏样式
.bsp-nested-layout-right-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    h3 {
      margin: 0;
      color: #333;
      font-size: 16px;
    }
    
    .count-badge {
      .ivu-badge-count {
        background: #2b5fd9;
      }
    }
  }
  
  .toolbar-right {
    .ivu-btn-group .ivu-btn {
      margin-left: 0;
    }
  }
}

// 表格底部样式
.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  
  .selection-info {
    color: #666;
    font-size: 14px;
  }
}

// 响应式适配
@media (max-width: 992px) {
  .bsp-nested-layout-right-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    .toolbar-left,
    .toolbar-right {
      width: 100%;
    }
    
    .toolbar-right {
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .table-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
```

### 样例2：复杂三层嵌套布局页面

```vue
<template>
  <div class="bsp-complex-nested-layout">
    <!-- 左侧复合区域 -->
    <div class="bsp-complex-nested-layout-left">
      <!-- 左上：高级搜索条件 -->
      <div class="bsp-complex-nested-layout-left-search">
        <h4>高级搜索</h4>
        <Form
          ref="advancedSearchForm"
          :model="advancedSearchForm"
          :label-width="70"
          size="small">

          <Row :gutter="16">
            <Col span="12">
              <FormItem label="姓名">
                <Input
                  v-model="advancedSearchForm.name"
                  placeholder="姓名"
                  clearable />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="编号">
                <Input
                  v-model="advancedSearchForm.code"
                  placeholder="人员编号"
                  clearable />
              </FormItem>
            </Col>
          </Row>

          <Row :gutter="16">
            <Col span="12">
              <FormItem label="年龄">
                <InputNumber
                  v-model="advancedSearchForm.ageMin"
                  placeholder="最小年龄"
                  :min="18"
                  :max="80"
                  style="width: 100%" />
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="至">
                <InputNumber
                  v-model="advancedSearchForm.ageMax"
                  placeholder="最大年龄"
                  :min="18"
                  :max="80"
                  style="width: 100%" />
              </FormItem>
            </Col>
          </Row>

          <FormItem label="时间范围">
            <DatePicker
              v-model="advancedSearchForm.dateRange"
              type="datetimerange"
              placement="bottom-start"
              placeholder="选择时间范围"
              style="width: 100%">
            </DatePicker>
          </FormItem>

          <FormItem label="多选状态">
            <CheckboxGroup v-model="advancedSearchForm.statusList">
              <Checkbox label="healthy">健康</Checkbox>
              <Checkbox label="sick">患病</Checkbox>
              <Checkbox label="serious">重病号</Checkbox>
            </CheckboxGroup>
          </FormItem>

          <FormItem>
            <Button
              type="primary"
              @click="handleAdvancedSearch"
              :loading="advancedSearchLoading"
              size="small"
              long>
              <Icon type="ios-search" />
              高级搜索
            </Button>
          </FormItem>
        </Form>
      </div>

      <!-- 左下：分类树形菜单 -->
      <div class="bsp-complex-nested-layout-left-tree">
        <Tabs value="category" size="small">
          <TabPane label="人员分类" name="category">
            <Tree
              :data="categoryTreeData"
              @on-select-change="handleCategorySelect"
              show-checkbox>
            </Tree>
          </TabPane>

          <TabPane label="监区分布" name="area">
            <Tree
              :data="areaTreeData"
              @on-select-change="handleAreaTreeSelect"
              show-checkbox>
            </Tree>
          </TabPane>

          <TabPane label="统计信息" name="stats">
            <div class="stats-panel">
              <Card dis-hover>
                <p slot="title">
                  <Icon type="ios-people" />
                  人员统计
                </p>
                <div class="stats-item">
                  <span>总人数：</span>
                  <Badge :count="stats.total" />
                </div>
                <div class="stats-item">
                  <span>健康：</span>
                  <Badge :count="stats.healthy" status="success" />
                </div>
                <div class="stats-item">
                  <span>患病：</span>
                  <Badge :count="stats.sick" status="warning" />
                </div>
                <div class="stats-item">
                  <span>重病号：</span>
                  <Badge :count="stats.serious" status="error" />
                </div>
              </Card>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>

    <!-- 右侧复合区域 -->
    <div class="bsp-complex-nested-layout-right">
      <!-- 右上：统计工具栏 -->
      <div class="bsp-complex-nested-layout-right-toolbar">
        <div class="stats-summary">
          <Statistic
            title="总记录数"
            :value="totalCount"
            suffix="条">
            <template slot="prefix">
              <Icon type="ios-people" />
            </template>
          </Statistic>

          <Statistic
            title="今日新增"
            :value="todayCount"
            suffix="条"
            :value-style="{ color: '#3f8600' }">
            <template slot="prefix">
              <Icon type="ios-add-circle" />
            </template>
          </Statistic>

          <Statistic
            title="待处理"
            :value="pendingCount"
            suffix="条"
            :value-style="{ color: '#cf1322' }">
            <template slot="prefix">
              <Icon type="ios-alert" />
            </template>
          </Statistic>
        </div>

        <div class="toolbar-actions">
          <ButtonGroup>
            <Button type="primary" icon="ios-add">新增</Button>
            <Button icon="ios-download-outline">导出</Button>
            <Button icon="ios-print">打印</Button>
          </ButtonGroup>

          <Dropdown @on-click="handleBatchAction">
            <Button>
              批量操作
              <Icon type="ios-arrow-down"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="batchEdit">批量编辑</DropdownItem>
              <DropdownItem name="batchDelete">批量删除</DropdownItem>
              <DropdownItem name="batchExport">批量导出</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>

      <!-- 右中：主数据表格 -->
      <div class="bsp-complex-nested-layout-right-main">
        <Table
          ref="mainTable"
          :columns="mainTableColumns"
          :data="mainTableData"
          :loading="mainTableLoading"
          stripe
          border
          size="small"
          highlight-row
          @on-selection-change="handleMainTableSelection"
          @on-current-change="handleCurrentRowChange">
        </Table>
      </div>

      <!-- 右下：详情面板 -->
      <div class="bsp-complex-nested-layout-right-detail">
        <Card dis-hover>
          <p slot="title">
            <Icon type="ios-information-circle" />
            详细信息
            <span v-if="currentRowData">- {{ currentRowData.name }}</span>
          </p>

          <div slot="extra">
            <ButtonGroup size="small">
              <Button
                type="primary"
                :disabled="!currentRowData"
                @click="handleEditDetail">
                编辑
              </Button>
              <Button
                :disabled="!currentRowData"
                @click="handleViewHistory">
                历史记录
              </Button>
            </ButtonGroup>
          </div>

          <div v-if="currentRowData" class="detail-content">
            <Tabs value="basic" size="small">
              <TabPane label="基本信息" name="basic">
                <Descriptions :column="2" border>
                  <DescriptionsItem label="姓名">{{ currentRowData.name }}</DescriptionsItem>
                  <DescriptionsItem label="编号">{{ currentRowData.code }}</DescriptionsItem>
                  <DescriptionsItem label="年龄">{{ currentRowData.age }}岁</DescriptionsItem>
                  <DescriptionsItem label="监区">{{ currentRowData.area }}</DescriptionsItem>
                  <DescriptionsItem label="健康状态">
                    <Tag :color="getStatusColor(currentRowData.healthStatus)">
                      {{ getStatusText(currentRowData.healthStatus) }}
                    </Tag>
                  </DescriptionsItem>
                  <DescriptionsItem label="入所时间">{{ currentRowData.entryDate }}</DescriptionsItem>
                </Descriptions>
              </TabPane>

              <TabPane label="健康档案" name="health">
                <Timeline>
                  <TimelineItem
                    v-for="record in healthRecords"
                    :key="record.id"
                    :color="record.type === 'serious' ? 'red' : 'blue'">
                    <p class="time">{{ record.date }}</p>
                    <p class="content">{{ record.description }}</p>
                  </TimelineItem>
                </Timeline>
              </TabPane>

              <TabPane label="用药记录" name="medicine">
                <Table
                  :columns="medicineColumns"
                  :data="medicineRecords"
                  size="small"
                  :show-header="false">
                </Table>
              </TabPane>
            </Tabs>
          </div>

          <div v-else class="no-selection">
            <Icon type="ios-information-circle-outline" size="48" />
            <p>请从上方表格中选择一条记录查看详情</p>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComplexNestedLayoutExample',
  data() {
    return {
      // 高级搜索表单
      advancedSearchForm: {
        name: '',
        code: '',
        ageMin: null,
        ageMax: null,
        dateRange: [],
        statusList: []
      },

      // 加载状态
      advancedSearchLoading: false,
      mainTableLoading: false,

      // 统计数据
      totalCount: 1234,
      todayCount: 15,
      pendingCount: 8,

      stats: {
        total: 1234,
        healthy: 980,
        sick: 230,
        serious: 24
      },

      // 当前选中行数据
      currentRowData: null,
      selectedMainRows: [],

      // 主表格数据
      mainTableData: [],

      // 分类树数据
      categoryTreeData: [
        {
          title: '在押人员',
          value: 'inmate',
          expand: true,
          children: [
            { title: '普通人员', value: 'normal' },
            { title: '重点人员', value: 'important' },
            { title: '特殊人员', value: 'special' }
          ]
        },
        {
          title: '健康分类',
          value: 'health',
          expand: true,
          children: [
            { title: '健康人员', value: 'healthy' },
            { title: '患病人员', value: 'sick' },
            { title: '重病号', value: 'serious' }
          ]
        }
      ],

      // 监区树数据
      areaTreeData: [
        {
          title: '一监区',
          value: '1',
          expand: true,
          children: [
            { title: '101室 (30人)', value: '1-101' },
            { title: '102室 (28人)', value: '1-102' },
            { title: '103室 (32人)', value: '1-103' }
          ]
        },
        {
          title: '二监区',
          value: '2',
          expand: true,
          children: [
            { title: '201室 (25人)', value: '2-201' },
            { title: '202室 (27人)', value: '2-202' }
          ]
        }
      ],

      // 健康记录
      healthRecords: [
        {
          id: 1,
          date: '2024-01-15 10:30',
          description: '定期体检，血压正常，建议继续观察',
          type: 'normal'
        },
        {
          id: 2,
          date: '2024-01-10 14:20',
          description: '血压偏高，已开具降压药物',
          type: 'serious'
        }
      ],

      // 用药记录
      medicineRecords: [
        { medicine: '降压药', dosage: '1片/日', startDate: '2024-01-10' },
        { medicine: '维生素C', dosage: '2片/日', startDate: '2024-01-05' }
      ],

      // 主表格列定义
      mainTableColumns: [
        { type: 'selection', width: 60, align: 'center' },
        { type: 'index', width: 60, align: 'center', title: '序号' },
        {
          title: '基本信息',
          width: 150,
          render: (h, params) => {
            return h('div', { class: 'person-basic' }, [
              h('div', { class: 'name' }, params.row.name),
              h('div', { class: 'code' }, params.row.code),
              h('div', { class: 'area' }, params.row.area)
            ]);
          }
        },
        {
          title: '年龄',
          key: 'age',
          width: 80,
          align: 'center',
          render: (h, params) => h('span', `${params.row.age}岁`)
        },
        {
          title: '健康状态',
          key: 'healthStatus',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Tag', {
              props: { color: this.getStatusColor(params.row.healthStatus) }
            }, this.getStatusText(params.row.healthStatus));
          }
        },
        {
          title: '疾病信息',
          key: 'diseaseName',
          width: 120,
          ellipsis: true,
          tooltip: true
        },
        {
          title: '入所时间',
          key: 'entryDate',
          width: 100,
          align: 'center'
        },
        {
          title: '最后检查',
          key: 'lastCheckDate',
          width: 100,
          align: 'center'
        },
        {
          title: '操作',
          key: 'action',
          width: 120,
          align: 'center',
          render: (h, params) => {
            return h('ButtonGroup', { props: { size: 'small' } }, [
              h('Button', {
                props: { type: 'primary', size: 'small' },
                on: { click: () => this.handleQuickEdit(params.row) }
              }, '编辑'),
              h('Button', {
                props: { type: 'error', size: 'small' },
                on: { click: () => this.handleQuickDelete(params.row) }
              }, '删除')
            ]);
          }
        }
      ],

      // 用药记录表格列
      medicineColumns: [
        { title: '药品名称', key: 'medicine', width: 100 },
        { title: '用量', key: 'dosage', width: 80 },
        { title: '开始时间', key: 'startDate', width: 100 }
      ]
    }
  },

  mounted() {
    this.loadMainTableData();
  },

  methods: {
    // 加载主表格数据
    async loadMainTableData() {
      this.mainTableLoading = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));

        this.mainTableData = [
          {
            id: 1,
            name: '张三',
            code: 'P001',
            age: 35,
            area: '一监区',
            healthStatus: 'healthy',
            diseaseName: '',
            entryDate: '2023-01-15',
            lastCheckDate: '2024-01-10'
          },
          {
            id: 2,
            name: '李四',
            code: 'P002',
            age: 42,
            area: '二监区',
            healthStatus: 'sick',
            diseaseName: '高血压',
            entryDate: '2023-02-20',
            lastCheckDate: '2024-01-08'
          },
          {
            id: 3,
            name: '王五',
            code: 'P003',
            age: 28,
            area: '一监区',
            healthStatus: 'serious',
            diseaseName: '糖尿病',
            entryDate: '2023-03-10',
            lastCheckDate: '2024-01-05'
          }
        ];

      } catch (error) {
        this.$Message.error('加载数据失败');
      } finally {
        this.mainTableLoading = false;
      }
    },

    // 高级搜索
    handleAdvancedSearch() {
      this.advancedSearchLoading = true;
      console.log('高级搜索参数:', this.advancedSearchForm);

      this.loadMainTableData().finally(() => {
        this.advancedSearchLoading = false;
      });
    },

    // 分类选择
    handleCategorySelect(selectedNodes) {
      console.log('选中分类:', selectedNodes);
    },

    // 监区树选择
    handleAreaTreeSelect(selectedNodes) {
      console.log('选中监区:', selectedNodes);
    },

    // 主表格选择变化
    handleMainTableSelection(selection) {
      this.selectedMainRows = selection;
    },

    // 当前行变化
    handleCurrentRowChange(currentRow, oldCurrentRow) {
      this.currentRowData = currentRow;
      if (currentRow) {
        this.loadPersonDetail(currentRow.id);
      }
    },

    // 加载人员详情
    async loadPersonDetail(personId) {
      try {
        // 模拟加载详情数据
        console.log('加载人员详情:', personId);
      } catch (error) {
        this.$Message.error('加载详情失败');
      }
    },

    // 批量操作
    handleBatchAction(name) {
      if (this.selectedMainRows.length === 0) {
        this.$Message.warning('请先选择要操作的记录');
        return;
      }

      switch (name) {
        case 'batchEdit':
          this.$Message.info('批量编辑功能');
          break;
        case 'batchDelete':
          this.$Modal.confirm({
            title: '批量删除确认',
            content: `确定要删除选中的 ${this.selectedMainRows.length} 条记录吗？`,
            onOk: () => {
              this.$Message.success('批量删除成功');
            }
          });
          break;
        case 'batchExport':
          this.$Message.info('批量导出功能');
          break;
      }
    },

    // 编辑详情
    handleEditDetail() {
      if (this.currentRowData) {
        this.$router.push(`/person/edit/${this.currentRowData.id}`);
      }
    },

    // 查看历史记录
    handleViewHistory() {
      if (this.currentRowData) {
        this.$router.push(`/person/history/${this.currentRowData.id}`);
      }
    },

    // 快速编辑
    handleQuickEdit(row) {
      this.$router.push(`/person/edit/${row.id}`);
    },

    // 快速删除
    handleQuickDelete(row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: `确定要删除【${row.name}】吗？`,
        onOk: () => {
          this.$Message.success('删除成功');
          this.loadMainTableData();
        }
      });
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        'healthy': 'success',
        'sick': 'warning',
        'serious': 'error'
      };
      return colorMap[status] || 'default';
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'healthy': '健康',
        'sick': '患病',
        'serious': '重病号'
      };
      return textMap[status] || '未知';
    }
  }
}
</script>

<style lang="less" scoped>
// 左上高级搜索区域
.bsp-complex-nested-layout-left-search {
  h4 {
    margin: 0 0 12px 0;
    color: #333;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 2px solid #2b5fd9;
    padding-bottom: 6px;
  }

  .ivu-form-item {
    margin-bottom: 12px;
  }
}

// 左下树形区域
.bsp-complex-nested-layout-left-tree {
  .stats-panel {
    .stats-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
    }
  }
}

// 右上工具栏
.bsp-complex-nested-layout-right-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .stats-summary {
    display: flex;
    gap: 32px;
  }

  .toolbar-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

// 右中主表格区域
.bsp-complex-nested-layout-right-main {
  .person-basic {
    .name {
      font-weight: 500;
      color: #333;
      margin-bottom: 2px;
    }

    .code {
      font-size: 12px;
      color: #666;
      margin-bottom: 2px;
    }

    .area {
      font-size: 12px;
      color: #999;
    }
  }
}

// 右下详情面板
.bsp-complex-nested-layout-right-detail {
  .detail-content {
    .time {
      font-size: 12px;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .content {
      margin: 0;
      color: #666;
    }
  }

  .no-selection {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    p {
      margin-top: 16px;
      margin-bottom: 0;
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .bsp-complex-nested-layout-right-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .stats-summary {
      width: 100%;
      justify-content: space-around;
    }

    .toolbar-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }
}

@media (max-width: 768px) {
  .stats-summary {
    flex-direction: column;
    gap: 16px !important;
  }

  .toolbar-actions {
    flex-direction: column;
    width: 100% !important;

    .ivu-btn-group,
    .ivu-dropdown {
      width: 100%;
    }
  }
}
</style>
```

## 🎯 布局特点说明

### 嵌套布局的优势

1. **层次清晰**：通过嵌套实现功能区域的清晰划分
2. **空间利用**：充分利用屏幕空间，提高信息密度
3. **交互便捷**：相关功能就近放置，减少操作路径
4. **响应式友好**：支持不同屏幕尺寸的自适应

### 使用场景

1. **复杂管理系统**：需要多个功能区域协同工作
2. **数据分析平台**：需要同时展示筛选、列表、详情
3. **监控系统**：需要实时展示多维度信息
4. **工作台应用**：需要集成多个工具和信息面板

### 组件选择说明

- **Form/FormItem**：表单组件，支持验证和布局
- **Table**：数据表格，支持排序、筛选、分页
- **Tree**：树形组件，用于层级数据展示
- **Tabs**：标签页，用于内容分组
- **Card**：卡片容器，用于内容分块
- **Timeline**：时间线，用于历史记录展示
- **Statistic**：统计数值，用于数据概览
- **Descriptions**：描述列表，用于详情展示

这些样例展示了如何使用iView组件构建复杂的嵌套布局，实现功能丰富的管理界面。
