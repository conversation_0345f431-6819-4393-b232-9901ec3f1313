# Form Layout & Structure
- User prefers merging multiple forms into a single form with section-based control rather than separate forms.
- User prefers using @/src/components/bsp-layout/layouts/FormLayout.vue for forms and DetailCardLayout.vue for detail card layouts with bottom buttons.
- For forms with person selection functionality, use DetailCardLayout.vue instead of FormLayout.vue - left side for person selection component, right side for form content.
- For DynamicForm component in FORM_MODES.VIEW mode, disable the form components instead of showing '暂无数据' message.
- form-body CSS class can block date picker dropdown overlays, requiring z-index adjustments to ensure proper display layering.
- DATE_TIME_PICKER components should have 100% width styling for better form layout consistency.

# Field Types & Configuration
- FIELD_TYPES.INPUT_NUMBER should be configured as number input type rather than text input type.
- For boolean yes/no fields, use the generic yes/no component with dictionary type ZD_TYSFDM.
- For database numeric fields like one_dosage_num, user prefers integer values without decimal places (truncating not rounding).
- Dictionary fields should be translated to Chinese using ops_dic_code table for proper display.
- For use_frequency field display, concatenate with unit from ihc_pm_medicine table's min_measurement_unit field.
- serverConfig 是在 public/base.js 中定义的全局配置，并且在 main.js 中通过 Vue.mixin 混入到所有组件中
- personnel-selector 人员选择组件
# Dynamic Form & Validation
- DynamicForm component should support conditional field dependencies where selecting certain values shows/hides related fields.
- For dynamic forms with conditional field visibility, validation rules should be removed when fields are hidden and added back when fields are shown.
- For dynamic form validation with conditional fields, prefer generating all validation rules in generateRules and mergeFieldConfig, then dynamically filter which rules to apply during validation.

# Component Implementation
- FIELD_TYPES.FILE_UPLOAD components should bind defaultList property and include serviceMark, bucketName, beforeUpload, fileSuccess, fileRemove, and fileComplete event handlers.
- For FIELD_TYPES.FILE_UPLOAD components, defaultList property should be populated after network data loading is complete, not set as empty array initially.
- File upload components should use computed properties to parse JSON strings into objects for defaultList when displaying existing files.
- Boolean yes/no components should use FIELD_TYPES.DICTIONARY with dicName: 'ZD_TYSFDM' configuration.
- For marker components, implement proper echo/display logic for view mode to show existing marker data when in read-only state.
- For file upload components, reference the configuration pattern used in @/src/view/snjy/aids-management/AddForm.vue for proper serviceMark, bucketName, and multiple props setup.

# API & Data Management
- For API encapsulation, use MCP interface tools to wrap APIs into dedicated files under @/src/path/ihc/ directory.
- Reference src/path/ihc/ihs-health-check.js for API URLs. Add new URLs if needed and import them in path.js.
- For health checkup fields, use these API mappings: checkupTime, checkupMethod (ZD_CYB_JKTJFS), checkupResult (ZD_CYB_CSJG_YXYX), checkupExaminer.
- Health education (jkjy) module uses /ihc/ephe/healthEdu/create for creation and /ihc/ephe/healthEdu/get for details retrieval.
- Data loading pattern: mounted()->initializeData()->loadRecordData()
- Data saving pattern: handleBottomAction()->handleSubmit()->validateAndSave()->saveData()
- Avoid breaking the original reactivity of objects during copy and assignment operations.
- The project uses iView framework for UI components.
- 列表组件使用 rs-DataGrid

# Bottom Button Handling
- Forms should follow the established bottom button handling logic patterns documented in the project.
- 新增的表单 底部按钮是 返回、提交
- 详情的表单 底部按钮是 返回
- 底部按钮事件
```
   // 处理底部操作
    handleBottomAction(event) {
      const {action} = event

      switch (action.name) {
        case 'back':
          this.handleBack()
          break
        case 'submit':
          this.handleSubmit()
          break
      }
    }
```

# HTTP Requests
- 表单保存，调用 authPostRequest ，入参 url 和 params

# 语言环境
- 使用中文输出